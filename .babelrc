{
    "presets": [
        [
            "@babel/preset-env",
            {
                "modules": false,
                // targets: "> 1% in AU and not dead", 
                // shippedProposals: true
            }
        ],
        // ["@babel/preset-react", { runtime: "automatic" }]
    ],
    "plugins": [
        [
            "component",
            {
                "libraryName": "element-ui",
                "styleLibraryName": "theme-chalk"
            }
        ]
    ]
}