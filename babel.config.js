/*
 * @Author: 得了得了又不是造原子弹
 * @Date: 2024-09-29 09:30:53
 * @LastEditors: 杨剑兴
 * @LastEditTime: 2024-09-29 09:39:40
 * @FilePath: \一德后台\manage\babel.config.js
 */
let transformRemoveConsolePlugin = [];
if (process.env.NODE_ENV === "production") {
    transformRemoveConsolePlugin = ["transform-remove-console"];
}
module.exports = {
    presets: ["@vue/app"],
    plugins: [...transformRemoveConsolePlugin],
};
