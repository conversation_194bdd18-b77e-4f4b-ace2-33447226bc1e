{"name": "manage-system-2.0", "version": "2.3.0", "private": true, "scripts": {"dev": "vue-cli-service serve --mode development", "dev:szj": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve --mode development", "build:dev": "vue-cli-service build --mode development", "build:uat": "vue-cli-service build --mode uat", "build:locals": "vue-cli-service build --mode locals", "build:prod": "vue-cli-service build --mode production", "build:uatrelease": "vue-cli-service build --mode uatrelease", "release": "standard-version"}, "dependencies": {"@tinymce/tinymce-vue": "^3.0.1", "ali-oss": "^6.17.1", "axios": "0.19.0", "dayjs": "^1.11.13", "driver.js": "^0.9.7", "echarts": "^4.2.1", "element-china-area-data": "^6.0.2", "element-ui": "^2.15.14", "encryptlong": "^3.1.4", "js-cookie": "^3.0.1", "js-pinyin": "^0.1.9", "jsencrypt": "^3.3.2", "jszip": "^3.10.1", "less": "^3.2.0", "less-loader": "5.0.0", "mqtt": "^4.3.7", "nprogress": "0.2.0", "photoswipe": "4.1.3", "qs": "^6.10.5", "sortablejs": "^1.15.0", "standard-version": "^9.5.0", "tinymce": "^5.5.0", "vue": "^2.6.10", "vue-cropper": "0.4.9", "vue-lazyload": "1.3.1", "vue-ls": "3.2.1", "vue-router": "3.6.5", "vue-slicksort": "^1.1.3", "vuedraggable": "^2.24.3", "vuex": "3.0.1", "ydutils": "git+http://root:<EMAIL>/web/ydutils.git#main"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.9.0", "@vue/cli-service": "^3.9.0", "@vue/test-utils": "1.0.0-beta.29", "babel-plugin-component": "^1.1.1", "babel-plugin-transform-remove-console": "^6.9.4", "image-webpack-loader": "^5.0.0", "lint-staged": "^8.1.5", "sass": "1.32.13", "sass-loader": "^7.3.1", "vue-cli-service": "^5.0.10", "vue-template-compiler": "^2.6.10"}, "prettier": {"tabWidth": 4}}