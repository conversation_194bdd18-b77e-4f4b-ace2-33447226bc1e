<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="icon" href="<%= BASE_URL %>favicon.png" />
    <title>一德综合管理平台</title>
    <script>
        function iEVersion() {
            var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
            var isIE =
                userAgent.indexOf("compatible") > -1 &&
                userAgent.indexOf("MSIE") > -1; //判断是否IE<11浏览器
            var isEdge = userAgent.indexOf("Edge") > -1 && !isIE; //判断是否IE的Edge浏览器
            var isIE11 =
                userAgent.indexOf("Trident") > -1 &&
                userAgent.indexOf("rv:11.0") > -1;
            if (isIE) {
                var reIE = new RegExp("MSIE (\\d+\\.\\d+);");
                reIE.test(userAgent);
                var fIEVersion = parseFloat(RegExp["$1"]);
                if (fIEVersion == 7) {
                    return false;
                } else if (fIEVersion == 8) {
                    return false;
                } else if (fIEVersion == 9) {
                    return false;
                } else if (fIEVersion == 10) {
                    return false;
                } else {
                    return false; //IE版本<=7
                }
            } else if (isEdge) {
                return true; //edge
            } else if (isIE11) {
                return false; //IE11
            } else {
                return true; //不是ie浏览器
            }
        }
        if (!iEVersion()) {
            alert("不支持IE浏览器哦！");
        }
    </script>

</head>

<body>
    <noscript>
        <strong> 为正常使用请激活JavaScript!</strong>
    </noscript>
    <div id="app">
        <style>
            dot {
                display: inline-block;
                height: 1em;
                line-height: 1;
                text-align: left;
                vertical-align: -0.25em;
                overflow: hidden;
            }

            dot::before {
                display: block;
                content: "...\A..\A.";
                white-space: pre-wrap;
                animation: dot 2s infinite step-start both;
            }

            @keyframes dot {
                33% {
                    transform: translateY(-2em);
                }

                66% {
                    transform: translateY(-1em);
                }
            }
        </style>
        <div style="padding: 50px">正在加载中<dot>...</dot>
        </div>
    </div>
    <!-- built files will be auto injected -->
  
    <!-- 滑块验证 -->
    <script src="https://turing.captcha.qcloud.com/TCaptcha.js"></script>
</body>
</html>