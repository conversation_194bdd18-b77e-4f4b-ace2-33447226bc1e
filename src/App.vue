<template>
    <section id="app">
        <transition name="slide-fade">
            <router-view />
        </transition>
    </section>
</template>

<script>
import { getEnableByType } from "@/api/announce.js";
import store from "@/store";
import { setRouter } from "@/router";
export default {
    props: {},
    data() {
        return {};
    },
    computed: {},
    created() {
        if (window.location.href.indexOf("login") == -1) {
            setRouter();
        }
        this.getEnableByTypeFn();
        setInterval(() => {
            this.getEnableByTypeFn();
        }, 8000);
    },
    watch: {},
    methods: {
        getEnableByTypeFn() {
            //announceType 公告对象
            //  1.云平台, 2. IOS, 3.Android, 4. H5 ,5.一德后台
            getEnableByType({ announceType: 5 }).then((res) => {
                if (res.data) {
                    store.commit("setAnnounceData", res.data);
                } else {
                    store.commit("setAnnounceData", {});
                }
            });
        },
    },
    mounted() {},
};
</script>
<style lang="scss">
#nprogress .bar {
    background: $subject-color !important;
}
.slide-fade-enter-active {
    transition: all 0.3s ease;
}
.slide-fade-leave-active {
    transition: all 0.5s ease;
}
.slide-fade-enter {
    transform: translateX(20px);

    opacity: 0;
}
.slide-fade-leave-active {
    opacity: 0;
}
.el-table th.gutter {
    display: table-cell !important;
}
</style>
