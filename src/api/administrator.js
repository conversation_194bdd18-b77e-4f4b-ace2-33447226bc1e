
/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: jing<PERSON>
 * @Date: 2022-06-11 16:40:41
 * @LastEditors: jingrou
 * @LastEditTime: 2022-06-14 14:38:51 
 */
import { Axios } from "@/utils/axios";
import Qs from "qs";

// 获取模板列表
export function getTemplateList(data) {
    return Axios({
        url: "manage/school/template/list",
        method: "get",
        params: data,
    });
}

//获取学校结构
export function getTemplateRollList(data) {
    return Axios({
        url: "manage/school/template/rollList",
        method: "get",
        params: data,
    });
}

// 学校应用列表
export function getSchoolAppList(data) {
    return Axios({
        url: "/manage/v2/manage/school/pageSchoolManageInfo",
        method: "post",
        data,
    });
}
// 新增学校应用信息
export function addSchoolAppList(data) {
    return Axios({
        url: "/manage/v2/manage/school/createSchool",
        method: "post",
        data,
    });
}
// 获取学校信息回显
export function echoSchoolAppList(data) {
    return Axios({
        url: "/manage/v2/manage/school/getSchoolInfo",
        method: "post",
        data,
    });
}
// 编辑学校应用信息
export function editSchoolAppList(data) {
    return Axios({
        url: "/manage/v2/manage/school/updateSchoolInfo",
        method: "post",
        data,
    });
}


// 新版 获取学校的详情接口
export function getSchoolInfov2(data) {
    return Axios({
        url: "/manage/v2/manage/school/getSchoolInfo",
        method: "post",
        data,
    });
}



// 新版 获取一些基础配置的模板列表
export function getSystemTemplateComment(data) {
    return Axios({
        url: "/manage/systemTemplateComment/list",
        method: "post",
        data,
    });
}


// 改平台状态
export function updateSchoolStatus(data) {
    return Axios({
        url: "/manage/v2/manage/school/updateSchoolStatus",
        method: "post",
        data,
    });
}

// 学校审核
export function updateStatus(data) {
    return Axios({
        url: "/manage/partner/campuspay-school/updateStatus",
        method: "post",
        data,
    });
}

// 学校审核列表
export function getSchoolCheckList(data) {
    return Axios({
        url: "/manage/partner/campuspay-school/page",
        method: "post",
        data,
    });
}

// 学校审核列表详情
export function getSchoolCheckInfo(data) {
    return Axios({
        url: "/manage/partner/campuspay-school/get",
        method: "get",
        params: data,
    });
}

// 获取天气配置地区
export function getWeatherArea(data) {
    return Axios({
        url: "/manage/v2/manage/school/getWeatherArea",
        method: "get",
        params: data,
    });
}

// 获取省市区配置地区
export function getAreaList() {
    return Axios({
        url: "/manage//partnerMgmt/getAreaList",
        method: "get",
    });
}
