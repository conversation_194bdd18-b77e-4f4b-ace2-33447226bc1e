// 公告管理接口

import { Axios } from "@/utils/axios";

// 字典
export function getDict(data) {
    return Axios({
        url: "/system/systemDict/get",
        method: "post",
        data,
    });
}

// 列表;
export function getAnnounceList(data) {
    return Axios({
        url: "/manage/v2/announcement/page",
        method: "post",
        data,
    });
}

// 新增
export function createAnnounce(data) {
    return Axios({
        url: "/manage/v2/announcement/create",
        method: "post",
        data,
    });
}

// 详情
export function announceDetail(data) {
    return Axios({
        url: "/manage/v2/announcement/getAnnouncementDetail",
        method: "post",
        data,
    });
}

// 编辑
export function updateAnnounce(data) {
    return Axios({
        url: "/manage/v2/announcement/update",
        method: "post",
        data,
    });
}

// 删除
export function deleteAnnounce(data) {
    return Axios({
        url: "/manage/v2/announcement/delete",
        method: "post",
        data,
    });
}

// 修改状态
export function updateStatus(data) {
    return Axios({
        url: "/manage/v2/announcement/updateStatus",
        method: "post",
        data,
    });
}

// 查询最新公告
export function getEnableByType(data) {
    return Axios({
        url: "/manage/v2/announcement/getEnableByType",
        method: "post",
        data,
    });
}
