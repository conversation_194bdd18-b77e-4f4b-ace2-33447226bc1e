import Qs from "qs";
import { Axios } from "@/utils/axios";

// 获取班牌系统信息
export function getBrandApplicationList(data) {
    return Axios({
        url: "/manage/v2/layoutOffice/queryBrandApplicationList",
        method: "get",
        params: data,
    });
}
// 验证方式列表
export function getValidationList(data) {
    return Axios({
        url: "/manage/v2/layoutOffice/queryValidationList",
        method: "post",
        data,
    });
}

// 删除验证方式
export function removeValidation(data) {
    return Axios({
        url: "/manage/v2/layoutOffice/deleteValidation",
        method: "post",
        data,
    });
}
// ____________________________________________________________________________
//添加模块分类
export function addClassify(data) {
    return Axios({
        url: "/manage/v2/layoutOffice/createClassify",
        method: "post",
        data,
    });
}
// 模板分类列表
export function getClassifyList(data) {
    return Axios({
        url: "/manage/v2/layoutOffice/queryLayoutClassifyList",
        method: "post",
        data,
    });
}
// 删除模板分类
export function deleteModuleEntity(data) {
    return Axios({
        url: "/manage/v2/layoutOffice/deleteClassify",
        method: "post",
        data,
    });
}
// 添加尺寸布局
export function addBrandLayoutSize(data) {
    return Axios({
        url: "/manage/v2/layoutOffice/createBrandLayoutSize",
        method: "post",
        data,
    });
}
// 尺寸分布列表
export function getLayoutSize(data) {
    return Axios({
        url: "/manage/v2/layoutOffice/queryBrandLayoutSizeList",
        method: "post",
        data,
    });
}
// 模块列表
export function getModuleList(data) {
    return Axios({
        url: "/manage/v2/layoutOffice/queryLayoutModuleList",
        method: "post",
        data,
    });
}
// 添加模块
export function createLayoutModule(data) {
    return Axios({
        url: "/manage/v2/layoutOffice/createLayoutModule",
        method: "post",
        data,
    });
}
// 回显模板
export function getLayoutModuleDetail(data) {
    return Axios({
        url: "/manage/v2/layoutOffice/getLayoutModuleDetail",
        method: "post",
        data,
    });
}
// 编辑模板
export function updateLayoutModule(data) {
    return Axios({
        url: "/manage/v2/layoutOffice/updateLayoutModule",
        method: "post",
        data,
    });
}
// 删除模板
export function deleteLayoutModule(data) {
    return Axios({
        url: "/manage/v2/layoutOffice/deleteLayoutModule",
        method: "post",
        data,
    });
}

// 添加/编辑模块风格
export function addEditStyleModule(data) {
    return Axios({
        url: "/manage/layout/style/saveOrUpdate",
        method: "post",
        data,
    });
}

// 模板风格列表
export function styleModuleList(data) {
    return Axios({
        url: "/manage/layout/style/list",
        method: "post",
        data,
    });
}

// 删除模板风格
export function deleteStyleModule(data) {
    return Axios({
        url: "/manage/layout/style/delete",
        method: "post",
        data,
    });
}

/* 菜单管理 ____________________________________________________________________________*/
//根据应用获取菜单列表
export function menuListApi(data) {
    return Axios({
        url: "/backstage/cloud-backstage/brand/menu/list",
        method: "post",
        data,
    });
}
//获取应用列表
export function applicationListApi(data) {
    return Axios({
        url: "/backstage/cloud-backstage/brand/application/list",
        method: "get",
        data,
    });
}
//获取应用列表
export function menuSaveApi(data) {
    return Axios({
        url: "/backstage/cloud-backstage/brand/menu/save",
        method: "post",
        data,
    });
}
//逻辑删除菜单
export function menuDeleteApi(data) {
    return Axios({
        url: "/backstage/cloud-backstage/brand/menu/delete/" + data,
        method: "get",
    });
}
//菜单排序
export function menuSortMenuApi(data) {
    return Axios({
        url: "/backstage/cloud-backstage/brand/menu/sortMenu",
        method: "post",
        data,
    });
}

/* 版本管理 ____________________________________________________________________________*/
//版本管理信息列表
export function quetyRegistrationCodeListApi(data) {
    return Axios({
        url: "/backstage/cloud-backstage/versionManagement/quetyRegistrationCodeList",
        method: "post",
        data,
    });
}

//版本分类添加
export function batchAddClassificationApi(data) {
    return Axios({
        url: "/backstage/cloud-backstage/versionManagement/batchAddClassification",
        method: "post",
        data,
    });
}

//版本分类查询
export function queryClassificListApi(data) {
    return Axios({
        url: "/backstage/cloud-backstage/versionManagement/queryClassificList",
        method: "post",
        data,
    });
}

//版本管理信息添加
export function insertVersionManagementApi(data) {
    return Axios({
        url: "/backstage/cloud-backstage/versionManagement/insertVersionManagement",
        method: "post",
        data: data,
        timeout: 60000000,
        headers: {
            "Content-Type": "application/json;charset=UTF-8",
        },
    });
}
//版本管理信息修改
export function updateVersionManagementApi(data) {
    return Axios({
        url: "/backstage/cloud-backstage/versionManagement/updateVersionManagement",
        method: "post",
        data: data,
        timeout: 60000000,
        headers: {
            "Content-Type": "application/json;charset=UTF-8",
        },
    });
}
//版本管理信息删除
export function delVersionManagementApi(data) {
    return Axios({
        url: "/backstage/cloud-backstage/versionManagement/delVersionManagement",
        method: "post",
        data: data,
    });
}
//删除版本分类
export function delVersionClassificationApi(data) {
    return Axios({
        url: "/backstage/cloud-backstage/versionManagement/delVersionClassification",
        method: "post",
        data: data,
    });
}

// 学校管理-新增
export function insetVersionSchoolApi(data) {
    return Axios({
        url: "/backstage/cloud-backstage/versionManagement/insetVersionSchool",
        method: "post",
        data: data,
    });
}

// 学校管理-查詢學校
export function searchSchoolApi(data) {
    return Axios({
        url: "/backstage/cloud-backstage/school/searchSchoolLikeName",
        method: "post",
        data: data,
    });
}
// 学校列表
export function searchSchoolList(data) {
    return Axios({
        url: "/backstage/cloud-backstage/school/searchSchool",
        method: "post",
        data: data,
    });
}
// 学校列表
export function searchVersionSchool(data) {
    return Axios({
        url: "/manage/v2/application/version/querySchoolInfoList",
        method: "get",
        data,
    });
}

// 学校管理-删除學校
export function delVersionSchoolApi(data) {
    return Axios({
        url: "/backstage/cloud-backstage/versionManagement/delVersionSchool",
        method: "post",
        data,
    });
}
