/*
 * @Descripttion: 应用API
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-04-02 11:29:38
 * @LastEditors: jingrou
 * @LastEditTime: 2022-10-10 14:39:32
 */
import { Axios } from "@/utils/axios";
import Qs from "qs";

// 应用分组
export function getApplygroupList(data) {
    return Axios({
        url: "/backstage/cloud-backstage/applicationGoup/search",
        method: "post",
        data,
    });
}
export function createApplygroup(data) {
    return Axios({
        url: "/backstage/cloud-backstage/applicationGoup/create",
        method: "post",
        data,
    });
}

export function getApplygroupInfo(data) {
    return Axios({
        url: "/backstage/cloud-backstage/applicationGoup/searchById",
        method: "post",
        data,
    });
}
export function updateApplygroup(data) {
    return Axios({
        url: "/backstage/cloud-backstage/applicationGoup/update",
        method: "post",
        data,
    });
}
export function deleteApplyGroup(id) {
    return Axios({
        url: `/backstage/cloud-backstage/applicationGoup?id=${id}`,
        method: "delete",
        data: {},
    });
}

// 应用列表
export function getApplyList(data) {
    return Axios({
        url: "/backstage/cloud-backstage/application/search",
        method: "post",
        data,
    });
}
export function createApply(data) {
    return Axios({
        url: "/backstage/cloud-backstage/application/create",
        method: "post",
        data,
    });
}

export function getApplyInfo(data) {
    return Axios({
        url: "/backstage/cloud-backstage/application/searchById",
        method: "post",
        data,
    });
}
export function updateApply(data) {
    return Axios({
        url: "/backstage/cloud-backstage/application/update",
        method: "post",
        data,
    });
}
export function deleteApply(id) {
    return Axios({
        url: `/backstage/cloud-backstage/application?id=${id}`,
        method: "delete",
        data: {},
    });
}
export function uploadApplyLogo(data) {
    return Axios({
        url: "/backstage/cloud-backstage/application/toOss",
        method: "post",
        data: data,
    });
}

export function applySchoolSave(data) {
    return Axios({
        url: "/backstage/cloud-backstage/application/school/save",
        method: "post",
        data: data,
    });
}
//注册码列表查询
export function quetyRegistrationCodeList(data) {
    return Axios({
        url: "/backstage/cloud-backstage/registrationCode/quetyRegistrationCodeList",
        method: "post",
        data: data,
    });
}
//添加注册码
export function insetRegistrationCode(data) {
    return Axios({
        url: "/backstage/cloud-backstage/registrationCode/insetRegistrationCode",
        method: "post",
        data: data,
    });
}
//注册码注销
export function updateRegistrationCode(data) {
    return Axios({
        url: "/backstage/cloud-backstage/registrationCode/updateRegistrationCode",
        method: "post",
        data: data,
    });
}
//添加注册码 学校名称查找  /backstage/cloud-backstage/kf/application/searchLikeName
export function applicationSearchLikeName(data) {
    return Axios({
        url: "backstage/cloud-backstage/registrationCode/searchLikeName",
        method: "post",
        data: data,
    });
}
//开通管理列表
export function openSchoolList(data) {
    return Axios({
        url: "/backstage/cloud-backstage/open/school/list",
        method: "post",
        data: data,
    });
}

//模块尺寸列表
export function openSchoolModule(data) {
    return Axios({
        url: "/backstage/cloud-backstage/open/school/module",
        method: "post",
        data: Qs.stringify(data),
    });
}
//模块尺寸分配 /backstage/cloud-backstage/open/save/school/module
export function saveSchoolModule(data) {
    return Axios({
        url: "/backstage/cloud-backstage/open/save/school/module",
        method: "post",
        data: data,
    });
}
//平台菜单树列表 /backstage/cloud-backstage/brand/schoolMenu/searchBySchool/{schoolId}
export function schoolMenuSearchBySchool(data) {
    return Axios({
        url:
            "/backstage/cloud-backstage/brand/schoolMenu/searchBySchool/" +
            data,
        method: "get",
        data: data,
    });
}
//平台菜单schoolMenu
export function openSchoolMenu(data) {
    return Axios({
        url: "/backstage/cloud-backstage/brand/schoolMenu/save",
        method: "post",
        data: data,
    });
}
//验证方式列表
export function openSchoolValidation(data) {
    return Axios({
        url: "/backstage/cloud-backstage/open/school/validation",
        method: "post",
        data: Qs.stringify(data),
    });
}
//日志管理之列表
export function versionManagementQuetyVersionLogList(data) {
    return Axios({
        url: "/backstage/cloud-backstage/versionManagement/quetyVersionLogList",
        method: "post",
        data: data,
    });
}
//日志管理之处理
export function versionManagementQuetyUpdateVersionLog(data) {
    return Axios({
        url: "/backstage/cloud-backstage/versionManagement/updateVersionLog",
        method: "post",
        data: data,
    });
}
// 启动开发平台
export function openPlatform(data) {
    return Axios({
        url: "/backstage/cloud-backstage/school/version/update",
        method: "post",
        data: data,
    });
}
// 版本编辑
export function versionUpdate(data) {
    return Axios({
        url: "/backstage/cloud-backstage/document/version/update",
        method: "post",
        data: data,
    });
}
// 学校管理
export function updateAllState(data) {
    return Axios({
        url: "/backstage/cloud-backstage/school/updateAllState",
        method: "post",
        data: data,
    });
}

// 学校管理回显
export function selectState(data) {
    return Axios({
        url: "/backstage/cloud-backstage/school/selectState",
        method: "post",
        data: data,
    });
}
// 导出
export function exportInfo(data, fileName) {
    return Axios({
        url: "/manage/v2/manage/school/exportSchool",
        method: "post",
        responseType: "arraybuffer",
        data,
    }).then((blob) => {
        const url = window.URL.createObjectURL(
            new Blob([blob], {
                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            })
        );
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.setAttribute(
            "download",
            fileName ? `${fileName}.xlsx` : "excel.xlsx"
        );
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    });
}
// 重置密码
export function resetAdminPassword(data) {
    return Axios({
        url: "/manage/v2/manage/school/resetAdminPassword",
        method: "get",
        params: data,
    });
}

// 改改新的应用了

// 获取所有的应用列表
export function getAppOwner(data) {
    return Axios({
        url: "/manage/config/app/owner",
        method: "get",
        params: data,
    });
}

// 根据学校id获取已经分配的学校应用列表
export function getSchoolAppList(data) {
    return Axios({
        url: "/manage/config/dist/app/list",
        method: "get",
        params: data,
    });
}

// 分配学校应用
export function getCreateAppSchool(data) {
    return Axios({
        url: "/manage/config/dist/createAppSchool",
        method: "post",
        data: data,
    });
}

// 开通学校应用
export function getConfigOpenApp(data) {
    return Axios({
        url: "/manage/config/open/app",
        method: "post",
        data: data,
    });
}


// 分配学校菜单资源
export function getUpdateMenuBid(data) {
    return Axios({
        url: "/manage/menu/updateMenuBid",
        method: "post",
        data: data,
    });
}


// 应用菜单列表
export function getMenuList(data) {
    return Axios({
        url: "/system/menu/getMenuList",
        method: "post",
        data: data,
    });
}


// 查询学校分配的菜单资源
export function getMenuListByBid(data) {
    return Axios({
        url: "/manage/menu/getMenuListByBid",
        method: "post",
        data: data,
    });
}



export function deleteAppSchool(data) {
    return Axios({
        url: "/manage/config/dist/deleteAppSchool",
        method: "post",
        data: data,
    });
}


// 查询学校分配的设备列表
export function brandListBySchool(data) {
    return Axios({
        url: "/manage/school/device/brand/listBySchool",
        method: "post",
        data: data,
    });
}


// 分配学校设备品牌
export function updateAssignedDevice(data) {
    return Axios({
        url: "/manage/school/device/brand/updateAssignedDevice",
        method: "post",
        data: data,
    });
}

// 解锁超管的账号
export function unlockSuperAdmin(data) {
    return Axios({
        url: "/manage/v2/manage/school/unlockSuperAdmin",
        method: "post",
        data: data,
    });
}

// 获取图书馆模式
export function getLibraryModel(data) {
    return Axios({
        url: "/manage/v2/manage/school/getLibraryModel",
        method: "post",
        data,
    });
}

// 修改图书馆模式
export function updateLibraryModel(data) {
    return Axios({
        url: "/manage/v2/manage/school/updateLibraryModel",
        method: "post",
        data,
    });
}

// 获取上位机配置
export function getAlarmConfig(data) {
    return Axios({
        url: "/manage/v2/manage/school/getAlarmConfig",
        method: "post",
        data,
    });
}

// 修改上位机配置
export function updateAlarmConfig(data) {
    return Axios({
        url: "/manage/v2/manage/school/updateAlarmConfig",
        method: "post",
        data,
    });
}



export function listAppIdentityCode(data) {
    return Axios({
        url: "/manage/appIdentityApp/listAppIdentityCode",
        method: "post",
        data,
    });
}

export function listAppIdentityApp(data) {
    return Axios({
        url: "/manage/appIdentityApp//listAppIdentityApp",
        method: "post",
        data,
    });
}


