import { Axios } from "@/utils/axios";

class Article {
    // 创建文章
    create(parameter) {
        return Axios.post("/v1/article/add", parameter);
    }
    // 获取文章列表
    list(parameter) {
        return Axios.post("/v1/article/list", parameter);
    }

    // 根据文章id获取文章
    info(parameter) {
        return Axios.post("/v1/article/info", parameter);
    }

    //根据id 更新文章
    update(parameter) {
        return Axios.post("/v1/article/update", parameter);
    }

    changeStatus(parameter) {
        return Axios.post("/v1/article/change/status", parameter);
    }

    //删除
    remove(parameter) {
        return Axios.post("/v1/article/remove", parameter);
    }

    //获取分类
    classify(parameter) {
        return Axios.get("/v1/article/getclassify", parameter);
    }

    //添加分类
    saveClassify(parameter) {
        return Axios.post("/v1/article/cate/add", parameter);
    }
    // 删除文章分类
    removeClassify(parameter) {
        return Axios.post("/v1/article/cate/remove", parameter);
    }
    // 编辑文章分类
    updateClassify(parameter) {
        return Axios.post("/v1/article/cate/update", parameter);
    }
}

export default new Article();
