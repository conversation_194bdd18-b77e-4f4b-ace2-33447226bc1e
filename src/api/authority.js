import { Axios } from "@/utils/axios";

class Authority {
    //添加
    add(params) {
        //  qs.stringify(params);
        return Axios.post("/v1/authority/save", params);
    }
    //列表
    list(params) {
        return Axios.get("/v1/authority/list", {
            params: params
        });
    }

    //删除
    del(params) {
        return Axios.post("/v1/authority/remove", params);
    }

    //详情
    info(params) {
        return Axios.get("/v1/authority/info", {
            params: params
        });
    }

    //编辑
    edit(params) {
        return Axios.post("/v1/authority/update", params);
    }

    // //禁用、正常
    // changeStatus(params) {
    //     return Axios.post('/authority/changeStatus', params);
    // }
}

export default new Authority();
