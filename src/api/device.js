import { Axios } from "@/utils/axios";

/**
 * 设备列表
 * @param {*} data 
 * @returns 
 */
export function getList(data) {
  return Axios({
      url: '/manage/machineInfo/page',
      method: "post",
      data
  })
}

/**
 * 设备详情
 * @param {*} params 
 * @returns 
 */
export function getDetail(params) {
  return Axios({
      url: '/manage/machineInfo/getEquipmentDetail',
      method: "get",
      params
  })
}

/**
 * 设备类型列表
 * @param {*} data 
 * @returns 
 */
export function getTypeList(data) {
  return Axios({
      url: '/manage/equipment/relation/page',
      method: "post",
      data
  })
}

/**
 * 添加设备类型
 * @param {*} data 
 * @returns 
 */
export function addType(data) {
  return Axios({
      url: '/manage/equipment/relation/add',
      method: "post",
      data
  })
}

/**
 * 修改设备类型
 * @param {*} data 
 * @returns 
 */
export function updateType(data) {
  return Axios({
      url: '/manage/equipment/relation/update',
      method: "post",
      data
  })
}

/**
 * 删除设备类型
 * @param {*} data 
 * @returns 
 */
export function deleteType(data) {
  return Axios({
      url: '/manage/equipment/relation/delete',
      method: "post",
      data
  })
}

/**
 * 查询所有学校
 * @param {*} params 
 * @returns 
 */
export function getSchoolList(params) {
  return Axios({
      url: '/manage/v2/manage/school/listSchoolInformation',
      method: "get",
      params
  })
}

/**
 * 获取设备类型
 * @param {*} params 
 * @returns 
 */
export function getEquipmentTypeList(params) {
  return Axios({
      url: '/manage/equipment/relation/equipmentTypeList',
      method: "get",
      params
  })
}

/**
 * 获取字典
 * @param {*} data 
 * @returns 
 */
export function getDict(data) {
  return Axios({
      url: '/manage/systemDict/get',
      method: "post",
      data
  })
}



// 创建设备品牌
export function createDevBrand(data) {
  return Axios({
      url: '/manage/device/brand/create',
      method: "post",
      data
  })
}


// 修改设备品牌
export function upDevBrand(data) {
  return Axios({
      url: '/manage/device/brand/updateById',
      method: "post",
      data
  })
}


// 删除设备品牌
export function deleteDevBrand(data) {
  return Axios({
      url: '/manage/device/brand/deleteByIds',
      method: "post",
      data
  })
}


// 设备品牌分页
export function selectPageDevBrand(data) {
  return Axios({
      url: '/manage/device/brand/selectPage',
      method: "post",
      data
  })
}



// 获取设备类型
export function equipmentTypeList(params) {
  return Axios({
      url: '/manage/equipmentType/list',
      method: "get",
      params
  })
}




// 新增学校配置
export function acsSchoolcreate(data) {
  return Axios({
      url: '/manage/acsSchool/create',
      method: "post",
      data
  })
}

// 获取学校配置列表
export function acsSchoolfindMany(params) {
  return Axios({
      url: '/manage/acsSchool/findMany',
      method: "get",
      params
  })
}


// 学校配置分页
export function acsSchoolfindPaged(data) {
  return Axios({
      url: '/manage/acsSchool/findPaged',
      method: "post",
      data
  })
}

// 新增设备配置
export function acsDevicecreate(data) {
  return Axios({
      url: '/manage/acsDevice/create',
      method: "post",
      data
  })
}



// 设备配置分页
export function acsDeviceFindPaged(data) {
  return Axios({
      url: '/manage/acsDevice/findPaged',
      method: "post",
      data
  })
}



// 生成账号
export function createUserName(params) {
  return Axios({
      url: '/manage/acsDevice/createUserName',
      method: "get",
      params
  })
}


// 删除设备配置
export function acsDevicedeleteById(params) {
  return Axios({
      url: '/manage/acsDevice/deleteById',
      method: "get",
      params
  })
}

// 查看设备配置
export function acsDevicefindById(params) {
  return Axios({
      url: '/manage/acsDevice/findById',
      method: "get",
      params
  })
}


// 编辑设备配置 
export function acsDeviceupdateById(data) {
  return Axios({
      url: '/manage/acsDevice/updateById',
      method: "post",
      data
  })
}


