/*
 * @Author: your name
 * @Date: 2021-08-14 17:10:41
 * @LastEditTime: 2022-06-14 14:53:39
 * @LastEditors: jingrou
 * @Description: In User Settings Edit
 * @FilePath: \manage\src\api\faceSystem.js
 */
import { Axios } from "@/utils/axios";
import Qs from "qs";

// 编辑
export function updateFaceInfo(data) {
    return Axios({
        url: "/backstage/cloud-backstage/face/authorize/updateFaceAuthorizeInfo",
        method: "post",
        data,
    });
}
export function updateEchoInfo(data) {
    return Axios({
        url: "/backstage/cloud-backstage/face/authorize/selectFaceAuthorizeInfoById",
        method: "post",
        data,
    });
}

// 导入
export function importFaceAuthorize(id) {
    return Axios({
        url: `/backstage/cloud-backstage/face/authorize/importFaceAuthorize/${id}`,
        method: "post",
    });
}

// 导出
export function exportInfo(data, fileName) {
    return Axios({
        url: "/backstage/cloud-backstage/face/authorize/excel/downloadActivationCode",
        method: "get",
        responseType: "arraybuffer",
        params: data,
    }).then((blob) => {
        const url = window.URL.createObjectURL(
            new Blob([blob], {
                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            })
        );
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.setAttribute(
            "download",
            fileName ? `${fileName}.xlsx` : "excel.xlsx"
        );
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    });
}
// 查询
export function selectFaceAuthorizeList(data) {
    return Axios({
        url: "/backstage/cloud-backstage/face/authorize/selectFaceAuthorizeList",
        method: "post",
        data,
    });
}
// 新增
export function addFaceInfo(data) {
    return Axios({
        url: "/backstage/cloud-backstage/face/authorize/addFaceAuthorizeInfo",
        method: "post",
        data,
    });
}
