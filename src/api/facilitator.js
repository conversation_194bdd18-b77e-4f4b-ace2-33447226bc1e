//服务商管理

import { Axios } from "@/utils/axios";

// 列表
export function getPartnerList(data) {
    return Axios({
        url: "/manage/partnerMgmt/page",
        method: "post",
        data,
    });
}

// 新增
export function createPartner(data) {
    return Axios({
        url: "/manage/partnerMgmt/create",
        method: "post",
        data,
    });
}

// 修改服务商
export function updatePartner(data) {
    return Axios({
        url: "/manage/partnerMgmt/update",
        method: "post",
        data,
    });
}

// 服务商详情
export function getPartnerInfo(data) {
    return Axios({
        url: "/manage/partnerMgmt/get",
        method: "get",
        params: data,
    });
}

// 开启|关闭服务商
export function updateStatus(data) {
    return Axios({
        url: "/manage/partnerMgmt/updateStatus",
        method: "post",
        data,
    });
}

// 服务商更新记录分页
export function changeLogPage(data) {
    return Axios({
        url: "/manage/partnerMgmt/changeLogPage",
        method: "post",
        data,
    });
}

// 绑定学校
export function updatePartnerSchool(data) {
    return Axios({
        url: "/manage/partnerMgmt/updatePartnerSchool",
        method: "post",
        data,
    });
}

// 收款配置
export function updatePartnerPay(data) {
    return Axios({
        url: "/manage/partnerMgmt/updatePartnerPay",
        method: "post",
        data,
    });
}

// 获取收款配置选择列表
export function getPartnerPayList(data) {
    return Axios({
        url: "/manage/partnerMgmt/getPartnerPayList",
        method: "get",
        params: data,
    });
}

// 获取学校
export function getSchoolList(data) {
    return Axios({
        url: "/manage/v2/manage/school/getSchoolList",
        method: "post",
        data,
    });
}

// 获取服务商子级
export function getPartnerChildren(data) {
    return Axios({
        url: "/manage/partnerMgmt/getMerchantList",
        method: "post",
        data,
    });
}

// 获取场景
export function getSceneList(data) {
    return Axios({
        url: "/manage/partnerMgmt/getSceneList",
        method: "get",
        params: data,
    });
}

// 获取场景回显
export function getPartnerScene(data) {
    return Axios({
        url: "/manage/partnerMgmt/getPartnerScene",
        method: "get",
        params: data,
    });
}

// 分配场景
export function updatePartnerScene(data) {
    return Axios({
        url: "/manage/partnerMgmt/updatePartnerScene",
        method: "post",
        data,
    });
}

// 支付商分页
export function getFindPaged(data) {
    return Axios({
        url: "/manage/partnerOrg/findPaged",
        method: "post",
        data,
    });
}
