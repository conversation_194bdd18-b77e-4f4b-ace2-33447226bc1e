/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: jingrou
 * @Date: 2022-01-13 14:35:14
 * @LastEditors: jingrou
 * @LastEditTime: 2022-06-30 23:37:47
 */
import { Axios } from "@/utils/axios";
import Qs from "qs";

// 查询激活码
export function selectAuthorizecodeList(data) {
    return Axios({
        url: "/manage/v3/authorize/code/page",
        method: "post",
        data,
    });
}
// 添加激活码
export function addAuthorizecode(data) {
    return Axios({
        url: "/manage/v3/authorize/code/createAuthorizeCode",
        method: "post",
        data,
    });
}
// 编辑激活码
export function updateAuthorizeCode(data) {
    return Axios({
        url: "/manage/v3/authorize/code/updateAuthorizeCode",
        method: "post",
        data,
    });
}

// 删除激活码
export function deleteAuthorizeCode(data) {
    return Axios({
        url: "/manage/v3/authorize/code/deleteAuthorizeCode",
        method: "post",
        data,
    });
}
// 导入 第三步
export function importAuthorizeCode(data) {
    return Axios({
        url: "/manage/v3/authorize/code/importAuthorizeCode",
        method: "get",
        params: data,
    });
}
// 导入 第二步
export function getUploadProgress(data) {
    return Axios({
        url: "/manage/v3/authorize/code/getUploadProgress",
        method: "get",
        params: data,
    });
}
// 导出
export function reportingExportInfo(data, fileName) {
    return Axios({
        url: "/manage/v3/authorize/code/exportAuthorizeCode",
        method: "post",
        responseType: "arraybuffer",
        data,
    }).then((blob) => {
        const url = window.URL.createObjectURL(
            new Blob([blob], {
                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            })
        );
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.setAttribute(
            "download",
            fileName ? `${fileName}.xlsx` : "excel.xlsx"
        );
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    });
}
// 激活码转移
export function postCodeTransfer(data) {
    return Axios({
        url: "/manage/v3/authorize/code/transfer",
        method: "post",
        data,
    });
}