/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: jingrou
 * @Date: 2022-01-13 14:35:14
 * @LastEditors: jingrou
 * @LastEditTime: 2023-03-16 11:07:13
 */
import { Axios } from "@/utils/axios";
import Qs from "qs";

// 设备管理
export function getEquipmentType(data) {
    return Axios({
        url: "/manage/equipmentType/list",
        method: "get",
        params: data,
    });
}

// 查询注册码
export function selectRegistrationList(data) {
    return Axios({
        url: "/manage/v3/registration/code/page",
        method: "post",
        data,
    });
}
// 新增注册码
export function addRegistration(data) {
    return Axios({
        url: "/manage/v3/registration/code/createRegistrationCode",
        method: "post",
        data,
    });
}
// 编辑注册码
export function updateRegistration(data) {
    return Axios({
        url: "/manage/v3/registration/code/updateRegistrationCode",
        method: "post",
        data,
    });
}
// 编辑回显
export function queryRegistrationDetail(data) {
    return Axios({
        url: "/manage/v3/registration/code/getRegistrationCodeById",
        method: "post",
        data,
    });
}

// 获取服务商
export function getFindPartner(data) {
    return Axios({
        url: "/manage/partnerMgmt/findPartner",
        method: "post",
        data,
    });
}

// 回收
export function retrieveCode(data) {
    return Axios({
        url: "/manage/v3/registration/code/recoveryRegistrationCode",
        method: "get",
        params: data,
    });
}

// 学校id
export function querySchoolInfoList(data) {
    return Axios({
        url: "/cloud/v2/application/version/querySchoolInfoList",
        method: "get",
        params: data,
    });
}
// 导出
export function reportingExportInfo(data, fileName) {
    return Axios({
        url: "/manage/v3/registration/code/exportRegistrationCode",
        method: "post",
        responseType: "arraybuffer",
        data,
    }).then((blob) => {
        const url = window.URL.createObjectURL(
            new Blob([blob], {
                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            })
        );
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.setAttribute(
            "download",
            fileName ? `${fileName}.xlsx` : "excel.xlsx"
        );
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    });
}

// 注册码转移
export function getCodeTransfer(data) {
    return Axios({
        url: "/manage/v3/registration/code/transfer",
        method: "post",
        data,
    });
}

// 注册码删除
export function getCodeDelete(data) {
    return Axios({
        url: "/manage/v3/registration/code/delete",
        method: "post",
        data,
    });
}
