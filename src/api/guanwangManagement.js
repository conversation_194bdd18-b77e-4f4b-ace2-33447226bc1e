/*
 * @Description:
 * @Version: 2.0
 * @Author: jingrou
 * @Date: 2021-11-03 11:21:48
 * @LastEditors: jingrou
 * @LastEditTime: 2022-07-13 18:08:15
 */
import { Axios } from "@/utils/axios";

// ----------------------banner管理-----------------------------
// banner列表
export function getBannerList(data) {
    return Axios({
        url: "/manage/v2/banner/pageBannerInfo",
        method: "post",
        data,
    });
}
// 新增banner
export function addBannerList(data) {
    return Axios({
        url: "/manage/v2/banner/createBanner",
        method: "post",
        data,
    });
}
// 回显banner
export function echoBannerInfo(data) {
    return Axios({
        url: "/manage/v2/banner/getBannerDetailsInfo",
        method: "get",
        params:data,
    });
}
// 编辑banner
export function updateBannerInfo(data) {
    return Axios({
        url: "/manage/v2/banner/updateBannerInfo",
        method: "post",
        data,
    });
}
// 删除banner
export function deleteBannerInfo(data) {
    return Axios({
        url: "/manage/v2/banner/deleteBanner",
        method: "get",
        params:data,
    });
}

// --------------------------友链管理-----------------------------
// 友链列表
export function getFriendChainList(data) {
    return Axios({
        url: "/manage/v2/chain/pageFriendChainInfo",
        method: "post",
        data,
    });
}
// 添加友链
export function addFriendChain(data) {
    return Axios({
        url: "/manage/v2/chain/createFriendChainInfo",
        method: "post",
        data,
    });
}
// 回显友链
export function echoFriendChain(data) {
    return Axios({
        url: "/manage/v2/chain/getFriendChainDetailsInfo",
        method: "get",
        params:data,
    });
}
// 编辑友链
export function updateFriendChain(data) {
    return Axios({
        url: "/manage/v2/chain/updateFriendChainInfo",
        method: "post",
        data,
    });
}
// 删除友链
export function deleteFriendChain(data) {
    return Axios({
        url: "/manage/v2/chain/deleteFriendChainInfo",
        method: "get",
        params:data,
    });
}

// --------------------------合作管理-----------------------------
// 合作列表
export function getCooperationList(data) {
    return Axios({
        url: "/manage/v2/cooperation/pageCooperation",
        method: "post",
        data,
    });
}
// 删除
export function deleteCooperation(data) {
    return Axios({
        url: "/manage/v2/cooperation/deleteCooperation",
        method: "get",
        params: data,
    });
}

// --------------------------新闻管理-----------------------------
// 新闻类型分类
export function getPlatformList(data) {
    return Axios({
        url: "/manage/v2/news/type/queryNewsTypeList",
        method: "get",
        params: data,
    });
}
// 新闻列表
export function getNewsList(data) {
    return Axios({
        url: "/manage/v2/news/pageNewsInfo",
        method: "post",
        data,
    });
}
// 新增新闻
export function createNews(data) {
    return Axios({
        url: "/manage/v2/news/createNewsInfo",
        method: "post",
        data,
    });
}
// 新闻回显
export function getNewsInfo(data) {
    return Axios({
        url: "/manage/v2/news/getNewsDetailsInfo",
        method: "get",
        params: data,
    });
}
// 修改新闻
export function updateNews(data) {
    return Axios({
        url: "/manage/v2/news/updateNewsDetailsInfo",
        method: "post",
        data,
    });
}
// 删除新闻
export function removeNews(data) {
    return Axios({
        url: "/manage/v2/news/deleteNewsDetailsInfo",
        method: "get",
        params: data,
    });
}
