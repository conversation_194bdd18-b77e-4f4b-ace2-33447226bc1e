/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-04-02 17:06:00
 * @LastEditors: jingrou
 * @LastEditTime: 2022-08-05 17:12:18
 */
import { Axios } from "@/utils/axios";

// 帮助中心
export function getHelpList(data) {
    return Axios({
        url: "/manage/v2/support/pageSupperDetailsInfo",
        method: "post",
        data,
    });
}
export function deleteHelp(data) {
    return Axios({
        url:  "/manage/v2/support/deleteSupportInfo",
        method: "post",
        data,
    });
}

export function createHelp(data) {
    return Axios({
        url: "/manage/v2/support/createSupportInfo",
        method: "post",
        data,
    });
}
export function getHelpInfo(data) {
    return Axios({
        url: "/manage/v2/support/getSupperDetailsInfo",
        method: "post",
        data,
    });
}
export function updataHelp(data) {
    return Axios({
        url: "/manage/v2/support/updateSupportInfo",
        method: "post",
        data,
    });
}

export function gotStsTokent(data) {
    return Axios({
        url: "/manage/file/getOssScrip",
        method: "get",
        data,
    });
}
