import { Axios } from "@/utils/axios";

// 图书馆管理
export function getSchoolAppList(data) {
    return Axios({
        url: "/manage/society/library/pageSchoolManageInfo",
        method: "post",
        data,
    });
}

// 获取省市区配置地区
export function getAreaList() {
    return Axios({
        url: "/manage//partnerMgmt/getAreaList",
        method: "get",
    });
}

// 创建图书馆
export function createSchool(data) {
    return Axios({
        url: "/manage/society/library/createSchool",
        method: "post",
        data,
    });
}
// 编辑学校
export function updateSchoolInfo(data) {
    return Axios({
        url: "/manage/society/library/updateSchoolInfo",
        method: "post",
        data,
    });
}
// 禁用启用
export function updateSchoolStatus(data) {
    return Axios({
        url: "/manage/society/library/updateSchoolStatus",
        method: "post",
        data,
    });
}
// 重置密码
export function resetAdminPassword(params) {
    return Axios({
        url: "/manage/society/library/resetAdminPassword",
        method: "get",
        params,
    });
}
// 账号解锁
export function unlockSuperAdmin(data) {
    return Axios({
        url: "/manage/society/library/unlockSuperAdmin",
        method: "post",
        data,
    });
}
// 学校详情
export function getSchoolInfo(data) {
    return Axios({
        url: "/manage/society/library/getSchoolInfo",
        method: "post",
        data,
    });
}
// 查询学校分配的设备列表
export function brandListBySchool(data) {
    return Axios({
        url: "/manage/school/device/brand/listBySchool",
        method: "post",
        data,
    });
}


// 分配学校设备品牌
export function updateAssignedDevice(data) {
    return Axios({
        url: "/manage/school/device/brand/updateAssignedDevice",
        method: "post",
        data,
    });
}