import { Axios } from "@/utils/axios";

class Life {
    save({ createTime, content, fileList }) {
        var formData = new FormData();
        formData.append("createTime", createTime);
        formData.append("content", content);
        fileList.forEach((el, index) => {
            formData.append("fileList", el);
        });
        return Axios({
            url: "/v1/life/record",
            method: "post",
            headers: {
                "Content-Type": "multipart/form-data"
            },
            data: formData
        });
    }
    remove(parameter) {
        return Axios.post("/v1/life/remove", parameter);
    }
    getEvent(parameter) {
        return Axios.get("/v1/life/allevent", parameter);
    }

    // saveCountry(parameter) {
    //     return Axios.post("/v1/country/save", parameter);
    // }
    // getCountry() {
    //     return Axios.get("/v1/country/list", {});
    // }
}

export default new Life();
