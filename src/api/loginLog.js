/*
 * @Descripttion: 
 * @version: 1.0.0
 * @Author: jing<PERSON>
 * @Date: 2022-11-12 10:09:38
 * @LastEditors: jingrou
 * @LastEditTime: 2022-11-12 10:46:28
 */
import { Axios } from "@/utils/axios";
import Qs from "qs";

// 登录日志
export function getLoginPage(data) {
    return Axios({
        url: "/manage/systemOperationLog/loginPage",
        method: "post",
        data,
    });
}

// 日志操作
export function operationPage(data) {
    return Axios({
        url: "/manage/systemOperationLog/operationPage",
        method: "post",
        data,
    });
}

// 获取日志模块列表
export function getModuleList(data) {
    return Axios({
        url: "/manage/systemOperationLog/moduleList",
        method: "get",
        parame: data,
    });
}

