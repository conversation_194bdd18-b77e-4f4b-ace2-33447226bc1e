import { Axios } from "@/utils/axios";

class Love {
    //上传图片
    uploadImg(parameter, fileName) {
        var formData = new FormData();
        formData.append("file", parameter, fileName);
        return Axios({
            url: "/v1/fll/upload/loveImg",
            method: "post",
            headers: {
                "Content-Type": "multipart/form-data"
            },
            data: formData
        });
    }

    getImg() {
        return Axios.get("/v1/fll/get/loveImg");
    }

    remove(parameter) {
        return Axios.post("/v1/fll/remove/loveImg", parameter);
    }
}

export default new Love();
