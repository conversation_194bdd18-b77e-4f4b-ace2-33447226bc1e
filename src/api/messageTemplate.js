/*
 * @Descripttion:消息模板 api
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-04-02 16:23:19
 * @LastEditors: jingrou
 * @LastEditTime: 2022-06-14 10:54:20
 */
import { Axios } from "@/utils/axios";

// 消息模板
export function getMsgTempList(data) {
    return Axios({
        url: "/backstage/cloud-backstage/message/selectMessageList",
        method: "post",
        data,
    });
}
export function deleteMsgTemp(id) {
    return Axios({
        url: `/backstage/cloud-backstage/message?id=${id}`,
        method: "delete",
        data: {},
    });
}
export function createMsgTemp(data) {
    return Axios({
        url: "/backstage/cloud-backstage/message/create",
        method: "post",
        data,
    });
}
export function getMsgTempInfo(data) {
    return Axios({
        url: "/backstage/cloud-backstage/message/searchById",
        method: "post",
        data,
    });
}
export function updateMsgTemp(data) {
    return Axios({
        url: "/backstage/cloud-backstage/message/updateMessage",
        method: "post",
        data,
    });
}

export function sendTask(data) {
    return Axios({
        url: "/timetable/cloud-timetable/app/lessions/sub/pushTodayTask",
        method: "get",
        data,
    });
}
export function sendClassReminder(data) {
    return Axios({
        url: "/timetable/cloud-timetable/app/subjcet/addClassTimePush",
        method: "post",
        data,
    });
}
