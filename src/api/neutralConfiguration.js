/*
 * @Descripttion: 
 * @version: 1.0.0
 * @Author: jing<PERSON>
 * @Date: 2023-05-17 16:23:45
 * @LastEditors: jingrou
 * @LastEditTime: 2023-05-18 15:12:32
 */
import { Axios } from "@/utils/axios";

// 获取列表
export function getConfigPage(data) {
    return Axios({
        url: "/manage/website/config/page",
        method: "post",
        data,
    });
}

// 上传接口
export function uploadConfigLogo(data) {
    return Axios({
        url: "/file/common/upload",
        method: "post",
        data,
    });
}

// 创建配置
export function createConfig(data) {
    return Axios({
        url: "/manage/website/config/create",
        method: "post",
        data,
    });
}

// 回显配置
export function getConfigInfo(data) {
    return Axios({
        url: "/manage/website/config/getInfo",
        method: "get",
        params: data,
    });
}

// 編輯配置
export function updateConfig(data) {
    return Axios({
        url: "/manage/website/config/update",
        method: "post",
        data,
    });
}

// 删除配置
export function deleteConfig(data) {
    return Axios({
        url: "/manage/website/config/delete",
        method: "post",
        data,
    });
}