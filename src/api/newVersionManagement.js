/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: jing<PERSON>
 * @Date: 2022-01-26 14:03:23
 * @LastEditors: jingrou
 * @LastEditTime: 2022-06-14 14:38:40
 */
import { Axios } from "@/utils/axios";

// 终端类型
export function queryAppTypeList(data) {
    return Axios({
        url: "/manage/v2/application/type/queryAppTypeList",
        method: "get",
        params: data,
    });
}
// 批量删除
export function deleteAppVersion(data) {
    return Axios({
        url: "/manage/v2/application/version/deleteAppVersion",
        method: "post",
        data,
    });
}

// 系统类型-联动
export function querySystemList(data) {
    return Axios({
        url: "/manage/v2/application/type/querySystemList",
        method: "get",
        params: data,
    });
}
//获取版本名称
export function pageAppVersion(data) {
    return Axios({
        url: "/manage/v2/application/edition/queryEditionList",
        method: "get",
        params: data,
    });
}
// 更新或新增版本名称
export function editVersionInfo(data) {
    return Axios({
        url: "/manage/v2/application/apply/updateAppApplyInfo",
        method: "post",
        data,
    });
}
// 获取应用名称
export function applicationApply(data) {
    return Axios({
        url: "/manage/v2/application/apply/queryApplicationApplyList",
        method: "get",
        params: data,
    });
}
// 更新或新增应用名称
export function editAppInfo(data) {
    return Axios({
        url: "/manage/v2/application/apply/updateAppApplyInfo",
        method: "post",
        data,
    });
}
// 删除应用名称或版本名称
export function deleteAppApplyInfo(data) {
    return Axios({
        url: "/manage/v2/application/apply/deleteAppApplyInfo",
        method: "post",
        data,
    });
}
//新增版本
export function addVersionInfoApi(data) {
    return Axios({
        url: "/manage/v2/application/version/createAppVersion",
        method: "post",
        data,
    });
}
//版本信息列表查询
export function appVersionList(data) {
    return Axios({
        url: "/manage/v2/application/version/pageAppVersion",
        method: "post",
        data,
    });
}
// 回显版本信息

export function echoVersionInfo(data) {
    return Axios({
        url: "/manage/v2/application/version/getAppVersionDetailById",
        method: "get",
        params: data,
    });
}
// 编辑版本信息
export function editVersionInfoApi(data) {
    return Axios({
        url: "/manage/v2/application/version/updateAppVersion",
        method: "post",
        data,
    });
}
// 启动
export function updateAppEnable(data) {
    return Axios({
        url: "/manage/v2/application/version/updateAppEnable",
        method: "get",
        params: data,
    });
}

// 上传文件 自动解析包文件信息
export function versionUpload(data) {
    return Axios({
        url: "/manage/v2/application/version/upload",
        method: "post",
        data,
    });
}


// 获取版本标签列表
export function getAppVersionLabelList(data) {
    return Axios({
        url: "/manage/v2/application/version/getAppVersionLabelList",
        method: "get",
        params: data,
    });
}
