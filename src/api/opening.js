/*
 * @Descripttion: 
 * @version: 1.0.0
 * @Author: jingrou
 * @Date: 2022-06-21 17:04:03
 * @LastEditors: jingrou
 * @LastEditTime: 2022-06-22 10:27:17
 */
import Qs from "qs";
import { Axios } from "@/utils/axios";

// 开通列表
export function openSchoolList(data) {
    return Axios({
        url: "/manage/v2/internal/manage/pageSchoolInfo",
        method: "post",
        data,
    });
}
export function openSchoolModule(data) {
    return Axios({
        url: "/manage/v2/internal/manage/queryOpenLayoutModuleList",
        method: "post",
        data,
    });
}
export function createModuleInfo(data) {
    return Axios({
        url: "/manage/v2/internal/manage/createOpenSchoolModuleInfo",
        method: "post",
        data,
    });
}
export function openSchoolValidation(data) {
    return Axios({
        url: "/manage/v2/internal/manage/queryOpenSchoolValidation",
        method: "post",
        data,
    });
}
export function createValidationInfo(data) {
    return Axios({
        url: "/manage/v2/internal/manage/createOpenSchoolModuleInfo",
        method: "post",
        data,
    });
}

