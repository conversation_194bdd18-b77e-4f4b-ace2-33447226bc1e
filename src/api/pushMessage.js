import { Axios } from "@/utils/axios";

export function getFunctionalModuleType(data) {
    return Axios({
        url: "/manage/pushOpenTemplate/getFunctionalModuleType",
        method: "get",
        params: data,
    });
}

export function getFunctionalModuleTypeParams(data) {
    return Axios({
        url: "/manage/pushOpenTemplate/getFunctionalModuleTypeParams",
        method: "get",
        params: data,
    });
}

export function createpushOpenTemplate(data) {
    return Axios({
        url: "/manage/pushOpenTemplate/create",
        method: "post",
        data,
    });
}



export function templatePagePushOpenTemplate(data) {
    return Axios({
        url: "/manage/pushOpenTemplate/templatePage",
        method: "post",
        data,
    });
}


export function deletepushOpenTemplate(data) {
    return Axios({
        url: "/manage/pushOpenTemplate/delete",
        method: "get",
        params: data,
    });
}


export function getTemplateInfo(data) {
    return Axios({
        url: "/manage/pushOpenTemplate/getTemplateInfo",
        method: "get",
        params: data,
    });
}

export function updatepushOpenTemplate(data) {
    return Axios({
        url: "/manage/pushOpenTemplate/update",
        method: "post",
        data,
    });
}



export function pageOpenTemplate(data) {
    return Axios({
        url: "/manage/pushOpenConfig/pageOpenTemplate",
        method: "post",
        data,
    });
}


export function queryPushOpenConfigInfo(data) {
    return Axios({
        url: "/manage/pushOpenConfig/queryPushOpenConfigInfo",
        method: "get",
        params: data,
    });
}


export function pageOpenTemplateLog(data) {
    return Axios({
        url: "/manage/pushOpenTemplateLog/pageOpenTemplateLog",
        method: "post",
        data,
    });
}


export function updatePushOpenConfig(data) {
    return Axios({
        url: "/manage/pushOpenConfig/updatePushOpenConfig",
        method: "post",
        data,
    });
}


export function selectTemplateList(data) {
    return Axios({
        url: "/manage/pushOpenTemplate/selectTemplateList",
        method: "post",
        data,
    });
}


export function getFunctionalModuleTypeTrigger(data) {
    return Axios({
        url: "/manage/pushOpenTemplate/getFunctionalModuleTypeTrigger",
        method: "get",
        params: data,
    });
}


export function updateEnableStatus(data) {
    return Axios({
        url: "/manage/pushOpenConfig/updateEnableStatus",
        method: "post",
        data,
    });
}

export function templateSchoolList(data) {
    return Axios({
        url: "/manage/pushOpenTemplate/templateSchoolList",
        method: "get",
        params: data,
    });
}



export function getcategoryList(data) {
    return Axios({
        url: "/manage/pushOpenTemplate/categoryList",
        method: "get",
        params: data,
    });
}


// 人脸日志
export function getfaceIdentifyLogPage(data) {
    return Axios({
        url: "/manage/faceIdentifyLog/page",
        method: "post",
        data,
    });
}


