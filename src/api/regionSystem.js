import { Axios } from "@/utils/axios";

// 区域管理列表
export function getRegionPage(data) {
    return Axios({
        url: "/manage/organization/page",
        method: "post",
        data,
    });
}

export function getRegionTree(data) {
    return Axios({
        url: "/manage/organization/region/tree",
        method: "get",
        params: data,
    });
}

export function createRegion(data) {
    return Axios({
        url: "/manage/organization/create",
        method: "post",
        data,
    });
}

export function updateRegion(data) {
    return Axios({
        url: "/manage/organization/update",
        method: "post",
        data,
    });
}

export function getSubRegionPage(data) {
    return Axios({
        url: "/manage/organization/sub/page",
        method: "post",
        data,
    });
}

export function createSubRegion(data) {
    return Axios({
        url: "/manage/organization/sub/create",
        method: "post",
        data,
    });
}

export function updateSubRegion(data) {
    return Axios({
        url: "/manage/organization/sub/update",
        method: "post",
        data,
    });
}

export function deleteSubRegion(data) {
    return Axios({
        url: "/manage/organization/sub/delete",
        method: "post",
        data,
    });
}

// 重置密码
export function resetAdminPassword(data) {
    return Axios({
        url: "/manage/organization/resetAdminPassword",
        method: "post",
        data,
    });
}
// 账号解锁
export function unlockSuperAdmin(data) {
    return Axios({
        url: "/manage/organization/unlockSuperAdmin",
        method: "post",
        data,
    });
}

export function exportInfo(data, fileName) {
    return Axios({
        url: "/manage/organization/exportOrganization",
        method: "post",
        responseType: "arraybuffer",
        data,
    }).then((blob) => {
        const url = window.URL.createObjectURL(
            new Blob([blob], {
                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            })
        );
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.setAttribute(
            "download",
            fileName ? `${fileName}.xlsx` : "excel.xlsx"
        );
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    });
}

export function getAppOwner(data) {
    return Axios({
        url: "/manage/organization/app/owner",
        method: "get",
        params: data,
    });
}

export function createAppOwner(data) {
    return Axios({
        url: "/manage/organization/dist/createAppOrganization",
        method: "post",
        data,
    });
}

export function deleteAppOwner(data) {
    return Axios({
        url: "/manage/organization/dist/deleteAppOrganization",
        method: "post",
        data,
    });
}

export function getOrganizationList(data) {
    return Axios({
        url: "/manage/organization/listAll",
        method: "get",
        params: data,
    });
}
