/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: jingrou
 * @Date: 2021-11-19 14:44:39
 * @LastEditors: jingrou
 * @LastEditTime: 2022-06-14 15:40:19
 */
import { Axios } from "@/utils/axios";
import Qs from "qs";

// 报备列表
export function getReportingList(data) {
    return Axios({
        url: "/backstage/cloud-backstage/reporting/list",
        method: "get",
        params: data,
    });
}
// 添加报备
export function addReportingList(data) {
    return Axios({
        url: "/backstage/cloud-backstage/reporting/add",
        method: "post",
        data,
    });
}
// 编辑报备
export function editReportingList(data) {
    return Axios({
        url: "/backstage/cloud-backstage/reporting/edit",
        method: "put",
        data,
    });
}
// 编辑回显
export function getEditReportingList(data) {
    return Axios({
        url: "/backstage/cloud-backstage/reporting/get",
        method: "get",
        params: data,
    });
}

// 删除报备
export function removeReporting(data) {
    return Axios({
        url: "/backstage/cloud-backstage/reporting/remove",
        method: "delete",
        data,
    });
}
// 导出
export function reportingExportInfo(data, fileName) {
    return Axios({
        url: "/backstage/cloud-backstage/reporting/excele",
        method: "get",
        responseType: "arraybuffer",
        params: data,
    }).then((blob) => {
        const url = window.URL.createObjectURL(
            new Blob([blob], {
                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            })
        );
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.setAttribute(
            "download",
            fileName ? `${fileName}.xlsx` : "excel.xlsx"
        );
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    });
}
