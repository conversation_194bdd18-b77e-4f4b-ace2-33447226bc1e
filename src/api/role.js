/*
 * @Author: <EMAIL>
 * @Date: 2019-12-07 15:34:35
 * @Last Modified by: <EMAIL>
 * @Last Modified time: 2020-03-17 10:17:59
 * @describe 角色接口
 */

import { Axios } from "@/utils/axios";
class Role {
    // 添加
    add(params) {
        return Axios.post("/v1/role/save", params);
    }
    // list 列表
    query(params) {
        return Axios.get("/v1/role/query", {
            params: params
        });
    }
    //删除
    remove(params) {
        return Axios.post("/v1/role/remove", params);
    }
    //编辑
    update(params) {
        return Axios.post("/v1/role/update", params);
    }
}

export default new Role();
