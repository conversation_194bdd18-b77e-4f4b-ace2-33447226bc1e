/*
 * @Descripttion: 
 * @version: 1.0.0
 * @Author: jing<PERSON>
 * @Date: 2023-05-24 14:44:15
 * @LastEditors: jingrou
 * @LastEditTime: 2023-05-24 18:00:09
 */
import Qs from "qs";
import { Axios } from "@/utils/axios";

// 学校列表
export function getSchoolAppList(data) {
    return Axios({
        url: "/manage/v2/manage/school/pageSchoolManageInfo",
        method: "post",
        data,
    });
}

// 获取列表
export function getWebsiteList(data) {
    return Axios({
        url: "/manage/school/website/page",
        method: "post",
        data,
    });
}
// 新增列表
export function createWebsite(data) {
    return Axios({
        url: "/manage/school/website/create",
        method: "post",
        data,
    });
}

// 回显
export function getWebsiteInfo(data) {
    return Axios({
        url: "/manage/school/website/getInfo",
        method: "get",
        params: data,
    });
}

// 编辑列表
export function updateWebsite(data) {
    return Axios({
        url: "/manage/school/website/update",
        method: "post",
        data,
    });
}

// 删除
export function deleteWebsite(data) {
    return Axios({
        url: "/manage/school/website/delete",
        method: "post",
        data,
    });
}