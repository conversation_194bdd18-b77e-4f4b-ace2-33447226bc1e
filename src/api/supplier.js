/*
 * @Description:
 * @Version: 2.0
 * @Author: ji<PERSON><PERSON>
 * @Date: 2021-08-24 11:50:45
 * @LastEditors: jingrou
 * @LastEditTime: 2022-06-14 11:11:22
 */
import { Axios } from "@/utils/axios";

// 新增
export function createSupplier(data) {
    return Axios({
        url: "/backstage/cloud-backstage/supplier/create",
        method: "post",
        data,
    });
}
// 列表 查询
export function supplierList(data) {
    return Axios({
        url: "/backstage/cloud-backstage/supplier/list",
        method: "post",
        data,
    });
}
// 查询供应商
export function supplierName(data) {
    return Axios({
        url: "/backstage/cloud-backstage/supplier/selectSupplierName",
        method: "post",
        data,
    });
}

// 编辑回显
export function editEcho(data) {
    return Axios({
        url: "/backstage/cloud-backstage/supplier/selectById",
        method: "post",
        data,
    });
}
// 保存编辑
export function editSupplier(data) {
    return Axios({
        url: "/backstage/cloud-backstage/supplier/edit",
        method: "post",
        data,
    });
}
// 修改状态
export function updateStatus(data) {
    return Axios({
        url: "/backstage/cloud-backstage/supplier/updateStatus",
        method: "post",
        data,
    });
}
// 修改上下架状态
export function updateApplicationStatus(data) {
    return Axios({
        url: "/backstage/cloud-backstage/application/updateStatus",
        method: "post",
        data,
    });
}
// 提供商相关学校
export function relationSchool(data) {
    return Axios({
        url: "/backstage/cloud-backstage/supplier/relationSchool",
        method: "post",
        data,
    });
}
