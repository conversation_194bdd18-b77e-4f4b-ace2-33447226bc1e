/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-10-11 18:36:25
 * @LastEditors: jingrou
 * @LastEditTime: 2022-06-14 14:58:17
 */
import { Axios } from "@/utils/axios";

// -------------平台标识-----------------------
export function sysShowHide(data) {
    return Axios({
        url: "api/dict/data/type/dictType",
        method: "get",
        params: data,
    });
}
// ------------用户管理------------------------
// 用户管理列表
export function userList(data) {
    return Axios({
        url: "api/user/list",
        method: "get",
        params: data,
    });
}
// 用户添加
export function userAddList(data) {
    return Axios({
        url: "api/user/add",
        method: "post",
        data,
    });
}
// 用户回显
export function userDetailList(data) {
    return Axios({
        url: "api/user/get",
        method: "get",
        params: data,
    });
}
// 用户编辑
export function userEditList(data) {
    return Axios({
        url: "/api/user/edit",
        method: "put",
        data,
    });
}
// 用户删除
export function deleteUserList(data) {
    return Axios({
        url: "api/user/remove",
        method: "delete",
        data,
    });
}

// --------------菜单管理---------------------------
// 菜单列表
export function getMenuList(data) {
    return Axios({
        url: "api/menu/list",
        method: "get",
        params: data,
    });
}
// 添加菜单
export function addMenuList(data) {
    return Axios({
        url: "api/menu",
        method: "post",
        data,
    });
}
// 修改菜单
export function updateMenuList(data) {
    return Axios({
        url: "api/menu",
        method: "put",
        data,
    });
}
// 菜单回显
export function echoMenuList(data) {
    return Axios({
        url: "/api/menu/get",
        method: "get",
        params: data,
    });
}
// 菜单删除
export function deleteMenuList(data) {
    return Axios({
        url: "api/menu/delete",
        method: "delete",
        params: data,
    });
}
//------------------部门管理-----------------------------------
// 部门管理列表
export function departmentDeptList(data) {
    return Axios({
        url: "api/dept/list",
        method: "get",
        params: data,
    });
}
// 部门管理添加
export function departmentAddDeptList(data) {
    return Axios({
        url: "/api/dept/add",
        method: "post",
        data,
    });
}
// 回显
export function departmentDeptIdList(data) {
    return Axios({
        url: "api/dept/get",
        method: "get",
        params: data,
    });
}
// 编辑
export function departmentEditList(data) {
    return Axios({
        url: "/api/dept/edit",
        method: "put",
        data,
    });
}
// 删除
export function departmentDeleteList(data) {
    return Axios({
        url: "api/dept/delete",
        method: "delete",
        params: data,
    });
}
// --------------角色权限------------------------------
// 角色权限列表
export function roleList(data) {
    return Axios({
        url: "api/role/list",
        method: "get",
        params: data,
    });
}
// 添加角色权限
export function addRoleList(data) {
    return Axios({
        url: "api/role/add",
        method: "post",
        data,
    });
}
// 回显角色权限
export function getRoleList(data) {
    return Axios({
        url: "api/role/get",
        method: "get",
        params: data,
    });
}
// 编辑角色权限
export function editRoleList(data) {
    return Axios({
        url: "api/role/edit",
        method: "put",
        data,
    });
}
// 删除角色权限
export function removeRoleList(data) {
    return Axios({
        url: "api/role/remove",
        method: "delete",
        data,
    });
}
// 新增角色的菜单权限
export function addMenuRoleList(data) {
    return Axios({
        url: "api/menu/treeselect",
        method: "get",
        params: data,
    });
}
// 角色权限设置
export function deptMenuRoleList(data) {
    return Axios({
        url: "api/dept/treeselect",
        method: "get",
        params: data,
    });
}
// 权限设置确定按钮
export function editDeptRoleList(data) {
    return Axios({
        url: "/api/role/addRoleAndDept",
        method: "put",
        data,
    });
}
// --------------字典类型------------------------------
// 字典列表
export function getTypeList(data) {
    return Axios({
        url: "api/dict/type/list",
        method: "get",
        params: data,
    });
}
// 添加字典
export function addTypeList(data) {
    return Axios({
        url: "api/dict/type/add",
        method: "post",
        data,
    });
}
// 修改字典
export function editTypeList(data) {
    return Axios({
        url: "api/dict/type/edit",
        method: "put",
        data,
    });
}
// 字典回显
export function echoTypeList(data) {
    return Axios({
        url: "api/dict/type/dictId",
        method: "get",
        params: data,
    });
}
// 字典删除
export function removeTypeList(data) {
    return Axios({
        url: "api/dict/type/remove",
        method: "delete",
        data,
    });
}
// 字典查询列表
export function selectDictList(data) {
    return Axios({
        url: "/api/dict/data/list",
        method: "get",
        params: data,
    });
}
// 添加字典内容
export function addDictList(data) {
    return Axios({
        url: "api/dict/data/add",
        method: "post",
        data,
    });
}
// 回显字典内容
export function echoDictList(data) {
    return Axios({
        url: "/api/dict/data/dictCode",
        method: "get",
        params: data,
    });
}
// 字典表单编辑
export function editDictList(data) {
    return Axios({
        url: "/api/dict/data/edit",
        method: "put",
        data,
    });
}
// 删除字典内容
export function removeDictList(data) {
    return Axios({
        url: "api/dict/data/remove",
        method: "delete",
        data,
    });
}
