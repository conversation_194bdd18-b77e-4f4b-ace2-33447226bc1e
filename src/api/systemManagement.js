/*
 * @Author: your name
 * @Date: 2021-10-06 14:04:56
 * @LastEditTime: 2022-06-14 14:58:16
 * @LastEditors: jingrou
 * @Description: In User Settings Edit
 * @FilePath: \laihq-web-servef:\备份\梦想编制者\manage-laihq\src\api\systemManagement.js
 */

import { Axios } from "@/utils/axios";

export function getPlatformList(data) {
    return Axios({
        url: "api/dict/data/type/dictType",
        method: "get",
        params: data,
    });
}

// user
export function getUserList(data) {
    return Axios({
        url: "api/user/list",
        method: "get",
        params: data,
    });
}

export function createUser(data) {
    return Axios({
        url: "api/user/add",
        method: "post",
        data: data,
    });
}

export function getUserInfo(data) {
    return Axios({
        url: "api/user/get",
        method: "get",
        params: data,
    });
}

export function updateUser(data) {
    return Axios({
        url: "/api/user/edit",
        method: "put",
        data,
    });
}

export function removeUser(data) {
    return Axios({
        url: "api/user/remove",
        method: "delete",
        data,
    });
}

export function resetPassword(data) {
    return Axios({
        url: "/api/user/resetPwd",
        method: "put",
        data,
    });
}

// role
export function getRoleList(data) {
    return Axios({
        url: "api/role/list",
        method: "get",
        params: data,
    });
}

export function createRole(data) {
    return Axios({
        url: "api/role/add",
        method: "post",
        data: data,
    });
}

export function getRoleInfo(data) {
    return Axios({
        url: "api/role/get",
        method: "get",
        params: data,
    });
}

export function updateRole(data) {
    return Axios({
        url: "api/role/edit",
        method: "put",
        data,
    });
}

export function removeRole(data) {
    return Axios({
        url: "api/role/remove",
        method: "delete",
        data,
    });
}

export function getRolePermission(data) {
    return Axios({
        url: "api/menu/treeselect",
        method: "get",
        params: data,
    });
}

export function updateRolePermission(data) {
    return Axios({
        url: "api/role/updateRoleMenu",
        method: "put",
        data: data,
    });
}

//permission
export function getPermissionList(data) {
    return Axios({
        url: "api/menu/list",
        method: "get",
        params: data,
    });
}

export function createPermission(data) {
    return Axios({
        url: "api/menu",
        method: "post",
        data,
    });
}

export function updatePermission(data) {
    return Axios({
        url: "api/menu",
        method: "put",
        data,
    });
}

export function getPermissionInfo(data) {
    return Axios({
        url: "/api/menu/get",
        method: "get",
        params: data,
    });
}

export function removePermission(data) {
    return Axios({
        url: "api/menu/delete",
        method: "delete",
        data: data,
    });
}

// department
export function getDepartmentList(data) {
    return Axios({
        url: "api/dept/list",
        method: "get",
        params: data,
    });
}

export function createDepartment(data) {
    return Axios({
        url: "/api/dept/add",
        method: "post",
        data,
    });
}

export function getDepartmentInfo(data) {
    return Axios({
        url: "api/dept/get",
        method: "get",
        params: data,
    });
}

export function updateDepartment(data) {
    return Axios({
        url: "/api/dept/edit",
        method: "put",
        data,
    });
}

export function removeDepartment(deptId) {
    return Axios({
        url: "api/dept/delete?deptId=" + deptId,
        method: "delete",
        // params: data
    });
}
