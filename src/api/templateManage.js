/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: jing<PERSON>
 * @Date: 2022-06-14 10:55:58
 * @LastEditors: jingrou
 * @LastEditTime: 2022-12-24 16:21:50
 */
import Qs from "qs";
import { Axios } from "@/utils/axios";

// 分类
export function getMessTypeList(data) {
    return Axios({
        url: "/manage/v2/message/temp/queryMessTypeList",
        method: "get",
        params: data,
    });
}

export function getschoolType(data) {
    return Axios({
        url: "/manage/mess/schoolType",
        method: "post",
        data,
    });
}
// 资源标签列表
export function getMessTemplateList(data) {
    return Axios({
        url: "/manage/v2/message/temp/queryMessTemplateList",
        method: "get",
        params: data,
    });
}
// 资源标签添加修改
export function addTemplateInfo(data) {
    return Axios({
        url: "/manage/v2/message/type/createOrUpdateMessTypeInfo",
        method: "post",
        data,
    });
}

// 删除标签
export function deleteTemplateInfo(data) {
    return Axios({
        url: "/manage/v2/message/type/deleteMessType",
        method: "get",
        params: data,
    });
}
// 模板列表
export function getImgList(data) {
    return Axios({
        url: "/manage/v2/message/temp/pageMessTemplate",
        method: "post",
        data,
    });
}
// 新增模板
export function addImgList(data) {
    return Axios({
        url: "/manage/v2/message/temp/createMessTemplate",
        method: "post",
        data,
    });
}
// 回显模板
export function echoImgInfo(data) {
    return Axios({
        url: "/manage/v2/message/temp/getMessTempDetailInfo",
        method: "get",
        params: data,
    });
}
// 编辑模板
export function editImgInfo(data) {
    return Axios({
        url: "/manage/v2/message/temp/updateMessTempDetailInfo",
        method: "post",
        data,
    });
}
// 模板排序
export function editImgSort(data) {
    return Axios({
        url: "/manage/v2/message/temp/updateTemplateSort",
        method: "post",
        data,
    });
}

// 回显更改分类
export function getTypeListInfo(data) {
    return Axios({
        url: "/manage/v2/message/temp/getMessTemplateDetail",
        method: "get",
        params: data,
    });
}
//更改分类
export function editImgListInfo(data) {
    return Axios({
        url: "/manage/v2/message/temp/updateMessTemplateType",
        method: "post",
        data,
    });
}
// 停用
export function deactivateImg(data) {
    return Axios({
        url: "/manage/v2/message/temp/updateTemplateEnabled",
        method: "post",
        data,
    });
}

// 获取信发类型列表
export function getTempMessTypeList(data) {
    return Axios({
        url: "/manage/v2/message/temp/getMessTypeList",
        method: "get",
        params: data,
    });
}



