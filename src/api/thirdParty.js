import { Axios } from "@/utils/axios";

// 获取列表
export function configList(data) {
    return Axios({
        url: "/manage/v2/manage/school/pageSchoolExternalApp",
        method: "post",
        data,
    });
}


// 获取单条数据回显
export function configInfo(data) {
    return Axios({
        url: "/manage/external/app/config/listSchoolExternalAppConfig/v2",
        method: "get",
        params: data,
    });
}


// 修改配置
export function editConfig(data) {
    return Axios({
        url: "/manage/external/app/config/editSchoolExternalAppConfig",
        method: "post",
        data,
    });
}


// 人员同步
export function pullPersonnel(data, appCode) {
    return Axios({
        url: `${"/sync2/trigger/pullEmployee/" + appCode}`,
        method: "post",
        data,
    });
}


// 部门同步
export function pullDepartment(data, appCode) {
    return Axios({
        url: `${"/sync2/trigger/pullDept/" + appCode}`,
        method: "post",
        data,
    });
}
