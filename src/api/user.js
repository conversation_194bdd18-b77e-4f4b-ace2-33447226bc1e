/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-04-01 11:10:51
 * @LastEditors: jingrou
 * @LastEditTime: 2023-02-18 10:05:03
 */
import { Axios } from "@/utils/axios";

export function login(data) {
    return Axios({
        url: "/auth/oauth/token",
        method: "post",
        data,
    });
}
// 获取用户信息
export function getInfo() {
    return Axios({
        url: "/api/user/getInfo",
        method: "get",
    });
}

// 获取用户菜单路由
export function getUserRouter() {
    return Axios({
        url: "/api/menu/getRouters",
        method: "get",
    });
}


export function upOldDatePwd(data) {
    return Axios({
        url: "/system/adminUser/updatePassword",
        method: "post",
        data,
    });
}

// 修改密码
export function updatePwd(data) {
    return Axios({
        url: "/system/adminUser/updateNewPassword",
        method: "post",
        data,
    });
}

//忘记密码
export function forgetPassword(data) {
    return Axios({
        url: "/system/adminUser/forgetPassword",
        method: "post",
        data,
    });
}



// 获取短信
export function smsMessage(data) {
    return Axios({
        url: "/cloud/sms/external/message",
        method: "post",
        data,
    });
}