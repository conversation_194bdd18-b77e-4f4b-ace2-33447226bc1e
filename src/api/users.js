/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-06-11 15:17:58
 * @LastEditors: jingrou
 * @LastEditTime: 2022-11-12 09:31:19
 */
import { Axios } from "@/utils/axios";

class Users {
    //登录
    login(params) {
        //  qs.stringify(params);
        return Axios.post("/auth/oauth/token", params);
    }

    getUserInfo(params) {
        return Axios({
            url: "/manage/adminUser/login/get",
            method: "get",
            data: params,
        });
    }

    getUserMenu(params) {
        return Axios({
            url: "/manage/menu/getRouters",
            method: "get",
            data: params,
        });
    }

    // 记录登录日志
    loginPage() {
        return Axios({
            url: "/manage/adminUser/loginLog",
            method: "get",
        });
    }

}

export default new Users();
