<!--
 * @Descripttion: 
 * @version: 1.0.0
 * @Author: jingrou
 * @Date: 2022-07-09 15:39:37
 * @LastEditors: jingrou
 * @LastEditTime: 2023-01-11 18:38:07
-->
<template>
    <div>
        <el-drawer
            :title="newDrawerTitle"
            :visible.sync="newDrawer"
            :before-close="close"
        >
            <!-- 新增与编辑 -->
            <el-form
                ref="ruleForm"
                :model="ruleForm"
                label-width="120px"
                class="demo-ruleForm"
                style="padding: 0px 14px 0px 0px"
            >
                <div style="margin-bottom: 92px">
                    <el-form-item
                        label="模板名称："
                        prop="name"
                        :rules="{
                            required: true,
                            message: '请输入模板名称',
                            trigger: 'blur',
                        }"
                    >
                        <el-input
                            v-model.trim="ruleForm.name"
                            maxlength="20"
                            placeholder="请输入模板名称"
                            show-word-limit
                        />
                    </el-form-item>

                    <el-form-item
                        label="标题："
                        :rules="{
                            required: true,
                            message: '请输入标题',
                            trigger: 'blur',
                        }"
                        prop="title"
                    >
                        <el-input
                            maxlength="50"
                            show-word-limit
                            v-model.trim="ruleForm.title"
                            placeholder="请输入标题"
                        />
                    </el-form-item>

                    <el-form-item
                        label="模板类型："
                        :rules="{
                            required: true,
                            message: '请选择模板类型',
                            trigger: 'blur',
                        }"
                        prop="schoolTypes"
                    >
                        <el-select
                            multiple
                            v-model="ruleForm.schoolTypes"
                            placeholder="请选择模板类型"
                            style="width: 100%"
                        >
                            <el-option
                                v-for="item in schoolTypeArr"
                                :key="item.schoolType"
                                :label="item.typeName"
                                :value="item.schoolType"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        v-if="typeMessId == 2"
                        label="可用信发："
                        :rules="{
                            required: true,
                            message: '请选择可用信发',
                            trigger: 'blur',
                        }"
                        prop="messTypeList"
                    >
                        <el-select
                            multiple
                            v-model="ruleForm.messTypeList"
                            placeholder="请选择可用信发"
                            style="width: 100%"
                        >
                            <el-option
                                v-for="item in availableMes"
                                :key="item.messType"
                                :label="item.text"
                                :value="item.messType"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <div
                        class="imgUpload"
                        v-for="(item, index) in ruleForm.details"
                        :key="index"
                    >
                        {{ item.filelist }}
                        <div class="sizeName">
                            {{ item.width }}*{{ item.height }}尺寸
                        </div>
                        <el-form-item
                            class="yd-uploader"
                            label="封面图："
                            :prop="'details.' + index + '.coverImg'"
                            :rules="{
                                required: true,
                                message: '请上传封面图',
                                trigger: 'blur',
                            }"
                        >
                            <!-- :on-error="importError" -->
                            <el-upload
                                :headers="{
                                    Authorization: token,
                                    platform: 'system',
                                }"
                                :action="action"
                                :show-file-list="false"
                                :file-list="item.filelist"
                                :on-success="
                                    index === 0 ? importOneList : importTwoList
                                "
                                name="file"
                                :multiple="false"
                            >
                                <img
                                    v-if="item.coverImg !== '' || item.coverImg"
                                    :src="item.coverImg"
                                    class="avatar"
                                    style="border: 1px solid #d8d8d8"
                                    :style="
                                        item.width < item.height
                                            ? 'width: 93px;height: 166px;'
                                            : 'width: 228px;height: 144px;'
                                    "
                                />
                                <i
                                    :style="
                                        item.width < item.height
                                            ? 'width: 93px;height: 166px;'
                                            : 'width: 228px;height: 144px;'
                                    "
                                    style="
                                        border: 1px solid #d8d8d8;
                                        display: flex;
                                        align-items: center;
                                        font-size: 38px;
                                        justify-content: center;
                                        color: #c6c6c6;
                                    "
                                    v-else
                                    class="el-icon-plus avatar-uploader-icon"
                                ></i>
                            </el-upload>
                        </el-form-item>
                        <el-form-item
                            v-if="typeMessId == 2"
                            label="JSON数据："
                            :rules="{
                                required: true,
                                message: '请输入JSON数据',
                                trigger: 'blur',
                            }"
                            :prop="'details.' + index + '.parseJson'"
                        >
                            <el-input
                                v-model="item.parseJson"
                                type="textarea"
                                :rows="8"
                            />
                        </el-form-item>
                    </div>
                </div>
                <div class="yd-form-footer">
                    <div class="footerForm">
                        <el-button
                            class="reset"
                            @click="handleClose('ruleForm')"
                            >取消</el-button
                        >
                        <el-button
                            type="primary"
                            :loading="templateLoading"
                            @click="submitForm('ruleForm')"
                            >确 定</el-button
                        >
                    </div>
                </div>
            </el-form>
        </el-drawer>
    </div>
</template>

<script>
import { ACCESS_TOKEN } from "@/store/mutation-types";
import {
    addImgList,
    editImgInfo,
    getTempMessTypeList,
} from "@/api/templateManage.js";
export default {
    props: {
        // 侧边框 标题
        drawerTitle: {
            type: String,
        },
        // 侧边框 状态
        drawer: {
            type: Boolean,
        },
        // 资源模板分类ID
        classifyType: {
            type: String,
        },
        // 资源模板 回显详情对象
        templateInfo: {
            type: Object,
        },
        // 图库，海报ID
        typeMessId: {
            type: String,
        },
        // 学校类型
        schoolTypeArr: {
            type: Array,
        },
    },
    data() {
        return {
            templateLoading: false,
            typeId: "", // 模板分类id
            token: "",
            newDrawer: false,
            newDrawerTitle: "新建模板",
            action: process.env.VUE_APP_API_BASE_URL + "/manage/file/upload",
            availableMes: [],
            ruleForm: {
                schoolTypes: [],
                messTypeList: [],
                name: "",
                title: "",
                details: [
                    {
                        width: 750,
                        height: 1334,
                        parseJson: "",
                        coverImg: "",
                    },
                    {
                        width: 1024,
                        height: 576,
                        parseJson: "",
                        coverImg: "",
                    },
                ],
            },
        };
    },
    methods: {
        // 关闭前的回调
        close() {
            this.$refs.ruleForm.resetFields();
            this.newDrawer = false;
            this.$emit("handleClose", false);
        },
        // 取消
        handleClose(formName) {
            this.$refs[formName].resetFields();
            this.newDrawer = false;
            this.$emit("handleClose", false);
        },
        // 确定按钮
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.templateLoading = true;
                    if (this.newDrawerTitle === "新建模板") {
                        addImgList({
                            ...this.ruleForm,
                            typeId: this.typeId,
                        })
                            .then((res) => {
                                this.handleClose("ruleForm");
                                this.$message.success(res.message);
                            })
                            .finally(() => {
                                this.templateLoading = false;
                            });
                    } else if (this.newDrawerTitle === "编辑模板") {
                        editImgInfo({
                            ...this.ruleForm,
                            typeId: this.typeId,
                        })
                            .then((res) => {
                                this.handleClose("ruleForm");
                                this.$message.success(res.message);
                            })
                            .finally(() => {
                                this.templateLoading = false;
                            });
                    }
                }
            });
        },
        // 图片上传成功  750，1334尺寸
        importOneList(res) {
            if (res.code === 0) {
                this.ruleForm.details[0].coverImg = res.data[0].url;
            } else {
                this.$message.error(res.message);
            }
        },
        // 图片上传成功 1024，576尺寸
        importTwoList(res) {
            if (res.code === 0) {
                this.ruleForm.details[1].coverImg = res.data[0].url;
            } else {
                this.$message.error(res.message);
            }
        },
        handlerTempMessTypeList() {
            getTempMessTypeList().then(({ data }) => {
                this.availableMes = data;
            });
        },
    },
    created() {
        this.token = "Bearer " + this.$ls.get(ACCESS_TOKEN);
    },
    watch: {
        // 标题
        drawerTitle(val) {
            this.newDrawerTitle = val;
        },
        // 弹框状态
        drawer(val) {
            this.newDrawer = val;
            if (val) {
                this.handlerTempMessTypeList();
            }
        },
        // 模板分类id
        classifyType(val) {
            this.typeId = val;
        },
        // 模板详情 回显
        templateInfo(val) {
            this.ruleForm = val;
        },
        // 图库 海报 ID
        typeMessId(val) {
            console.log(val, "77777");
        },
    },
};
</script>

<style lang="scss" scoped>
.imgUpload {
    width: 95%;
    background: #efefef5e;
    margin: 14px 20px 14px 14px;
    padding: 0px 16px 0px 0px;
}

.sizeName {
    width: 200px;
    margin: 0px 20px;
    padding: 20px 0px;
    font-size: 16px;
}

.yd-form-footer {
    z-index: 2;
    text-align: center;
    height: 64px;
    position: absolute;
    bottom: 0px;
    background: #fff;
    width: 100%;

    .footerForm {
        display: flex;
        width: 100%;
        justify-content: center;
        align-items: center;
        height: 64px;
        border: 1px solid #eee;
    }
}
</style>
