<script>

import router from "@/router/index";

export default {
	functional: true,
	props: {
		auth: {
			type: String,
			required: true
		}
	},
	render(h, context) {
		const { props, scopedSlots } = context;
		const permissions = (router.history.current.meta && router.history.current.meta.permissions) ? router.history.current.meta.permissions : [];
		return permissions.some((item) => item == props.auth) ? (scopedSlots.default && scopedSlots.default()) : null;
	}
};
</script>