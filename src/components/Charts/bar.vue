<template>
    <div style="height:100%;width:100%;" ref="chart1"></div>
</template>
<script>
import echarts from "echarts";
export default {
	data() {
		return {
			myChart: {}
		};
	},
	methods: {
		initCharts() {
			this.$nextTick(() => {
				this.myChart = echarts.init(this.$refs.chart1);
				this.myChart.setOption({
					title: {
						text: "总访问量 126,560"
					},
					color: ["#3398DB"],
					tooltip: {
						trigger: "axis",
						axisPointer: {
							type: "shadow"
						}
					},
					grid: {
						left: "3%",
						right: "4%",
						bottom: "3%",
						containLabel: true
					},
					xAxis: [
						{
							type: "category",
							data: ["3月", "4月", "5月", "6月", "7月", "8月"],
							axisTick: {
								alignWithLabel: true
							}
						}
					],
					yAxis: [
						{
							type: "value"
						}
					],
					series: [
						{
							name: "直接访问",
							type: "bar",
							barWidth: "60%",
							data: [52, 200, 334, 390, 330, 220]
						}
					]
				});
			});
		}
	},
	mounted() {
		this.initCharts();
		const _this = this;
		window.onresize = () => {
			_this.myChart.resize();
		};
	}
};
</script>



