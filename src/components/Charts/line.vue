<template>
    <div style="height:100%;width:100%;" ref="chart2"></div>
</template>
<script>
import echarts from "echarts";
export default {
	data() {
		return {
			myChart: {}
		};
	},
	methods: {
		initCharts() {
			this.myChart = echarts.init(this.$refs.chart2);
			// 绘制图表
			this.myChart.setOption({
				title: {
					text: "访问量 3721"
				},
				tooltip: {
					trigger: "axis",
					axisPointer: {
						type: "cross",
						label: {
							backgroundColor: "#6a7985"
						}
					}
				},
				legend: {
					data: ["直接访问", "搜索引擎"]
				},
				toolbox: {
					feature: {
						saveAsImage: {}
					}
				},
				grid: {
					left: "3%",
					right: "4%",
					bottom: "3%",
					containLabel: true
				},
				xAxis: [
					{
						type: "category",
						boundaryGap: false,
						data: [
							"周一",
							"周二",
							"周三",
							"周四",
							"周五",
							"周六",
							"周日"
						]
					}
				],
				yAxis: [
					{
						type: "value"
					}
				],
				series: [
					{
						name: "直接访问",
						type: "line",
						stack: "总量",
						// areaStyle: { normal: { color: '#3398DB' } },
						data: [1, 10, 25, 60, 32, 71, 18]
					},
					{
						name: "搜索引擎",
						type: "line",
						stack: "总量",
						label: {
							normal: {
								show: true,
								position: "top"
							}
						},
						// areaStyle: { normal: {} },
						data: [1, 18, 2, 25, 18, 12, 6]
					}
				]
			});
		}
	},
	mounted() {
		this.initCharts();
		const _this = this;
		window.onresize = () => {
			_this.myChart.resize();
		};
	}
};
</script>



