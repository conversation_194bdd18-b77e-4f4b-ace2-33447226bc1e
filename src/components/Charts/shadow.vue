<template>
    <div style="height:100%;width:100%;" ref="chart"></div>
</template>
<script>
import echarts from "echarts";
export default {
	data() {
		return {
			myChart: {}
		};
	},
	methods: {
		initCharts() {
			this.$nextTick(() => {
				this.myChart = echarts.init(this.$refs.chart);
				this.myChart.setOption({
					title: {
						text: "访问来源",
						subtext: "数据来自网络"
					},
					tooltip: {
						trigger: "axis",
						axisPointer: {
							type: "shadow"
						}
					},
					grid: {
						left: "1%",
						right: "4%",
						bottom: "3%",
						containLabel: true
					},
					xAxis: {
						type: "value",
						boundaryGap: [0, 0.01]
					},
					yAxis: {
						type: "category",
						data: ["深圳", "广州", "成都", "北京", "上海", "江西"]
					},
					color: "#3398DB",
					series: [
						{
							type: "bar",
							barWidth: "10",
							barGap: "1%",
							data: [60, 20, 32, 15, 48, 28]
						}
					]
				});
			});
		}
	},
	mounted() {
		this.initCharts();
		const _this = this;
		window.onresize = () => {
			_this.myChart.resize();
		};
	}
};
</script>



