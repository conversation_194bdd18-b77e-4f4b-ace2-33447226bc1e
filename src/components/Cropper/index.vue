<template>
    <div class="cropper-container">
        <div v-if="isHandle" class="handle-box">
            <div class="left">
                <div class="img-box">
                    <vue-cropper ref="cropper" :img="options.img" :info="true" :autoCrop="options.autoCrop" :autoCropWidth="options.autoCropWidth" :autoCropHeight="options.autoCropHeight" :fixedBox="options.fixedBox" :original="options.original" :high="options.high" :full="options.full" @realTime="realTime"></vue-cropper>
                </div>
                <div class="img-handle">
                    <div style="display: flex;line-height: 50px;">
                        <el-upload ref="upload" :accept="accept" action="/" :on-change="changeUpload" :show-file-list="false" :auto-upload="false">
                            <el-button slot="trigger" size="small">上传</el-button>
                        </el-upload>
                        <el-button v-if="batchUpload" @click="isHandle=false" size="small" style="height: 26px;margin-top:13px;margin-left: -1px;">批量上传</el-button>
                    </div>
                    <div class="flex">
                        <i class="el-icon-refresh-left icon" @click="rotateLeft" />
                        <div class="change-img-size">
                            <el-slider v-model="sizeValue" @input="changeScale" style="margin-top: 5px;"></el-slider>
                        </div>
                        <i class="el-icon-refresh-right icon" @click="rotateRight" />
                    </div>
                    <div>
                        <i class="el-icon-view icon" @click="previewsImg" />
                        <i class="el-icon-download icon" @click="download" />
                    </div>
                </div>
            </div>
            <div class="right">
                <div class="preview">
                    <div class="preview-container">
                        <img :src="previews.url" :style="previews.img" width="100%" height="auto" />
                    </div>
                </div>
                <div class="preview circle">
                    <div class="preview-container">
                        <img :src="previews.url" :style="previews.img" width="100%" height="auto" />
                    </div>
                </div>

                <div class="btn">
                    <el-button @click="$emit('cancel')">取 消</el-button>
                    <el-button :loading="loading" :disabled="options.img?false:true" type="primary" @click="upload">确 认</el-button>
                </div>
            </div>
        </div>
        <!--  -->

        <div v-else class="no-handle">
            <el-upload class="batchupload" :on-preview="handlePreview" :accept="accept" drag :on-success="uploadSuccess" :headers="headers" :action="action" multiple>
                <i class="el-icon-upload"></i>
                <div>
                    将文件拖到此处，或
                    <em>点击上传</em>
                </div>
                <ul class="upload__tip" slot="tip">
                    <li>1.只能上传jpg/png/jpeg文件</li>
                    <li>2.支持批量长传</li>
                    <li>
                        3.如需裁剪图片，请点击：
                        <span @click="isHandle=true" class="handle-img" :style="stylelink">裁剪图片</span>
                    </li>
                </ul>
            </el-upload>
        </div>
        <!--  -->

        <div class="model" v-show="model">
            <div class="model-show" @click="model = false">
                <img :src="modelSrc" alt @click="model = false" />
            </div>
        </div>
    </div>
</template>

<script>
import { VueCropper } from "vue-cropper";
import {
	ACCESS_TOKEN
} from "@/store/mutation-types";
export default {
	props: {
		handleImg: {
			type: Boolean,
			default: false
		},
		batchUpload: {
			type: Boolean,
			default: true
		},
		handleImgsrc: {
			type: String,
			default: ""
		},
		action: {
			type: String,
			default: "/"
		}
	},
	watch: {
		handleImg: {
			handler(b) {
				this.isHandle = b;
			},
			immediate: true
		},
		handleImgsrc: {
			handler(src) {
				this.options.img = src;
			},
			immediate: true
		}
	},
	data() {
		return {
			options: {
				img: "",
				autoCrop: true,
				autoCropWidth: 150,
				autoCropHeight: 150,
				fixedBox: false,
				original: false,
				high: true,
				full: true,
				name: null
			},
			previews: {},
			model: false,
			modelSrc: "",
			sizeValue: 50,
			befoval: 1,
			accept: "image/png, image/jpeg, image/jpg",
			isHandle: false,
			loading: false,
			headers: {
				Authorization: "Bearer " + this.$ls.get(ACCESS_TOKEN)
			}
		};
	},
	computed: {
		stylelink() {
			return `color:${this.$store.state.system.themeColor}`;
		}
	},
	methods: {
		uploadSuccess(response, file, fileList) {
			this.$message.success(response.msg);

			this.$emit("updata", response);
		},
		upload() {
			this.$refs.cropper.getCropBlob(blob => {
				this.loading = true;
				this.$emit("upload", blob, this.options.name, () => {
					this.loading = false;
				});
			});
		},
		handlePreview(file) {
			this.getBase64(file.raw).then(src => {
				this.model = true;
				this.modelSrc = src;
			});
		},
		changeUpload(file) {
			const _this = this;
			this.getBase64(file.raw)
				.then(src => {
					_this.options.img = src;
					this.options.name = file.name;
				})
				.catch(err => {
					console.log(err);
				});
		},
		//2进制转换成base64
		getBase64(file) {
			return new Promise(function (resolve, reject) {
				let reader = new FileReader();
				let imgResult = "";
				reader.readAsDataURL(file);
				reader.onload = function () {
					imgResult = reader.result;
				};
				reader.onerror = function (error) {
					reject(error);
				};
				reader.onloadend = function () {
					resolve(imgResult);
				};
			});
		},
		// 旋转90度
		rotateLeft() {
			this.$refs.cropper.rotateLeft();
		},
		rotateRight() {
			this.$refs.cropper.rotateRight();
		},
		changeScale(num) {
			let v = 1;
			if (num > this.befoval) {
				v = 1;
			} else {
				v = -1;
			}
			this.befoval = num;
			this.$refs.cropper.changeScale(v);
		},
		//实时预览
		realTime(data) {
			this.previews = data;
		},
		// download
		download() {
			// 获取截图的base64 数据/getCropData  blob数据//getCropBlob
			this.$refs.cropper.getCropBlob(blob => {
				let aLink = document.createElement("a");
				let evt = document.createEvent("HTMLEvents");
				evt.initEvent("click", true, true);
				aLink.download = "下载";
				aLink.href = URL.createObjectURL(blob);
				aLink.click();
			});
		},
		//previews
		previewsImg() {
			// 输出
			// var test = window.open('about:blank');
			// test.document.body.innerHTML = '图片生成中..';
			this.$refs.cropper.getCropData(data => {
				this.model = true;
				this.modelSrc = data;
			});
		}
	},
	components: {
		VueCropper
	}
};
</script>

<style lang="scss">
	.cropper-container {
		min-height: 460px;
		.handle-box {
			display: flex;
			width: 100%;
		}
		.batchupload {
			.el-upload,
			.el-upload-dragger {
				width: 100%;
			}
			.upload__tip {
				li {
					padding: 8px 0;
				}
				.handle-img {
					cursor: pointer;
					padding: 8px;
					text-decoration: underline;
					transition: color 0.3s;
					&:hover {
						color: $hover-color !important;
					}
				}
			}
		}
		.left {
			width: 70%;
			padding: 5px 5px 0;
			border: 1px solid rgba(0, 0, 0, 0.15);
			border-radius: 4px;
			.img-box {
				height: 400px;
			}
			.img-handle {
				height: 50px;
				// line-height: 50px;
				display: flex;
				justify-content: space-between;
			}
			.icon {
				font-size: 16px;
				line-height: 50px;
				margin-left: 10px;
				cursor: pointer;
			}
			.flex {
				display: flex;
			}
			.change-img-size {
				width: 150px;
				margin-left: 10px;
			}
		}
		.right {
			flex: 1;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			.preview {
				height: 150px;
				width: 150px;
				margin: 0 auto;
				padding: 5px;
				border-radius: 4px;
				border: 1px solid rgba(0, 0, 0, 0.15);
			}
			.preview-container {
				height: 100%;
				width: 100%;
				overflow: hidden;
			}
			.circle {
				margin-top: 20px;
				border-radius: 50%;
				overflow: hidden;
				img {
					border-radius: 50%;
				}
				.preview-container {
					border-radius: 50%;
				}
			}
			.btn {
				text-align: center;
			}
		}
		.model {
			position: fixed;
			z-index: 2500;
			width: 100vw;
			height: 100vh;
			// overflow: auto;
			top: 0;
			left: 0;
			background: rgba(0, 0, 0, 0.8);
		}
		.model-show {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 100vw;
			height: 100vh;
			text-align: center;
		}
		.model img {
			display: block;
			margin: auto;
			max-width: 80%;
			width: auto;
			user-select: none;
			background-position: 0px 0px, 10px 10px;
			background-size: 20px 20px;
			background-image: linear-gradient(
					45deg,
					#eee 25%,
					transparent 25%,
					transparent 75%,
					#eee 75%,
					#eee 100%
				),
				linear-gradient(
					45deg,
					#eee 25%,
					white 25%,
					white 75%,
					#eee 75%,
					#eee 100%
				);
		}
	}
</style>