import {
    Button,
    Container,
    Header,
    Aside,
    Main,
    Footer,
    Row,
    Col,
    Dropdown,
    DropdownMenu,
    DropdownItem,
    Tooltip,
    Menu,
    MenuItem,
    Submenu,
    Breadcrumb,
    BreadcrumbItem,
    ColorPicker,
    Image,
    Badge,
    Select,
    Option,
    Checkbox,
    Dialog,
    Slider,
    ButtonGroup,
    Upload,
    Link,
    Tag,
    Input,
    Tabs,
    TabPane,
    Card,
    Pagination,
    Form,
    FormItem,
    RadioGroup,
    RadioButton,
    DatePicker,
    Table,
    TableColumn,
    MessageBox,
    Message,
    Alert,
    CheckboxGroup,
    Notification,
    Popover,
    Timeline,
    TimelineItem,
    Radio,
    TimePicker,
    Loading,
    Carousel,
    CarouselItem,
    Tree,
    Drawer,
    Empty,
    Cascader,
    Switch,
    InputNumber,
    Progress,
    Descriptions,
    DescriptionsItem
} from "element-ui";

const element = {
    install: function (Vue) {
        Vue.use(Tree);
        Vue.use(CarouselItem);
        Vue.use(Carousel);
        Vue.use(Button);
        Vue.use(Container);
        Vue.use(Header);
        Vue.use(Aside);
        Vue.use(Main);
        Vue.use(Footer);
        Vue.use(Row);
        Vue.use(Col);
        Vue.use(Dropdown);
        Vue.use(DropdownMenu);
        Vue.use(DropdownItem);
        Vue.use(Tooltip);
        Vue.use(Menu);
        Vue.use(MenuItem);
        Vue.use(Submenu);
        Vue.use(Breadcrumb);
        Vue.use(BreadcrumbItem);
        Vue.use(ColorPicker);
        Vue.use(Image);
        Vue.use(Badge);
        Vue.use(Select);
        Vue.use(Option);
        Vue.use(Checkbox);
        Vue.use(Dialog);
        Vue.use(Slider);
        Vue.use(ButtonGroup);
        Vue.use(Upload);
        Vue.use(Link);
        Vue.use(Tag);
        Vue.use(Input);
        Vue.use(Tabs);
        Vue.use(TabPane);
        Vue.use(Card);
        Vue.use(Pagination);
        Vue.use(Form);
        Vue.use(FormItem);
        Vue.use(RadioGroup);
        Vue.use(RadioButton);
        Vue.use(DatePicker);
        Vue.use(Table);
        Vue.use(TableColumn);
        Vue.use(Alert);
        Vue.use(CheckboxGroup);
        Vue.use(Popover);
        Vue.use(Timeline);
        Vue.use(TimelineItem);
        Vue.use(Radio);
        Vue.use(TimePicker);
        Vue.use(Loading);
        Vue.use(Drawer);
        Vue.use(Empty);
        Vue.use(Cascader);
        Vue.use(Switch);
        Vue.use(InputNumber);
        Vue.use(Progress);
        Vue.use(Descriptions);
        Vue.use(DescriptionsItem);

        // Vue.use(MessageBox);
        // Vue.use(Message);
        Vue.prototype.$confirm = MessageBox.confirm;
        Vue.prototype.$message = Message;
        Vue.prototype.$notify = Notification;
    },
};
export default element;
