
<template>
    <div class="expression">
        <el-tabs v-model="defaultActiveKey" tab-position="left" @tab-click="tabChange">
            <el-tab-pane :label="k | typeFilter" :name="k" v-for="(v, k,)  in tabs" :key="k">
                <div class="container">
                    <el-input v-model="searchVal" style="margin-left:20px;max-width: 200px">
                        <i slot="suffix" class="el-input__icon el-icon-search search_icon" @click="onSearch(searchVal)"></i>
                    </el-input>
                    <ul class="list">
                        <li class="item" @click="handleChange(item)" v-for="(item,index) in searchList.length?searchList:tabs[k]" :key="index" :title="item.keywords[0]">{{item.char}}</li>
                    </ul>
                </div>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script>
import { emojis } from "./emojis";
export default {
	name: "expression",
	data() {
		return {
			searchVal: "",
			tabs: {},
			defaultActiveKey: "symbols",
			searchList: []
		};
	},
	filters: {
		typeFilter(k) {
			const list = [
				{
					k: "symbols",
					v: "符号"
				}, {
					k: "people",
					v: "人类"
				}, {
					k: "animals_and_nature",
					v: "动物和自然"
				}, {
					k: "food_and_drink",
					v: "食物和饮品"
				}, {
					k: "activity",
					v: "活动"
				}, {
					k: "travel_and_places",
					v: "旅游和地点"
				}, {
					k: "objects",
					v: "物件"
				}, {
					k: "flags",
					v: "旗帜"
				}];
			const obj = list.find(item => item.k === k);
			return obj.v || k;
		}
	},
	methods: {
		tabChange(tab) {
			this.searchList = [];
			this.defaultActiveKey = tab.name;
		},
		onSearch(value) {
			let arr = this.tabs[this.defaultActiveKey];
			if (!arr.length) return;
			let result = [];
			arr.forEach(item => {
				if (item.keywords && item.keywords.length) {
					item.keywords.forEach(c => {
						if (c.indexOf(value) !== -1) {
							result.push(item);
						}
					});
				}
			});
			this.searchList = result;
		},
		handleChange(item) {
			this.$emit("change", item);
		}
	},
	created() {
		let obj = {};
		for (let k in emojis) {
			obj[emojis[k].category] = obj[emojis[k].category] !== undefined ? obj[emojis[k].category].concat(emojis[k]) : [emojis[k]];
		}
		this.tabs = obj;
	}
};
</script>

<style lang='scss'>
	.expression {
		display: flex;
		font-size: 16px;
		font-style: normal;
		font-weight: 400;
		line-height: 1.3;
		.search_icon {
			cursor: pointer;
		}
		.container {
			padding-top: 24px;
		}
		.list {
			display: flex;
			flex-wrap: wrap;
			list-style: none;
			padding: 0;
			margin: 0;
			max-height: 650px;
			height: 302px;
			padding-top: 24px;
			.item {
				border-radius: 3px;
				padding: 4px;
				color: #222f3e;
				cursor: pointer;
				user-select: none;
				align-items: center;
				display: flex;
				height: 28px;
				justify-content: center;
				width: 28px;
				&:hover {
					background: rgb(222, 224, 226);
				}
			}
		}
	}
</style>
