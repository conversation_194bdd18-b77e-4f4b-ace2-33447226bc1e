<template>
    <div class="dialog_class">
        <el-dialog
            :modal="false"
            :title="titleVisible ? '新增帮助' : '编辑帮助'"
            :visible.sync="helpVisible"
            width="80%"
            top="50px"
            style="height: 1000px; overflow: hidden"
            :before-close="cloce"
        >
            <el-form
                ref="helpForm"
                :model="helpForm"
                :rules="helpRules"
                label-width="200px"
            >
                <el-form-item label="范围：" prop="idRanges">
                    <el-checkbox-group v-model="helpForm.idRanges">
                        <el-checkbox label="0"> 教师端（app）</el-checkbox>
                        <el-checkbox label="1"> 家长端（app）</el-checkbox>
                        <el-checkbox label="2"> 教师端（web）</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label="标题：" prop="name">
                    <el-input
                        v-model="helpForm.name"
                        style="width: 300px"
                        placeholder="请在这里输入标题"
                        maxlength="64"
                        show-word-limit
                    />
                </el-form-item>
                <el-form-item label="分类：" prop="type">
                    <el-radio-group v-model="helpForm.type">
                        <!-- <el-radio label="0">入门指引</el-radio>
                        <el-radio label="1">进阶使用</el-radio> -->
                        <el-radio label="2">常见问题</el-radio>
                        <el-radio label="3">功能指南</el-radio>
                        <el-radio label="4">教学视频</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="设置为热门问题" prop="hotInd">
                    <el-switch
                        v-model="helpForm.hotInd"
                        active-value="0"
                        inactive-value="1"
                    />
                </el-form-item>
                <el-form-item label="排序：" prop="sort">
                    <el-input-number
                        v-model="helpForm.sort"
                        controls-position="right"
                        :min="1"
                        :max="9999"
                    />
                    <p style="color: #ccc">数值越小顺序越前</p>
                </el-form-item>
                <el-form-item label="版本标识：" prop="versionType">
                    <el-select
                        v-model="helpForm.versionType"
                        placeholder="请选择"
                    >
                        <el-option label="1.0" :value="1" />
                        <el-option label="2.0" :value="2" />
                    </el-select>
                </el-form-item>
                <el-form-item label>
                    <el-radio-group v-model="helpForm.status" size="medium">
                        <el-radio-button :label="0">富文本</el-radio-button>
                        <el-radio-button :label="1">视频</el-radio-button>
                    </el-radio-group>
                </el-form-item>

                <el-form-item
                    v-if="helpForm.status === 1"
                    label="视频："
                    prop="video"
                >
                    <el-upload
                        action="/"
                        accept=".mp4, .mpg, .mpeg, .avi, .rm, .rmv, .mov, .wmv, .asf, .mp4, .mov, .mkv"
                        :on-change="uploadChange"
                        :before-remove="uploadRemove"
                        :file-list="fileList"
                        :multiple="false"
                        :auto-upload="false"
                        :show-file-list="false"
                        :on-preview="handlePictureCardPreview"
                        name="file"
                    >
                        <i
                            v-if="helpForm.video === null"
                            style="
                                width: 200px;
                                height: 149px;
                                border: 1px solid #eee;
                                line-height: 149px;
                                font-size: 28px;
                            "
                            class="el-icon-plus avatar-uploader-icon"
                        ></i>
                        <video
                            style="
                                width: 100%;
                                height: 149px;
                                border: 1px solid #eee;
                                min-width: 200px;
                            "
                            v-else
                            :src="helpForm.video"
                            controls
                        ></video>
                    </el-upload>
                    <el-progress
                        v-if="this.percentage != 0 && this.percentage != 100"
                        :percentage="this.percentage"
                    >
                    </el-progress>
                </el-form-item>
                <el-form-item
                    v-if="helpForm.status === 1"
                    label="链接地址："
                    prop="video"
                >
                    <el-input
                        v-model="helpForm.video"
                        style="width: 300px"
                        placeholder="请在这里链接地址"
                        show-word-limit
                    />
                </el-form-item>
                <el-form-item
                    v-if="helpForm.status === 0"
                    prop="message"
                    label="内容："
                    style="padding-bottom: 100px"
                >
                    <tinymce-editor
                        :plugins="plugins"
                        :toolbar="toolbar"
                        folderType="helpCore"
                        v-model="helpForm.message"
                        height="500px"
                    />
                </el-form-item>
            </el-form>
            <div shadow="never" class="article_createOrEdit__footer">
                <div class="btn_warp_bottom">
                    <el-button @click="cloce">取消</el-button>
                    <el-button type="primary" @click="addHelpForm('helpForm')"
                        >提交</el-button
                    >
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import TinymceEditor from "@/components/Tinymce-editor";
import { getUploadFileOSSpath } from "@/utils/oss";
import { createHelp, updataHelp } from "@/api/helpCore.js";
export default {
    components: { TinymceEditor },
    props: {
        helpVisible: {
            type: Boolean,
            default: false,
        },
        titleVisible: {
            type: Boolean,
            default: false,
        },
        infoForm: {
            type: Object,
        },
    },
    data() {
        return {
            dialogTitle: false,
            plugins:
                "anchor charmap lineheight lists  advlist image media table wordcount fullscreen autoresize print preview save hr code codesample link pagebreak insertdatetime searchreplace emoticons template noneditable help",
            toolbar: [
                " code lineheight undo redo copy cut bold italic forecolor backcolor underline strikethrough link alignleft aligncenter alignright alignjustify bullist numlist outdent indent blockquote subscript superscript removeformat formatselect fontselect fontsizeselect table image media anchor charmap emoticons template  hr codesample insertdatetime  pagebreak searchreplace print lists fullscreen preview save help",
            ],
            helpForm: {
                idRanges: [],
                name: "",
                type: 0,
                hotInd: "0",
                sort: 1,
                video: null,
                message: "",
                status: 0,
                versionType: null,
            },
            helpRules: {
                name: [
                    {
                        required: true,
                        message: "请输入标题名称",
                        trigger: "blur",
                    },
                    {
                        min: 1,
                        max: 64,
                        message: "长度在 1 到 64 个字符",
                        trigger: "blur",
                    },
                ],
                type: [
                    {
                        required: true,
                        message: "请选择分类",
                        trigger: "change",
                    },
                ],
                idRanges: [
                    {
                        required: true,
                        message: "请选择范围",
                        trigger: "change",
                    },
                ],
                sort: [
                    {
                        required: true,
                        message: "排序不能为空",
                        trigger: "blur",
                        type: "number",
                    },
                    {
                        min: 1,
                        max: 9999,
                        message: "排序区间 1 到 9999 个",
                        trigger: "blur",
                        type: "number",
                    },
                ],
                versionType: [
                    {
                        required: true,
                        message: "请选择版本标识",
                        trigger: "blur",
                    },
                ],

                video: [
                    {
                        required: true,
                        message: "请上传视频",
                        trigger: "blur",
                    },
                    {
                        pattern:
                            /^(https?|ftp|file):\/\/[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]$/,
                        message: "请输入正确链接地址",
                        trigger: "blur",
                    },
                ],
                message: [
                    {
                        required: true,
                        message: "内容不能为空",
                        trigger: "blur",
                    },
                ],
            },
            fileList: [],
            percentage: 0,
        };
    },

    watch: {
        titleVisible(val) {
            this.dialogTitle = val;
            if (!val) {
                this.fileList = [];
                this.helpForm.video = null;
            }
        },
        infoForm(val) {
            this.helpForm = val || { video: null };
        },
    },
    methods: {
        uploadChange(file, fileList) {
            const isLt50M = file.size / 1024 / 1024 < 50;
            if (!isLt50M) {
                this.$message.error("视频大于50M，请重新上传");
                this.fileList = [];
                return false;
            } else {
                this.uploadImgVideo(file, fileList);
            }
        },
        async uploadImgVideo(file, fileList) {
            this.percentage = 0;
            const url = await getUploadFileOSSpath(
                file.raw,
                "helpCore",
                (num) => {
                    this.percentage = num;
                }
            );
            if (url) {
                if (fileList.length > 0) {
                    this.fileList = [fileList[fileList.length - 1]];
                }
                this.helpForm.video = url;
            } else {
                this.$message.error("上传失败");
                this.helpForm.video = null;
            }
        },
        handlePictureCardPreview(file) {
            this.helpForm.video = file.url;
            // this.dialogVisible = true;
        },
        uploadRemove(file) {
            file.url = "";
            this.helpForm.video = null;
        },
        addHelpForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    switch (this.dialogTitle) {
                        case false:
                            createHelp(this.helpForm).then((res) => {
                                this.$message.success(res.message);
                                this.cloce();
                            });
                            break;
                        case true:
                            updataHelp(this.helpForm).then((res) => {
                                this.$message.success(res.message);
                                this.cloce();
                            });
                            break;

                        default:
                            break;
                    }
                } else {
                    return false;
                }
            });
        },
        cloce() {
            this.helpForm.video = null;
            this.fileList = [];
            this.$emit("handleClose", false);
        },
    },
};
</script>

<style lang="scss">
.dialog_class {
    .el-dialog__wrapper {
        z-index: 1100 !important;
    }
    .el-dialog {
        position: relative;
        .el-dialog__body {
            height: 770px !important;
            overflow-y: auto !important;
        }
    }
    .article_createOrEdit__footer {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        // box-shadow: 0 -1px 2px rgb(0 0 0 / 3%);
        border-top: 1px solid #e8e8e8;
        .btn_warp_bottom {
            padding: 0px 24px;
            border-top: 1px solid #e8e8e8;
            justify-content: right;
        }
        z-index: 9;
        height: 60px;
        line-height: 60px;
        background: #fff;
        text-align: right;
    }
}
</style>
