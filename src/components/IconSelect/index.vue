<!--
 * @Descripttion: ICON 选择器
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-09-24 11:21:40
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2021-10-06 22:52:39
-->
<template>
	<div icon="icon_select">
		<el-popover v-model="visible" placement="bottom" width="265" trigger="click">
			<transition name="el-zoom-in-top">
				<div v-show="visible" class="icon-selector-warp">
					<div class="icon-selector-warp-row">
						<el-row :gutter="10">
							<el-col v-for="(v, k) in iconsList" :key="k" :xs="5" :sm="4" :md="4" :lg="4" :xl="4">
								<div class="icon-selector-warp-item active" @click="handleSelect(v)">
									<i :class="`${v}`" />
								</div>
							</el-col>
						</el-row>
						<!-- <el-empty :image-size="100" description="无相关图标"></el-empty> -->
					</div>
				</div>
			</transition>

			<el-input slot="reference" :placeholder="icon">
				<i slot="prepend" :class="`${icon}`" style="font-size: 21px;" />
			</el-input>
		</el-popover>
	</div>
</template>

<script>
import { iconsList } from "./icons";
export default {
	props: {
		value: {
			type: String,
			default: ""
		}
	},
	data() {
		return {
			iconsList,
			visible: false,
			icon:this.value,
		};
	},
	watch:{
        value(v){
           this.icon = v;
        },
        icon(v){
            this.$emit("input",v);
        }
    },
	methods: {
		handleSelect(v) {
			this.icon = v;
			this.visible = false;
		}
	}
};
</script>

<style lang="scss" scoped>
	.icon-selector-warp {
		height: 300px;
		position: relative;
		overflow: hidden;
	}
	.icon-selector-warp-row {
		height: 100%;
		padding-right: 10px;
		overflow-y: auto;
	}
	.icon-selector-warp-item {
		text-align: center;
		padding: 3px;
		cursor: pointer;
		z-index: 9999;
		i {
			font-size: 24px;
			transition: color 0.15s linear;
		}
		&:hover {
			i {
				color: #409eff;
			}
		}
	}
</style>
