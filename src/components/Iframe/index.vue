<template>
    <div v-loading="spinning" element-loading-text="加载中" class="iframe_warp">
        <iframe
            :src="src"
            frameborder="0"
            class="my_iframe"
            id="_iframe"
            @load="load"
            :key="src"
        ></iframe>
    </div>
</template>

<script>
import { ACCESS_TOKEN } from "@/store/mutation-types";
export default {
    name: "Iframe",
    props: {
        src: {
            type: String,
            default: "",
        },
        isPostMessage: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            spinning: true,
            token: "",
        };
    },
    computed: {
        comPostMessage() {
            return this.isPostMessage;
        },
    },
    created() {
        this.token = "Bearer " + this.$ls.get(ACCESS_TOKEN);
    },
    mounted() {
        this.load();
    },
    methods: {
        load() {
            this.spinning = false;
            // if (this.comPostMessage) {
            //     const iframe = document.querySelector("#_iframe");
            //     iframe.contentWindow.postMessage(
            //         { token: this.token },
            //         this.src
            //     );
            // }
        },
    },
};
</script>

<style lang="scss">
.iframe_warp {
    width: 100%;
    height: calc(100vh - 100px);
}
.my_iframe {
    width: 100%;
    height: 100%;
}
</style>
