<template>
    <el-breadcrumb
        separator-class="el-icon-arrow-right"
        class="base-breadcrumb"
    >
        <transition-group name="breadcrumb">
            <el-breadcrumb-item
                :to="{ path: item.path }"
                v-for="item in routerList"
                :key="item.meta.title"
            >
                <span>{{ item.meta.title }}</span>
            </el-breadcrumb-item>
        </transition-group>
    </el-breadcrumb>
</template>

<script>
export default {
    data() {
        return {
            routerList: [],
        };
    },
    watch: {
        $route() {
            this.getRouterName();
        },
    },
    methods: {
        getRouterName() {
            let matched = this.$route.matched.filter((item) => item.name);
            const first = matched[0];
            if (
                first &&
                first.name.trim().toLocaleLowerCase() !==
                    "/".toLocaleLowerCase()
            ) {
                // { path: "/", meta: { title: "首页" } }]
                matched = [].concat(matched);
            }
            this.routerList = matched;
        },
    },
    mounted() {
        this.getRouterName();
    },
};
</script>
<style lang="scss" scoped>
.base-breadcrumb {
    line-height: 60px;
}
.breadcrumb-enter-active,
.breadcrumb-leave-active {
    transition: all 0.5s;
}

.breadcrumb-enter,
.breadcrumb-leave-active {
    opacity: 0;
    transform: translateX(20px);
}

.breadcrumb-move {
    transition: all 0.5s;
}

.breadcrumb-leave-active {
    position: absolute;
}
</style>
