<template>
    <div class="base-header">
        <!-- logo -->
        <logo />
        <!--  -->
        <div class="amusing">
            <!--  -->
            <div style="display:flex;">
                <div class="h-icon-collapse" :class="isCollapse?'el-icon-s-unfold':'el-icon-s-fold'" @click="$store.commit('changeCollapse')"></div>
                <breadcrumb />
            </div>
            <!--  -->
            <User />
        </div>
        <!--  -->
    </div>
</template>

<script>
import Logo from "./logo";
import User from "./user";
import Breadcrumb from "./breadcrumb";
export default {
	components: { Breadcrumb, User, Logo },
	computed: {
		isCollapse() {
			return this.$store.state.system.isCollapse;
		}
	}
};
</script>
<style lang="scss">
	.base-header {
		height: 100%;
		display: flex;
		line-height: 60px;
		.amusing {
			flex: 1;
			padding-left: 20px;
			display: flex;
			justify-content: space-between;
			.h-icon-collapse {
				line-height: 60px;
				font-size: 24px;
				padding-right: 12px;
				cursor: pointer;
				transition: all 0.2s;
			}
		}
	}
</style>

