<template>
    <div>
        <el-tabs v-model="activeName" @tab-click="handleClick" stretch>
            <el-tab-pane label="通知" name="first">空空如也</el-tab-pane>
            <el-tab-pane label="消息" name="second">空空如也</el-tab-pane>
            <el-tab-pane label="待办" name="third">空空如也</el-tab-pane>
            <el-tab-pane label="联系人" name="fourth">
                <ul class="contacts-list">
                    <li class="item">
                        <div class="avatar-warp">
                            <img
                                src="https://img3.mukewang.com/szimg/5d08d0b308c9749706000338.jpg"
                                width="40"
                                height="40"
                                alt
                            />
                        </div>
                        <div class="info-warp">
                            <h3 class="userName">
                                Laihq
                                <span>(付笑谈中)</span>
                            </h3>
                            <p
                                class="autograph"
                                title="他并没有什么个性，但还是在这签了个名!"
                            >
                                他并没有什么个性，但还是在这签了个名!
                            </p>
                            <div class="video">
                                <i class="el-icon-video-camera" />
                            </div>
                        </div>
                    </li>
                    <li class="item" @click="initiateChat">
                        <div class="avatar-warp">
                            <img
                                src="https://img2.mukewang.com/szimg/5c60ed0008c8ddcc06000338.jpg"
                                class="notOnline"
                                width="40"
                                height="40"
                                alt
                            />
                        </div>
                        <div class="info-warp">
                            <h3 class="userName">
                                你的名字
                                <span>(北城南笙l)</span>
                            </h3>
                            <p class="autograph" title="静如水，动如风">
                                静如水，动如风!
                            </p>
                            <div class="video">
                                <i class="el-icon-video-camera" />
                            </div>
                        </div>
                    </li>
                </ul>
            </el-tab-pane>
        </el-tabs>
        <!--  -->
        <el-dialog
            :title="dialog.title"
            :visible.sync="dialog.visible"
            width="680px"
            center
            append-to-body
            :modal-append-to-body="false"
        >
            <span>需要注意的是内容是默认不居中的</span>
        </el-dialog>
    </div>
</template>

<script>
export default {
    data() {
        return {
            activeName: "second",
            dialog: {
                title: "",
                visible: false,
            },
        };
    },
    methods: {
        handleClick(tab, event) {
            console.log(tab, event);
        },
        initiateChat() {
            this.dialog.title = "你的名字";
            this.dialog.visible = true;
        },
    },
};
</script>
<style lang="scss">
.contacts-list {
    .item {
        display: flex;
        padding: 5px;
        transition: all 0.2s;
        cursor: pointer;
        position: relative;
        &:hover {
            background: #ebeef5;
            .video {
                opacity: 1;
                background: #ebeef5;
            }
        }
    }
    .notOnline {
        -webkit-filter: grayscale(100%);
        -moz-filter: grayscale(100%);
        -ms-filter: grayscale(100%);
        -o-filter: grayscale(100%);
        filter: grayscale(100%);
        filter: gray;
    }
    .avatar-warp {
        img {
            display: block;
            border-radius: 50%;
        }
    }
    .info-warp {
        margin-left: 10px;
        .userName {
            color: #333;
            font-size: 15px;
            span {
                color: #333;
                font-size: 14px;
            }
        }
        .autograph {
            white-space: nowrap;
        }
        .video {
            opacity: 0;
            position: absolute;
            top: 0;
            right: 0;
            height: 100%;
            width: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            text-align: center;
            transition: all 0.2s;
            box-shadow: -10px 0px 10px #ebeef5;
        }
    }
}
</style>
