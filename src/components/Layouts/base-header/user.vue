<template>
    <div class="user-profile-container">
        <div class="user-profile-content">
            <div style="display: flex; align-items: center">
                <span
                    style="
                        max-width: 254px;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        overflow: hidden;
                        word-break: break-all;
                    "
                >
                    {{ time }}
                </span>
            </div>
            <div class="menu-icons">
                <span class="menu-icon">
                    <theme-picker
                        size="mini"
                        @change="changeTheme"
                        :default="themePickerColor"
                    />
                </span>
            </div>
            <div class="user-profile-body">
                <el-dropdown @command="handleCommand">
                    <div
                        style="
                            display: flex;
                            justify-content: space-around;
                            align-items: center;
                            height: 100%;
                        "
                    >
                        <el-image
                            class="user-avatar"
                            :src="userInfo.avatar"
                            fit="cover"
                        ></el-image>
                        <span class="user-name">{{ userInfo.name }}</span>
                    </div>

                    <el-dropdown-menu class="user-dropdown" slot="dropdown">
                        <el-dropdown-item icon="el-icon-user-solid" command="1"
                            >修改密码</el-dropdown-item
                        >
                        <!-- <el-dropdown-item icon="el-icon-s-tools" command="2"
                            >个人设置</el-dropdown-item
                        >
                        <el-dropdown-item icon="el-icon-thumb" command="4"
                            >联系管理员</el-dropdown-item
                        >
                        <el-dropdown-item icon="el-icon-eleme" command="5"
                            >打开引导页</el-dropdown-item
                        > -->
                        <el-dropdown-item
                            icon="el-icon-switch-button"
                            command="2"
                            >退出登录</el-dropdown-item
                        >
                    </el-dropdown-menu>
                </el-dropdown>
            </div>
        </div>
        <el-dialog
            :modal-append-to-body="false"
            width="600px"
            class="yd-dialog"
            title="修改密码"
            :visible.sync="dialogFormVisible"
            :before-close="handleClose"
            :wrapper-closable="false"
        >
            <el-form
                ref="ruleForm"
                :model="ruleForm"
                :rules="rules"
                label-width="100px"
                class="demo-ruleForm"
                auto-complete="new-password"
            >
                <el-form-item
                    label="手机号"
                    class="form-item-code"
                    prop="currentPhone"
                >
                    <el-input
                        size="large"
                        class="code-input"
                        maxlength="11"
                        v-model:value="ruleForm.currentPhone"
                        placeholder="请输入手机号"
                        autocomplete="new-password"
                    >
                    </el-input>
                </el-form-item>

                <el-form-item
                    label="验证码"
                    class="form-item-code"
                    prop="verifyCode"
                >
                    <el-input
                        size="large"
                        class="code-input"
                        v-model:value="ruleForm.verifyCode"
                        placeholder="请输入验证码"
                        autocomplete="new-password"
                    >
                        <template #suffix>
                            <el-button
                                size="small"
                                type="text"
                                link
                                class="code-num"
                                v-if="codeNum != 180"
                            >
                                {{ codeNum }} 秒
                            </el-button>
                            <el-button
                                class="code-num"
                                size="small"
                                type="text"
                                link
                                v-else
                                @click="handleCode"
                                >获取验证码</el-button
                            >
                        </template>
                    </el-input>
                </el-form-item>
                <el-form-item label="新密码" prop="newPwd">
                    <el-input
                        v-model.trim="ruleForm.newPwd"
                        show-password
                        autocomplete="new-password"
                        placeholder="请输入含字母、数字、特殊字符，长度为8-20个字符的密码"
                    />
                </el-form-item>
                <el-form-item label="确认密码" prop="confirmPwd">
                    <el-input
                        v-model.trim="ruleForm.confirmPwd"
                        autocomplete="new-password"
                        show-password
                        placeholder="请再次输入密码确认"
                    />
                </el-form-item>
                <el-form-item style="margin-bottom: 0" />
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="handleClose">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确 定</el-button
                >
            </div>
        </el-dialog>
    </div>
</template>

<script>
//
import Driver from "driver.js";
import "driver.js/dist/driver.min.css";
// import steps from "@/config/guide";
import { updatePwd, smsMessage } from "@/api/user";
import RSA from "@/utils/rsa.js";
import { mapMutations } from "vuex";
import { ACCESS_TOKEN, USER_ID } from "@/store/mutation-types";
import ThemePicker from "@/components/Theme-picker/";
import { verifyPhone } from "@/utils/toolsValidate.js";

export default {
    data() {
        const me = this,
            reg =
                /^(?=.*[A-Za-z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{}|;':",.\/<>?])[A-Za-z\d!@#$%^&*()_+\-=\[\]{}|;':",.\/<>?]{8,20}$/,
            validatePass = (rule, value, callback) => {
                if (value === "") {
                    callback(new Error("请输入密码"));
                } else {
                    if (!reg.test(value)) {
                        return callback(new Error("请输入含字母、数字、特殊字符，长度为8-20个字符的密码"));
                    }
                    if (this.ruleForm.confirmPwd !== "") {
                        this.$refs.ruleForm.validateField("confirmPwd");
                    }
                    callback();
                }
            },
            validatePass2 = (rule, value, callback) => {
                if (value === "") {
                    callback(new Error("请再次输入密码"));
                } else if (value !== this.ruleForm.newPwd) {
                    callback(new Error("两次输入密码不一致!"));
                } else {
                    callback();
                }
            };

        const validateCode = async (_rule, value) => {
            if (!value) {
                return Promise.reject("请输入验证码");
            }
            return Promise.resolve();
        };

        const validateCurrentPhone = async (_rule, value) => {
            if (value && verifyPhone(value)) {
                this.isCorrectPhone = true;
                return Promise.resolve();
            }
            this.isCorrectPhone = false;
            return Promise.reject("请输入正确的手机号码");
        };

        return {
            time: me.getnewDate(),
            screen: false,
            driver: null,
            msgAmount: 10,
            dialogFormVisible: false,
            codeNum: 180,
            ruleForm: {
                currentPhone: "",
                verifyCode: "",
                newPwd: "",
                confirmPwd: "",
            },

            rules: {
                currentPhone: [
                    {
                        required: true,
                        message: "请输入手机号码",
                        trigger: "blur",
                    },
                    { validator: validateCurrentPhone, trigger: "blur" },
                ],
                verifyCode: [
                    {
                        required: true,
                        message: "请输入验证码",
                        trigger: "blur",
                    },
                    { validator: validateCode, trigger: "blur" },
                ],
                newPwd: [
                    {
                        required: true,
                        validator: validatePass,
                        trigger: "blur",
                    },
                ],
                confirmPwd: [
                    {
                        required: true,
                        validator: validatePass2,
                        trigger: "blur",
                    },
                ],
            },
            isCorrectPhone: false,
        };
    },
    computed: {
        themePickerColor() {
            return this.$store.state.system.themeColor;
        },
        userInfo() {
            if (this.$store.state.userInfo.name) {
                return this.$store.state.userInfo || {};
            }
            return this.$ls.get("userInfo");
        },
    },
    mounted() {
        const _this = this;

        this.ruleForm.currentPhone = this.userInfo.phone;
        this.codeNum = 180;

        this.timer = setInterval(() => {
            _this.time = _this.getnewDate();
        }, 1000);
        this.driver = new Driver({
            opacity: 0.6,
            closeBtnText: "关闭",
            prevBtnText: "上一步",
            nextBtnText: "下一步",
            // doneBtnText: 'GO',
            onHighlightStarted: (Element) => {
                // Element.node.style.color = '#212121';
                // if (Element.node.innerText == '收起') {
                //     Element.node.style.color = '#212121';
                // }
            },
            // 点击步骤触发
            onDeselected: (Element) => {
                console.log(Element);
                if (Element.node.innerText == "收起") {
                    // Element.node.style.color = '#fff';
                }
            },
        });
    },
    beforeDestroy() {
        if (this.timer) {
            clearInterval(this.timer);
        }
    },
    watch: {
        screen(bool) {
            bool ? this.exitScreen() : this.fullScreen();
        },
    },
    methods: {
        getnewDate() {
            const date = new Date(),
                Y = date.getFullYear(),
                M = date.getMonth() + 1,
                D = date.getDate(),
                h = date.getHours(),
                m = date.getMinutes(),
                s = date.getSeconds(),
                weeks = new Array(
                    "星期日",
                    "星期一",
                    "星期二",
                    "星期三",
                    "星期四",
                    "星期五",
                    "星期六"
                ),
                week = weeks[date.getDay()],
                v = h >= 12 ? "下午" : "上午",
                pad = (num) => {
                    return num < 10 ? `0${num}` : num;
                };

            return `${Y}年${M}月${D}日  ${v}  ${pad(h)}:${pad(m)}:${pad(
                s
            )}  ${week}`;
        },
        handleClose() {
            this.dialogFormVisible = false;
            this.$refs["ruleForm"].resetFields();
        },
        submitForm(formName) {
            const _that = this;
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    const { newPwd, confirmPwd, currentPhone, verifyCode } =
                        _that.ruleForm;
                    const userData = {
                        // 加密
                        paramEncipher: RSA.encrypt(
                            JSON.stringify({
                                phone: currentPhone,
                                verifyCode,
                                newPwd,
                                confirmPwd,
                            })
                        ),
                    };
                    updatePwd(userData).then((res) => {
                        if (parseInt(res.code) === 0) {
                            window.localStorage.clear();
                            this.$message({
                                showClose: true,
                                message: "密码修改成功",
                                type: "success",
                            });
                            this.$router.push("/login");
                        } else {
                            this.$message({
                                showClose: true,
                                message: res.message,
                                type: "warning",
                            });
                        }
                    });
                } else {
                    return false;
                }
            });
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
        },
        ...mapMutations(["changeThemeColor"]),
        changeTheme(color) {
            localStorage.setItem("COLOR_THEME", color);
            this.changeThemeColor(color);
        },
        logout() {
            console.log("Logout");
        },
        handleCommand(type) {
            switch (type * 1) {
                case 1:
                    this.dialogFormVisible = true;
                    break;
                // case 2:
                //     break;
                // case 3:
                //     break;
                // case 4:
                //     window.location.href =
                //         "tencent://message/?uin=309838300&Menu=yes";
                //     break;
                // case 5:
                //     this.driver.defineSteps(steps);
                //     this.driver.start();
                //     break;
                case 2:
                    this.$confirm("真的要注销登录吗 ?", "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                        customClass: "confirmation",
                    })
                        .then(() => {
                            this.$ls.remove(ACCESS_TOKEN);
                            this.$ls.remove(USER_ID);
                            localStorage.removeItem("isShowAnnounce");
                            location.reload();
                            // this.$router.push({
                            //     path: "/login",
                            // });
                        })
                        .catch(() => {
                            return false;
                        });
                    break;
                default:
                    break;
            }
        },
        // 发送验证码
        handleCode() {
            this.$refs.ruleForm.validate();
            let isSms = true;
            if (this.isCorrectPhone && isSms) {
                const params = {
                    phone: this.ruleForm.currentPhone,
                };
                isSms = false;
                smsMessage(params)
                    .then((res) => {
                        this.codeNum = 180;
                        clearInterval(this.time);
                        this.time = setInterval(() => {
                            if (this.codeNum <= 1) {
                                clearInterval(this.time);
                                this.codeNum = 180;
                            } else {
                                this.codeNum--;
                            }
                        }, 1000);
                    })
                    .finally(() => {
                        isSms = true;
                    });
            }
        },
    },
    components: { ThemePicker },
};
</script>

<style lang="scss">
.user-profile-container {
    cursor: pointer;

    .user-profile-content {
        display: flex;
    }

    .menu-icons {
        .menu-icon {
            padding: 0 8px;

            .icon {
                font-size: 18px;
            }

            .el-badge__content.is-fixed {
                top: 13px !important;
            }
        }
    }

    .user-profile-body {
        position: relative;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        text-align: center;
        height: 50px;
        margin-top: 5px;
    }

    .user-avatar {
        width: 30px;
        height: 30px;
        border-radius: 50px;
        margin-right: 5px;
    }

    .user-name {
        color: $text-color;
    }
}

.confirmation {
    margin-top: -500px;
}
</style>
