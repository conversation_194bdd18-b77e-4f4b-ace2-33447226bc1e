<template>
    <div>
        <el-container class="base-layout">
            <el-alert
                v-if="alertVisible"
                class="alert_class"
                type="warning"
                @close="closeAlert"
            >
                <template #title>
                    <div class="alert_title">
                        <span
                            id="content"
                            class="scrollable-content"
                            :style="
                                isScrollable
                                    ? {
                                          animation: `scroll-left ${number}s linear infinite`,
                                      }
                                    : {}
                            "
                        >
                            {{ longTitle }}: {{ longMessage }}
                        </span>
                    </div>
                </template>
            </el-alert>
            <el-header
                :style="alertVisible ? 'top: 34px' : 'top: 0'"
                class="base-header-container"
            >
                <base-header />
            </el-header>
            <el-container
                :style="
                    fixedHeader && alertVisible
                        ? 'margin-top: 94px'
                        : fixedHeader
                        ? 'margin-top: 60px;'
                        : ''
                "
                style="min-height: 90vh"
            >
                <!--  -->
                <el-aside
                    :width="
                        isCollapse && alertVisible
                            ? '98px'
                            : isCollapse
                            ? '64px'
                            : '254px'
                    "
                    class="base-aside"
                >
                    <sidebar-menu />
                    <a
                        class="collapse"
                        @click="$store.commit('changeCollapse')"
                    >
                        <i
                            :class="
                                isCollapse
                                    ? 'el-icon-d-arrow-right'
                                    : 'el-icon-d-arrow-left'
                            "
                        ></i>
                        <transition name="el-fade-in-linear">
                            <span class="collapse-text" v-show="!isCollapse"
                                >收起</span
                            >
                        </transition>
                    </a>
                </el-aside>
                <!--  -->
                <el-container
                    class="base-view-container"
                    :style="
                        isCollapse ? 'margin-left:64px' : 'margin-left:254px'
                    "
                >
                    <el-main class="base-view">
                        <!-- <div class="base-view-warp"> -->
                        <transition name="fade-transform" mode="out-in">
                            <router-view />
                        </transition>
                        <!-- </div> -->
                    </el-main>
                    <!-- <el-footer>Footer</el-footer> -->
                </el-container>
            </el-container>
        </el-container>
        <div class="announce_dialog">
            <el-dialog :visible.sync="dialogVisible" width="500px">
                <div class="announce_class">
                    <div class="bg"></div>
                    <div class="box">
                        <div class="head">
                            <div class="title">{{ longTitle }}</div>
                            <i class="el-icon-close" @click="closeAlert"></i>
                        </div>
                        <div class="content">
                            {{ longMessage }}
                        </div>
                    </div>
                </div>
            </el-dialog>
        </div>
    </div>
</template>
<script>
import BaseHeader from "./base-header/";
import RouterView from "./route-view";
import SidebarMenu from "./sidebar-menu/";
import store from "@/store";

export default {
    data() {
        return {
            alertVisible: false,
            dialogVisible: false,
            longMessage: "",
            longTitle: "",
            number: 0,
            isScrollable: false,
        };
    },
    components: {
        BaseHeader,
        SidebarMenu,
        RouterView,
    },
    computed: {
        fixedHeader() {
            return this.$store.state.system.fixedHeader;
        },
        isCollapse() {
            return this.$store.state.system.isCollapse;
        },
        announceData() {
            return this.$store.state.announceData;
        },
        isShowAnnounce() {
            return this.$store.state.isShowAnnounce;
        },
    },
    methods: {
        checkAndApplyScroll() {
            const contentEl = document.getElementById("content");
            this.number = (contentEl.offsetWidth / window.innerWidth) * 18;
            if (contentEl.offsetWidth > window.innerWidth) {
                this.isScrollable = true;
            }
        },
        closeAlert() {
            localStorage.setItem("announceId", this.announceData.id);
            localStorage.setItem("isShowAnnounce", false);
            this.dialogVisible = false;
            this.alertVisible = false;
        },
        announcefu(newValue) {
            this.alertVisible = newValue.formType == 1 ? true : false;
            this.dialogVisible = newValue.formType == 2 ? true : false;
            if (newValue.formType === 1) {
                this.$nextTick(() => {
                    this.checkAndApplyScroll();
                });
            }
        },
        // handleScroll() {
        //     const offsetTop = this.$el.getBoundingClientRect().top;
        //     if (offsetTop < -60) {
        //         console.log(offsetTop);
        //         return;
        //     }
        // }
    },

    watch: {
        // fixedHeader: {
        //     handler(v) {
        //         if (!v) {
        //             window.addEventListener('scroll', this.handleScroll);
        //         } else {
        //             window.removeEventListener('scroll', this.handleScroll);
        //         }
        //     },
        //     immediate: true
        // }
        announceData: {
            handler(newValue) {
                const announceId = localStorage.getItem("announceId");
                const isShowAnnounce = localStorage.getItem("isShowAnnounce");
                this.longMessage = newValue.announcementContent || "";
                this.longTitle = newValue.title || "";
                if (announceId == newValue.id) {
                    this.alertVisible = false;
                    this.dialogVisible = false;
                    if (isShowAnnounce == null) {
                        this.announcefu(newValue);
                    }
                } else {
                    this.announcefu(newValue);
                }
            },
            deep: true,
            immediate: true,
        },
    },
};
</script>

<style lang="scss">
.base-layout {
    height: 100%;
    .base-header-container {
        position: fixed;
        width: 100%;
        box-shadow: 2px 0 6px #0015290a;
        z-index: 9;
        background: #fff;
    }
    .alert_class {
        position: fixed;
        z-index: 9;
        display: flex;
        justify-content: center;
    }
    .base-aside {
        background: $menu-bg-color;
        transition: all 0.2s;
        z-index: 8;
        // color: #fff;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        position: fixed;
        height: calc(100% - 61px);
        .collapse {
            display: block;
            height: 50px;
            line-height: 50px;
            box-shadow: 2px 0 6px #0015290a;
            padding-left: 20px;
            cursor: pointer;
            i {
                margin-right: 5px;
                width: 24px;
                text-align: center;
                font-size: 18px;
                vertical-align: middle;
                transition: all 0.2s;
            }
            .collapse-text {
                vertical-align: middle;
                transition: all 0.2s;
            }
        }
    }
    .base-view-container {
        // background: $view-bg-color;
        background: url(./backgrnd.jpg);
        transition: all 0.2s;
        .base-view {
            overflow: hidden;
            position: relative;
            background: rgba(255, 255, 255, 0.77);
        }
        // .base-view-warp {
        //     position: relative;
        //     background: #fff;
        //     padding: 10px;
        // }
    }
}

//
@media screen and (max-width: 300px) {
    .base-aside {
        background-color: red;
    }
}

// .fade-transform-leave-active,
// .fade-transform-enter-active {
//     transition: all 0.5s;
// }

// .fade-transform-enter {
//     opacity: 0;
//     transform: translateX(-30px);
// }

// .fade-transform-leave-to {
//     opacity: 0;
//     transform: translateX(30px);
// }

.fade-transform-leave-active,
.fade-transform-enter-active {
    transition: all 0.3s;
}
.fade-transform-enter {
    opacity: 0;
    transform: translateX(-30px);
}
.fade-transform-leave-to {
    opacity: 0;
    transform: translateX(30px);
}

.fadeChild-transform-leave-active,
.fadeChild-transform-enter-active {
    transition: all 0.3s;
}
.fadeChild-transform-enter {
    opacity: 0;
    transform: translateX(-30px);
}
.fadeChild-transform-leave-to {
    opacity: 0;
    transform: translateX(30px);
}

.alert_title {
    overflow: hidden;
    max-width: 94vw;
    white-space: nowrap;
}

.scrollable-content {
    overflow-x: hidden; /* 隐藏默认滚动条 */
    white-space: nowrap;
    display: inline-block;
}

.auto-scroll {
    white-space: nowrap;
    /* 使用CSS动画实现滚动效果 */
    animation: scroll-left 0s linear infinite;
}

@keyframes scroll-left {
    0% {
        transform: translateX(0%);
    }
    100% {
        transform: translateX(-100%);
    }
}
.announce_class {
    height: 350px;
    width: 500px;
    background: url("../../assets/announceBg.png") no-repeat;
    background-size: contain;
    position: relative;
    .bg {
        position: absolute;
        right: 0px;
        top: 17px;
        background: url("../../assets/announceBg2.png") no-repeat;
        background-size: contain;
        width: 142px;
        height: 85px;
    }
    .box {
        position: absolute;
        left: 0px;
        top: 0px;
        padding: 16px 24px;
        width: calc(100% - 48px);
        .head {
            display: flex;
            justify-content: space-between;
            div {
                width: 100%;
                text-align: center;
                font-weight: 600;
                font-size: 16px;
                color: rgba(0, 0, 0, 0.85);
                line-height: 22px;
                white-space: nowrap; /* 确保文本不换行 */
                overflow: hidden; /* 超出容器部分隐藏 */
                text-overflow: ellipsis;
            }
            i {
                color: #fff;
                font-size: 18px;
                cursor: pointer;
            }
        }
        .content {
            margin-top: 10px;
            width: calc(100% - 40px);
            min-height: 240px;
            max-height: 240px;
            overflow: auto;
            background: #fff;
            padding: 14px 20px;
            font-weight: 400;
            font-size: 12px;
            color: #262626;
            line-height: 24px;
            box-shadow: inset 0px 1px 0px 0px rgba(255, 255, 255, 0.5);
            border-radius: 4px;
        }
    }
}
.announce_dialog {
    .el-dialog {
        .el-dialog__header {
            display: none !important;
        }
        .el-dialog__body {
            padding: 0px !important;
        }
    }
}
</style>
