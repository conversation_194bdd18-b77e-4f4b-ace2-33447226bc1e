<template>
	<div class="handle-header">
		<slot>
			<div class="left">
				<slot name="left" />
			</div>
			<div class="right">
				<slot name="right" />
			</div>
		</slot>
	</div>
</template>

<script>
export default {};
</script>
<style lang="scss" scoped>
	.handle-header {
		height: 49px;
		line-height: 49px;
		font-size: 16px;
		padding-left: 20px;
		border-bottom: 1px solid #dae1ed;
		display: flex;
		justify-content: space-between;
		.left {
		}
		.right {
			flex: 1;
			display: flex;
			align-items: center;
			justify-content: flex-end;
		}
	}
</style>