<!--
 * @Descripttion: 
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-06-11 15:17:58
 * @LastEditors: jingrou
 * @LastEditTime: 2022-06-15 10:57:30
-->
<template>
    <div class="sidebar-warp">
        <el-menu
            router
            :default-active="defaultActive"
            :collapse="isCollapse"
            :collapse-transition="false"
            class="sidebar"
            v-bind="$attrs"
            v-on="$listeners"
        >
            <sidebar-item
                :item="item"
                v-for="(item, index) in menuData"
                v-if="item.name != 'NotFound'"
                :key="index"
            />
        </el-menu>
    </div>
</template>

<script>
import sidebarItem from "./sidebarItem";
export default {
    components: {
        sidebarItem,
    },
    data() {
        return {
            defaultActive: "",
        };
    },
    watch: {
        $route: {
            handler(to) {
                this.defaultActive = to.path;
            },
            immediate: true,
        },
    },
    computed: {
        menuData() {
            return this.$store.state.routers;
        },

        isCollapse() {
            return this.$store.state.system.isCollapse;
        },
    },
    methods: {
        handleOpen(key, keyPath) {
            console.log(key, keyPath);
        },
        handleClose(key, keyPath) {
            console.log(key, keyPath);
        },
    },
};
</script>

<style lang="scss">
.sidebar-warp {
    padding: 16px 0;
    flex: 1;
    overflow-y: auto;
    .el-menu {
        border-right: none;
        .el-menu-item,
        .el-submenu__title {
            height: 46px;
            line-height: 46px;
        }
        .el-menu-item:hover {
            background: #c6e1fa !important ;
        }
        .is-active {
            background: #c6e1fa !important;
        }
    }
}
</style>
