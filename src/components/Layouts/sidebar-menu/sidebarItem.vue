<template>
    <el-submenu
        v-if="item.children && item.children.length"
        :index="item.path"
        :key="item.path"
    >
        <template slot="title">
            <i :class="['sider', item.meta.icon]"></i>
            <span>{{ item.meta.title }}</span>
        </template>
        <sidebar-item
            :item="j"
            v-for="(j, index) in item.children"
            :key="index"
        />
    </el-submenu>

    <el-menu-item
        v-else-if="item.meta && item.meta.title"
        :index="item.path"
        :key="'k' + item.path"
    >
        <i :class="['sider', item.meta.icon]"></i>
        <span>{{ item.meta && item.meta.title }}</span>
    </el-menu-item>
</template>
<script>
export default {
    name: "SidebarItem",
    props: {
        item: {
            type: Object,
            required: true,
        },
    },
};
</script>
<style scoped>
.sider {
    margin-right: 10px;
}
</style>
