<!--
 * @Descripttion: 
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-11-06 09:44:54
 * @LastEditors: jingrou
 * @LastEditTime: 2022-08-04 17:57:34
-->
<template>
    <div
        class="article_createOrEdit__warp"
        v-loading="loading"
        :element-loading-text="loadingText"
    >
        <el-card shadow="never">
            <div class="article_createOrEdit">
                <el-form
                    class="article_info"
                    ref="newForm"
                    :model="form"
                    :rules="rules"
                    label-width="110px"
                >
                    <el-form-item prop="title" label="标题：">
                        <el-input
                            v-model.trim="form.title"
                            placeholder="请输入标题"
                            show-word-limit
                            maxlength="35"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="描述：">
                        <el-input
                            type="textarea"
                            v-model.trim="form.newDescribe"
                            :rows="4"
                            placeholder="请输入描述"
                            show-word-limit
                            maxlength="78"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="关键字：">
                        <el-input
                            type="textarea"
                            :rows="4"
                            v-model.trim="form.keyword"
                            placeholder="请输入关键字"
                            show-word-limit
                            maxlength="50"
                        ></el-input>
                    </el-form-item>
                    <el-form-item
                        class="yd-uploader"
                        label="封面图："
                        style="height: 218px"
                        prop="icon"
                    >
                        <el-upload
                            action="/"
                            accept=".jpg, .jpeg, .png, .JPG, .JPEG, .mp4"
                            :on-change="uploadChange"
                            :before-remove="uploadRemove"
                            :file-list="fileList"
                            :multiple="false"
                            :auto-upload="false"
                            :show-file-list="false"
                            :on-preview="handlePictureCardPreview"
                            name="file"
                        >
                            <img
                                v-if="form.icon"
                                :src="form.icon"
                                class="avatar"
                                style="
                                    width: 150px;
                                    height: 149px;
                                    border: 1px solid #eee;
                                "
                            />
                            <i
                                style="
                                    width: 150px;
                                    height: 149px;
                                    border: 1px solid #eee;
                                    line-height: 149px;
                                "
                                v-else
                                class="el-icon-plus avatar-uploader-icon"
                            ></i>
                        </el-upload>

                        <!-- <p class="tips">
                            建议上传图片尺寸为640*640，大小不超过1MB视觉元素保持在方形区域内
                        </p> -->
                    </el-form-item>
                    <el-form-item label="分类：" prop="newTypeId">
                        <el-radio-group v-model="form.newTypeId">
                            <el-radio
                                :label="item.id"
                                v-for="(item, index) in newTypeList"
                                :key="`news_${index}`"
                                >{{ item.name }}</el-radio
                            >
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item prop="content" label="内容：">
                        <tinymce-editor
                            :plugins="plugins"
                            :toolbar="toolbar"
                            v-model="form.content"
                            height="500px"
                            folderType="officialWebsiteBanner"
                            class="tinymceEditor"
                        />
                    </el-form-item>
                    <el-form-item label="是否显示：">
                        <el-switch
                            v-model="form.status"
                            active-value="0"
                            inactive-value="1"
                        ></el-switch>
                    </el-form-item>
                    <el-form-item label="首页新闻：">
                        <el-switch
                            v-model="form.newIndex"
                            :active-value="0"
                            :inactive-value="1"
                        ></el-switch>
                    </el-form-item>
                    <el-form-item label="标为经典案例：">
                        <el-switch
                            v-model="form.isClassic"
                            :active-value="0"
                            :inactive-value="1"
                        ></el-switch>
                    </el-form-item>
                </el-form>
            </div>
        </el-card>
        <div shadow="never" class="article_createOrEdit__footer">
            <div class="btn_warp_bottom">
                <el-button @click="cancel">取消</el-button>
                <el-button
                    type="primary"
                    @click="submitForm('newForm')"
                    :loading="submitbtnLoading"
                    >提交</el-button
                >
            </div>
        </div>
    </div>
</template>

<script>
import TinymceEditor from "@/components/Tinymce-editor";
import { createNews, updateNews } from "@/api/guanwangManagement.js";
import { getUploadFileOSSpath } from "@/utils/oss";
import { ACCESS_TOKEN } from "@/store/mutation-types";
export default {
    props: {
        addNewForm: {
            type: Object,
            default: {},
        },
        newTypeList: {
            type: Array,
        },
        formTitle: {
            type: String,
        },
    },
    data() {
        return {
            action: process.env.VUE_APP_API_BASE_URL + "/manage/file/upload",

            filelist: [],
            plugins:
                "anchor charmap lineheight lists advlist image media table wordcount fullscreen autoresize print preview save hr code codesample link pagebreak insertdatetime searchreplace emoticons template noneditable help",
            toolbar: [
                "code lineheight undo redo copy cut bold italic forecolor backcolor underline strikethrough link alignleft aligncenter alignright alignjustify bullist numlist outdent indent blockquote subscript superscript removeformat formatselect fontselect fontsizeselect table image media anchor charmap emoticons template  hr codesample insertdatetime  pagebreak searchreplace print lists fullscreen preview save help",
            ],
            token: "",
            form: {
                title: "",
                content: "",
                newTypeId: "",
                newType: "",
                newDescribe: "",
                keyword: "",
                icon: "",
                id: "",
                newClass: "",
                status: "0",
                newIndex: 1,
                isClassic: 1,
            },
            rules: {
                title: [
                    {
                        required: true,
                        message: "请输入标题名称",
                        trigger: "blur",
                    },
                ],
                newTypeId: [
                    {
                        required: true,
                        message: "请选择分类",
                        trigger: "change",
                    },
                ],
                content: [
                    {
                        required: true,
                        message: "请输入内容",
                        trigger: "blur",
                    },
                ],
                icon: [
                    {
                        required: true,
                        message: "请上传封面图",
                    },
                ],
            },
            loading: false,
            loadingText: "正在获取数据...",
            submitbtnLoading: false,
            addFormTitle: "新增新闻",
        };
    },
    components: {
        TinymceEditor,
    },
    watch: {
        addNewForm(val) {
            this.form = val;
        },
        formTitle: {
            immediate: true,
            handler(newValue, oldValue) {
                this.addFormTitle = newValue;
                console.log(newValue);
            },
        },
    },
    methods: {
        uploadRemove(file) {
            file.url = "";
            this.form.icon = "";
        },
        handlePictureCardPreview(file) {
            this.form.icon = file.url;
        },
        uploadChange(file, fileList) {
            let uploadUrl = file.raw.type.substr(
                file.raw.type.lastIndexOf("/") + 1
            );
            let fileImg =
                ["jpg", "JPG", "png", "PNG", "jpeg", "JPEG"].indexOf(
                    uploadUrl.toLowerCase()
                ) !== -1;
            if (fileImg) {
                const isLt1M = file.size / 1024 / 1024 < 1;
                if (!isLt1M) {
                    this.$message.error("图片大于1M，请重新上传");
                    fileList = [];
                    this.fileList = [];
                    return false;
                } else {
                    this.uploadImg(file, fileList);
                }
            }
        },
        async uploadImg(file) {
            this.percentage = 0;
            const url = await getUploadFileOSSpath(
                file.raw,
                "officialWebsiteBanner",
                (num) => {
                    this.percentage = num;
                }
            );
            if (url) {
                this.form.icon = url;
                this.filelist = [{ url }];
            } else {
                this.$message.error("上传失败");
                this.form.icon = "";
            }
        },
        importError(res) {
            this.$message.error(res.message);
        },
        cancel() {
            this.$refs.newForm.resetFields();
            this.$emit("dialogOpen", false);
        },
        submitForm(formName) {
            console.log(this.addFormTitle, "888888888888");
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.submitbtnLoading = true;
                    if (this.addFormTitle === "新增新闻") {
                        createNews({ ...this.form })
                            .then((res) => {
                                this.$message.success(res.message);
                                this.cancel();
                            })
                            .finally(() => {
                                this.submitbtnLoading = false;
                            });
                    } else if (this.addFormTitle === "编辑新闻") {
                        updateNews({ ...this.form })
                            .then((res) => {
                                this.$message.success(res.message);
                                this.cancel();
                            })
                            .finally(() => {
                                this.submitbtnLoading = false;
                            });
                    }
                } else {
                    console.log(valid);
                    return false;
                }
            });
        },
    },
    created() {
        this.token = "Bearer " + this.$ls.get(ACCESS_TOKEN);
    },
};
</script>

<style lang="scss" scoped>
.article_createOrEdit__warp {
    height: calc(100vh - 144px);
}
.article_createOrEdit {
    position: relative;
    height: calc(100vh - 144px);

    .article_preview {
        display: flex;
        align-items: center;
        justify-content: center;
        .pc {
            position: relative;
            width: 600px;
            height: 456px;
            background-size: cover;
            background-position: center;
        }
        .pc_content {
            position: absolute;
            top: 14px;
            left: 17px;
            width: 566px;
            height: 346px;
        }
    }
    .article_info {
        background: #fff;
        height: calc(100vh - 144px);
        overflow-y: scroll;
        &::-webkit-scrollbar {
            display: none;
        }
    }
}
</style>
<style lang="scss">
// .tinymce {
// 	&:hover {
// 		transition: all 0.3s;
// 		box-shadow: 0 4px 5px rgba(75, 75, 75, 0.2);
// 	}
// }
// .tox-tinymce {
// 	border: none !important;
// }
.tox .tox-toolbar,
.tox .tox-toolbar__overflow,
.tox .tox-toolbar__primary {
    border: none !important;
    background: none !important;
}
.avatar-uploader-image .el-upload:hover {
    width: 260px !important;
    height: 200px !important;
    border-color: #409eff;
}
.avatar-uploader-image {
    // height: -1px !important;
    .el-upload {
        height: 100%;
        border: 1px dashed #dcdfe6;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }
}
.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 260px;
    height: 200px;
    line-height: 200px;
    text-align: center;
}
.avatar {
    // width: 260px;
    width: auto;
    height: 200px;
    display: block;
}
.article_createOrEdit__footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    // box-shadow: 0 -1px 2px rgb(0 0 0 / 3%);
    border-top: 1px solid #e8e8e8;
    .btn_warp_bottom {
        padding: 0px 24px;
        border-top: 1px solid #e8e8e8;

        justify-content: right;
    }
    z-index: 9;
    height: 60px;
    line-height: 60px;
    background: #fff;
    text-align: right;
}
.tinymceEditor {
    width: 1200px;
}
</style>
