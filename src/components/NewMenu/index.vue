<!--
 * @Descripttion: 
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-07-30 10:56:22
 * @LastEditors: jingrou
 * @LastEditTime: 2022-06-14 18:24:06
-->
<template>
    <ul class="application_menu">
        <li
            v-for="(item, index) in versionNavigationList"
            :key="item.id"
            class="application_bg"
            :class="navActive == index ? 'active' : ''"
            @click="childByValue(item, index)"
        >
            <div class="application_img">
                <img :src="item.icon" alt />
            </div>
            <span>{{ item.name }}</span>
        </li>
    </ul>
</template>

<script>
import { mapGetters } from "vuex";
// import menu from "@/utils/menu";
// import { applicationListApi } from "@/api/application.js";
export default {
    name: "NewMenu",
    props: {
        // required: true
        // applicationList: { type: Array, default: () => [] }
    },
    data() {
        return {
            navActive: "",
        };
    },
    computed: {
        ...mapGetters(["versionNavigationList"]),
    },
    watch: {},
    created() {},
    mounted() {},
    methods: {
        childByValue(item, idx) {
            this.$emit("childByValue", item);
            this.navActive = idx;
        },
        // async initFn() {
        // 	await applicationListApi()
        // 		.then(res => {
        // 			console.log(res.data, "导航");
        // 			if (res.code == "200") {
        // 				this.applicationList = res.data;
        // 				menu.$emit("text", this.applicationList[0]);
        // 				this.navActive = this.applicationList[0].name;
        // 			}
        // 		})
        // 		.catch();
        // }
    },
};
</script>

<style lang="scss" scoped>
.application_menu {
    width: 100%;
}
.application_bg {
    padding: 10px 0px;
    height: 60px;
    overflow: hidden;
    cursor: pointer;
    span {
        float: left;
        line-height: 40px;
        font-size: 16px;
        color: #333;
        margin-left: 10px;
    }
}
.application_bg:hover {
    background-color: #ebf0f6;
}
.active {
    background-color: #ebf0f6;
}
.application_img {
    width: 40px;
    height: 40px;
    margin-left: 43px;
    float: left;
    img {
        width: 100%;
        height: 100%;
    }
}
</style>
