<template>
    <div v-show="drawer" class="yd-new-modify-school">
        <el-drawer
            :title="isEdit ? '编辑学校' : '新增学校'"
            :visible.sync="drawer"
            :before-close="handleClose"
            :wrapper-closable="false"
        >
            <el-form
                ref="ruleFormChild"
                :model="ruleFormChild"
                :rules="rules"
                label-width="120px"
                class="demo-ruleForm"
            >
                <div class="yd-form-box">
                    <el-form-item label="学校名称：" prop="name">
                        <el-input
                            class="reset-input"
                            v-model="ruleFormChild.name"
                            size="medium"
                            show-word-limit
                            maxlength="50"
                            placeholder="请输入学校名称(长度不能超过50个字)"
                        />
                    </el-form-item>
                    <el-form-item label="学校类型：" prop="schoolType">
                        <el-select
                            :disabled="campusDisabled"
                            v-model="ruleFormChild.schoolType"
                            style="width: 100%"
                            size="medium"
                            placeholder="请选择类型"
                            @change="selectIfon($event)"
                        >
                            <el-option label="初中等教育" value="1" />
                            <el-option label="高等教育" value="2" />
                        </el-select>
                    </el-form-item>
                    <!--  -->
                    <el-form-item v-if="ruleFormChild.schoolType==1" label="学制类型：" prop="subSchoolType">                        
                        <el-select
                            :disabled="campusDisabled"
                            v-model="ruleFormChild.subSchoolType"
                            style="width: 100%"
                            size="medium"
                            placeholder="请选择"
                        >
                            <el-option label="六三制教育" :value="1" />
                            <el-option label="五四制教育" :value="2" />
                        </el-select>
                    </el-form-item>

                    <el-form-item
                        label="云平台到期日："
                        prop="cloudDeadlineTime"
                    >
                        <el-date-picker
                            style="width: 100%"
                            v-model="ruleFormChild.cloudDeadlineTime"
                            :picker-options="pickerOptions"
                            type="date"
                            :clearable="false"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                            placeholder="选择日期"
                        >
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="默认基础数据：">
                        <el-select
                            v-if="!campusDisabled"
                            :disabled="campusDisabled"
                            v-model="ruleFormChild.deptConfigId"
                            style="width: 100%"
                            size="medium"
                            placeholder="请选择"
                        >
                            <el-option
                                v-for="item in initDeptList"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                        <el-input
                            v-if="campusDisabled"
                            :disabled="true"
                            v-model="ruleFormChild.deptConfig"
                        />
                    </el-form-item>
                    <el-form-item
                        v-if="!campusDisabled"
                        label="默认权限模板："
                        prop="menuRoleConfigId"
                        :rules="{ required: true, message: '请选择!' }"
                    >
                        <el-select
                            :disabled="campusDisabled"
                            v-model="ruleFormChild.menuRoleConfigId"
                            style="width: 100%"
                            size="medium"
                            placeholder="请选择"
                        >
                            <el-option
                                v-for="item in initMenuList"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item v-if="campusDisabled" label="默认权限模板：">
                        <el-input
                            :disabled="true"
                            v-model="ruleFormChild.menuRoleConfig"
                        />
                    </el-form-item>
                    <el-form-item
                        label="组织结构："
                        :prop="!campusDisabled ? 'templateId' : ''"
                        v-if="ruleFormChild.schoolType"
                        v-loading="templateLoading"
                    >
                        <el-radio-group
                            :disabled="campusDisabled"
                            v-model="ruleFormChild.templateId"
                            style="width: 100%"
                            size="medium"
                            @change="campusPeriodChange"
                        >
                            <el-radio
                                v-for="item in templateList"
                                :key="item.idx"
                                class="sections"
                                :label="item.id"
                                name="type"
                            >
                                {{ item.name }}
                            </el-radio>
                        </el-radio-group>
                        <el-tree
                            :disabled="campusDisabled"
                            v-if="templateList.length"
                            :data="treeData"
                            :props="defaultProps"
                            @node-click="handleNodeClick"
                        />
                        <!--  暂时先隐藏一下这个 -->
                        <!-- <el-checkbox
                            :disabled="campusDisabled"
                            v-model="isShowCampus"
                            >显示校区</el-checkbox
                        > -->
                    </el-form-item>
                    <el-form-item
                        v-if="ruleFormChild.schoolType == 1"
                        label="学段："
                        prop="roleType"
                    >
                        <el-checkbox-group
                            v-model="ruleFormChild.roleType"
                            style="width: 100%"
                            size="medium"
                            @change="sectionsChange"
                            :disabled="campusDisabled"
                        >
                            <el-checkbox
                                v-for="(item, idx) in newSections"
                                :key="idx"
                                class="sections"
                                :label="item.id"
                                name="type"
                                >{{ item.name }}</el-checkbox
                            >
                        </el-checkbox-group>                                                
                    </el-form-item>
                    <div v-if="ruleFormChild.schoolType == 1 && ruleFormChild.subSchoolType==2" style="color: red;padding-bottom: 10px;">【提示】选择五四制教育时，勾选小学，初始化模板学段为1-5年级，勾选初中初始化模板学段为6-9年级</div>
                    <el-form-item label="省市区：" prop="region"> 
                        <el-cascader
                            v-model="ruleFormChild.region"
                            :props="{
                                checkStrictly: true,
                                value: 'name',
                                label: 'name',
                                children: 'area',
                            }"
                            style="width: 100%"
                            :options="areaList"
                        />
                        <!-- checkStrictly: true, -->
                        <!-- <el-cascader
                            v-model="ruleFormChild.region"
                            :props="{
                                value: 'label',
                                expandTrigger: 'hover',
                            }"
                            popper-class="cascaderClass"
                            ref="cascaderRegion"
                            style="width: 100%"
                            :options="options"
                            @change="regionChange"
                        /> -->
                    </el-form-item>

                    <el-form-item label="详细地址：" prop="address">
                        <el-input
                            v-model="ruleFormChild.address"
                            size="medium"
                            placeholder="请输入详细地址"
                        />
                    </el-form-item>
                    <el-form-item label="上级区域：" prop="organizationId">
                        <el-select
                            v-model="ruleFormChild.organizationId"
                            style="width: 100%"
                            clearable
                            size="medium"
                            placeholder="请选择"
                        >
                            <el-option
                                v-for="item in organizationList"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <!-- <el-form-item label="校区" prop="campus">
                        <el-button
                            :disabled="campusDisabled"
                            type="primary"
                            icon="el-icon-plus"
                            plain
                            @click="addCampus"
                            >添加校区</el-button
                        >
                        <div
                            class="campusClass"
                            v-for="(item, index) in ruleFormChild.campus"
                            :key="index"
                        >
                            <el-form-item
                                style="margin-bottom: 5px"
                                :label="`${'校区' + (index + 1) + '：'}`"
                                :prop="'campus.' + index + '.name'"
                                :rules="{
                                    required: true,
                                    message: '请输入校区名称',
                                    trigger: 'blur',
                                }"
                            >
                                <el-input
                                    :disabled="campusDisabled"
                                    v-model="item.name"
                                    placeholder="请输入校区名称"
                                />
                            </el-form-item>

                            <el-form-item
                                style="margin-bottom: 5px"
                                :prop="'campus.' + index + '.type'"
                                label="学段："
                                :rules="{
                                    required: true,
                                    message: '请选择校区学段',
                                    trigger: 'change',
                                }"
                            >
                                <el-checkbox-group
                                    v-model="item.type"
                                    style="width: 100%"
                                    size="medium"
                                    @change="campusPeriodChange"
                                >
                                    <el-checkbox
                                        :disabled="campusDisabled"
                                        v-for="(item, idx) in newSections"
                                        :key="idx"
                                        class="sections"
                                        :label="item.id"
                                        name="type"
                                        >{{ item.name }}</el-checkbox
                                    >
                                </el-checkbox-group>
                            </el-form-item>

                            <el-form-item
                                style="margin-bottom: 5px"
                                :prop="'campus.' + index + '.region'"
                                label="区域："
                                :rules="{
                                    required: true,
                                    message: '请选择校区区域',
                                    trigger: 'change',
                                }"
                            >
                                <el-cascader
                                    :disabled="campusDisabled"
                                    v-model="item.region"
                                    :props="{
                                        value: 'label',
                                        expandTrigger: 'hover',
                                    }"
                                    :ref="'campus.' + index + '.region'"
                                    popper-class="cascaderClass"
                                    style="width: 100%"
                                    :options="options"
                                    @change="cascaderChange(index)"
                                />
                            </el-form-item>
                            <i
                                v-if="campusDisabled === false"
                                @click="removeCampus(index, item)"
                                class="el-icon-remove removeCampus"
                            ></i>
                        </div>
                    </el-form-item> -->
                    <el-form-item label="联系人：" prop="contact">
                        <el-input
                            v-model="ruleFormChild.contact"
                            size="medium"
                            placeholder="请输入联系人"
                        />
                    </el-form-item>

                    <el-form-item label="手机号码：" prop="contactPhone">
                        <el-input
                            :disabled="campusDisabled"
                            v-model.number="ruleFormChild.contactPhone"
                            size="medium"
                            maxlength="11"
                            placeholder="请输入手机号码"
                        />
                        <p class="tips">
                            该手机号码为学校智慧云平台超级管理员账号，登录密码为号码后6位
                        </p>
                    </el-form-item>
                    <el-form-item
                        class="yd-uploader"
                        label="学校logo："
                        style="height: 218px"
                        prop="badgeUrl"
                    >
                        <el-upload
                            action="/"
                            accept=".jpg, .jpeg, .png, .JPG, .JPEG, .mp4"
                            :on-change="uploadChange"
                            :before-remove="uploadRemove"
                            :multiple="false"
                            :auto-upload="false"
                            :show-file-list="false"
                            :on-preview="handlePictureCardPreview"
                            name="file"
                            :file-list="ruleFormChild.filelist"
                        >
                            <img
                                v-if="ruleFormChild.badgeUrl"
                                :src="ruleFormChild.badgeUrl"
                                class="avatar"
                                style="
                                    width: 150px;
                                    height: 149px;
                                    border: 1px solid;
                                "
                            />
                            <i
                                style="
                                    width: 150px;
                                    height: 149px;
                                    border: 1px solid #eee;
                                "
                                v-else
                                class="el-icon-plus avatar-uploader-icon"
                            ></i>
                        </el-upload>

                        <p class="tips">
                            建议上传图片尺寸为640*640，大小不超过1MB视觉元素保持在方形区域内
                        </p>
                    </el-form-item>

                    <!-- <el-form-item
                        label="超级账号："
                        prop="superAccount"
                        v-if="campusDisabled"
                    >
                        <el-input
                            :disabled="campusDisabled"
                            v-model.number="ruleFormChild.superAccount"
                            size="medium"
                            maxlength="11"
                            placeholder="暂无数据"
                        />
                    </el-form-item>
                    <el-form-item
                        class="yd-uploader"
                        label="学校logo："
                        style="height: 218px"
                        prop="badgeUrl"
                    >
                        <el-upload
                            :headers="{
                                Authorization: token,
                                platform: 'system',
                            }" :action="action"
                            :show-file-list="false" :file-list="ruleFormChild.filelist" :on-success="importList"
                            :on-error="importError" name="file" :multiple="false">
                            <img v-if="ruleFormChild.badgeUrl" :src="ruleFormChild.badgeUrl" class="avatar" style="
                                    width: 150px;
                                    height: 149px;
                                    border: 1px solid #eee;
                                " />
                            <i style="
                                    width: 150px;
                                    height: 149px;
                                    border: 1px solid #eee;
                                " v-else class="el-icon-plus avatar-uploader-icon"></i>
                        </el-upload>

                        <p class="tips">
                            建议上传图片尺寸为640*640，大小不超过1MB视觉元素保持在方形区域内
                        </p>
                    </el-form-item> -->
                    <el-form-item label="销售负责人：" prop="headSales">
                        <el-input
                            v-model="ruleFormChild.headSales"
                            size="medium"
                            placeholder="请输入销售负责人"
                        />
                    </el-form-item>
                    <el-form-item label="客户负责人：" prop="headClient">
                        <el-input
                            v-model="ruleFormChild.headClient"
                            size="medium"
                            placeholder="请输入客户负责人"
                        />
                    </el-form-item>
                    <el-form-item label="业务类型：" prop="serviceType">
                        <el-select
                            v-model="ruleFormChild.serviceType"
                            style="width: 100%"
                            placeholder="请选择"
                        >
                            <el-option label="演示版" :value="1"></el-option>
                            <el-option label="商业版" :value="2"></el-option>
                        </el-select>
                    </el-form-item>
                    <!-- 应测试提出暂未使用，怕引起误解故注释2024/9/24 -->
                    <!-- <el-form-item label="天气配置：" prop="cityCode">
                        <el-cascader
                            v-model="ruleFormChild.cityCode"
                            :props="{
                                emitPath: false,
                                checkStrictly: false,
                                value: 'adcode',
                                label: 'name',
                                children: 'city',
                            }"
                            style="width: 100%"
                            :options="weatherList"
                        />
                    </el-form-item> -->
                </div>
            </el-form>
            <div class="yd-form-footer">
                <div class="footerForm">
                    <el-button
                        class="reset"
                        @click="cancelForm('ruleFormChild')"
                        >取消</el-button
                    >
                    <el-button
                        type="primary"
                        :loading="loading"
                        @click="submitForm('ruleFormChild')"
                        >确 定</el-button
                    >
                </div>
            </div>
        </el-drawer>
    </div>
</template>

<script>
import { mapGetters } from "vuex";
import { getUploadFileOSSpath } from "@/utils/oss";
import { ACCESS_TOKEN } from "@/store/mutation-types";
import {
    addSchoolAppList,
    editSchoolAppList,
    getTemplateList,
    getTemplateRollList,
    getSystemTemplateComment,
    getWeatherArea,
    getAreaList,
} from "@/api/administrator.js";
import { getOrganizationList } from "@/api/regionSystem.js";

const dayjs = require("dayjs");
export default {
    name: "NewModifSchool",
    props: {
        drawerTitle: {
            type: Boolean,
            default: false,
        },
        drawer: {
            type: Boolean,
            default: false,
        },
        schoolRuleForm: {
            type: Object,
        },
    },
    data() {
        const validateTelephone = (rule, value, callback) => {
            if (!/^1[3-9]\d{9}$/.test(value)) {
                return callback(new Error("请输入正确的手机号！"));
            }
            callback();
        };
        return {
            organizationList: [],
            weatherList: [],
            initDeptList: [],
            initMenuList: [],
            isEdit: false,
            templateLoading: false,
            // sectionsList: [{ id: 0, name: '幼儿园' }, { id: 1, name: '小学' }, { id: 2, name: '初中' }, { id: 3, name: '高中' }],
            campusDisabled: false,
            token: "",
            treeData: [],
            templateList: [],
            isShowCampus: false,
            defaultProps: {
                children: "children",
                label: "label",
            },
            ruleFormChild: {
                cityCode: null,
                name: "",
                schoolType: "1",
                subSchoolType:"",
                deptConfigId: "",
                menuRoleConfigId: "",
                templateId: "",
                roleType: [],
                area: "",
                address: "",
                contact: "",
                region: "",
                campus: [],
                contactPhone: "",
                badgeUrl: "",
                headSales: "",
                headClient: "",
                serviceType: 1,
                filelist: [],
                cloudDeadlineTime: dayjs().add(3, "year").format("YYYY-MM-DD"),
            },
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() < Date.now();
                },
            },
            checkList: [],
            loading: false,
            imageUrl: "",
            areaList: [],
            rules: {
                name: [
                    {
                        required: true,
                        message: "请输入学校名称",
                        trigger: "blur",
                    },
                    {
                        min: 1,
                        max: 50,
                        message: "学校名称长度在 50 个字符内",
                        trigger: "blur",
                    },
                ],
                schoolType: [
                    {
                        required: true,
                        message: "请选择类型",
                        trigger: "change",
                    },
                ],
                subSchoolType:[
                    {
                        required: true,
                        message: "请选择学制类型",
                        trigger: "change",
                    },
                ],
                menuRoleConfigId: [
                    {
                        required: true,
                        message: "请选择",
                        trigger: "change",
                    },
                ],
                region: [
                    {
                        required: true,
                        message: "请选择组织结构",
                        trigger: "blur",
                    },
                ],
                roleType: [
                    {
                        required: true,
                        message: "请选择学段",
                        trigger: "blur",
                    },
                ],
                templateId: [
                    {
                        required: true,
                        message: "请选择组织结构",
                        trigger: "blur",
                    },
                ],
                contact: [
                    {
                        required: true,
                        message: "请输入联系人",
                        trigger: "blur",
                    },
                ],
                contactPhone: [
                    {
                        required: true,
                        validator: validateTelephone,
                        message: "请输入正确的手机号码",
                        trigger: "blur",
                    },
                ],
                headSales: [{ message: "请输入销售负责人", trigger: "blur" }],
                headClient: [{ message: "请输入客户负责人", trigger: "blur" }],
            },
            iconformData: {},
            action: process.env.VUE_APP_API_BASE_URL + "/manage/file/upload",
            newSections: [
                {
                    name: "学前教育",
                    id: 0,
                    disabled: false,
                },
                {
                    name: "小学",
                    id: 1,
                    disabled: false,
                },
                {
                    name: "初中",
                    id: 2,
                    disabled: false,
                },
                {
                    name: "高中",
                    id: 3,
                    disabled: false,
                },
            ],
        };
    },
    computed: {
        ...mapGetters(["showHeight"]),
    },
    created() {
        this.token = "Bearer " + this.$ls.get(ACCESS_TOKEN);
        this.getTemplate();
        this.getTemplateCommentList();
        this.reqWeatherArea();
    },
    mounted() {},
    methods: {
        processArray(arr) {
            const arr1 = arr.map((item) => {
                return {
                    adcode: item.adcode,
                    name: item.name,
                    city: item.city.map((cityItem) => {
                        return {
                            adcode: cityItem.adcode,
                            name: cityItem.name,
                        };
                    }),
                };
            });
            return arr1;
        },
        reqWeatherArea() {
            getWeatherArea().then((res) => {
                this.weatherList = this.processArray(res.data);
            });
        },
        handleNodeClick(data) {
            console.log(data);
            // cascaderChange(index) {
            //     this.$refs[
            //         "campus." + index + ".region"
            //     ][0]._data.dropDownVisible = false;
            // },
            // regionChange() {
            //     this.$refs.cascaderRegion._data.dropDownVisible = false;
            // },
        },
        sectionsChange(res) {
            this.newSections.map((item) => {
                return res.map((id) => {
                    if (item.id === id) {
                        item.disabled = true;
                    }
                    return [];
                });
            });
        },
        campusPeriodChange(templateId) {
            this.ruleFormChild.templateId = templateId;
            getTemplateRollList({ templateId }).then(({ data }) => {
                const filteredArr = data.filter(
                    (item) => item.key !== "campus"
                );
                this.treeData = filteredArr;
            });
        },
        async uploadImg(file) {
            this.percentage = 0;
            const url = await getUploadFileOSSpath(
                file.raw,
                "schoolLogo",
                (num) => {
                    this.percentage = num;
                }
            );
            if (url) {
                this.ruleFormChild.badgeUrl = url;
                this.ruleFormChild.filelist = [{ url }];
            } else {
                this.$message.error("上传失败");
                this.ruleFormChild.badgeUrl = "";
            }
        },
        uploadChange(file, fileList) {
            let uploadUrl = file.raw.type.substr(
                file.raw.type.lastIndexOf("/") + 1
            );
            let fileImg =
                ["jpg", "JPG", "png", "PNG", "jpeg", "JPEG"].indexOf(
                    uploadUrl.toLowerCase()
                ) !== -1;
            if (fileImg) {
                const isLt1M = file.size / 1024 / 1024 < 1;
                if (!isLt1M) {
                    this.$message.error("图片大于1M，请重新上传");
                    fileList = [];
                    this.fileList = [];
                    return false;
                } else {
                    this.uploadImg(file, fileList);
                }
            }
        },
        selectIfon(e) {
            this.ruleFormChild.deptConfigId = null;
            this.ruleFormChild.menuRoleConfigId = null;
            this.getTemplate();
            this.getTemplateCommentList();
            if (e !== 1) {
                this.ruleFormChild.roleType = [];
            }
        },
        // 获取模板列表
        getTemplateCommentList() {
            getSystemTemplateComment({
                type: this.ruleFormChild.schoolType,
                status: 0,
            }).then((res) => {
                console.log("res", res);
                this.initDeptList = res.data.filter(
                    (item) => item.commentKey === "INIT_DEPT"
                );
                this.initMenuList = res.data.filter(
                    (item) => item.commentKey === "INIT_DEFAULT_MENU"
                );
            });
        },
        getTemplate() {
            this.templateLoading = true;
            getTemplateList({ type: this.ruleFormChild.schoolType })
                .then(({ data }) => {
                    this.templateList = data;
                    if (data && data.length > 0) {
                        const { id } = data[0];
                        this.ruleFormChild.templateId = id;
                        this.campusPeriodChange(id);
                    } else {
                        this.ruleFormChild.templateId = "";
                    }
                })
                .finally(() => {
                    this.templateLoading = false;
                });
        },
        area() {
            if (Array.isArray(this.ruleFormChild.region)) {
                this.ruleFormChild.area = this.ruleFormChild.region.join("/");
            } else {
                this.ruleFormChild.region = [];
            }
            if (this.ruleFormChild.campus.length > 0) {
                this.ruleFormChild.campus.map((item) => {
                    if (Array.isArray(item.region)) {
                        item.area = item.region.join("/");
                    } else {
                        item.area = [];
                    }
                });
            }
        },
        handlePictureCardPreview(file) {
            this.ruleFormChild.badgeUrl = file.url;
        },
        uploadRemove(file) {
            file.url = "";
            this.ruleFormChild.badgeUrl = "";
        },
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.loading = true;
                    if (!this.isEdit) {
                        this.area();
                        addSchoolAppList({
                            ...this.ruleFormChild,
                            isShowCampus: this.isShowCampus,
                        })
                            .then((res) => {
                                this.$message.success(res.message);
                                this.handleClose();
                            })
                            .finally(() => {
                                this.loading = false;
                            });
                    } else if (this.isEdit) {
                        this.area();
                        editSchoolAppList({
                            ...this.ruleFormChild,
                            isShowCampus: this.isShowCampus,
                        })
                            .then((res) => {
                                this.$message.success(res.message);
                                this.handleClose();
                            })
                            .finally(() => {
                                this.loading = false;
                            });
                    }
                } else {
                    return false;
                }
            });
        },
        resetForm(formName) {
            try {
                this.$refs[formName].resetFields();
            } catch (e) {
                console.log(e);
            }
        },
        async handleClose() {
            this.ruleFormChild.schoolType = "1";
            this.isShowCampus = false;
            this.ruleFormChild.templateId = null;
            this.templateList = [];
            this.treeData = [];
            await this.getTemplate();
            this.resetForm("ruleFormChild");
            this.$emit("handleClose", false);
        },
        async cancelForm(formName) {
            this.ruleFormChild.schoolType = "1";
            this.isShowCampus = false;
            this.ruleFormChild.templateId = null;
            this.templateList = [];
            this.treeData = [];
            await this.getTemplate();
            this.resetForm(formName);
            this.$emit("handleClose", false);
        },
        removeEmptyChildren(data) {
            data.forEach((item) => {
                if (item.area && item.area.length === 0) {
                    delete item.area;
                } else if (item.area) {
                    this.removeEmptyChildren(item.area);
                }
            });
            return data;
        },

        getAreaListFn() {
            getAreaList().then((res) => {
                this.areaList = this.removeEmptyChildren(res.data);
            });
        },
        getOrganizationFn() {
            getOrganizationList().then((res) => {
                this.organizationList = res.data;
            });
        },
    },
    watch: {
        async schoolRuleForm(val) {
            if (val) {
                this.ruleFormChild = val;
                this.ruleFormChild.cityCode = val.cityCode
                    ? val.cityCode
                    : null;
                this.ruleFormChild.region = val.area ? val.area.split("/") : "";
                if (val.campus.length !== 0) {
                    val.campus.map((item) => {
                        item.region = item.area ? item.area.split("/") : "";
                    });
                }
                await this.getTemplate();
                this.isShowCampus = val.isShowCampus;
            }
        },
        async drawerTitle(val) {
            this.isEdit = val;            
            if (val) {
                this.campusDisabled = true;
                this.isShowCampus = false;                
            } else {
                await this.getTemplate();
                this.campusDisabled = false;
                // this.ruleFormChild.roleType = [];
            }
        },
        drawer(val) {            
            this.getAreaListFn();
            this.getTemplateCommentList();
            this.getOrganizationFn();
        },
    },
};
</script>

<style lang="scss" scoped>
.campusClass {
    width: 95%;
    height: 228px;
    background: #eee;
    margin-top: 10px;
    padding: 20px 20px 20px 0px;
    // overflow-y: auto;
    position: relative;
}

.removeCampus {
    position: absolute;
    right: -25px;
    top: 10px;
    color: red;
    font-size: 20px;
    cursor: pointer;
}

.yd-new-modify-school {
    .yd-form-box {
        outline: none;
        padding: 20px 50px 0;
        margin-bottom: 76px;
        overflow-y: auto;
        border-top: 1px solid #ebeef5;
        .reset-input {
            ::v-deep .el-input__inner {
                padding-right: 45px !important;
            }
        }
    }

    .tips {
        margin-top: 10px;
        color: #c4c7cf;
        line-height: initial;
    }

    .el-drawer__header {
        border-bottom: #dcdfe6;
    }

    ::v-deep :focus {
        outline: 0;
    }

    ::v-deep .yd-uploader .el-form-item__content {
        display: flex;
        display: flex;
        flex-direction: column;
    }

    ::v-deep .avatar-uploader {
        width: 150px;
        height: 150px;
        margin-right: 20px;

        .el-upload {
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            min-height: 150px;
        }

        .el-upload:hover {
            border-color: #409eff;
        }

        .avatar {
            height: 100%;
            width: 100%;
            display: block;
        }
    }

    .avatar-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 150px;
        height: 149px;
        line-height: 138px;
        text-align: center;
    }

    .yd-form-footer {
        z-index: 2;
        text-align: center;
        height: 64px;
        position: absolute;
        bottom: 0px;
        background: #fff;
        width: 100%;

        .footerForm {
            display: flex;
            width: 100%;
            justify-content: center;
            align-items: center;
            height: 64px;
            border: 1px solid #eee;
        }
    }
}
</style>
<style lang="scss">
.el-drawer__container ::-webkit-scrollbar {
    display: none;
}

.campusClass {
    .el-form-item__label {
        width: 88px !important;
    }
}

.yd-new-modify-school {
    .el-drawer {
        width: 554px !important;
    }
}

.el-tree-node {
    // background: #eeeeee94 !important;
}
</style>
