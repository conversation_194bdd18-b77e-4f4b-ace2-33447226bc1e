<template>
    <div v-show="drawer" class="yd-new-modify-school">
        <el-drawer :title="drawerTitle" :visible.sync="drawer" :before-close="handleClose" :wrapper-closable="false">
            <el-form ref="ruleFormChild" :model="ruleFormChild" :rules="rules" label-width="100px"
                class="demo-ruleForm">
                <div class="yd-form-box">
                    <el-form-item label="学校名称：" prop="name">
                        <el-input v-model="ruleFormChild.name" size="medium" maxlength="20"
                            placeholder="请输入学校名称(长度不能超过20个字)" />
                    </el-form-item>
                    <el-form-item label="学校类型：" prop="schoolType">
                        <el-select :disabled="campusDisabled" v-model="ruleFormChild.schoolType" style="width: 100%"
                            size="medium" placeholder="请选择类型" @change="selectIfon($event)">
                            <el-option label="初中等教育" value="1" />
                            <!-- <el-option label="大学" :value="2" /> -->
                        </el-select>
                    </el-form-item>
                    <el-form-item v-if="periodState || ruleFormChild.schoolType == 1" label="学段：" prop="roleType">
                        <el-checkbox-group v-model="ruleFormChild.roleType" style="width: 100%" size="medium"
                            @change="sectionsChange" :disabled="campusDisabled">
                            <el-checkbox v-for="(item, idx) in sections" :key="idx" class="sections" :label="item.id"
                                name="type">{{ item.name }}</el-checkbox>
                        </el-checkbox-group>
                    </el-form-item>
                    <el-form-item label="区域：" prop="region">
                        <el-cascader v-model="ruleFormChild.region" :props="{ checkStrictly: true, value: 'label' }"
                            style="width: 100%" :options="options" />
                    </el-form-item>

                    <el-form-item label="详细地址：" prop="address">
                        <el-input v-model="ruleFormChild.address" size="medium" placeholder="请输入详细地址" />
                    </el-form-item>

                    <el-form-item label="校区" prop="campus">
                        <el-button :disabled="campusDisabled" type="primary" icon="el-icon-plus" plain
                            @click="addCampus">添加校区</el-button>
                        <div class="campusClass" v-for="(item, index) in ruleFormChild.campus" :key="index">
                            <el-form-item style="margin-bottom: 5px" :label="`${'校区' + (index + 1) + '：'}`"
                                :prop="'campus.' + index + '.name'" :rules="{
                                    required: true,
                                    message: '请输入校区名称',
                                    trigger: 'blur',
                                }">
                                <el-input :disabled="campusDisabled" v-model="item.name" placeholder="请输入校区名称" />
                            </el-form-item>

                            <el-form-item style="margin-bottom: 5px" :prop="'campus.' + index + '.type'" label="学段："
                                :rules="{
                                    required: true,
                                    message: '请选择校区学段',
                                    trigger: 'change',
                                }">
                                <el-checkbox-group v-model="item.type" style="width: 100%" size="medium"
                                    @change="campusPeriodChange">
                                    <!-- :disabled="item.disabled" -->
                                    <el-checkbox :disabled="campusDisabled" v-for="(item, idx) in newSections"
                                        :key="idx" class="sections" :label="item.id" name="type">{{
                                            item.name
                                        }}</el-checkbox>
                                </el-checkbox-group>
                            </el-form-item>

                            <el-form-item style="margin-bottom: 5px" :prop="'campus.' + index + '.region'" label="区域："
                                :rules="{
                                    required: true,
                                    message: '请选择校区区域',
                                    trigger: 'change',
                                }">
                                <el-cascader :disabled="campusDisabled" v-model="item.region" :props="{
                                    checkStrictly: true,
                                    value: 'label',
                                }" style="width: 100%" :options="options" />
                            </el-form-item>
                            <i v-if="campusDisabled === false" @click="removeCampus(index, item)"
                                class="el-icon-remove removeCampus"></i>
                        </div>
                    </el-form-item>
                    <el-form-item label="联系人：" prop="contact">
                        <el-input v-model="ruleFormChild.contact" size="medium" placeholder="请输入联系人" />
                    </el-form-item>

                    <el-form-item label="手机号码：" prop="contactPhone">
                        <el-input :disabled="campusDisabled" v-model.number="ruleFormChild.contactPhone" size="medium"
                            maxlength="11" placeholder="请输入手机号码" />
                        <p class="tips">
                            该手机号码为学校智慧云平台超级管理员账号，登录密码为号码后6位
                        </p>
                    </el-form-item>
                    <el-form-item class="yd-uploader" label="学校logo：" style="height: 218px" prop="badgeUrl">
                        <el-upload :headers="{
                            Authorization: token,
                            platform: 'system',
                        }" :action="action" :show-file-list="false" :file-list="ruleFormChild.filelist"
                            :on-success="importList" :on-error="importError" name="file" :multiple="false">
                            <img v-if="ruleFormChild.badgeUrl" :src="ruleFormChild.badgeUrl" class="avatar" style="
                                    width: 150px;
                                    height: 149px;
                                    border: 1px solid #eee;
                                " />
                            <i style="
                                    width: 150px;
                                    height: 149px;
                                    border: 1px solid #eee;
                                " v-else class="el-icon-plus avatar-uploader-icon"></i>
                        </el-upload>

                        <p class="tips">
                            建议上传图片尺寸为640*640，大小不超过1MB视觉元素保持在方形区域内
                        </p>
                    </el-form-item>
                    <el-form-item label="销售负责人：" prop="headSales">
                        <el-input v-model="ruleFormChild.headSales" size="medium" placeholder="请输入销售负责人" />
                    </el-form-item>
                    <el-form-item label="客户负责人：" prop="headClient">
                        <el-input v-model="ruleFormChild.headClient" size="medium" placeholder="请输入客户负责人" />
                    </el-form-item>
                    <el-form-item label="业务类型：" prop="serviceType">
                        <el-select v-model="ruleFormChild.serviceType" placeholder="请选择">
                            <el-option label="演示版" :value="1"></el-option>
                            <el-option label="商业版" :value="2"></el-option>
                        </el-select>
                    </el-form-item>
                </div>
            </el-form>
            <div class="yd-form-footer">
                <div class="footerForm">
                    <el-button class="reset" @click="cancelForm('ruleFormChild')">取消</el-button>
                    <el-button type="primary" :loading="loading" @click="submitForm('ruleFormChild')">确 定</el-button>
                </div>
            </div>
        </el-drawer>
    </div>
</template>

<script>
import { mapGetters } from "vuex";
import { regionData } from "element-china-area-data";
import { ACCESS_TOKEN } from "@/store/mutation-types";
import { addSchoolAppList, editSchoolAppList } from "@/api/administrator.js";
export default {
    name: "NewModifSchool",
    props: {
        drawerTitle: {
            type: String,
            default: "",
        },
        drawer: {
            type: Boolean,
            default: false,
        },
        schoolRuleForm: {
            type: Object,
        },
    },
    data() {
        const validateTelephone = (rule, value, callback) => {
            if (!/^1[3-9]\d{9}$/.test(value)) {
                return callback(new Error("请输入正确的手机号！"));
            }
            callback();
        };
        return {
            campusDisabled: false,
            formTitle: "新增学校",
            token: "",
            sections: [
                {
                    name: "学前教育",
                    id: 0,
                    disabled: false,
                },
                {
                    name: "小学",
                    id: 1,
                    disabled: false,
                },
                {
                    name: "初中",
                    id: 2,
                    disabled: false,
                },
                {
                    name: "高中",
                    id: 3,
                    disabled: false,
                },
            ],
            ruleFormChild: {
                name: "",
                schoolType: "",
                roleType: [],
                area: "",
                address: "",
                contact: "",
                region: [],
                campus: [
                    // {
                    // 	name: "",
                    // 	type: [],
                    // 	region: [],
                    //  area: ''
                    // },
                ],
                contactPhone: "",
                badgeUrl: "",
                headSales: "",
                headClient: "",
                serviceType: 1,
                filelist: [],
            },
            checkList: [],
            // 省级联动
            options: regionData,
            loading: false,
            imageUrl: "",
            periodState: false,
            rules: {
                name: [
                    {
                        required: true,
                        message: "请输入学校名称",
                        trigger: "blur",
                    },
                    {
                        min: 1,
                        max: 20,
                        message: "学校名称长度在 20 个字符内",
                        trigger: "blur",
                    },
                ],
                schoolType: [
                    {
                        required: true,
                        message: "请选择类型",
                        trigger: "change",
                    },
                ],
                roleType: [
                    {
                        type: "array",
                        required: true,
                        message: "请至少选择一个学段",
                        trigger: "change",
                    },
                ],
                // campus: [
                //     {
                //         type: "array",
                //         required: true,
                //         message: "请把校区填写完整",
                //         trigger: "change",
                //     },
                // ],
                region: [
                    {
                        required: true,
                        message: "请选择区域",
                        trigger: "change",
                    },
                ],
                // address: [
                //     {
                //         required: true,
                //         message: "请输入详细地址",
                //         trigger: "blur",
                //     },
                // ],
                contact: [
                    {
                        required: true,
                        message: "请输入联系人",
                        trigger: "blur",
                    },
                ],
                contactPhone: [
                    {
                        required: true,
                        validator: validateTelephone,
                        message: "请输入正确的手机号码",
                        trigger: "blur",
                    },
                ],
                // badgeUrl: [
                //     {
                //         required: true,
                //         message: "请上传学校logo",
                //         trigger: "click",
                //     },
                // ],
                headSales: [{ message: "请输入销售负责人", trigger: "blur" }],
                headClient: [{ message: "请输入客户负责人", trigger: "blur" }],
            },
            iconformData: {},
            action: process.env.VUE_APP_API_BASE_URL + "/cloud-web/file/upload",
            newSections: [
                {
                    name: "学前教育",
                    id: 0,
                    disabled: false,
                },
                {
                    name: "小学",
                    id: 1,
                    disabled: false,
                },
                {
                    name: "初中",
                    id: 2,
                    disabled: false,
                },
                {
                    name: "高中",
                    id: 3,
                    disabled: false,
                },
            ],
        };
    },
    computed: {
        ...mapGetters(["showHeight"]),
    },
    created() {
        this.token = "Bearer " + this.$ls.get(ACCESS_TOKEN);
    },
    mounted() { },
    methods: {
        sectionsChange(res) {
            // this.newSections.map((item) => {
            // 	return res.map((id) => {
            // 		if (item.id === id) {
            // 			item.disabled = true;
            // 		}
            // 		return [];
            // 	});
            // });
        },
        campusPeriodChange(res) {
            // this.sections.map((item) => {
            // 	return res.map((id) => {
            // 		if (item.id === id) {
            // 			item.disabled = true;
            // 		}
            // 		return [];
            // 	});
            // });
        },
        addCampus() {
            if (this.ruleFormChild.campus.length < 10) {
                this.ruleFormChild.campus.push({
                    id: "",
                    name: "",
                    type: [],
                    area: "",
                    region: [],
                });
            }
        },
        removeCampus(index, item) {
            this.ruleFormChild.campus.splice(index, 1);
        },
        importList(res) {
            if (res.code === 0) {
                this.ruleFormChild.badgeUrl = res.data[0].url;
                this.ruleFormChild.filelist = res.data;
            } else {
                this.$message.error(res.message);
            }
        },
        selectIfon(e) {
            if (e === "初中等教育") {
                this.periodState = true;
            } else {
                this.periodState = false;
            }
        },
        area() {
            if (Array.isArray(this.ruleFormChild.region)) {
                this.ruleFormChild.area = this.ruleFormChild.region.join("/");
            } else {
                this.ruleFormChild.region = [];
            }
            if (this.ruleFormChild.campus.length > 0) {
                this.ruleFormChild.campus.map((item) => {
                    if (Array.isArray(item.region)) {
                        item.area = item.region.join("/");
                    } else {
                        item.area = [];
                    }
                });
            }
        },
        importError(res) {
            this.$message.error(res.message);
        },
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.loading = true;
                    if (this.formTitle === "新增学校") {
                        this.area();
                        addSchoolAppList({
                            ...this.ruleFormChild,
                        })
                            .then((res) => {
                                this.$message.success(res.message);
                                this.handleClose();
                            })
                            .finally(() => {
                                this.loading = false;
                            });
                    } else if (this.formTitle === "编辑学校") {
                        this.area();
                        editSchoolAppList({ ...this.ruleFormChild })
                            .then((res) => {
                                this.$message.success(res.message);
                                this.handleClose();
                            })
                            .finally(() => {
                                this.loading = false;
                            });
                    }
                } else {
                    return false;
                }
            });
        },
        resetForm(formName) {
            try {
                this.$refs[formName].resetFields();
            } catch (e) {
                console.log(e);
            }
        },
        handleClose() {
            this.ruleFormChild.schoolType = "";
            this.periodState = false;
            this.resetForm("ruleFormChild");
            this.$emit("handleClose", false);
        },
        cancelForm(formName) {
            this.periodState = false;
            this.resetForm("ruleFormChild");
            this.$emit("handleClose", false);
        },
    },
    watch: {
        schoolRuleForm(val) {
            if (val) {
                this.ruleFormChild = val;
                this.ruleFormChild.region = val.area ? val.area.split("/") : "";
                if (val.campus.length !== 0) {
                    val.campus.map((item) => {
                        item.region = item.area ? item.area.split("/") : "";
                    });
                }
                this.sections.map((item) => {
                    return val.roleType.map((id) => {
                        if (item.id === id) {
                            item.disabled = true;
                        }
                        return [];
                    });
                });
            }
        },
        drawerTitle(val) {
            if (val === "编辑学校") {
                this.campusDisabled = true;
            } else {
                this.campusDisabled = false;
                this.ruleFormChild.roleType = [];
            }
            if (val) {
                this.formTitle = val;
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.campusClass {
    width: 95%;
    height: 228px;
    background: #eee;
    margin-top: 10px;
    padding: 20px 20px 20px 0px;
    // overflow-y: auto;
    position: relative;

    .removeCampus {
        position: absolute;
        right: -25px;
        top: 50%;
        color: red;
        font-size: 20px;
        cursor: pointer;
    }
}

.yd-new-modify-school {
    .yd-form-box {
        outline: none;
        padding: 20px 50px 0;
        margin-bottom: 76px;
        overflow-y: auto;
        border-top: 1px solid #ebeef5;
    }

    .tips {
        margin-top: 10px;
        color: #c4c7cf;
        line-height: initial;
    }

    .el-drawer__header {
        border-bottom: #dcdfe6;
    }

    ::v-deep :focus {
        outline: 0;
    }

    ::v-deep .yd-uploader .el-form-item__content {
        display: flex;
        display: flex;
        flex-direction: column;
    }

    ::v-deep .avatar-uploader {
        width: 150px;
        height: 150px;
        margin-right: 20px;

        .el-upload {
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            min-height: 150px;
        }

        .el-upload:hover {
            border-color: #409eff;
        }

        .avatar {
            height: 100%;
            width: 100%;
            display: block;
        }
    }

    .avatar-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 150px;
        height: 149px;
        line-height: 138px;
        text-align: center;
    }

    .yd-form-footer {
        z-index: 2;
        text-align: center;
        height: 64px;
        position: absolute;
        bottom: 0px;
        background: #fff;
        width: 100%;

        .footerForm {
            display: flex;
            width: 100%;
            justify-content: center;
            align-items: center;
            height: 64px;
            border: 1px solid #eee;
        }
    }
}
</style>
<style lang="scss">
.el-drawer__container ::-webkit-scrollbar {
    display: none;
}

.campusClass {
    .el-form-item__label {
        width: 88px !important;
    }
}

.yd-new-modify-school {
    .el-drawer {
        width: 554px !important;
    }
}
</style>
