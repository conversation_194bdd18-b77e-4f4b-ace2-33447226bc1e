<!--
 * @Descripttion: 
 * @version: 1.0.0
 * @Author: jingrou
 * @Date: 2022-07-07 18:21:42
 * @LastEditors: jingrou
 * @LastEditTime: 2023-02-20 18:08:59
-->
<template>
    <div>
        <div class="resourceType">
            <div class="resourceTypeBtn">
                <el-button
                    v-auth="'manage.templateManage.type'"
                    icon="el-icon-tickets"
                    type="primary"
                    @click="resourceType"
                    >资源分类</el-button
                >
            </div>
            <div class="resourceTypeItem" v-if="isResource">
                资源分类：
                <p
                    :style="resourceTypeId == item.id ? 'color: #2196f3;' : ''"
                    v-for="(item, index) in resourceTypeList"
                    :key="index"
                    @click="resourceTypeChang(item, index)"
                >
                    {{ item.name }}
                </p>
            </div>
        </div>
        <div class="schoolTypeClass">
            <el-select
                v-model="schoolType"
                placeholder="请选择模板类型"
                @change="queryList"
            >
                <el-option label="全部" value=""> </el-option>
                <el-option
                    v-for="item in schoolTypeList"
                    :key="item.schoolType"
                    :label="item.typeName"
                    :value="item.schoolType"
                >
                </el-option>
            </el-select>
        </div>
        <!-- 模板列表 -->
        <el-row :gutter="20" style="margin-left: 6px" v-if="isResource">
            <el-col :span="6" :offset="2" class="addTemplate">
                <div class="addTemplate" @click="addImgTemplateFn">
                    <i class="el-icon-plus add_plus" />
                    <span>新建模板</span>
                </div>
            </el-col>
            <draggable
                v-if="templateList.length !== 0"
                v-model="templateList"
                group="people"
                animation="1000"
                @start="onTemplateStart"
                @end="onTemplateEnd"
            >
                <transition-group>
                    <el-col
                        :span="6"
                        :offset="2"
                        class="templateItem"
                        v-for="(item, index) in templateList"
                        :key="item.id + 'template' + index"
                    >
                        <div
                            class="templateItemType"
                            @click.stop.prevent="echoclassifyTypeInfo(item)"
                        >
                            更改分类
                        </div>
                        <div
                            class="deactivateType"
                            @click.stop.prevent="deactivateImgFn(item)"
                        >
                            {{ item.isEnabled == 1 ? "停用" : "恢复" }}
                        </div>

                        <img
                            :src="item.coverImg"
                            alt=""
                            @click.stop.prevent="echoImgTemplateFn(item)"
                        />
                        <div class="templateItemName">
                            <span
                                style="
                                    white-space: nowrap;
                                    text-overflow: ellipsis;
                                    overflow: hidden;
                                    word-break: break-all;
                                "
                                >{{ item.title }}</span
                            ><i style="color: #666" class="el-icon-sort" />
                        </div>
                        <div class="templateItemPublisher">
                            <div>
                                <span
                                    v-for="type in item.schoolName"
                                    :key="type"
                                    >{{ type }}</span
                                >
                            </div>
                            <span>{{ item.createBy }}</span>
                            <span>{{ item.createTime }}</span>
                        </div>
                    </el-col>
                </transition-group>
            </draggable>
            <div v-else>
                <el-empty
                    style="width: 1000px; height: 500px"
                    :image-size="200"
                ></el-empty>
            </div>
        </el-row>
        <!-- 分页 -->
        <el-pagination
            v-if="templateList.length > 0 && isResource"
            background
            :current-page="pagination.pageNo"
            :page-sizes="[9, 19, 49, 99]"
            :page-size="pagination.pageSize"
            style="margin-top: 16px; text-align: right"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
        <el-empty v-if="!isResource" :image-size="200"></el-empty>
        <!-- 新增编辑分类 -->
        <el-drawer
            title="资源分类"
            :visible.sync="resourceDrawer"
            :before-close="handleClose"
        >
            <el-form
                ref="resourceTypeForm"
                :model="resourceTypeForm"
                style="padding-bottom: 65px"
            >
                <draggable
                    v-model="resourceTypeForm.list"
                    group="people"
                    animation="1000"
                    @start="onStart"
                    @end="onEnd"
                    style="padding: 0px 20px; width: 100%"
                >
                    <transition-group>
                        <div
                            v-for="(item, index) in resourceTypeForm.list"
                            :key="item.id + 'resourceType' + index"
                        >
                            <el-form-item
                                :prop="`list[${index}].name`"
                                :rules="{
                                    required: true,
                                    message: '请输入分类名称',
                                    trigger: 'blur',
                                }"
                            >
                                <i
                                    class="el-icon-sort"
                                    style="
                                        font-size: 20px;
                                        color: #ccc;
                                        margin-right: 12px;
                                    "
                                />
                                <el-input
                                    v-model.trim="item.name"
                                    style="width: 75%"
                                    maxlength="6"
                                    show-word-limit
                                    placeholder="请输入分类名称"
                                />
                                <i
                                    v-if="
                                        index ==
                                        resourceTypeForm.list.length - 1
                                    "
                                    class="el-icon-plus adddeleteBtn"
                                    @click="addTemplateFn"
                                />
                                <i
                                    class="el-icon-minus adddeleteBtn"
                                    @click="deleteTemplateFn(item, index)"
                                />
                            </el-form-item>
                        </div>
                    </transition-group>
                </draggable>
                <el-form-item class="yd-form-footer">
                    <div class="footerForm">
                        <el-button
                            class="reset"
                            @click="handleClose('resourceTypeForm')"
                            >取消</el-button
                        >
                        <el-button
                            type="primary"
                            :loading="resourceTypeLoading"
                            @click="addResourceType('resourceTypeForm')"
                            >确 定</el-button
                        >
                    </div>
                </el-form-item>
            </el-form>
        </el-drawer>
        <!-- 新增、修改、查看 -->
        <AddTemplate
            :drawer="addDrawer"
            :classifyType="resourceTypeId"
            :drawerTitle="addDrawerTitle"
            :templateInfo="imgTemplateInfo"
            :schoolTypeArr="schoolTypeList"
            @handleClose="close"
            :typeMessId="id"
        />
        <!-- 更改分类 -->
        <el-drawer
            title="更改分类"
            :visible.sync="classifyDrawer"
            class="classifyDrawer"
        >
            <el-form ref="classifyTypeForm" :model="classifyTypeForm">
                <div
                    style="
                        width: 94%;
                        display: flex;
                        height: 240px;
                        margin: auto;
                        border: 1px solid #e0e0e0;
                        overflow: hidden;
                        justify-content: center;
                        align-items: center;
                    "
                >
                    <img
                        style="height: 100%"
                        :src="classifyTypeForm.coverImg"
                        alt=""
                    />
                </div>
                <div style="width: 50%; margin: 16px; font-size: 18px">
                    选择更改的分类名称
                </div>
                <el-form-item
                    label=""
                    prop="typeIds"
                    :rules="{
                        required: true,
                        message: '请选择分类名称',
                        trigger: 'blur',
                    }"
                >
                    <el-checkbox-group
                        style="margin-left: 8px"
                        class="classificationName_list"
                        v-model="classifyTypeForm.typeIds"
                        placeholder="请选择分类名称"
                    >
                        <el-checkbox
                            style="margin: 5px 0px"
                            v-for="(item, index) in resourceTypeList"
                            :key="index"
                            :label="item.id"
                            border
                            >{{ item.name }}</el-checkbox
                        >
                    </el-checkbox-group>
                </el-form-item>

                <el-form-item class="yd-form-footer">
                    <div class="footerForm">
                        <el-button
                            class="reset"
                            @click="classifyTypeClose('classifyTypeForm')"
                            >取消</el-button
                        >
                        <el-button
                            type="primary"
                            :loading="classifyTypeLoading"
                            @click="classifyTypeOk('classifyTypeForm')"
                            >确 定</el-button
                        >
                    </div>
                </el-form-item>
            </el-form>
        </el-drawer>
    </div>
</template>

<script>
import draggable from "vuedraggable";
import {
    getMessTemplateList,
    addTemplateInfo,
    deleteTemplateInfo,
    getImgList,
    echoImgInfo,
    getTypeListInfo,
    editImgListInfo,
    deactivateImg,
    editImgSort,
    getschoolType,
} from "@/api/templateManage.js";
import AddTemplate from "@/components/AddTemplate";
export default {
    components: {
        draggable,
        AddTemplate,
    },
    props: {
        typeMessId: String, // 图库 海报 ID
    },
    data() {
        return {
            schoolType: null,
            schoolTypeList: [],
            // 更改模板分类 表单
            classifyTypeForm: {
                coverImg: "",
                typeIds: [],
            },
            classifyDrawer: false, // 更改模板分类 表单状态
            // 模板分类 表单
            resourceTypeForm: {
                list: [{ name: "" }],
            },
            resourceTypeList: [], // 模板分类 列表
            resourceDrawer: false,
            templateList: [], // 模板 列表
            imgTemplateInfo: {}, // 模板回显 详情
            pagination: {
                pageNo: 1,
                pageSize: 9,
                total: 0,
            },
            resourceTypeId: "1", // 模板分类ID
            id: "1", // 图库 海报 ID

            addDrawerTitle: "新建模板", // 新增编辑模板 表单标题
            addDrawer: false, // 新增编辑模板 表单状态
            tempId: "", // 模板 Id
            classifyTypeLoading: false,
            resourceTypeLoading: false,
            isResource: true, // 是否 有模板分类，判断如果没有 显示无数据,
            oldList: [],
        };
    },
    methods: {
        // 获取模板学校类型
        getschoolTypeFn() {
            getschoolType({}).then((res) => {
                this.schoolTypeList = res.data;
            });
        },
        // ····························更改模板分类和停用模板···············································································
        // 停用
        deactivateImgFn(item) {
            let Tips =
                item.isEnabled == 0
                    ? "此操作恢复该模板, 是否继续?"
                    : "此操作将永久停用该模板, 是否继续?";
            this.$confirm(Tips, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    let obj = {
                        isEnabled: item.isEnabled == 1 ? 0 : 1,
                        tempId: item.tempId,
                    };
                    deactivateImg(obj).then((res) => {
                        this.$message.success(res.message);
                        this.getMessTemplateList();
                    });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: `已取消${
                            item.isEnabled == 0 ? " 恢复 " : "停用"
                        }`,
                    });
                });
        },
        // 回显模板更改分类
        echoclassifyTypeInfo(item) {
            this.tempId = item.tempId;
            this.classifyDrawer = true;
            getTypeListInfo({ tempId: item.tempId }).then((res) => {
                this.classifyTypeForm = res.data;
                this.classifyTypeForm.typeIds = res.data.typeIds.map(Number);
            });
        },
        // 取消更改分类
        classifyTypeClose() {
            this.classifyDrawer = false;
        },
        // 确定更改分类
        classifyTypeOk(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.classifyTypeLoading = true;
                    let obj = {
                        tempId: this.tempId,
                        typeIds: this.classifyTypeForm.typeIds,
                    };
                    editImgListInfo(obj)
                        .then((res) => {
                            this.$message.success(res.message);
                            this.classifyDrawer = false;
                        })
                        .finally(() => {
                            this.classifyTypeLoading = false;
                            this.getMessTemplateList();
                        });
                } else {
                }
            });
        },
        // ····························增删改查 模板···············································································
        //新建模板
        addImgTemplateFn() {
            this.addDrawerTitle = "新建模板";
            this.addDrawer = true;
        },
        // 回显模板
        echoImgTemplateFn(item) {
            this.addDrawerTitle = "编辑模板";
            this.addDrawer = true;
            echoImgInfo({ tempId: item.tempId }).then((res) => {
                this.imgTemplateInfo = res.data;
            });
        },
        // 获取模板列表
        getImgList() {
            let obj = {
                pTypeId: this.id,
                typeId: this.resourceTypeId,
                ...this.pagination,
                schoolType: this.schoolType,
            };
            getImgList(obj).then((res) => {
                let { data } = res;
                this.templateList = data.list.map((item) => {
                    return {
                        ...item,
                        schoolName:
                            item.schoolTypes &&
                            item.schoolTypes.map((i) => {
                                if (i === 1) {
                                    return "中";
                                } else if (i === 2) {
                                    return "高";
                                }
                            }),
                    };
                });
                this.oldList = data.list;
                this.pagination.pageNo = data.pageNo;
                this.pagination.pageSize = data.pageSize;
                this.pagination.total = data.total;
            });
        },
        async queryList() {
            this.pagination.pageNo = 1;
            this.getImgList();
        },
        close(state) {
            this.addDrawer = state;
            this.addDrawerTitle = "新建模板";
            this.getImgList();
        },
        // ····························增删改查 资源标签分类···············································································
        // 获取资源标签列表
        getMessTemplateList() {
            getMessTemplateList({ id: this.id }).then((res) => {
                if (res.data.length === 0) {
                    this.isResource = false;
                } else {
                    this.isResource = true;
                }
                this.resourceTypeList = res.data;
                this.resourceTypeForm.list = res.data;
                if (this.resourceTypeForm.list.length <= 0) {
                    this.addTemplateFn();
                } else {
                    this.resourceTypeId = String(res.data[0].id);
                    this.getImgList();
                }
            });
        },
        // 点击标签 获取列表
        resourceTypeChang(item, index) {
            this.resourceTypeId = String(item.id);
            this.pagination.pageNo = 1;
            this.pagination.pageSize = 9;
            this.getImgList();
        },
        //添加分类名称
        addTemplateFn() {
            this.resourceTypeForm.list.push({
                name: "",
                id: "",
                messTypeId: this.id,
                sort: this.resourceTypeForm.list.length,
            });
        },
        // 删除分类名称
        deleteTemplateFn(item, index) {
            if (item.id !== "") {
                this.$confirm("请确认删除?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(() => {
                        deleteTemplateInfo({ id: item.id })
                            .then((res) => {
                                this.$message.success(res.message);
                                this.getMessTemplateList();
                            })
                            .catch();
                    })
                    .catch(() => {
                        this.$message({
                            type: "info",
                            message: "已取消删除",
                        });
                    });
            } else {
                this.resourceTypeForm.list.splice(index, 1);
            }
        },
        // 分类保存添加和修改
        addResourceType(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.resourceTypeLoading = true;
                    addTemplateInfo({
                        typeList: this.resourceTypeForm.list,
                    })
                        .then((res) => {
                            this.$message.success(res.message);
                            this.handleClose();
                        })
                        .finally(() => {
                            this.resourceTypeLoading = false;
                            this.getMessTemplateList();
                        });
                } else {
                    return false;
                }
            });
        },
        // 打开标签弹框
        resourceType() {
            this.getMessTemplateList();
            this.resourceDrawer = true;
        },
        // 关闭标签弹框
        handleClose() {
            this.resourceDrawer = false;
            this.getMessTemplateList();
        },
        // 拖拽
        onStart() {},
        // 模板分类列表 拖拽结束
        onEnd() {
            for (let i = 0; i < this.resourceTypeForm.list.length; i++) {
                this.resourceTypeForm.list[i].sort = i;
            }
        },
        onTemplateStart(arr) {},
        // 模板列表 拖拽结束
        onTemplateEnd(val) {
            console.log(this.templateList, "888888888888888888");
            console.log(this.oldList);
            // console.log(val.oldIndex, val.newIndex);
            // let oldObj = {
            //     tempId: this.templateList[val.oldIndex].tempId,
            //     sort: this.templateList[val.oldIndex].tempSort,
            // };
            // let newObj = {
            //     tempId: this.templateList[val.newIndex].tempId,
            //     sort: this.templateList[val.newIndex].tempSort,
            // };
            // const list = [];
            // let obj = {
            //     tempId: this.templateList[newIndex].tempId,
            // }
            // for (let index = 0; index < this.templateList.length; index++) {
            //     const obj = {
            //         tempId: this.templateList[index].tempId,
            //         sort: index,
            //     };

            //     list.push(obj);
            // }
            editImgSort({
                newList: this.templateList,
                oldList: this.oldList,
            }).then((res) => {
                this.$message.success(res.message);
                this.getImgList();
            });
        },
        //分页
        handleSizeChange(val) {
            this.pagination.pageNo = 1;
            this.pagination.pageSize = val;
            this.getImgList();
        },
        handleCurrentChange(val) {
            this.pagination.pageNo = val;
            this.getImgList();
        },
    },
    created() {
        this.getMessTemplateList();
        this.$nextTick(() => {
            this.getschoolTypeFn();
        });
    },
    watch: {
        typeMessId(val) {
            this.id = val;
            this.getMessTemplateList();
        },
    },
};
</script>

<style lang="scss" scoped>
.resourceType {
    width: 99%;
    height: 100px;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    margin-bottom: 12px;

    .resourceTypeBtn {
        display: flex;
        justify-content: flex-end;
        margin-right: 12px;
    }

    .resourceTypeItem {
        display: flex;
        font-size: 16px;
        align-items: center;
        font-weight: 500;
        margin-left: 15px;
        flex-wrap: wrap;

        p {
            cursor: pointer;
            color: #555;
            padding-left: 10px;
            font-size: 14px;
            font-weight: 500;
            margin: 5px;
        }

        p:hover {
            color: #2196f3;
        }
    }
}

.schoolTypeClass {
    padding: 0px 0px 12px 12px;
}

.yd-form-footer {
    z-index: 2;
    text-align: center;
    height: 64px;
    position: absolute;
    bottom: 0px;
    background: #fff;
    width: 100%;

    .footerForm {
        display: flex;
        width: 100%;
        justify-content: center;
        align-items: center;
        height: 64px;
        border: 1px solid #eee;
    }
}

.adddeleteBtn {
    margin-left: 10px;
    cursor: pointer;
}

.addTemplate {
    width: 300px;
    height: 180px;
    border: 1px solid #f4f4f4;
    border-radius: 5px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    font-size: 16px;
    color: #999;
    margin: 0px 6px;

    i {
        font-size: 36px;
    }

    span {
        margin-top: 10px;
    }
}

.templateItem {
    width: 300px;
    height: 240px;
    margin: 0px 6px 28px 6px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 4px 0px;
    position: relative;

    .templateItemType,
    .deactivateType {
        position: absolute;
        bottom: 62px;
        right: 20px;
        color: #333;
        font-size: 12px;
        cursor: pointer;
        padding: 0 12px;
        background-color: #74baff;
        color: #fff;
        border-radius: 3px;
        line-height: 24px;
    }

    .deactivateType {
        right: 100px;
    }

    img {
        width: 100%;
        height: 180px;
    }

    .templateItemName,
    .templateItemPublisher {
        display: flex;
        justify-content: space-between;
        padding: 0px 8px;

        div {
            display: flex;
            justify-content: space-between;
            width: 60px;
            align-items: center;
            height: 16px;

            span {
                padding: 2px 8px;
                background: #00b781;
                color: #fff;
                border-radius: 10px;
                font-size: 12px;
            }
        }
    }

    .templateItemName {
        font-size: 16px;
        color: #333;
    }

    .templateItemPublisher {
        height: 16px;
        font-size: 14px;
        color: rgb(153, 153, 153);
        overflow: hidden;
    }
}
</style>
<style lang="scss">
.classifyDrawer {
    .el-form-item__error {
        margin: 6px 20px;
    }
}
</style>
