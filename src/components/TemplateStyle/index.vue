<template>
    <div>
        <el-drawer title="添加模块风格" :visible.sync="drawerStatus" :before-close="close">
            <el-form ref="form" :model="form" style="padding-bottom: 65px; height: 100%" v-loading="draggableLoading">
                <draggable v-model="form.templateStyleList" chosen-class="chosen" force-fallback="true" group="people"
                    animation="1000" @start="onStart" @end="onEnd" style="padding: 0px 20px; width: 100%">
                    <transition-group>
                        <div v-for="(item, index) in form.templateStyleList" :key="item.id" class="draggableItem">
                            <el-form-item :prop="`templateStyleList[${index}].styleName`"
                                :rules="{ required: true, message: '请输入模板风格名称', trigger: 'blur' }">
                                <i class="el-icon-sort" style="font-size: 20px;color: #ccc;  margin-right: 12px;" />
                                <el-input v-model.trim="item.styleName" style="width: 75%" maxlength="10" show-word-limit
                                    placeholder="请输入模板风格名称" />
                                <i v-if="index == form.templateStyleList.length - 1" @click="addStyleFn"
                                    class="el-icon-plus adddeleteBtn" />
                                <i @click="deleteStyleFn(item, index)" class="el-icon-minus adddeleteBtn" />
                            </el-form-item>
                        </div>
                    </transition-group>
                </draggable>
                <el-form-item class="yd-form-footer">
                    <div class="footerForm">
                        <el-button class="reset" @click="close">取消</el-button>
                        <el-button type="primary" :loading="styleOkLoading" @click="okStyleFn('form')">确 定</el-button>
                    </div>
                </el-form-item>
            </el-form>
        </el-drawer>
    </div>
</template>

<script>
import draggable from "vuedraggable";
export default {
    components: {
        draggable,
    },
    props: {
        drawerStatus: {
            type: Boolean,
            default: false,
        },
        list: {
            type: Array,
            default: () => [],
        },
        styleOkLoading: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            form: {
                templateStyleList: [],
            },
            draggableLoading: false,
        };
    },
    watch: {
        list(val) {
            this.form.templateStyleList = val;
        },
    },
    methods: {
        close() {
            this.$emit("closeDrawer", false);
        },
        // 拖拽
        onStart() { },
        // 模板分类列表 拖拽结束
        onEnd(event) {
            for (let i = 0; i < this.form.templateStyleList.length; i++) {
                this.form.templateStyleList[i].sort = i;
            }
        },
        addStyleFn() {
            this.form.templateStyleList.push({
                styleName: "",
                id: "",
                sort: this.form.templateStyleList.length,
            });
        },
        // 删除分类名称
        deleteStyleFn(item, index) {
            if (item.id !== "") {
                this.$confirm("请确认删除?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(() => {
                        this.$emit("deleteStyle", item.id);
                    })
                    .catch(() => {
                        this.$message({
                            type: "info",
                            message: "已取消删除",
                        });
                    });
            } else {
                if (this.form.templateStyleList.length > 1) {
                    this.form.templateStyleList.splice(index, 1);
                }
            }
        },
        // 点击确定
        okStyleFn(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.$emit("okStyleFn", this.form.templateStyleList);
                } else {
                    return false;
                }
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.draggableItem {
    cursor: move;
}

.draggableItem:hover {
    cursor: move;
}

.adddeleteBtn {
    margin-left: 10px;
    cursor: pointer;
}

.yd-form-footer {
    z-index: 2;
    text-align: center;
    height: 64px;
    position: absolute;
    bottom: 0px;
    background: #fff;
    width: 100%;

    .footerForm {
        display: flex;
        width: 100%;
        justify-content: center;
        align-items: center;
        height: 64px;
        border: 1px solid #eee;
    }
}
</style>
