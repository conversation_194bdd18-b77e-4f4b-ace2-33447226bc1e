<template>
    <div class="tinymce-editor">
        <editor
            v-model="myValue"
            :init="init"
            :disabled="disabled"
            @onClick="onClick"
        ></editor>
    </div>
</template>
<script>
import tinymce from "tinymce/tinymce";
import Editor from "@tinymce/tinymce-vue";
import "tinymce/icons/default";
import "tinymce/themes/silver";
// 编辑器插件plugins
// 更多插件参考：https://www.tiny.cloud/docs/plugins/
// 中文文档 http://tinymce.ax-z.cn/
import "tinymce/plugins/image"; // 插入上传图片插件
import "tinymce/plugins/media"; // 插入视频插件
import "tinymce/plugins/table"; // 插入表格插件
import "tinymce/plugins/lists"; // 列表插件
import "tinymce/plugins/wordcount"; // 字数统计插件
// import "tinymce/plugins/fullscreen"; // 全屏
import "tinymce/plugins/print"; // 打印
import "tinymce/plugins/preview"; // 预览
import "tinymce/plugins/save"; // 保存
import "tinymce/plugins/hr"; // 水平
import "tinymce/plugins/link"; // 链接
import "tinymce/plugins/pagebreak"; // 分页符
import "tinymce/plugins/insertdatetime"; // 时间
import "tinymce/plugins/codesample"; // 代码
import "tinymce/plugins/searchreplace"; // 查找和替换
import "tinymce/plugins/code"; // 代码
import "tinymce/plugins/help"; // 帮助
import "tinymce/plugins/emoticons"; // 插入表情插件
import "tinymce/themes/mobile";
// import "tinymce/plugins/autoresize";
import "tinymce/plugins/charmap"; //特殊字符
import "tinymce/plugins/anchor"; //锚点
import "tinymce/plugins/fullpage"; //文档属性

import "../../../public/tinymce/bdmap/plugin";
import { getUploadFileOSSpath } from "@/utils/oss";

export default {
    components: {
        Editor,
    },
    props: {
        value: {
            type: String,
            default: "",
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        plugins: {
            type: [String, Array],
            default:
                "fullpage bdmap anchor charmap lists image media table wordcount fullscreen autoresize print preview save hr code codesample link pagebreak insertdatetime searchreplace help emoticons",
        },
        toolbar: {
            type: [String, Array],
            default:
                "fontsizeselect | fontselect | fullpage | code anchor bdmap charmap undo redo |  formatselect | emoticons bold italic forecolor backcolor hr codesample insertdatetime link pagebreak searchreplace| alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | lists image media table | removeformat |  fullscreen  print preview save help | ",
        },
        height: {
            type: [String, Number],
            default: 400,
        },
        skin: {
            type: String,
            default: "white",
        },
        language: {
            type: String,
            default: "zh_CN",
        },
        // 上传文件 类型
        folderType: {
            type: String,
            default: "",
        },
    },
    data() {
        let obj = {};
        if (this.language === "zh_CN") {
            obj = {
                language_url: "/tinymce/langs/zh_CN.js",
                language: "zh_CN",
            };
        } else {
            obj = {};
        }
        return {
            init: {
                ...obj,

                skin_url:
                    this.skin === "white"
                        ? "/tinymce/skins/ui/oxide"
                        : "/tinymce/skins/ui/oxide-dark",
                height: this.height,
                plugins: this.plugins,
                toolbar: this.toolbar,
                branding: false,
                menubar: true,
                file_picker_types: "media",
                selector: "textarea",
                emoticons_database_url: "/tinymce/emoticons/js/emojis.js",
                //一个汉字算一个字符，为了统计相对准确
                wordcount_countregex:
                    /([\w\u2019\x27\-\u00C0-\u1FFF]+)|([^\x00-\xff])/g,
                // 模态窗口允许拖动
                draggable_modal: true,
                resize: true,
                fontsize_formats:
                    "12px 14px 16px 18px 24px 36px 48px 56px 72px",
                lineheight_formats: "1 1.1 1.2 1.3 1.4 1.5 2",
                content_style:
                    "html,body{ font-family:PingFang SC;font-size:14px}",
                font_formats:
                    "微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;苹果苹方=PingFang SC,Microsoft YaHei,sans-serif;宋体=simsun,serif;仿宋体=FangSong,serif;黑体=SimHei,sans-serif;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;",
                setup: (editor) => {
                    const image =
                        '<svg t="1585044160565" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="18129" width="24" height="24"><path d="M963.8 809H60.2C26.9 809 0 782.1 0 748.8v-575c0-33.2 26.9-60.2 60.2-60.2h903.7c33.2 0 60.2 26.9 60.2 60.2v575c-0.1 33.3-27 60.2-60.3 60.2z" fill="#D3EEF2" p-id="18130"></path><path d="M636.9 492.9L439.2 720.7h395.3L636.9 492.9z m0 0" fill="#86B26F" p-id="18131"></path><path d="M602.7 720.7L339.2 417.1 75.7 720.7H0v132.7c0 31.4 25.5 56.9 56.9 56.9h910.2c31.4 0 56.9-25.5 56.9-56.9V720.7H602.7z m0 0" fill="#8FC675" p-id="18132"></path><path d="M758.5 303.5c0 41.9 33.9 75.9 75.8 75.9s75.9-33.9 75.9-75.8v-0.1c0-41.9-33.9-75.9-75.8-75.9s-75.9 33.9-75.9 75.9z m0 0" fill="#F9D28C" p-id="18133"></path></svg>';
                    const emoji =
                        '<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="21" height="21"><path d="M0,512a512,512,0,1,0,1024,0,512,512,0,1,0-1024,0z" fill="#FFC51F"></path><path d="M204.8,614.4C214.2,739.2,348.1,838.2,512,838.2s297.8-99,307.2-223.8H204.8z" fill="#B67600"></path><path d="M234.1,409.6a87.8,73.1,90,1,0,146.3,0,87.8,73.1,90,1,0-146.3,0z" fill="#815400"></path><path d="M643.7,409.6a87.8,73.1,90,1,0,146.3,0,87.8,73.1,90,1,0-146.3,0z" fill="#815400"></path></svg>';
                    const table =
                        '<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="21" height="21"><path d="M960,0H64C25.6,0,0,25.6,0,64V960c0,38.4,25.6,64,64,64H960c38.4,0,64-25.6,64-64V64C1024,25.6,998.4,0,960,0zm0,960H64V256H960V960z" fill="#61A3FF"></path><path d="M320,256h64V960H320zm320,0h64V960H640z" fill="#61A3FF"></path><path d="M64,448H960v64H64zm0,256H960v64H64z" fill="#61A3FF"></path></svg>';
                    const link =
                        '<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="21" height="21"><path d="M1006.9,266.2L757.8,17.1a58,58,0,0,0-81.9,0L426.7,266.2a58,58,0,0,0,0,81.9l40.3,40.3,250.5-251.9,170,170-251.9,250.6,40.3,40.3a58,58,0,0,0,81.9,0L1007,348.2a58,58,0,0,0,0-81.9zM557.1,635.6L306.5,887.5,136.5,717.5,388.4,467,348.1,426.7a58,58,0,0,0-81.9,0L17.1,675.8a58,58,0,0,0,0,81.9l249.2,249.2a58,58,0,0,0,81.9,0L597.4,757.7a58,58,0,0,0,0-81.9z" fill="#1296db"></path><path d="M341.3,676.5a58,58,0,0,0,81.9,0L675.1,426.6A58,58,0,0,0,593.2,344.7L341.3,594.6a58,58,0,0,0,0,81.9z" fill="#7dc5eb"></path></svg>';
                    const fullscreen =
                        '<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="20" height="20"><path d="M112,112H320a48,48,0,1,0,0-96H64A48,48,0,0,0,16,64V320a48,48,0,1,0,96,0V112zm800,0V320a48,48,0,1,0,96,0V64A48,48,0,0,0,960,16H704a48,48,0,1,0,0,96H912zM112,912V704a48,48,0,1,0-96,0V960c0,26.5,21.5,48,48,48H320a48,48,0,1,0,0-96H112zm800,0H704a48,48,0,1,0,0,96H960a48,48,0,0,0,48-48V704a48,48,0,1,0-96,0V912z" fill="#1c92f9"></path><path d="M30.1,97.9l224,224a48,48,0,0,0,67.8-67.8L97.9,30.1A48,48,0,0,0,30.1,97.9zm896-67.8l-224,224a48,48,0,0,0,67.8,67.8l224-224A48,48,0,0,0,926.1,30.1zM97.9,993.9l224-224A48,48,0,0,0,254.1,702.1l-224,224a48,48,0,0,0,67.8,67.8zm896-67.8l-224-224a48,48,0,0,0-67.8,67.8l224,224a48,48,0,0,0,67.8-67.8z" fill="#1c92f9"></path></svg>';
                    const insertTime =
                        '<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="24" height="24"><path d="M511.846 859.136c-191.16 0-347.555-156.396-347.555-347.546s156.395-347.555 347.555-347.555S859.392 320.43 859.392 511.59c0 192.727-156.396 347.546-347.546 347.546zm0-631.91c-156.395 0-284.364 128-284.364 284.364s128 284.365 284.364 284.365 284.365-128 284.365-284.365-128-284.364-284.365-284.364zm0 0" fill="#1F91F2"></path><path d="M638.218 574.771H480.256a31.683 31.683 0 0 1-31.6-31.59V353.608a31.6 31.6 0 0 1 63.19 0V511.59h126.372a31.59 31.59 0 1 1 0 63.191zm0 0" fill="#FC7265"></path></svg>';
                    const embed =
                        '<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="24" height="24"><path d="M829.7,98.2H204.6C144,98.2,97.3,144.6,97.3,204.9V826.8c0,55.7,46.6,102.1,102.6,102.1H825c56,0,102.6-46.4,102.6-102.1V204.9C932.3,144.6,885.7,98.2,829.7,98.2z" fill="#40A9FF"></path><path d="M649.4,558.1L439.5,678.8c-28,13.9-65.3-4.6-65.3-37.1V391.1c0-32.5,37.3-55.7,65.3-37.1L649.4,483.9c28,18.6,28,55.7,0,74.3z" fill="#FFF"></path></svg>';
                    editor.ui.registry.addIcon("image", image);
                    editor.ui.registry.addIcon("emoji", emoji);
                    editor.ui.registry.addIcon("table", table);
                    editor.ui.registry.addIcon("link", link);
                    editor.ui.registry.addIcon("fullscreen", fullscreen);
                    editor.ui.registry.addIcon("insert-time", insertTime);
                    editor.ui.registry.addIcon("embed", embed);
                },
                images_upload_handler: async (
                    blobInfo,
                    success,
                    failure,
                    progress
                ) => {
                    progress(0);
                    // blobInfo.blob() 得到图片的file对象
                    let file = blobInfo.blob();
                    const url = await getUploadFileOSSpath(
                        file,
                        this.folderType,
                        (num) => {
                            progress(num);
                        }
                    );
                    if (url) {
                        progress(100);
                        success(url);
                        return;
                    }
                    progress(0);
                    failure("上传失败");
                    return;
                },
                file_picker_callback: async (callback, value, meta) => {
                    let folderType = this.folderType;
                    if (meta.filetype === "media") {
                        let input = document.createElement("input");
                        input.type = "file";
                        input.onchange = async function () {
                            let file = this.files[0];
                            const url = await getUploadFileOSSpath(
                                file,
                                folderType,
                                (num) => {
                                    console.log(num);
                                }
                            );
                            console.log(url);
                            callback(url);
                        };
                        input.click();
                    }
                },
            },
            myValue: this.value,
        };
    },
    mounted() {
        tinymce.init({});
    },
    methods: {
        // 添加相关的事件，可用的事件参照文档=> https://github.com/tinymce/tinymce-vue => All available events
        // 需要什么事件可以自己增加
        onClick(e) {
            this.$emit("click", e, tinymce);
        },
        // 可以添加一些自己的自定义事件，如清空内容
        clear() {
            this.myValue = "";
        },
    },
    watch: {
        value(newValue) {
            this.myValue = newValue;
        },
        myValue(newValue) {
            this.$emit("input", newValue);
        },
    },
};
</script>
<style scoped></style>
