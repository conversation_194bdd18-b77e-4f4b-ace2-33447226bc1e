<!--
 * @Descripttion: 表格
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-08-03 11:25:39
 * @LastEditors: jingrou
 * @LastEditTime: 2022-07-29 17:49:51
-->

<template>
    <div class="yd_table" ref="ydTableComp">
        <!-- search -->
        <div class="yd_table__search">
            <!-- <el-form :inline="true" :model="search" size="medium">
				<el-form-item label="审批人">
					<el-input v-model="search.user" placeholder="审批人"></el-input>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" :loading="loading" @click="handleSearch">查询</el-button>
				</el-form-item>
			</el-form> -->
            <slot name="search" :loading="loading" :handleSearch="handleSearch" />
        </div>
        <div class="yd_table__content">
            <!-- table handle -->
            <div class="yd_table__handle">
                <div>
                    <slot name="describe">{{ describe }}</slot>
                </div>
                <div class="yd_table__handle-list">
                    <div class="ythl_btn">
                        <slot name="btn" />
                    </div>
                    <div class="ythl_icon">
                        <el-tooltip class="item" effect="dark" content="导入" placement="top">
                            <el-link v-if="action.indexOf('import') > -1" icon="el-icon-upload2" :underline="false"
                                class="ythl_icon__item" @click="handleTableData('import')"></el-link>
                        </el-tooltip>
                        <el-tooltip class="item" effect="dark" content="导出" placement="top">
                            <el-link v-if="action.indexOf('export') > -1" icon="el-icon-download" :underline="false"
                                class="ythl_icon__item" @click="handleTableData('export')"></el-link>
                        </el-tooltip>
                        <el-tooltip class="item" effect="dark" content="打印" placement="top">
                            <el-link v-if="action.indexOf('print') > -1" icon="el-icon-printer" :underline="false"
                                class="ythl_icon__item" @click="handleTableData('print')"></el-link>
                        </el-tooltip>
                        <el-tooltip class="item" effect="dark" content="刷新" placement="top">
                            <el-link v-if="action.indexOf('refresh') > -1" icon="el-icon-refresh-right"
                                :underline="false" :class="{
                                    ythl_icon__item: true,
                                    ythl_icon_refresh: refreshLoading,
                                }" @click="handleRefresh(true)"></el-link>
                        </el-tooltip>
                        <el-tooltip class="item" effect="dark" content="密度" placement="top">
                            <el-dropdown v-if="action.indexOf('tableSize') > -1" size="medium" trigger="click"
                                @command="handleTableSize">
                                <el-link icon="el-icon-d-caret" :underline="false" class="ythl_icon__item"></el-link>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item command="medium">默认</el-dropdown-item>
                                    <el-dropdown-item command="small">中等</el-dropdown-item>
                                    <el-dropdown-item command="mini">紧凑</el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown>
                        </el-tooltip>
                        <el-tooltip v-if="action.indexOf('tableSet') > -1" class="item" effect="dark" content="列设置"
                            placement="top">
                            <el-popover placement="bottom-end" width="208" trigger="click">
                                <div class="ythl_column__control">
                                    <!--  -->
                                    <div class="ythl_column__control-header">
                                        <el-checkbox :indeterminate="isColumnSelectAll" v-model="columnSelectAll"
                                            @change="handleCheckAllColumn">列展示</el-checkbox>
                                        <el-link :underline="false" class="ythl_column__control-reset"
                                            @click="handleCheckAllColumnReset">重置</el-link>
                                    </div>
                                    <!--  -->
                                    <div class="ythl_column__control-list">
                                        <template v-for="(
                                                item, index
                                            ) in _tableColumn">
                                            <div :key="`select_column_${index}`" class="select_column_item">
                                                <el-checkbox v-model="item.isShow" @change="
                                                    changeColumnSelect(
                                                        item,
                                                        index
                                                    )
                                                    ">{{
                                                        item.label
                                                    }}</el-checkbox>
                                                <!-- <div>
													<el-tooltip class="item" effect="dark" content="固定在列首" placement="top">
														<el-link icon="el-icon-upload2" :underline="false" class="ythlc_treecustom_icon"></el-link>
													</el-tooltip>
													<el-tooltip class="item" effect="dark" content="固定在列尾" placement="top">
														<el-link icon="el-icon-download" :underline="false" class="ythlc_treecustom_icon"></el-link>
													</el-tooltip>
												</div>-->
                                            </div>
                                        </template>
                                    </div>
                                    <!--  -->
                                </div>
                                <el-link slot="reference" icon="el-icon-setting" :underline="false"
                                    class="ythl_icon__item"></el-link>
                            </el-popover>
                        </el-tooltip>
                        <el-tooltip class="item" effect="dark" content="全屏" placement="top">
                            <el-link v-if="action.indexOf('tableFullScreen') > -1" icon="el-icon-rank"
                                :underline="false" class="ythl_icon__item" @click="handleFullscreen"></el-link>
                        </el-tooltip>
                    </div>
                </div>
            </div>
            <!--  -->
            <div v-loading="loading" element-loading-background="rgba(255, 255, 255, 0.8)" class="yd_table__table_box">
                <!-- table -->
                <div class="yd_table__table">
                    <!--  -->
                    <transition name="el-zoom-in-top">
                        <div class="yd_table__select" v-show="tableSelection.length">
                            <div class="yts_total">
                                <span>已选择</span>
                                <span class="yts_total__num">{{
                                    tableSelection.length
                                }}</span>
                                <span>项</span>
                            </div>
                            <div class="yts__cancel" @click="deselect">
                                取消选择
                            </div>
                        </div>
                    </transition>
                    <!--  -->
                    <el-table :row-key="rowKey" :border="border" :data="tableData" :size="tableSize" class="ytt_table"
                        @selection-change="handleSelectionChange" ref="ydtable" :header-cell-style="{
                            background: '#fafafa',
                            color: '#d2d2d2',
                        }" :tree-props="treeProps">
                        <!-- <el-table-column type="selection" width="55" align="center"></el-table-column> -->
                        <template v-for="(item, index) in columnSelect">
                            <el-table-column :type="item.type" :prop="item.index"
                                :formatter="item.formatter && item.formatter" :label="item.label" :width="item.width"
                                :fixed="item.fixed" :key="`table_item${index}`"
                                :show-overflow-tooltip="item.showtooltip" :align="item.align">
                                <template v-slot="scope" v-if="item.scopedSlots &&
                                    item.scopedSlots.customRender
                                    ">
                                    <slot :name="item.scopedSlots.customRender" :row="scope.row" :column="tableColumn"
                                        v-bind:$index="scope.$index"></slot>
                                </template>
                            </el-table-column>
                        </template>
                        <!--  -->
                        <el-empty slot="empty" :image-size="100"></el-empty>
                        <!--  -->
                    </el-table>
                </div>
                <!-- pagination  -->
                <div class="yd_table__pagination">
                    <el-pagination :small="tableSize == 'mini'" background :current-page="pagination.pageNo"
                        :page-sizes="[10, 20, 30, 40]" :page-size="pagination.pageSize"
                        layout="total, sizes, prev, pager, next, jumper" :total="pagination.total"
                        @size-change="handleSizeChange" @current-change="handleCurrentChange"></el-pagination>
                </div>
            </div>
            <!-- import -->
            <el-dialog title="导入数据" :visible.sync="importData.visible" width="440px" :modal-append-to-body="false">
                <div>
                    <el-form label-width="100px">
                        <el-form-item label="文件名：">
                            <el-upload action="/" :limit="1"
                                accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                                :auto-upload="false">
                                <el-button size="small" plain style="border-style: dashed">选择文件</el-button>
                            </el-upload>
                        </el-form-item>
                        <el-form-item label="文件类型：">*.xlsx</el-form-item>
                        <el-form-item label="参数设置：">
                            <el-radio-group v-model="importData.type">
                                <el-radio label="add">新增</el-radio>
                                <el-radio label="update">更新</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-form>
                </div>
                <div slot="footer">
                    <el-button @click="importData.visible = false">取 消</el-button>
                    <el-button type="primary">导 入</el-button>
                </div>
            </el-dialog>
            <!-- export -->
            <el-dialog :modal-append-to-body="false" title="导出数据" :visible.sync="exportData.visible" width="660px">
                <el-form label-width="230px">
                    <el-form-item label="文件名：">
                        <el-input style="width: 217px" v-model="exportData.name" />
                    </el-form-item>
                    <el-form-item label="保存类型：">
                        <el-select placeholder="请选择" v-model="exportData.type">
                            <el-option label="Excel 工作簿(*.xls)" value=".xls"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="标题：">
                        <el-input style="width: 217px" v-model="exportData.title" />
                    </el-form-item>
                    <el-form-item label="选择数据：">
                        <el-select placeholder="请选择" v-model="exportData.data">
                            <el-option label="选中数据（当前页数据）" value="current"></el-option>
                            <el-option label="选中数据（当前页选中的数据）" value="select"></el-option>
                            <el-option label="全量数据（包括所有分页的数据）" value="all"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="选择字段：">
                        <el-checkbox-group v-model="exportData.field">
                            <el-checkbox v-for="(item, index) in _tableColumn" :label="item.index"
                                :key="`field_${index}`">{{
                                    `${item.label}` }}</el-checkbox>
                        </el-checkbox-group>
                    </el-form-item>
                </el-form>
                <div slot="footer">
                    <el-button @click="exportData.visible">取 消</el-button>
                    <el-button type="primary" @click="outExport">导 出</el-button>
                </div>
            </el-dialog>
            <!-- print -->
            <el-dialog title="打印数据" :modal-append-to-body="false" :visible.sync="printData.visible" width="660px">
                <div>
                    <el-form label-width="230px">
                        <el-form-item label="标题：">
                            <el-input style="width: 217px" v-model="printData.title" />
                        </el-form-item>
                        <el-form-item label="选择数据：">
                            <el-select placeholder="请选择" v-model="printData.data">
                                <el-option label="选中数据（当前页选中数据）" value="current"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="选择字段：">
                            <el-checkbox-group v-model="printData.field">
                                <el-checkbox v-for="(item, index) in _tableColumn" :label="item.index"
                                    :key="`field_${index}`">{{
                                        `${item.index}（${item.label}）`
                                    }}</el-checkbox>
                            </el-checkbox-group>
                        </el-form-item>
                    </el-form>
                </div>
                <div slot="footer">
                    <el-button @click="exportData.visible">取 消</el-button>
                    <el-button type="primary" @click="print">打 印</el-button>
                </div>
            </el-dialog>
        </div>
    </div>
</template>
<script>
// import Sortable from 'sortablejs';
export default {
    name: "ydTable",
    props: {
        data: {
            type: Function,
            required: true,
        },
        tableColumn: {
            type: Array,
            default: () => [],
        },
        describe: {
            type: String,
            default: "",
        },
        action: {
            type: Array,
            // ["import","export","print","refresh","tableSize","tableSet","tableFullScreen"]
            default: () => ["refresh", "tableSize"],
        },
        treeProps: {
            type: Object,
            default: () => { },
        },
        rowKey: {
            type: String,
            default: "",
        },
        border: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            loading: false,
            refreshLoading: false,
            search: {},
            // 表格size
            tableSize: "medium",
            // 表格选中
            tableSelection: [],
            //列是否全选
            isColumnSelectAll: true,
            // 列全选
            columnSelectAll: false,
            // 表格选中的列
            columnSelect: [],
            // 表格中所有的列
            columnAll: [],
            //
            importData: {
                visible: false,
                type: "add",
            },
            exportData: {
                visible: false,
                name: "",
                title: "",
                type: ".xls",
                data: "current",
                field: [],
            },
            printData: {
                visible: false,
                data: "current",
                title: "",
                field: [],
            },
            pagination: {
                pageSize: 10,
                total: 0,
                pageNo: 1,
            },
            tableData: [],
        };
    },
    created() {
        this.loadData();
        this.columnSelect = this.tableColumn.filter((i) => i.isShow);
        this._tableColumn = this.tableColumn.map((i) => i);
        this.columnAll = this.tableColumn.map((item) => {
            return {
                ...item,
                isShow: true,
            };
        });

        // this.$nextTick(() => {
        // 	this.initDraggable()

        // })
    },
    methods: {
        handleSizeChange(v) {
            this.pagination.pageSize = v;
            this.loadData();
        },
        handleCurrentChange(v) {
            this.pagination.pageNo = v;
            this.loadData();
        },
        // 加载数据方法
        loadData() {
            this.loading = true;
            const _this = this;
            try {
                const parameter = {
                    pageSize: this.pagination.pageSize,
                    pageNo: this.pagination.pageNo,
                }
                const result = this.data(parameter);

                result
                    .then((res) => {
                        const { pageNo, pageSize, total, list } = res.data;
                        // 为防止删除数据后导致页面当前页面数据长度为 0 ,自动翻页到上一页

                        if (list.length == 0 && pageNo > 1) {
                            _this.pagination.pageNo--;
                            _this.loadData();
                            return;
                        }
                        _this.pagination.pageNo =
                            pageNo || this.pagination.pageNo;
                        _this.pagination.pageSize =
                            pageSize || this.pagination.pageSize;
                        _this.pagination.total = total;
                        _this.tableData = list;
                    })
                    .finally(() => {
                        _this.$nextTick(() => {
                            _this.loading = false;
                            _this.refreshLoading = false;
                        });
                    });
            } catch (error) {
                _this.loading = false;
                _this.refreshLoading = false;
            }
        },
        // 打印
        print() {
            this.printData.visible = false;
            const title = document.title;

            window.document.title = this.printData.title;
            setTimeout(() => {
                window.print();
                window.document.title = title;
            }, 500);
        },
        // 表格导出
        outExport() {
            const _this = this;

            let str = _this.exportData.title
                ? `${_this.exportData.title}\n`
                : "";
            // const jsonData = [{ 'lable1': '', 'lable2': '', }]
            const objArr = this._tableColumn.filter((i) => {
                return _this.exportData.field.some((j) => j == i.index);
            }),
                jsonData = this.tableData.map((item) => {
                    const strip = [];

                    for (const k in item) {
                        const c = objArr.find((i) => i.index == k);

                        if (c) {
                            strip.push({
                                [c.label]: item[k],
                            });
                        }
                    }
                    let obj = {};

                    if (strip.length) {
                        strip.forEach((i) => {
                            obj = { ...obj, ...i };
                        });
                    }
                    return obj;
                });
            // return console.log(arr)

            for (const k in jsonData[0]) {
                str += k + ",";
            }
            str = str.slice(0, str.length - 1) + "\n";

            // 增加\t为了不让表格显示科学计数法或者其他格式
            for (let i = 0; i < jsonData.length; i++) {
                for (const item in jsonData[i]) {
                    str += `${jsonData[i][item] + "\t"},`;
                }
                str += "\n";
            }
            // console.log(str);
            // encodeURIComponent解决中文乱码
            const uri =
                "data:application/vnd.ms-excel;charset=utf-8,\ufeff" +
                encodeURIComponent(str),
                // 通过创建a标签实现
                link = document.createElement("a");

            link.href = uri;
            // 对下载的文件命名
            link.download = `${_this.exportData.name}.xls` || "test.xls";
            document.body.appendChild(link);
            link.click();
            _this.exportData.visible = false;
            document.body.removeChild(link);
        },
        // 表格数据数量
        handleTableData(type) {
            const field = this._tableColumn.filter((i) => i.isShow == true);

            switch (type) {
                case "import":
                    this.importData.visible = true;
                    break;
                case "export":
                    this.exportData.visible = true;
                    this.exportData.field = field.map((i) => i.index);
                    break;
                case "print":
                    this.printData.visible = true;
                    this.printData.field = field.map((i) => i.index);
                    break;

                default:
                    break;
            }
        },
        // 表格拖动
        initDraggable() {
            // http://www.sortablejs.com/options.html
            const _this = this,
                dom = document.querySelector(".el-table__body-wrapper tbody");

            this.draggable = Sortable.create(dom, {
                animation: 150,
                ghostClass: "blue-background-class",
                onEnd(evt) {
                    _this.tableData.splice(
                        evt.newIndex,
                        0,
                        _this.tableData.splice(evt.oldIndex, 1)[0]
                    );
                    const newArray = _this.tableData.slice(0);

                    _this.tableData = [];
                    _this.$nextTick(function () {
                        _this.tableData = newArray;
                    });
                },
            });
        },
        // 搜索
        handleSearch() {
            console.log("table搜索");
            this.handleRefresh(true);
        },
        // 刷新
        handleRefresh(bool = false) {
            this.refreshLoading = true;
            if (bool) {
                this.pagination = Object.assign(
                    {},
                    {
                        pageNo: 1,
                        pageSize: this.pagination.pageSize || 10,
                        total: 0,
                    }
                );
            }
            this.loadData();
        },
        // 取消选择
        deselect() {
            this.$refs.ydtable.clearSelection();
        },
        // 表格选择
        handleSelectionChange(val) {
            this.tableSelection = val;
            this.$emit("selectionChange", val);
        },
        // 列改变
        changeColumnSelect(item, index) {
            if (item.isShow) {
                this.columnSelect.splice(index, 0, item);
            } else {
                const findIndex = this.columnSelect.findIndex(
                    (i) => i.label == item.label
                );

                this.columnSelect.splice(findIndex, 1);
            }
            const boole = this._tableColumn.every((i) => i.isShow == true);

            this.isColumnSelectAll = this._tableColumn.every(
                (i) => i.isShow == false
            )
                ? false
                : !boole;
            this.columnSelectAll = boole;
        },
        // 列全选，
        handleCheckAllColumn(val) {
            this.columnSelect = val ? [...this._tableColumn] : [];
            this.isColumnSelectAll = false;
            this._tableColumn.forEach((i) => {
                i.isShow = val;
                if (val) {
                    //
                } else {
                    //
                }
            });
        },
        //列重置
        handleCheckAllColumnReset() { },
        // 表格大小
        handleTableSize(size) {
            this.tableSize = size;
        },
        //全屏
        handleFullscreen() {
            const element = this.$refs.ydTableComp;

            if (this.isFullScreen()) {
                // element.style.padding="0"
                this.exitFullscreen();
            } else {
                // element.style.padding="20px"
                this.launchIntoFullscreen(element);
            }
        },
        //全屏
        launchIntoFullscreen(element) {
            if (element.requestFullscreen) {
                element.requestFullscreen();
            } else if (element.mozRequestFullScreen) {
                element.mozRequestFullScreen();
            } else if (element.webkitRequestFullscreen) {
                element.webkitRequestFullscreen();
            } else if (element.msRequestFullscreen) {
                element.msRequestFullscreen();
            }
        },
        // 退出全屏
        exitFullscreen() {
            if (document.exitFullscreen) {
                document.exitFullscreen();
            } else if (document.mozCancelFullScreen) {
                document.mozCancelFullScreen();
            } else if (document.webkitExitFullscreen) {
                document.webkitExitFullscreen();
            }
        },
        // 判断当前是否全屏
        isFullScreen() {
            return Boolean(
                document.fullscreen ||
                document.mozFullScreen ||
                document.webkitIsFullScreen ||
                document.webkitFullScreen ||
                document.msFullScreen
            );
        },
    },
};
</script>

<style lang="scss" scoped>
@keyframes load {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.yd_table {
    position: relative;
    overflow: hidden;

    // background-color: #fff;
    /* 针对dom的全屏设置 */
    &:not(:root):fullscreen {
        min-height: 100vh;
        overflow: auto;
        background: #fff;
    }

    .ytt_table {
        width: 100%;
        transition: all 0.25s;
        border-top: 1px solid #ebeef5;
    }
}

.yd_table__search {
    // margin-bottom: 16px;
    // padding: 24px 24px 0;
    background: #fff;
}

.yd_table__select {
    margin-bottom: 16px;
    padding: 12px 24px;
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;
    box-sizing: border-box;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: "tnum", "tnum";
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    word-wrap: break-word;
    border-radius: 2px;
    transition: all 0.25s;

    .yts_total {
        .yts_total__num {
            padding: 0 8px;
        }
    }

    .yts__cancel {
        cursor: pointer;
        color: #1890ff;
    }
}

.yd_table__table_box {
    transition: all 0.25s;
}

//
.yd_table__content {
    border-top: 1px solid #ebeef5;
    background: #fff;
    // padding: 0 24px;
    transition: all 0.25s;
    overflow: hidden;
}

//
.yd_table__handle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;

    .yd_table__handle-list {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .ythl_btn {
            margin-right: 16px;
        }
    }

    .ythl_icon {
        .ythl_icon__item {
            margin: 0 4px;
        }

        .ythl_icon_refresh {
            animation: load 1s linear 0s infinite;
        }
    }
}

.ythl_column__control {
    .ythl_column__control-header {
        border-bottom: 1px solid #ebeef5;
        display: flex;
        justify-content: space-between;
        padding: 0 4px 12px 4px;

        .ythl_column__control-reset {
            color: #1890ff;
        }
    }

    .ythl_column__control-list {
        padding: 12px 0;

        .select_column_item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px 4px;
            min-height: 24px;

            &:hover {
                background-color: #f5f7fa;

                .ythlc_treecustom_icon {
                    display: inline-block;
                    color: #1890ff;
                    display: inline-block;
                }
            }
        }
    }
}

.yd_table__pagination {
    margin: 16px 0;
    text-align: right;
}

.ythlc_treecustom_icon {
    font-size: 14px;
    padding: 0 3px;
    transition: all 0.25s;
    padding-left: 4px;
    display: none;
}
</style>

<style lang="scss">
@media print {

    .yd_table__search,
    .yd_table__handle,
    .yd_table__pagination {
        display: none !important;
    }

    .yd_table__content {
        padding: 0 !important;
    }

    .el-table--border {
        border: 1px solid #ebeef5 !important;
    }
}

.blue-background-class {
    background-color: #c8ebfb !important;
}

.ythl_icon__item i {
    font-size: 20px;
}
</style>
