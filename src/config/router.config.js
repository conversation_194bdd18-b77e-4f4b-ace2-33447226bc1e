import BaseLayout from "@/components/Layouts/base-layout";

// 基础路由
export const constantRouterMap = [
    {
        path: "/login",
        component: () =>
            import(/* webpackChunkName: "login" */ "@/views/users/login"),
    },
];
// 异步路由
export const asyncRouterMap = [
    {
        path: "/",
        component: BaseLayout,
        redirect: "/home",
        children: [
            {
                path: "/home",
                name: "home",
                meta: {
                    title: "首页",
                    icon: "el-icon-monitor",
                    keepAlive: true,
                },
                component: () => import("@/views/home"),
            },
            // 学校管理
            {
                path: "/schoolManage",
                name: "schoolManage",
                meta: {
                    title: "学校管理",
                    icon: "el-icon-setting",
                },
                component: {
                    render: (h) => h("router-view"),
                },
                redirect: "/schoolManage/schoolSystem",
                children: [
                    {
                        path: "/schoolManage/schoolSystem",
                        component: () =>
                            import("@/views/schoolManage/schoolSystem"),
                        name: "schoolSystem",
                        meta: {
                            title: "学校管理",
                            icon: "el-icon-setting",
                        },
                    },
                    {
                        path: "/schoolManage/librarySystem",
                        component: () =>
                            import("@/views/schoolManage/librarySystem"),
                        name: "librarySystem",
                        meta: {
                            title: "图书馆管理",
                            icon: "el-icon-setting",
                        },
                    },
                    {
                        path: "/schoolManage/schoolCheck",
                        component: () =>
                            import("@/views/schoolManage/schoolCheck"),
                        name: "schoolCheck",
                        meta: {
                            title: "学校审核",
                            icon: "el-icon-setting",
                        },
                    },
                    {
                        path: "/schoolManage/thirdPartyConfiguration",
                        component: () =>
                            import(
                                "@/views/schoolManage/thirdPartyConfiguration"
                            ),
                        name: "thirdPartyConfiguration",
                        meta: {
                            title: "数据接入源配置",
                            icon: "el-icon-setting",
                        },
                    },
                ],
            },

            // // 消息模板
            // {
            //     path: "/messageTemplate/list",
            //     component: () => import("@/views/messageTemplate/index"),
            //     name: "message_template",
            //     hidden: false,
            //     meta: {
            //         title: "消息模板",
            //         icon: "dashboard",
            //         permission: ["message_template"],
            //     },
            // },
            // 公告管理
            {
                path: "/announce",
                component: () => import("@/views/announce/index"),
                name: "announce",
                hidden: false,
                meta: {
                    title: "公告管理",
                    icon: "el-icon-help",
                },
            },
            {
                path: "/xyfManage",
                name: "xyfManage",
                meta: {
                    title: "校易付管理",
                    icon: "el-icon-set-up",
                },
                component: {
                    render: (h) => h("router-view"),
                },
                redirect: "/xyfManage/facilitator",
                children: [
                    {
                        path: "/xyfManage/facilitator",
                        component: () =>
                            import("@/views/xyfManage/facilitator/index.vue"),
                        name: "facilitator",
                        hidden: false,
                        meta: {
                            title: "服务商管理",
                            icon: "el-icon-help",
                        },
                    },
                    {
                        path: "/xyfManage/payerManage",
                        component: () =>
                            import("@/views/xyfManage/payerManage/index.vue"),
                        name: "payerManage",
                        hidden: false,
                        meta: {
                            title: "支付商管理",
                            icon: "el-icon-help",
                        },
                    },
                    {
                        path: "/xyfManage/expendType",
                        component: () =>
                            import("@/views/xyfManage/expendType/index.vue"),
                        name: "expendType",
                        hidden: false,
                        meta: {
                            title: "消费类型管理",
                            icon: "el-icon-help",
                        },
                    },
                ],
            },
            // 版本管理
            {
                path: "/newVersionManagement",
                component: () => import("@/views/newVersionManagement/index"),
                name: "newVersionManagement",
                hidden: false,
                meta: {
                    title: "版本管理",
                    icon: "el-icon-help",
                },
            },
            // 资源模板管理
            {
                path: "/templateManage",
                component: () => import("@/views/templateManage/index.vue"),
                name: "templateManage",
                hidden: false,
                meta: {
                    title: "资源模板管理",
                    icon: "el-icon-picture-outline",
                },
            },
            // 帮助中心
            {
                path: "/helpCore",
                component: () => import("@/views/helpCore/index.vue"),
                name: "helpCore",
                hidden: false,
                meta: {
                    title: "帮助中心",
                    icon: "el-icon-picture-outline",
                },
            },
            // 应用管理
            {
                path: "/application",
                name: "application",
                meta: {
                    title: "应用管理",
                    icon: "el-icon-set-up",
                },
                component: {
                    render: (h) => h("router-view"),
                },
                redirect: "/application/list",
                children: [
                    // {
                    //     path: "/application/supplier",
                    //     component: () => import("@/views/application/supplier"),
                    //     name: "applicationSupplier",
                    //     meta: {
                    //         title: "供应商管理",
                    //     },
                    // },
                    // {
                    //     path: "/application/list",
                    //     component: () => import("@/views/application/list"),
                    //     name: "applicationList",
                    //     hidden: false,
                    //     meta: {
                    //         title: "应用列表",
                    //     },
                    // },
                    // {
                    //     path: "/application/grouping",
                    //     component: () => import("@/views/application/grouping"),
                    //     name: "applicationGrouping",
                    //     hidden: false,
                    //     meta: {
                    //         title: "应用分组",
                    //     },
                    // },
                    // {
                    //     path: "/application/menuManagement",
                    //     component: () =>
                    //         import("@/views/application/menuManagement"),
                    //     name: "menu_system",
                    //     hidden: false,
                    //     meta: {
                    //         title: "菜单管理",
                    //     },
                    // },
                    {
                        path: "/application/classManagement",
                        component: () =>
                            import("@/views/application/classManagement"),
                        name: "classManagement",
                        hidden: false,
                        meta: {
                            title: "班牌管理",
                            icon: "el-icon-money",
                        },
                    },
                    {
                        path: "/application/openingManagement",
                        component: () =>
                            import("@/views/application/openingManagement"),
                        name: "openingManagement",
                        hidden: false,
                        meta: {
                            title: "开通管理",
                            icon: "el-icon-money",
                        },
                    },
                    // {
                    //     path: "/application/registrationCode",
                    //     component: () =>
                    //         import("@/views/application/registrationCode"),
                    //     name: "registrationCode",
                    //     hidden: false,
                    //     meta: {
                    //         title: "注册码管理",
                    //     },
                    // },
                    // {
                    //     path: "/application/versionManagement",
                    //     component: () =>
                    //         import("@/views/application/versionManagement"),
                    //     name: "versionManagement",
                    //     hidden: false,
                    //     meta: {
                    //         title: "版本管理",
                    //         permission: ["versionManagement"],
                    //     },
                    // },
                    // {
                    //     path: "/application/versionManagementChild",
                    //     component: () =>
                    //         import(
                    //             "@/views/application/versionManagementChild"
                    //         ),
                    //     name: "versionManagementChild",
                    //     hidden: true,
                    //     meta: {
                    //         title: "学校版本管理",
                    //         affix: false,
                    //         oCache: true,
                    //         permission: ["versionManagementChild"],
                    //     },
                    // },
                    // {
                    //     path: "/application/logManagement",
                    //     component: () =>
                    //         import("@/views/application/logManagement"),
                    //     name: "logManagement",
                    //     hidden: false,
                    //     meta: {
                    //         title: "日志管理",
                    //         affix: false,
                    //         oCache: true,
                    //         permission: ["logManagement"],
                    //     },
                    // },

                    {
                        path: "/application/kitManagement",
                        component: () =>
                            import("@/views/application/kitManagement"),
                        name: "kitManagement",
                        hidden: false,
                        meta: {
                            title: "套件分组管理",
                            affix: false,
                            oCache: true,
                            permission: ["kitManagement"],
                        },
                    },

                    {
                        path: "/application/bulletinManagement",
                        component: () =>
                            import("@/views/application/bulletinManagement"),
                        name: "bulletinManagement",
                        hidden: false,
                        meta: {
                            title: "看板模版管理",
                            affix: false,
                            oCache: true,
                            permission: ["bulletinManagement"],
                        },
                    },
                ],
            },
            // 日志管理
            {
                path: "/journal",
                name: "journal",
                meta: {
                    title: "日志管理",
                    icon: "el-icon-set-up",
                },
                component: {
                    render: (h) => h("router-view"),
                },
                redirect: "/journal/loginLog",
                children: [
                    {
                        path: "/journal/loginLog",
                        component: () => import("@/views/journal/loginLog.vue"),
                        name: "loginLog",
                        hidden: false,
                        meta: {
                            title: "登录日志",
                            icon: "el-icon-money",
                        },
                    },
                    {
                        path: "/journal/operationLog",
                        component: () => import("@/views/journal/operationLog"),
                        name: "operationLog",
                        hidden: false,
                        meta: {
                            title: "操作日志",
                            icon: "el-icon-money",
                        },
                    },
                ],
            },
            // 人脸激活码
            // {
            //     path: "/faceSystem/list",
            //     component: () => import("@/views/faceSystem/index.vue"),
            //     hidden: false,
            //     name: "face_system",
            //     meta: {
            //         title: "人脸授权管理",
            //     },
            // },
            // 系统管理
            // {
            //     path: "/systemAdministration",
            //     component: {
            //         render: (h) => h("router-view"),
            //     },
            //     meta: {
            //         title: "系统管理",
            //         icon: "dashboard",
            //     },
            //     redirect: "/systemAdministration/department",
            //     name: "system_management",
            //     children: [
            //         {
            //             path: "/systemAdministration/dictionariesType",
            //             component: () =>
            //                 import(
            //                     "@/views/systemAdministration/dictionariesType"
            //                 ),
            //             name: "dictionariesType",
            //             meta: {
            //                 title: "字典类型",
            //             },
            //         },
            //         {
            //             path: "/systemAdministration/department",
            //             component: () =>
            //                 import("@/views/systemAdministration/department"),
            //             name: "department_system",
            //             hidden: false,
            //             meta: {
            //                 title: "部门管理",
            //                 affix: false,
            //                 oCache: true,
            //                 permission: ["department_system"],
            //             },
            //         },
            //         {
            //             path: "/systemAdministration/permission",
            //             component: () =>
            //                 import("@/views/systemAdministration/permission"),
            //             name: "permission_system",
            //             hidden: false,
            //             meta: {
            //                 title: "权限管理",
            //                 affix: false,
            //                 oCache: true,
            //                 permission: ["permission_system"],
            //             },
            //         },
            //         {
            //             path: "/systemAdministration/role",
            //             component: () =>
            //                 import("@/views/systemAdministration/role"),
            //             name: "role_system",
            //             hidden: false,
            //             meta: {
            //                 title: "角色管理",
            //                 affix: false,
            //                 oCache: true,
            //                 permission: ["role_system"],
            //             },
            //         },
            //         {
            //             path: "/systemAdministration/user",
            //             component: () =>
            //                 import("@/views/systemAdministration/user"),
            //             name: "user_system",
            //             hidden: false, // 是否菜单栏显示
            //             meta: {
            //                 title: "用户管理",
            //                 affix: false, // tag-view显示
            //                 oCache: true, // true 不会缓存页面
            //                 permission: ["user_system"],
            //             },
            //         },
            //     ],
            // },
            // 官网管理
            {
                path: "/guanwangManagement",
                name: "guanwangManagement",
                component: {
                    render: (h) => h("router-view"),
                },
                redirect: "/guanwangManagement/cooperation",
                meta: {
                    title: "官网管理",
                    icon: "el-icon-takeaway-box",
                },
                children: [
                    {
                        path: "/guanwangManagement/rotationChart",
                        component: () =>
                            import("@/views/guanwangManagement/rotationChart"),
                        name: "rotationChart",
                        meta: {
                            title: "banner管理",
                            icon: "el-icon-takeaway-box",
                        },
                    },
                    {
                        path: "/guanwangManagement/friendChain",
                        component: () =>
                            import("@/views/guanwangManagement/friendChain"),
                        name: "friendChain",
                        meta: {
                            title: "友链管理",
                            icon: "el-icon-takeaway-box",
                        },
                    },
                    {
                        path: "/guanwangManagement/cooperation",
                        component: () =>
                            import("@/views/guanwangManagement/cooperation"),
                        name: "cooperation",
                        meta: {
                            title: "合作管理",
                            icon: "el-icon-takeaway-box",
                        },
                    },
                    {
                        path: "/guanwangManagement/newslist",
                        component: () =>
                            import("@/views/guanwangManagement/newslist"),
                        name: "newslist",
                        meta: {
                            title: "新闻资讯",
                            icon: "el-icon-takeaway-box",
                        },
                    },
                    // {
                    //     path: "/guanwangManagement/newslist/create",
                    //     component: () =>
                    //         import(
                    //             "@/views/guanwangManagement/newsCreateOrEdit"
                    //         ),
                    //     name: "newslist_create",
                    //     hidden: true,
                    //     meta: {
                    //         title: "新闻资讯-新增",
                    //         icon: "dashboard",
                    //     },
                    // },
                    // {
                    //     path: "/guanwangManagement/newslist/:id",
                    //     component: () =>
                    //         import(
                    //             "@/views/guanwangManagement/newsCreateOrEdit"
                    //         ),
                    //     name: "newslist_edit",
                    //     hidden: true,
                    //     meta: {
                    //         title: "新闻资讯-编辑",
                    //         icon: "dashboard",
                    //     },
                    // },
                ],
            },
            // // 报备系统
            // {
            //     path: "/reportingSystem/list",
            //     component: () => import("@/views/reportingSystem/index"),
            //     name: "reportingSystem",
            //     meta: {
            //         title: "报备系统",
            //         icon: "dashboard",
            //     },
            // },
            // 注册码管理
            {
                path: "/gateRegistrationCode",
                component: () => import("@/views/gateRegistrationCode/index"),
                name: "gateRegistrationCode",
                meta: {
                    title: "注册码管理",
                    icon: "el-icon-tickets",
                },
            },
            // 激活码管理
            {
                path: "/gateActivationCode",
                component: () => import("@/views/gateActivationCode/index"),
                name: "gateActivationCode",
                meta: {
                    title: "激活码管理",
                    icon: "el-icon-document",
                },
            },
            // 中性版配置
            {
                path: "/neutralConfiguration",
                name: "neutralConfiguration",
                meta: {
                    title: "中性版配置",
                    icon: "el-icon-setting",
                },
                hidden: false,
                component: () => import("@/views/neutralConfiguration/index"),
            },
            // 校园门户管理
            {
                path: "/schoolPortal",
                component: () => import("@/views/schoolPortal/index"),
                name: "schoolPortal",
                meta: {
                    title: "校园门户",
                    icon: "el-icon-document",
                },
            },
            // 图书馆设备管理
            {
                component: {
                    render: (h) => h("router-view"),
                },
                path: "/device-management",
                name: "deviceManagement",
                meta: {
                    title: "设备管理",
                    icon: "el-icon-document",
                },
                children: [
                    {
                        path: "/device-management/libraryManage",
                        name: "libraryManage",
                        component: () => import("@/views/libraryManage/index"),
                        meta: {
                            title: "图书馆设备管理",
                            icon: "el-icon-document",
                        },
                    },
                    {
                        path: "/device-management/deviceType",
                        name: "deviceType",
                        component: () =>
                            import("@/views/device-management/deviceType.vue"),
                        meta: {
                            title: "设备类型",
                            icon: "el-icon-document",
                        },
                    },
                    {
                        path: "/device-management/deviceList",
                        name: "deviceList",
                        component: () =>
                            import("@/views/device-management/deviceList.vue"),
                        meta: {
                            title: "设备列表",
                            icon: "el-icon-document",
                        },
                    },
                    {
                        path: "/device-management/configuration",
                        name: "deviceList",
                        component: () =>
                            import(
                                "@/views/device-management/configuration.vue"
                            ),
                        meta: {
                            title: "设备配置",
                            icon: "el-icon-document",
                        },
                    },
                ],
            },
            // 系统设置
            {
                path: "/systemSet",
                component: () => import("@/views/systemSet/index.vue"),
                name: "systemSet",
                meta: {
                    title: "系统设置",
                    icon: "el-icon-document",
                },
                children: [
                    {
                        path: "/systemSet/organization",
                        name: "organization",
                        component: () =>
                            import("@/views/systemSet/organization/index"),
                        meta: {
                            title: "组织管理",
                            icon: "el-icon-document",
                        },
                    },
                    {
                        path: "/systemSet/user",
                        name: "user",
                        component: () => import("@/views/systemSet/user/index"),
                        meta: {
                            title: "用户管理",
                            icon: "el-icon-document",
                        },
                    },
                    {
                        path: "/systemSet/role",
                        name: "role",
                        component: () => import("@/views/systemSet/role/index"),
                        meta: {
                            title: "角色管理",
                            icon: "el-icon-document",
                        },
                    },
                    {
                        path: "/systemSet/log",
                        name: "log",
                        meta: {
                            title: "日志管理",
                            icon: "el-icon-document",
                        },
                        component: () => import("@/views/systemSet/log/index"),
                    },
                    {
                        path: "/systemSet/dictionaryManaged",
                        name: "dictionaryManaged",
                        meta: {
                            title: "字典管理",
                            icon: "el-icon-document",
                        },
                        component: () =>
                            import("@/views/systemSet/dictionaryManaged/index"),
                    },
                    {
                        path: "/systemSet/menuConfig",
                        name: "menuConfig",
                        component: () =>
                            import("@/views/systemSet/menuConfig/index"),
                        meta: {
                            title: "菜单配置",
                            icon: "el-icon-document",
                        },
                    },
                ],
            },
            {
                path: "*",
                component: () =>
                    import(
                        /* webpackChunkName: "error" */
                        "@/components/Error/404.vue"
                    ),
            },
        ],
    },
];
