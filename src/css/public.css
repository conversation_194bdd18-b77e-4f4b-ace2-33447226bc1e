.el-select .el-input__inner {
  height: 34px;
  line-height: 34px;
  border-color: #DDE0E3;
  color: #444B59;
  text-align: center;
}
.el-select .el-input__icon {
  line-height: 34px;
}
.el-select .el-select__caret {
  color: #444B59 !important;
}
.el-input__inner:focus {
  border-color: #4877fb !important;
}
.el-input.is-focus .el-input__inner {
  border-color: #4877fb !important;
}
.el-input .el-input__inner {
  height: 34px;
  line-height: 34px;
  color: #444B59;
  text-align: center;
}
.blueCheckbox .el-checkbox__input.is-checked .el-checkbox__inner,
.blueCheckbox .el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #4877FB;
  border-color: #4877FB;
}
.blueCheckbox .el-checkbox__inner:hover {
  border-color: #4877FB;
}
.el-table .el-table__body {
  color: #303B4D;
}
.el-table .el-table__header th {
  background-color: #E8EBEF;
  border-color: #DDE0E3;
  color: #444B59;
  padding: 7px 0 !important;
}
.el-table td,
.el-table th {
  padding: 6px 0 !important;
}
.el-table td {
  padding: 6px 0 !important;
}
.mainBox .el-tabs__active-bar {
  background-color: #4877FB;
}
.mainBox .el-tabs__item.is-active,
.mainBox .el-tabs__item:hover {
  color: #4877FB;
}
.radioOne .radioTxt {
  color: #303942;
}
.radioOne .el-radio__input.is-checked .el-radio__inner {
  border-color: #4877FB;
  background: #4877FB;
}
.radioOne .el-radio {
  margin-right: 20px;
}
.radioOne .el-radio__input.is-checked + .el-radio__label {
  color: #303942;
}
.popupOne {
  max-height: 85vh;
  overflow: auto;
}
.popupOne .el-dialog__header {
  background-color: #E8EBEF;
  padding: 15px 40px;
}
.popupOne .el-dialog__header .el-dialog__title,
.popupOne .el-dialog__header .el-dialog__headerbtn > i {
  color: #303942;
  font-weight: bold;
}
.popupOne .el-dialog__header .el-dialog__headerbtn {
  top: 15.5px;
  right: 40px;
}
.popupOne .el-dialog__header .el-dialog__headerbtn i {
  font-size: 22px;
}
.popupOne .el-dialog__body {
  padding: 40px;
}
.popupOne .el-dialog__footer {
  text-align: center;
  padding: 0;
  padding-bottom: 40px;
}
.el-pagination {
  margin-top: 40px;
  text-align: center;
}
.el-pagination.is-background .btn-next,
.el-pagination.is-background .btn-prev,
.el-pagination.is-background .el-pager li {
  background-color: #FAFBFE !important;
  color: #9094A0 !important;
  border-radius: 4px !important;
}
.el-pagination.is-background .el-pager li:not(.disabled):hover {
  color: #4877FB !important;
}
.el-pagination.is-background .el-pager li:not(.disabled).active {
  background-color: #4877FB !important;
  color: #fff !important;
}
.textBtn {
  user-select: none;
  cursor: pointer;
}
.mgl20 {
  margin-left: 20px;
}
.mgr20 {
  margin-right: 20px;
}
.mgt20 {
  margin-top: 20px;
}
.mgb20 {
  margin-bottom: 20px;
}
.cRed {
  color: #FF5370 !important;
}
.bgcRed {
  background-color: #FF5370 !important;
}
.bdcRed {
  border-color: #FF5370 !important;
}
.cBlue {
  color: #4877FB !important;
}
.bgcBlue {
  background-color: #4877FB !important;
}
.bdcBlue {
  border-color: #4877FB !important;
}
.floatL {
  float: left;
}
.floatR {
  float: right;
}
.listTitle {
  display: inline-block;
  min-width: 70px;
  text-align: right;
}
.listTitle i {
  color: #FF5370;
  position: relative;
  left: -1em;
}
.eleDate .el-input__suffix {
  right: 25px;
}
.eleDate .el-input__suffix .el-input__icon {
  line-height: 34px;
}
.eleDate .el-input__prefix {
  left: inherit;
  right: 5px;
}
.eleDate .el-input__prefix .el-input__icon {
  line-height: 34px;
}
.eleDate .dash {
  color: #DDE0E3;
  font-size: 22px;
}
.eleDateShort .el-input__suffix {
  right: 17px;
}
.eleDateShort .el-input__suffix .el-input__icon {
  line-height: 34px;
}
.eleDateShort .el-input__prefix {
  left: inherit;
  right: 0px;
}
.eleDateShort .el-input__prefix .el-input__icon {
  line-height: 34px;
}
.eleDateShort .el-input--prefix .el-input__inner {
  padding-left: 0px;
}
.eleDateShort .dash {
  color: #DDE0E3;
  font-size: 22px;
}
.dialogTitle {
  color: #303942;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 30px;
  position: relative;
}
.dialogTitle i {
  color: #FF5370;
  position: absolute;
  left: -1em;
}
.plainTable {
  width: 100%;
}
.plainTable th,
.plainTable td {
  border: 1px solid #DDE0E3;
  line-height: 46px;
  min-height: 46px;
}
.plainTable .el-input__inner {
  border: none;
}
.scopeFlex {
  display: flex;
  align-items: center;
  justify-content: center;
}
.scopeFlex > * {
  width: 50%;
}
.WarFlow {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.WarFlow > * {
  width: 33.33%;
}
/* 实心蓝白字按钮 加 primaryBlue*/
.primaryBlue.el-button--primary:focus,
.primaryBlue.el-button--primary:hover {
  background: #4877fb;
  border-color: #4877fb;
}
.primaryBlue.el-button--primary {
  background-color: #4877fb;
  border-color: #4877fb;
}
.primaryBlue.el-button {
  padding: 8px 18px;
}
.primaryBlue.el-button span {
  font-size: 16px;
}
/* 实心红白字按钮 加 primaryBlue*/
.primaryRed.el-button--primary:focus,
.primaryRed.el-button--primary:hover {
  background: #FF5370;
  border-color: #FF5370;
}
.primaryRed.el-button--primary {
  background-color: #FF5370;
  border-color: #FF5370;
}
.primaryRed.el-button {
  padding: 8px 18px;
}
.primaryRed.el-button span {
  font-size: 16px;
}
/* 实心蓝白字42左边内边距的按钮 加 primaryBlue42*/
.primaryBlue42.el-button--primary:focus,
.primaryBlue42.el-button--primary:hover {
  background: #4877fb;
  border-color: #4877fb;
}
.primaryBlue42.el-button--primary {
  background-color: #4877fb;
  border-color: #4877fb;
}
.primaryBlue42.el-button {
  padding: 8px 42px;
}
.primaryBlue42.el-button span {
  font-size: 16px;
}
/* 朴素蓝框蓝字按钮 加 plainBlue*/
.plainBlue.el-button.is-plain:focus,
.plainBlue.el-button.is-plain:hover {
  border-color: #4877fb;
  color: #4877fb;
}
.plainBlue.el-button {
  padding: 8px 18px;
  border-color: #4877fb;
  color: #4877fb;
}
.plainBlue.el-button span {
  font-size: 16px;
}
