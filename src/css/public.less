// 选择器
.el-select {
    .el-input__inner {
        height: 34px;
        line-height: 34px;
        border-color: #DDE0E3;
        color: #444B59;
        text-align: center;
    }

    .el-input__icon {
        line-height: 34px;
    }

    .el-select__caret {
        color: #444B59 !important;
    }
}


// 输入框
.el-input__inner:focus {
    border-color: #4877fb !important;
}

.el-input.is-focus .el-input__inner {
    border-color: #4877fb !important;
}

.el-input {
    .el-input__inner {
        height: 34px;
        line-height: 34px;
        color: #444B59;

        text-align: center;
    }

}

// 蓝色色调checkbox
.blueCheckbox {

    .el-checkbox__input.is-checked .el-checkbox__inner,
    .el-checkbox__input.is-indeterminate .el-checkbox__inner {
        background-color: #4877FB;
        border-color: #4877FB;
    }

    .el-checkbox__inner:hover {
        border-color: #4877FB;
    }
}


// 列表
.el-table {
    .el-table__body {
        color: #303B4D;
    }

    .el-table__header {
        th {
            background-color: #E8EBEF;
            border-color: #DDE0E3;
            color: #444B59;
            padding: 7px 0 !important;
        }

    }

    td,
    th {
        padding: 6px 0 !important;
    }

    td {
        padding: 6px 0 !important;
    }
}

// 标签页蓝色调
.mainBox .el-tabs__active-bar {
    background-color: #4877FB;
}

.mainBox .el-tabs__item.is-active,
.mainBox .el-tabs__item:hover {
    color: #4877FB;
}

// 单选框 公共
.radioOne {
    .radioTxt {
        color: #303942;
    }

    .el-radio__input.is-checked .el-radio__inner {
        border-color: #4877FB;
        background: #4877FB;
    }

    .el-radio {
        // margin-right: 20px;
    }

    .el-radio__input.is-checked+.el-radio__label {
        color: #303942;
    }

}

// 弹窗 custom-class="popupOne"
.popupOne {
    max-height: 85vh;
    overflow: auto;

    .el-dialog__header {
        background-color: #E8EBEF;
        padding: 15px 40px;

        .el-dialog__title,
        .el-dialog__headerbtn>i {
            color: #303942;
            font-weight: bold;
        }

        .el-dialog__headerbtn {
            top: 15.5px;
            right: 40px;

            i {
                font-size: 22px;
            }
        }
    }

    .el-dialog__body {
        padding: 40px;
    }

    .el-dialog__footer {
        text-align: center;
        padding: 0;
        padding-bottom: 40px;
    }
}

// 分页
.el-pagination {
    margin-top: 40px;
    text-align: center;
}

.el-pagination.is-background .btn-next,
.el-pagination.is-background .btn-prev,
.el-pagination.is-background .el-pager li {
    background-color: #FAFBFE !important;
    color: #9094A0 !important;
    border-radius: 4px !important;
}

.el-pagination.is-background .el-pager li:not(.disabled):hover {
    color: #4877FB !important;
}

.el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: #4877FB !important;
    color: #fff !important;
}

// 字体按钮
.textBtn {
    user-select: none;
    cursor: pointer;
}

// 20像素偏移的快捷class
.mgl20 {
    margin-left: 20px;
}

.mgr20 {
    margin-right: 20px;
}

.mgt20 {
    margin-top: 20px;
}

.mgb20 {
    margin-bottom: 20px;
}

// 色系快捷class
.cRed {
    color: #FF5370 !important;
}

.bgcRed {
    background-color: #FF5370 !important;
}

.bdcRed {
    border-color: #FF5370 !important;
}

.cBlue {
    color: #4877FB !important;
}

.bgcBlue {
    background-color: #4877FB !important;
}

.bdcBlue {
    border-color: #4877FB !important;
}

// 浮动
.floatL {
    float: left;
}

.floatR {
    float: right;
}

// 弹窗里列名称对齐
.listTitle {
    display: inline-block;
    min-width: 70px;
    text-align: right;

    i {
        color: #FF5370;
        position: relative;
        left: -1em;
    }
}

// 时间选择器
.eleDate {
    .el-input__suffix {
        right: 25px;

        .el-input__icon {
            line-height: 34px;
        }
    }

    .el-input__prefix {
        left: inherit;
        right: 5px;

        .el-input__icon {
            line-height: 34px;
        }
    }

    .dash {
        color: #DDE0E3;
        font-size: 22px;
    }
}

.eleDateShort {
    .el-input__suffix {
        right: 17px;

        .el-input__icon {
            line-height: 34px;
        }
    }

    .el-input__prefix {
        left: inherit;
        right: 0px;

        .el-input__icon {
            line-height: 34px;
        }
    }

    .el-input--prefix .el-input__inner {
        padding-left: 0px;
    }

    .dash {
        color: #DDE0E3;
        font-size: 22px;
    }
}

//弹窗 h4标题
.dialogTitle {
    color: #303942;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 30px;
    position: relative;

    i {
        color: #FF5370;
        position: absolute;
        left: -1em;
    }
}

// 朴素表格
.plainTable {
    width: 100%;

    th,
    td {
        border: 1px solid #DDE0E3;
        line-height: 46px;
        min-height: 46px;
    }

    .el-input__inner {
        border: none;
    }
}

// 弹性布局 二等分结构（可style改比例）
.scopeFlex {
    display: flex;
    align-items: center;
    justify-content: center;
}

.scopeFlex>* {
    width: 50%;
}

// 弹性布局 三等分结构（可style改比例）
.WarFlow {
    display: flex;
    align-items: center;
    justify-content: space-between;

}

.WarFlow>* {
    width: 33.33%;
}

/* 实心蓝白字按钮 加 primaryBlue*/
.primaryBlue.el-button--primary:focus,
.primaryBlue.el-button--primary:hover {
    background: #4877fb;
    border-color: #4877fb;
}

.primaryBlue.el-button--primary {
    background-color: #4877fb;
    border-color: #4877fb;
}

.primaryBlue.el-button {
    padding: 8px 18px;
}

.primaryBlue.el-button span {
    font-size: 16px;
}

/* 实心红白字按钮 加 primaryBlue*/
.primaryRed.el-button--primary:focus,
.primaryRed.el-button--primary:hover {
    background: #FF5370;
    border-color: #FF5370;
}

.primaryRed.el-button--primary {
    background-color: #FF5370;
    border-color: #FF5370;
}

.primaryRed.el-button {
    padding: 8px 18px;
}

.primaryRed.el-button span {
    font-size: 16px;
}

/* 实心蓝白字42左边内边距的按钮 加 primaryBlue42*/
.primaryBlue42.el-button--primary:focus,
.primaryBlue42.el-button--primary:hover {
    background: #4877fb;
    border-color: #4877fb;
}

.primaryBlue42.el-button--primary {
    background-color: #4877fb;
    border-color: #4877fb;
}

.primaryBlue42.el-button {
    padding: 8px 42px;
}

.primaryBlue42.el-button span {
    font-size: 16px;
}

/* 朴素蓝框蓝字按钮 加 plainBlue*/
.plainBlue.el-button.is-plain:focus,
.plainBlue.el-button.is-plain:hover {
    border-color: #4877fb;
    color: #4877fb;
}

.plainBlue.el-button {
    padding: 8px 18px;
    border-color: #4877fb;
    color: #4877fb;
}

.plainBlue.el-button {
    span {
        font-size: 16px;
    }
}