import Vue from "vue";
import App from "./App.vue";
import router from "./router/";
import store from "./store/";
import Element from "components/Element-ui/";
import "@/style/element-variables.scss";
import VueStorage from "vue-ls";
import PreviewImage from "components/Preview/index.js";

import Axios from "@/utils/axios";
import routerConfig from "@/permission";

import auth from "@/utils/directive";
import Authorized from "components/Authorized/index.vue";
import { version } from "../package.json";
import { includeLinkStyle } from "ydutils"
import { MessageBox } from 'element-ui';


includeLinkStyle()
// 生成环境关闭 浏览器的vue插件
Vue.config.productionTip = false;
const isDebug_mode = process.env.NODE_ENV !== "production";
Vue.config.debug = isDebug_mode;
Vue.config.devtools = isDebug_mode;
Vue.config.productionTip = isDebug_mode;
Vue.prototype.$alert = MessageBox.alert;

Vue.use(auth);
Vue.use(Axios);
Vue.use(PreviewImage);
Vue.use(Element);
Vue.use(VueStorage, {
    namespace: "yide__", // key prefix
    name: "ls", // name variable Vue.[ls] or this.[$ls],
    storage: "local", // storage name session, local, memory
});

Vue.component("Authorized", Authorized);

if (version) {
    const localVersion = window.localStorage.getItem(`version`);
    if (version !== localVersion) {
        window.localStorage.clear();
        window.location.reload();
        window.localStorage.setItem(`version`, version);
    }
}
routerConfig(router)
new Vue({
    router,
    store,
    render: (h) => h(App),
}).$mount("#app");

