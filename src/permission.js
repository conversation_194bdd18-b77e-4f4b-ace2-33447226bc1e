/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-06-11 15:17:58
 * @LastEditors: jingrou
 * @LastEditTime: 2022-07-07 09:58:12
 */
import Vue from "vue";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
NProgress.configure({
    showSpinner: false,
});
import { ACCESS_TOKEN, USER_ID } from "@/store/mutation-types";

export default (router) => {
    router.beforeEach(async (to, from, next) => {
        NProgress.start();
        if (to.meta && to.meta.title) {
            document.title = `${to.meta.title} - 一德管理平台`;
        }

        if (Vue.ls.get(ACCESS_TOKEN)) {
            next()
        } else {
            if (to.path !== "/login") {
                next("/login");
            } else {
                next();
            }
        }
    });

    router.afterEach(() => {
        NProgress.done();
    });
}
