import Vue from "vue";
import Router from "vue-router";
import { constantRouterMap, asyncRouterMap } from "@/config/router.config";
import { Axios } from "@/utils/axios";
import store from "@/store"
import BaseLayout from "@/components/Layouts/base-layout";
const routerView = () => import(`@/views/routerView.vue`)

Vue.use(Router);
const setRoute = (arr) => {
    let list = []
    arr.forEach(i => {
        let item = {
            meta: {
                title: i.name,
                icon: i.icon,
            },
            path: i.path,
            children: setRoute(i.children)
        }
        if (i.filePath) {
            item.component = () => import(`@/${i.filePath}`)

        } else {
            item.component = routerView
        }
        list.push(item)
    })
    return list
}
const permsList = []
const setAuthCode = (arr) => {
    arr.forEach(i => {
        setAuthCode(i.children)
        setAuthCode(i.directoryList)
        setAuthCode(i.tabList)
        setAuthCode(i.btnList)
        if (i.perms) {
            permsList.push(i.perms)
        }
    })
}
const getRouter = () => {
    return Axios.get('/system/menu/getRouters').then(res => {
        const routerList = setRoute(res.data)
        setAuthCode(res.data)
        store.commit('setRouters', routerList)
        store.commit('setAuthCode', permsList)
        return routerList
    })
}

const router = new Router({
    mode: "hash",
    base: process.env.BASE_URL,
    scrollBehavior: () => ({
        y: 0,
    }),
    routes: constantRouterMap
});
const setRouter = async () => {
    const list = await getRouter()
    list.push({
        path: "/404",
        name: "NotFound",
        meta: {
            title: ""
        },
        component: () =>
            import(
                    /* webpackChunkName: "error" */ "@/components/Error/404.vue"
            ),
    })


    router.addRoute({
        path: "/",
        component: BaseLayout,
        redirect: "/home",
        children: list
    })
    list.push({
        path: '/:pathMatch(.*)',
        redirect: '/404',
    },)
    return Promise.resolve()
}
export { setRouter }
export default router

