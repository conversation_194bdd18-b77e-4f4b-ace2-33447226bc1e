/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-04-01 11:10:51
 * @LastEditors: jingrou
 * @LastEditTime: 2022-06-13 18:05:45
 */
const getters = {
    sidebar: (state) => state.app.sidebar,
    size: (state) => state.app.size,
    device: (state) => state.app.device,
    visitedViews: (state) => state.tagsView.visitedViews,
    cachedViews: (state) => state.tagsView.cachedViews,
    token: (state) => state.user.token,
    avatar: (state) => state.user.avatar,
    name: (state) => state.user.name,
    user: (state) => state.user,
    introduction: (state) => state.user.introduction,
    roles: (state) => state.user.roles,
    errorLogs: (state) => state.errorLog.logs,
    // ------------------------ 新 -------------------
    permission_routes: (state) => state.permission.routes, // 111111
    showHeight: (state) => state.administrator.showHeight, // 111111
    showWidth: (state) => state.administrator.showWidth,
    ruleFormChild: (state) => state.administrator.ruleFormChild,
    schoolData: (state) => state.administrator.schoolData,
    modifySchool: (state) => state.administrator.modifySchool,
    tableHodyData: (state) => state.administrator.tableHodyData,

    learningSection: (state) => state.administrator.learningSection,
    editTrue: (state) => state.administrator.editTrue,
    // ----------------------  版本管理 --------------
    versionNavigationList: (state) => state.VersionSchool.versionNavigationList,
    versionTableData: (state) => state.VersionSchool.versionTableData,
    schoolTableData: (state) => state.VersionSchool.schoolTableData,
    brandApplicationId: (state) => state.VersionSchool.brandApplicationId,
    pages: (state) => state.VersionSchool.pages,
};
export default getters;
