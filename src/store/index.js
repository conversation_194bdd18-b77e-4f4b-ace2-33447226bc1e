/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-06-11 15:17:58
 * @LastEditors: jingrou
 * @LastEditTime: 2023-05-24 14:34:30
 */
import Vue from "vue";
import Vuex from "vuex";
import users from "@/api/users";
import router from "@/router/index";
import { asyncRouterMap } from "@/config/router.config";
Vue.use(Vuex);
import { ACCESS_TOKEN } from "@/store/mutation-types";
import { Message } from "element-ui";

// https://webpack.js.org/guides/dependency-management/#requirecontext
const modulesFiles = require.context("./modules", true, /\.js$/);

// you do not need `import app from './modules/app'`
// it will auto require all vuex module from modules file
const modules = modulesFiles.keys().reduce((modules, modulePath) => {
    // set './app.js' => 'app'
    const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, "$1");
    const value = modulesFiles(modulePath);
    modules[moduleName] = value.default;
    return modules;
}, {});

// 获取用户信息失败或路由获取失败后  清空缓存调回登录重新登录
function errorLogin(error) {
    console.log(error, "获取用户信息失败，请重新登录");
    Message.error("获取用户信息失败，请重新登录");
    Vue.ls.remove(ACCESS_TOKEN);
    setTimeout(() => {
        router.replace({
            path: "/login",
        });
    }, 1000);
}
export default new Vuex.Store({
    modules,
    state: {
        system: {
            //固定header
            fixedHeader: true,
            //收起菜单
            isCollapse: false,
            //主題顔色
            themeColor: localStorage.getItem("COLOR_THEME") || "#3963bc",
            // 主題颜色的三原色
            backgroundColor: "rgba(32, 45, 64, .3)",
        },
        network: true, //网络状态
        userInfo: { name: "", avatar: "" },
        announceData: {},
        routers: [],
        permsList: [],
    },
    getters: {
        userInfo(state) {
            return state.userInfo;
        },
    },
    mutations: {
        changeCollapse(state) {
            state.system.isCollapse = !state.system.isCollapse;
        },
        changeThemeColor(state, color) {
            const tintColor = (color) => {
                let red = parseInt(color.slice(0, 2), 16);
                let green = parseInt(color.slice(2, 4), 16);
                let blue = parseInt(color.slice(4, 6), 16);

                return [red, green, blue];
            };
            let _color = color.replace("#", "");
            _color = tintColor(_color);
            state.system.themeColor = color;
            // 转成rgba格式的，并添加透明度
            state.system.backgroundColor = `rgba(${_color[0]}, ${_color[1]}, ${_color[2]}, .3)`;
        },
        changeNetwork(state, boole) {
            state.network = boole;
        },
        setUserInfo(state, userInfo) {
            Vue.ls.set("userInfo", userInfo);
            state.userInfo = userInfo;
        },
        setAnnounceData(state, data) {
            state.announceData = data;
        },
        setRouters(state, routers) {
            state.routers = routers;
        },
        setAuthCode(state, permsList) {
            state.permsList = permsList;
        },
    },
    actions: {
        async getUserInfo({ commit }) {
            try {
                const { data: userInfo } = await users.getUserInfo();
                commit("setUserInfo", userInfo);
            } catch (error) {
                errorLogin(error);
            }
        },
        async getRouters({ commit }) {},
    },
});
