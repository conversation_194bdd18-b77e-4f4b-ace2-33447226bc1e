import axios from "axios";
import { post, postQs, get, put } from "@/api";
import { Message } from "element-ui";
import { getToken } from "@/utils/auth";

const state = {
    showHeight: document.documentElement.clientHeight, // 默认屏幕高度
    showWidth: document.documentElement.clientWidth,
    ruleFormChild: {
        id: "",
        schoolName: "",
        schoolType: "",
        schoolArea: "",
        schoolAddress: "",
        contactPerson: "",
        contactPhone: null,
        headOfSales: "",
        headOfClient: "",
        sections: [],
        // status: "N",
        version: 0,
    },
    schoolData: null,
    modifySchool: false,
    tableHodyData: [],
    users: {},
    total: 0,
    listQuery: {
        current: 0,
        size: 10,
    },
    listLoading: false,
    learningSection: [],
    editTrue: false,
};

const mutations = {
    EDITTRUE: (state, data) => {
        state.editTrue = data;
    },
    SCHOOL_LIST: (state, data) => {
        state.editTrue = false;
        state.schoolData = data;
    },


    GET_USER_INFORMATION: (state, paylod) => {
        state.users = paylod;
    },
};

const actions = {
    schoolList({ commit }, paylod = {}) {
        return new Promise((resolve, reject) => {
            const URL = "/backstage/cloud-backstage/school/searchSchool";
            post(URL, paylod)
                .then((response) => {
                    commit("SCHOOL_LIST", response.data);
                })
                .catch((error) => {
                    reject(error);
                });
        });
    },
    // addSchoolLists({ commit }, paylod) {
    // 	const _that = this;
    // 	if (paylod.state) {
    // 		const obj = JSON.parse(JSON.stringify(paylod.data));
    // 		let params = {};
    // 		if (
    // 			_that.state["administrator"].modifySchool ||
    // 			paylod.modifySchool
    // 		) {
    // 			const TMPURL = "/backstage/cloud-backstage/school/updateSchool"; // 编辑
    // 			params = {
    // 				createdBy: obj.createdBy,
    // 				createdDateTime: obj.createdDateTime,
    // 				id: obj.id,
    // 				schoolName: obj.schoolName,
    // 				schoolType: obj.schoolType,
    // 				schoolArea: obj.schoolArea,
    // 				schoolAddress: obj.schoolAddress,
    // 				contactPerson: obj.contactPerson,
    // 				contactPhone: obj.contactPhone,
    // 				headOfSales: obj.headOfSales,
    // 				headOfClient: obj.headOfClient,
    // 				sections: obj.sections,
    // 				status: obj.status,
    // 				logoPic: obj.logoPic
    // 			};
    // 			return new Promise((resolve, reject) => {
    // 				post(TMPURL, params)
    // 					.then(response => {
    // 						if (response.code === 200) {
    // 							Message({
    // 								message: "编辑成功！",
    // 								type: "success"
    // 							});
    // 							commit("EDITTRUE", true);
    // 							_that.dispatch(
    // 								"administrator/schoolList",
    // 								_that.state["administrator"].listQuery
    // 							);
    // 						} else {
    // 							Message({
    // 								message: response.message,
    // 								type: "warning"
    // 							});
    // 						}
    // 						resolve(response);
    // 					})
    // 					.catch(error => {
    // 						reject(error);
    // 					});
    // 			});
    // 		} else {
    // 			const TMPURL = "/backstage/cloud-backstage/school/createSchool"; // 新增
    // 			params = {
    // 				id: obj.id,
    // 				schoolName: obj.schoolName,
    // 				schoolType: obj.schoolType,
    // 				schoolArea: obj.schoolArea,
    // 				schoolAddress: obj.schoolAddress,
    // 				contactPerson: obj.contactPerson,
    // 				contactPhone: obj.contactPhone,
    // 				logoPic: obj.logoPic,
    // 				headOfSales: obj.headOfSales,
    // 				headOfClient: obj.headOfClient,
    // 				status: obj.status
    // 			};
    // 			if (obj.schoolType === "初中等教育") {
    // 				params.sections = obj.sections;
    // 			}
    // 			return new Promise((resolve, reject) => {
    // 				post(TMPURL, params)
    // 					.then(response => {
    // 						resolve(response);
    // 						if (response.code === 200) {
    // 							commit("EDITTRUE", true);
    // 							Message({
    // 								message: "新增学校成功",
    // 								type: "success"
    // 							});
    // 							_that.dispatch(
    // 								"administrator/schoolList",
    // 								_that.state["administrator"].listQuery
    // 							);
    // 						} else {
    // 							Message({
    // 								message: response.message,
    // 								type: "warning"
    // 							});
    // 						}
    // 					})
    // 					.catch(error => {
    // 						reject(error);
    // 					});
    // 			});
    // 		}
    // 	} else {
    // 		commit("ADD_SCHOOL_LIST", paylod);
    // 	}
    // },
    // 获取学校logo
    getLogoInfo({ commit }, paylod) {
        return new Promise((resolve, reject) => {
            const URL = `/backstage/cloud-backstage/oss/selectSchoolDetails/${paylod.data.id}`;
            // const URL = "/backstage/cloud-backstage/oss/getList";
            const params = {};
            // const params = { schoolId: paylod.data.id };
            post(URL, params)
                .then((response) => {
                    if (response.code == 200) {
                        response.data.schoolArea =
                            response.data.schoolArea.replace(/^\s*|\s*$/g, "");
                        response.data.contactPerson =
                            response.data.contactPerson.replace(
                                /^\s*|\s*$/g,
                                ""
                            );
                        response.data.createdBy =
                            response.data.createdBy.replace(/^\s*|\s*$/g, "");
                        response.data.schoolAddress =
                            response.data.schoolAddress.replace(
                                /^\s*|\s*$/g,
                                ""
                            );
                        response.data.schoolType =
                            response.data.schoolType.replace(/^\s*|\s*$/g, "");
                    }
                    resolve(response);
                })
                .catch((error) => {
                    reject(error);
                });
        });
    },
    getUserInformationInfo({ commit }, paylod) {
        // return new Promise((resolve, reject) => {
        // 	const URL = "/backstage/cloud-backstage/admin/adminRoleList";
        // 	post(URL, paylod)
        // 		.then(response => {
        // 			commit(
        // 				"GET_USER_INFORMATION",
        // 				response.title && response.title[0]
        // 			);
        // 		})
        // 		.catch(error => {
        // 			reject(error);
        // 		});
        // });
    },
    // 修改密码
    updatePasswordInfo({ commit }, userInfo) {
        const { oldPassword, password } = userInfo;
        const params = {
            oldPassword: oldPassword,
            newPassword: password,
        };
        return new Promise((resolve, reject) => {
            const URL = "/api/user/profile/updatePwd";
            put(URL, params)
                .then((response) => {
                    resolve(response);
                })
                .catch((error) => {
                    reject(error);
                });
        });
    },

    logout({ commit }, userInfo) {
        return new Promise((resolve, reject) => {
            const URL = "/backstage/cloud-backstage/login/userLogout";
            post(URL, userInfo)
                .then((response) => {
                    resolve(response);
                })
                .catch((error) => {
                    reject(error);
                });
        });
    },
    // 上传学校logo
    schoolLogoInfo({ commit }, userInfo) {
        const config = {
            timeout: 30000,
            headers: {
                "Content-Type":
                    "multipart/octet-stream; boundary=----WebKitFormBoundarynl6gT1BKdPWIejNq",
                Authorization: getToken(),
            },
        };
        const formData = new FormData();
        // formData.append('file', userInfo)/
        formData.append("file", userInfo.file); // 传参改为formData格式
        formData.append("schoolId", userInfo.schoolId); // 传schoolId参改为formData格式
        return new Promise((resolve, reject) => {
            axios
                .post(
                    process.env.VUE_APP_BASE_API +
                        "/backstage/cloud-backstage/oss/toOss",
                    formData,
                    config
                )
                .then((res) => {
                    resolve(res);
                })
                .catch((error) => {
                    reject(error);
                    // 请求失败
                });
        });
        // return new Promise((resolve, reject) => {
        //   const URL = '/backstage/cloud-backstage/oss/toOss'
        //   post(URL, userInfo).then(response => {
        //     resolve(response)
        //   }).catch(error => {
        //     reject(error)
        //   })
        // })
    },
};

export default {
    namespaced: true,
    state,
    mutations,
    actions,
};
