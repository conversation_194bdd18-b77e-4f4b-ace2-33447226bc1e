//基础全局样式
@import "./variable.scss";
#app {
    font-family: $font-family;
    height: 100%;
    color: $text-color;
    font-size: 14px;
}

a {
    text-decoration: none;
    outline: none;
    cursor: pointer;
    transition: color 0.3s;
}
//显示小手
.__pointer {
    cursor: pointer;
}
//使用在字符图标上hover时的样式
.__ibass-default {
    color: red;
    transition: color 0.5s;
    cursor: pointer;
    &:hover {
        color: blue;
    }
}
//文本不允许选中
.__noselect {
    user-select: none;
}
//点击无效
.__click--invalid {
    pointer-events: none;
}
// 去掉与el-form-item__content重叠问题
.el-form-item {
    margin-bottom: 0;
}

.height100 {
    height: 100%;
}
