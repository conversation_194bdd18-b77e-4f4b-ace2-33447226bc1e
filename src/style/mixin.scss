//全局方法
@charset "UTF-8";

@function black($opacity) {
    @return rgba(0, 0, 0, $opacity);
}

@function white($opacity) {
    @return rgba(255, 255, 255, $opacity);
}

// iPhone 6尺寸作为设计稿基准 响应式开发时
$vw_base: 375;

@function vw($px) {
    @return ($px / $vm_base) * 100vw;
}

// rem 单位换算：定为 75px 只是方便运算，750px-75px、640-64px、1080px-108px，如此类推
$vw_fontsize: 75; // iPhone 6尺寸的根元素大小基准值

@function rem($px) {
    @return ($px / $vw_fontsize) * 1rem;
}


// 根元素大小使用 vw 单位
// $vw_design: 750;
// html {
//     font-size: ($vw_fontsize / ($vw_design / 2)) * 100vw; 
//     // 同时，通过Media Queries 限制根元素最大最小值
//     @media screen and (max-width: 320px) {
//         font-size: 64px;
//     }
//     @media screen and (min-width: 540px) {
//         font-size: 108px;
//     }
// }
// // body 也增加最大最小宽度限制，避免默认100%宽度的 block 元素跟随 body 而过大过小
// body {
//     max-width: 540px;
//     min-width: 320px;
// }

//增加 某个class
@mixin when-inside($context) {
    #{$context} & {
        @content;
    }
}

// @include when-inside('.active') {
//     animation: fadeIn 0.3s 1s forwards;
//   }

// 浏览器前缀
@mixin css3($property, $value) {

    @each $prefix in -webkit-,
    -moz-,
    -ms-,
    -o-,
    '' {
        #{$prefix}#{$property}: $value;
    }
}

//响应式断点
@mixin breakpoint($point) {
    @if $point==large {
        @media (min-width: 64.375em) {
            @content;
        }
    }

    @else if $point==medium {
        @media (min-width: 50em) {
            @content;
        }
    }

    @else if $point==small {
        @media (min-width: 37.5em) {
            @content;
        }
    }
}

/*
  使用 @include breakpoint(large) { 
    width: 60%; 
  }
*/

/*
  检测高分辨率显示
  将高分辨率替代方案放在与普通图像相同的规则中
*/
@mixin image-2x($image, $width, $height) {

    @media (min--moz-device-pixel-ratio: 1.3),
    (-o-min-device-pixel-ratio: 2.6/2),
    (-webkit-min-device-pixel-ratio: 1.3),
    (min-device-pixel-ratio: 1.3),
    (min-resolution: 1.3dppx) {
        /* on retina, use image that's scaled by 2 */
        background-image: url($image);
        background-size: $width $height;
    }
}

// flex布局
@mixin flex($direction: columin, $inline: block) {
    display: if($inline==block, flex, inline-flex);
    flex-direction: $direction;
    flex-wrap: wrap;
}

// 多行文本溢出省略显示
@mixin text-ellipsis($num: 1) {
    overflow: hidden;
    text-overflow: ellipsis;
    // display: -webkit-box;
    -webkit-line-clamp: $num;
    -webkit-box-orient: vertical;
}

/**
  *imageSrc 路径
  *width 宽度
  *height 高度
*/
@mixin bgImg($imageSrc,
    $width: auto,
    $height: auto,
    $position: center,
    $repeat: no-repeat) {
    background: url($imageSrc) $repeat $position;
    background-size: $width $height;
}

//透明度
@mixin opacity($opacity) {
    opacity: $opacity;
    $opacity-ie: $opacity * 100;
    filter: alpha(opacity=$opacity-ie); //IE8
}

//阴影
@mixin box-shadow($shadows...) {
    @if length($shadows)>=1 {
        -webkit-box-shadow: $shadows;
        box-shadow: $shadows;
    }

    @else {
        $shadows: 0 0 2px rgba(#000, 0.25);
        -webkit-box-shadow: $shadow;
        box-shadow: $shadow;
    }
}

/*
 三角形
 direction 方向
 size 大小
 borderColor 颜色
 */
@mixin triangle($direction, $size, $borderColor) {
    content: '';
    height: 0;
    width: 0;
    display: block;

    @if $direction==top {
        border-bottom: $size solid $borderColor;
        border-left: $size dashed transparent;
        border-right: $size dashed transparent;
    }

    @else if $direction==right {
        border-left: $size solid $borderColor;
        border-top: $size dashed transparent;
        border-bottom: $size dashed transparent;
    }

    @else if $direction==bottom {
        border-top: $size solid $borderColor;
        border-left: $size dashed transparent;
        border-right: $size dashed transparent;
    }

    @else if $direction==left {
        border-right: $size solid $borderColor;
        border-top: $size dashed transparent;
        border-bottom: $size dashed transparent;
    }
}

//圆角
@mixin radius($radius: 5px) {
    -webkit-border-radius: $radius;
    -moz-border-radius: $radius;
    border-radius: $radius;
}

// 清除浮动
@mixin clearfix() {

    &:before,
    &:after {
        content: '';
        display: table;
    }

    &:after {
        clear: both;
    }
}

//绝对定位
@mixin abs-pos($top: auto, $right: auto, $bottom: auto, $left: auto) {
    top: $top;
    right: $right;
    bottom: $bottom;
    left: $left;
    position: absolute;
}

// 行高
@mixin line-height($heightValue: 12) {
    line-height: $heightValue + px; //fallback for old browsers
    line-height: (0.125 * $heightValue) + rem;
}

// 过度
@mixin transition($args...) {
    -webkit-transition: $args;
    -moz-transition: $args;
    -ms-transition: $args;
    -o-transition: $args;
    transition: $args;
}

//鼠标hover显示下划线
@mixin hoverLine($height: 2px, $color: $color-text-primary) {
    position: relative;

    &:hover::after {
        content: '';
        position: absolute;
        height: $height;
        width: 100%;
        background-color: $color;
        bottom: 0;
        left: 0;
    }
}

//文本居中
@mixin center($height: 100%) {
    height: $height;
    line-height: $height;
    text-align: center;
}

//毛玻璃效果
@mixin blur($blur: 10px) {
    -webkit-filter: blur($blur);
    -moz-filter: blur($blur);
    -o-filter: blur($blur);
    -ms-filter: blur($blur);
    filter: progid:DXImageTransform.Microsoft.Blur(PixelRadius='${blur}');
    filter: blur($blur);
    *zoom: 1;
}