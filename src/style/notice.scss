.notice_main {
	.add_btn {
		margin: 280px auto;
		width: 200px;
		cursor: pointer;

		i {
			font-size: 36px;
			display: block;
			color: #ccc;
			text-align: center;
		}

		span {
			font-size: 18px;
			display: block;
			color: #ccc;
			text-align: center;
			line-height: 48px;
		}
	}

	.drag_drop {
		font-size: 18px;
		color: #ccc;
		margin: 3px 6px 0px 20px;
	}

	.adddelete_btn {
		font-size: 16px;
		font-weight: bold;
		margin: 0px 5px;
		cursor: pointer;
	}

	.classication_form {
		.el-form-item__error {
			margin-left: 45px;
		}
	}

	//弹窗footer
	.menu_drawer {
		position: fixed;
	}

	.yd-form-footer {
		width: 100%;
		text-align: center;
		border-top: 1px solid #dcdfe6;
		position: absolute;
		padding-top: 20px;
		bottom: 0px;

		.el-form-item__content {
			margin-left: 0 !important;
		}
	}

	//模板信息


	.add_template_box {
		width: 18.5%;
		height: 240px;
		float: left;
		margin-right: 1.5%;
		margin-bottom: 20px;
	}

	.add_template {
		width: 100%;
		height: 180px;
		border: 1px solid #f4f4f4;
		border-radius: 5px;
		cursor: pointer;
		position: relative;

		.add_plus {
			font-size: 36px;
			color: #999;
			display: block;
			text-align: center;
			margin-top: 50px;
		}

		span {
			font-size: 16px;
			color: #999;
			display: block;
			text-align: center;
			line-height: 30px;
		}

		.close_btn {
			position: absolute;
			right: 2px;
			top: 2px;
			color: #333;
			font-size: 20px;
			cursor: pointer;
		}

		.copy {
			position: absolute;
			bottom: 4px;
			right: 4px;
			color: #333;
			font-size: 12px;
			cursor: pointer;
			padding: 0px 12px;
			background-color: rgb(116, 186, 255);
			color: #fff;
			border-radius: 3px;
			line-height: 24px;
		}

		.copy1 {
			position: absolute;
			bottom: 4px;
			right: 80px;
			color: #333;
			font-size: 12px;
			cursor: pointer;
			padding: 0px 12px;
			background-color: rgb(116, 186, 255);
			color: #fff;
			border-radius: 3px;
			line-height: 24px;
		}
	}

	.template_title {
		overflow: hidden;

		span {
			font-size: 16px;
			color: #333;
			float: left;
			line-height: 36px;
		}

		i {
			font-size: 16px;
			color: #666;
			float: right;
			line-height: 26px;
		}
	}

	.template_cover {
		width: 80%;
		margin: 0px auto;

		span {
			font-size: 15px;
			color: #333;
			line-height: 36px;
		}
	}

	.template_photo {
		width: 100%;
		height: 240px;
		border: 1px solid #f5f5f5;
		overflow: hidden;
		img {
			height: 100%;
			margin: 0px auto;
			display: block;
		}
	}

	.copy_classificationName {
		.el-checkbox.is-bordered + .el-checkbox.is-bordered {
			margin-left: 0px;
		}
		.el-checkbox.is-bordered.el-checkbox--medium {
			margin-bottom: 10px;
		}
		.copy_list {
			max-height: 420px;
			overflow-y: auto;
			margin-top: 20px;
		}

		.copy_name {
			font-size: 16px;
			color: #333;
			margin-left: 20px;
			margin-top: 30px;
			display: block;
		}

		.classificationName_list {
			margin-left: 20px;
		}
	}
}

.add_notice_main {
	width: 95%;
	margin: 0px auto;

	.avatar-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}

	.avatar-uploader .el-upload:hover {
		border-color: #409eff;
	}

	.avatar-uploader-icon {
		font-size: 28px;
		color: #8c939d;
		width: 120px;
		height: 120px;
		line-height: 120px;
		text-align: center;
	}

	.avatar {
		width: 120px;
		height: auto;
		display: block;
	}

	.cover_map {
		border: 1px solid #eee;
		border-radius: 6px;
		width: 120px;
		height: auto;
		text-align: center;
		overflow: hidden;
		img {
			display: block;
		}
	}
}
