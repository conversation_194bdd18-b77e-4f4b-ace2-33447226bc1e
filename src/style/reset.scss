@import './variable.scss';
html,
body,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
font,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
center,
u,
b,
i {
    margin: 0;
    padding: 0;
    border: 0;
    outline: 0;
    font-weight: normal;
    font-style: normal;
    font-size: 100%;
    vertical-align: baseline;
}
body {
    line-height: 1;
}
:focus {
    outline: 0;
}
ol,
ul {
    list-style: none;
}
table {
    border-collapse: collapse;
    border-spacing: 0;
}
blockquote:before,
blockquote:after,
q:before,
q:after {
    content: '';
}
blockquote,
q {
    quotes: '';
}
input,
textarea {
    margin: 0;
    padding: 0;
}
hr {
    margin: 0;
    padding: 0;
    border: 0;
    color: #000;
    background-color: #000;
    height: 1px;
}
html,
body {
    height: 100%;
}

/* ::-webkit-scrollbar：整个滚动条
        ::-webkit-scrollbar-button：滚动条上的按钮（下下箭头）
        ::-webkit-scrollbar-thumb：滚动条上的滚动滑块
        ::-webkit-scrollbar-track：滚动条轨道
        ::-webkit-scrollbar-track-piece：滚动条没有滑块的轨道部分
        ::-webkit-scrollbar-corner：当同时有垂直和水平滚动条时交汇的部分
        ::-webkit-resizer：某些元素的交汇部分的部分样式（类似textarea的可拖动按钮） */
::-webkit-scrollbar {
    width: 5px;
    height: 5px;
    background-color: #f5f5f5;
}
::-webkit-scrollbar-thumb {
    box-shadow: inset 0 0 2.5px rgba(0, 0, 0, 0.3);
    border-radius: 2.5px;
    background-color: $scrollbar-color;
    opacity: 0.7;
    transition: opacity ease-in-out 200ms;
}
::-webkit-scrollbar-track {
    box-shadow: inset 0 0 2.5px rgba(0, 0, 0, 0.3);
    border-radius: 2.5px;
    background-color: #f5f5f5;
}
::selection {
    background: $selection-color;
}
