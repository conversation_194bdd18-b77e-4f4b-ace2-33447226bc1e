/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-04-01 11:10:51
 * @LastEditors: jingrou
 * @LastEditTime: 2022-06-13 17:29:14
 */
import Cookies from "js-cookie";
const TokenKey = "Admin-Token";

export function getToken() {
    return Cookies.get(TokenKey);
}

export function setToken(token) {
    return Cookies.set(TokenKey, token);
}

export function setRefreshToken(refreshToken) {
    return Cookies.set("refreshToken", refreshToken);
}
export function getRefreshToken() {
    return Cookies.get("refreshToken");
}
export function removeToken() {
    Cookies.remove("refreshToken");
    return Cookies.remove(TokenKey);
}

export function setStorage(key, value) {
    return localStorage.setItem(key, JSON.stringify(value));
}

export function getStorage(key) {
    key = localStorage.getItem(key);
    return key;
}

export function removStorage(key) {
    return localStorage.removeItem(key);
}
