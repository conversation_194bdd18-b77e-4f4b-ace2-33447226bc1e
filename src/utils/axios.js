/* eslint-disable indent */
/**
 * 请求拦截、相应拦截、错误统一处理
 */

import Vue from "vue";
import axios from "axios";
import router from "@/router/index";
import store from "@/store/index";
import { Message } from "element-ui";
import { ACCESS_TOKEN } from "@/store/mutation-types";

// 创建axios实例
export const Axios = axios.create({
    baseURL: process.env.VUE_APP_API_BASE_URL,
    timeout: 8000,
    headers: {
        "Content-Type": "application/json;charset=UTF-8",
        "platform": "manage"
    },
});

// 设置post请求头
Axios.defaults.headers.post["Content-Type"] =
    "application/x-www-form-urlencoded;charset=UTF-8";

//请求拦截
Axios.interceptors.request.use(
    (config) => {
        // 每次发送请求之前判断是否存在token
        //如果存在，则统一在http请求的header都加上token
        const token = Vue.ls.get(ACCESS_TOKEN);
        token && (config.headers.Authorization = "Bearer " + token);
        return config;
    },
    (error) => {
        return Promise.error(error);
    }
);
//响应拦截
Axios.interceptors.response.use(
    //请求成功
    (res) => {
        if (
            res.headers["content-type"] ==
            "application/vnd.ms-excel;charset=utf-8" ||
            res.headers["content-type"] == "application/vnd.ms-excel"
        ) {
            return res.data;
        }
        if (res.status === 200) {
            // 1002002012, 手机号已变更  1002002013, 密码已变更  1002002014, 用户信息失效
            const codeArr = [1002002012, 1002002013, 1002002014,1002002024]
            if (res.data.code == 0) {
                return Promise.resolve(res.data);
            } else if (codeArr.includes(res.data.code)) {
                // 清空缓存token 跳转到登录页
                Vue.ls.remove(ACCESS_TOKEN);
                tip(res.data.message);
                setTimeout(() => {
                    toLogin();
                }, 1000);
            } else {
                errorHandle(res.data.code, res.data.message);
                return Promise.reject(res.data);
            }
        } else {
            Promise.reject(res);
        }
    },
    //请求失败
    (error) => {
        const { response } = error;
        if (response) {
            // 请求已发出，但是不在2xx的范围
            errorHandle(response.status, response.data.msg);
            return Promise.reject(response);
        } else {
            // 处理断网的情况
            // eg:请求超时或断网时，更新state的network状态
            // network状态在app.vue中控制着一个全局的断网提示组件的显示隐藏
            // 关于断网组件中的刷新重新获取数据
            store.commit("changeNetwork", false);
        }
    }
);
// 提示函数
const tip = (msg) => {
    Message({
        message: msg,
        type: "error",
        duration: 2000,
    });
};
/**
 * 跳转登录页
 * 携带当前页面路由，以期在登录页面完成登录后返回当前页面
 */
const toLogin = () => {
    router.replace({
        path: "/login",
        query: {
            redirect: router.currentRoute.fullPath,
        },
    });
};
/**
 * 请求失败后的错误统一处理
 * @param {Number} status 请求失败的状态码
 */
const errorHandle = (status, other) => {
    // 状态码判断
    switch (status) {
        // 401: 未授权 或未登录状态，跳转登录页
        case 401:
            Vue.ls.remove(ACCESS_TOKEN);
            toLogin();
            break;
        // 1002002014 token过期
        case 1002002014:
            // 清除token并跳转登录页
            Vue.ls.remove(ACCESS_TOKEN);
            setTimeout(() => {
                toLogin();
            }, 1000);
            break
        case 403:
            // eslint-disable-next-line indent
            tip(` ${other}`);
            Vue.ls.remove(ACCESS_TOKEN);
            setTimeout(() => {
                toLogin();
            }, 1000);
            break;
        // 404请求不存在
        case 404:
            tip("请求的资源不存在 404");
            //404页面
            break;
        case 500:
            tip("系统异常，请稍后再试");
            //404页面
            break;
        default:
            tip(other || `系统异常：错误码${status}`);
            console.log(status, other);
    }
};

// 对axios的实例重新封装成一个plugin ,方便 Vue.use(xxxx)
export default {
    install(Vue) {
        Object.defineProperty(Vue.prototype, "$http", {
            value: Axios,
        });
    },
};
