/*
 * @Descripttion: 上传到oss
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-08-20 16:34:19
 * @LastEditors: jingrou
 * @LastEditTime: 2022-06-23 17:13:09
 */

import uploadFiles from './ydutils'

export function getUploadFileOSSpath(file, config, progressCallback) {
    return new Promise((resolve, reject) => {
        uploadFiles(file, config, progressCallback)
            .then((url) => {
                resolve(url)
            })
    })
}
