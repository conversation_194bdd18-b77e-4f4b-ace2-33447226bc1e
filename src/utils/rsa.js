/* 产引入jsencrypt实现数据RSA加密 */
import JSEncrypt from 'jsencrypt' // 处理长文本数据时报错 jsencrypt.js Message too long for RSA
/* 产引入encryptlong实现数据RSA加密 */
import Encrypt from 'encryptlong' // encryptlong是基于jsencrypt扩展的长文本分段加解密功能。

// 存储加密公钥密钥串 
const publicKey = `MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAMVHPiXWLxpMjeKcoYlaGGBawma/58yUJrOll0Azt54oRZ/vKrirdekJkrKSQ4dk0lTb0vp54/J61ORQuENFfuHcbvpCcjCAqCC4cUpNw5dPdaBMLNcclcuojzcGXqsdpY7K3C/EHalWN3I1+r2fFy4PTCYZPNt3qGAqRZneEB2zAgMBAAECgYEAg0DCHYRiNFoosFPLucminEDmRFx7yYXsNev/NKz0Bpr+r8j9IH6bZV+EJaDgou02b1UOCq4cNdD5Yao+3isXndyfyc7qq7fLYwgAUHzhpsTggHkiiTeKKFSvZp+PMxb0vKCF8OjDSopO0PaPkNEZWW0PKUqWpgjfRJfg5N2SV6ECQQDrj75bVtHnqtHe6MQiPn6648LNUqjdxZkXVClj03wpvKD669ir3HbfiE4Fu3zyIlqsRdgoY/fXYATbfy/MUC3jAkEA1mUojBtoKjS+llj+hYwht/ikw8GPkjF3ObwC6hi0LJvQU2cEaDl5isvwEulkARtNXRFdGIHZ+4xIgGUsAKtZ8QJAYGr4xpg12LZxquTpiv67jrG1RaeE2SmNLSBUxLiGxOqhlbM9f7MAsSQwk2YXkWCAbq0z+exZQL3bnyBjJyTWcwJAeKGkhejp3E2fKyNZ2rS1SDl8I7UmXkC879xM24EY57yoJDVjsm0caS9Enq95mHHPPt2FP0PY7gPJy4yhjKLdUQJBAOFae/uCuLY7KtgwKd+sCwKbwXJHlyrlHDT/uUrdPwXaRk6v/8Uz1KFYIPSXSmqWC+wHQ8J43oL4g3A5zLA0anU=`

export default {
  /* JSEncrypt加密 */
  rsaPublicData(data) {
    var jsencrypt = new JSEncrypt()
    jsencrypt.setPublicKey(publicKey)
    // 如果是对象/数组的话，需要先JSON.stringify转换成字符串
    var result = jsencrypt.encrypt(data)
    return result
  },
  /* JSEncrypt解密 */
  rsaPrivateData(data) {
    var jsencrypt = new JSEncrypt()
    jsencrypt.setPrivateKey(publicKey)
    // 如果是对象/数组的话，需要先JSON.stringify转换成字符串
    var result = jsencrypt.encrypt(data)
    return result
  },
  /* 加密 */
  encrypt(data) {
    var encryptor = new Encrypt()
    encryptor.setPublicKey(publicKey)
    // 如果是对象/数组的话，需要先JSON.stringify转换成字符串
    const result = encryptor.encryptLong(data)
    return result
  },
  /* 解密 - PRIVATE_KEY - 验证 */
  decrypt(data) {
    var encryptor = new Encrypt()
    encryptor.setPrivateKey(publicKey)
    // 如果是对象/数组的话，需要先JSON.stringify转换成字符串
    var result = encryptor.decryptLong(data)
    return result
  }
}