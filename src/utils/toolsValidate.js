/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-12-30 18:37:24
 * @LastEditors: 舒志建 <EMAIL>
 * @LastEditTime: 2023-03-07 10:01:34
 */
/**
 * 手机号码
 * @param val 当前值字符串
 * @returns 返回 true: 手机号码正确
 */
export function verifyPhone(val) {
    // false: 手机号码不正确
    // if (!/^((12[0-9])|(13[0-9])|(14[5])|(15([0-3]|[5-9]))|(18[0,5-9]))\d{8}$/.test(val)) return false;
    if (!/^[1][3-9][0-9]{9}$/.test(val)) return false
    // true: 手机号码正确
    else return true
}

/**
 * 国内电话号码
 * @param val 当前值字符串
 * @returns 返回 true: 国内电话号码正确
 */
export function verifyTelPhone(val) {
    // false: 国内电话号码不正确
    // true: 国内电话号码正确
    return /^0\d{2,3}-?\d{7,8}$/.test(val)
}

/**
 * 手机号码座机号
 * @param val 当前值字符串
 * @returns 返回 true: 手机号码正确
 */
export function verifyTelPhones(val) {
    const phone = /^[1][3-9][0-9]{9}$/
    // 座机号
    const tel = /^0\d{2,3}-?\d{7,8}$/
    // true: 号码正确 false: 号码不正确
    return phone.test(val) || tel.test(val)
}
/**
 * 邮箱
 * @param val 当前值字符串
 * @returns 返回 true: 邮箱正确
 */
export function verifyEmail(val) {
    // false: 邮箱不正确 // true: 邮箱正确
    const reg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/
    return reg.test(val)
}

/**
 * 去掉中文及空格
 * @param val 当前值字符串
 * @returns 返回处理后的字符串
 */
export function verifyCnAndSpace(val) {
    // 匹配中文与空格
    let v = val.replace(/[\u4e00-\u9fa5\s]+/g, '')
    // 匹配空格
    v = v.replace(/(^\s*)|(\s*$)/g, '')
    // 特殊字符
    v = v.replace(/[~'！!@#￥$%^&*()-+_=:]/g, '')
    // 返回结果
    return v
}

/**
 * 字符长度
 * @param value input框的值
 * @param maxLength 需求最大限制的字符
 * @param minLength 需求最小限制的字符
 * @returns 克隆后的对象
 */
export function checkField(value, minLength, maxLength) {
    const newvalue = value.replace(/[^\x00-\xff]/g, '**')
    const length = newvalue.length
    // 当填写的字节数小于 大于设置的字节数
    if (length * 1 < minLength * 1 || length * 1 > maxLength * 1) {
        return
    }
    const limitDate = newvalue.substring(0, maxLength)
    let count = 0
    let limitvalue = ''
    for (let i = 0; i < limitDate.length; i++) {
        const flat = limitDate.substring(i, 1)
        if (flat == '*') count++
    }
    let size = 0
    const istar = newvalue.substring(maxLength * 1 - 1, 1) // 校验点是否为“×”
    // if 基点是×; 判断在基点内有×为偶数还是奇数
    if (count % 2 === 0) {
        // 当为偶数时
        size = count / 2 + (maxLength * 1 - count)
        limitvalue = value.substring(0, size)
    } else {
        // 当为奇数时
        size = (count - 1) / 2 + (maxLength * 1 - count)
        limitvalue = value.substring(0, size)
    }
    return limitvalue
}