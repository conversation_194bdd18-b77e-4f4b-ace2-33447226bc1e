
/* eslint-disable indent */
export function timeFix() {
    const time = new Date();
    const hour = time.getHours();
    return hour < 9
        ? "早上好"
        : hour <= 11
        ? "上午好"
        : hour <= 13
        ? "中午好"
        : hour < 20
        ? "下午好"
        : "晚上好";
}

export function welcome() {
    const arr = [
        "休息一会儿吧",
        "准备吃什么呢?",
        "要不要打一把 DOTA",
        "我猜你可能累了"
    ];
    const index = Math.floor(Math.random() * arr.length);
    return arr[index];
}

/**
 * 触发 window.resize
 */
export function triggerWindowResizeEvent() {
    const event = document.createEvent("HTMLEvents");
    event.initEvent("resize", true, true);
    event.eventType = "message";
    window.dispatchEvent(event);
}

/**
 * Remove loading animate
 * @param id parent element id or class
 * @param timeout
 */
export function removeLoadingAnimate(id = "", timeout = 1500) {
    if (id === "") {
        return;
    }
    setTimeout(() => {
        document.body.removeChild(document.getElementById(id));
    }, timeout);
}
//日期格式化
// 格式 YYYY/yyyy/YY/yy 表示年份
// MM/M 月份
// W/w 星期
// dd/DD/d/D 日期
// hh/HH/h/H 时间
// mm/m 分钟
// ss/SS/s/S 秒

export function format(time, formatStr) {
    var str = formatStr;
    var date = new Date(time);
    var Week = ["日", "一", "二", "三", "四", "五", "六"];

    str = str.replace(/yyyy|YYYY/, date.getFullYear());
    str = str.replace(
        /yy|YY/,
        date.getYear() % 100 > 9
            ? (date.getYear() % 100).toString()
            : "0" + (date.getYear() % 100)
    );

    str = str.replace(
        /MM/,
        date.getMonth() + 1 > 9
            ? (date.getMonth() + 1).toString()
            : "0" + (date.getMonth() + 1)
    );
    str = str.replace(/M/g, date.getMonth() + 1);

    str = str.replace(/w|W/g, Week[date.getDay()]);

    str = str.replace(
        /dd|DD/,
        date.getDate() > 9 ? date.getDate().toString() : "0" + date.getDate()
    );
    str = str.replace(/d|D/g, date.getDate());

    str = str.replace(
        /hh|HH/,
        date.getHours() > 9 ? date.getHours().toString() : "0" + date.getHours()
    );
    str = str.replace(/h|H/g, date.getHours());
    str = str.replace(
        /mm/,
        date.getMinutes() > 9
            ? date.getMinutes().toString()
            : "0" + date.getMinutes()
    );
    str = str.replace(/m/g, date.getMinutes());

    str = str.replace(
        /ss|SS/,
        date.getSeconds() > 9
            ? date.getSeconds().toString()
            : "0" + date.getSeconds()
    );
    str = str.replace(/s|S/g, date.getSeconds());

    return str;
}

export function uncodeUtf16(str) {
    // eslint-disable-next-line no-useless-escape
    var reg = /\&#.*?;/g;
    var result = str.replace(reg, function(char) {
        var H, L, code;
        if (char.length === 9) {
            code = parseInt(char.match(/[0-9]+/g));
            H = Math.floor((code - 0x10000) / 0x400) + 0xd800;
            L = ((code - 0x10000) % 0x400) + 0xdc00;
            return unescape("%u" + H.toString(16) + "%u" + L.toString(16));
        } else {
            return char;
        }
    });
    return result;
}
export function utf16toEntities(str) {
    var patt = /[\ud800-\udbff][\udc00-\udfff]/g; // 检测utf16字符正则
    str = str.replace(patt, function(char) {
        var H, L, code;
        if (char.length === 2) {
            H = char.charCodeAt(0); // 取出高位
            L = char.charCodeAt(1); // 取出低位
            code = (H - 0xd800) * 0x400 + 0x10000 + L - 0xdc00; // 转换算法
            return "&#" + code + ";";
        } else {
            return char;
        }
    });
    return str;
}