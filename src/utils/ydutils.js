import { upload } from 'ydutils'
import Vue from "vue";
import { ACCESS_TOKEN } from "@/store/mutation-types";
const token = `${'Bearer ' + Vue.ls.get(ACCESS_TOKEN)}`;
const fileOption = { multipart: '/file/manage/common/multipart/create', upload: '/file/manage/common/upload', bucket: "source" }
const uploadFile = upload({ host: process.env.VUE_APP_API_BASE_URL, token, fileOption })

export default uploadFile
