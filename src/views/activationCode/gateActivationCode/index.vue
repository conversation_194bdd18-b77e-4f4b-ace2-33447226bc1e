<!--
 * @Descripttion: 激活码
 * @version: 1.0.0
 * @Author: jingrou
 * @Date: 2022-01-13 13:52:02
 * @LastEditors: 杨剑兴
 * @LastEditTime: 2024-10-31 09:53:40
-->
<template>
    <el-card shadow="never" class="card_warp">
        <!-- 搜索表单 -->
        <el-form
            ref="gateCodeFrom"
            :inline="true"
            :model="gateCodeFrom"
            class="demo-form-inline"
            label-position="right"
        >
            <el-form-item label="学校：" prop="schoolName">
                <el-input
                    v-model="gateCodeFrom.schoolName"
                    placeholder="请输入学校名称"
                ></el-input>
            </el-form-item>
            <el-form-item label="品牌：" prop="type">
                <el-select v-model="gateCodeFrom.type" placeholder="请选择品牌">
                    <el-option label="旷视" :value="1"></el-option>
                    <el-option label="吉为" :value="2"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="激活码：" prop="authorizeCode">
                <el-input
                    v-model="gateCodeFrom.authorizeCode"
                    placeholder="请输入激活码"
                ></el-input>
            </el-form-item>
            <el-form-item label="设备序列号：" prop="no">
                <el-input
                    v-model="gateCodeFrom.no"
                    placeholder="请输入设备序列号"
                ></el-input>
            </el-form-item>
            <el-form-item label="激活码状态：" prop="status">
                <el-select
                    v-model="gateCodeFrom.status"
                    placeholder="请选择激活码状态"
                >
                    <el-option label="全部" value></el-option>
                    <el-option label="未使用" :value="1"></el-option>
                    <el-option label="已使用" :value="2"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="onSubmit">查询</el-button>
                <el-button @click="handleSearchReset">重 置</el-button>
            </el-form-item>
        </el-form>
        <!-- 功能按钮 -->
        <div class="btn_warp">
            <span>激活码管理</span>
            <div style="display: flex">
                <el-button
                    type="primary"
                    icon="el-icon-plus"
                    v-auth="'manage.gateActivationCode.add'"
                    @click="gateCodeFromAddEdit(false)"
                    >新增激活码</el-button
                >
                <el-upload
                    class="upload-demo"
                    :headers="{ Authorization: token, platform: 'system' }"
                    :before-upload="importChang"
                    :action="action"
                    :show-file-list="false"
                    :on-success="importData"
                    name="file"
                >
                    <el-button
                        icon="el-icon-top"
                        :loading="importLoading"
                        v-auth="'manage.gateActivationCode.Import'"
                        style="margin-left: 16px"
                        type="primary"
                        plain
                        >导入</el-button
                    >
                </el-upload>

                <el-button
                    icon="el-icon-bottom"
                    style="margin-left: 16px"
                    type="primary"
                    plain
                    @click="exportData"
                    v-auth="'manage.gateActivationCode.export'"
                    :loading="exportLoading"
                    >导出</el-button
                >
                <div class="download" @click="handleExport">下载模板</div>
            </div>
        </div>
        <!-- 列表数据 -->
        <div class="content_warp">
            <el-table
                :data="list"
                style="width: 100%"
                ref="table"
                border
                :header-cell-style="{ background: '#fafafa', color: '#5b5d61' }"
            >
                <el-table-column
                    type="index"
                    width="50"
                    label="序号"
                    align="center"
                ></el-table-column>
                <el-table-column prop="type" label="品牌" align="center">
                    <template slot-scope="scope">
                        <span>{{ brandType[scope.row.type] }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="schoolName"
                    label="学校"
                    align="center"
                ></el-table-column>
                <el-table-column
                    prop="authorizeCode"
                    label="激活码"
                    align="center"
                    width="350"
                >
                    <template slot-scope="scope">
                        <span
                            style="cursor: pointer"
                            @click="authorizeCopy(scope.$index, scope.row)"
                            >{{ scope.row.authorizeCode }}</span
                        >
                    </template>
                </el-table-column>

                <el-table-column
                    prop="creationDate"
                    label="生成日期"
                    align="center"
                ></el-table-column>
                <el-table-column
                    prop="no"
                    label="设备序列号"
                    align="center"
                ></el-table-column>
                <el-table-column
                    prop="activationDate"
                    label="激活日期"
                    align="center"
                ></el-table-column>
                <el-table-column
                    prop="status"
                    label="激活码状态"
                    align="center"
                >
                    <template slot-scope="scope">
                        <span>
                            {{
                                scope.row.surplusNum == "1"
                                    ? "未使用"
                                    : scope.row.surplusNum == "0"
                                    ? "已使用"
                                    : ""
                            }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="remark"
                    align="center"
                    label="备注"
                ></el-table-column>
                <el-table-column
                    label="操作"
                    width="170"
                    align="right"
                    fixed="right"
                >
                    <template slot-scope="scope">
                        <el-link
                            type="primary"
                            icon="el-icon-edit"
                            @click="gateCodeFromCodeTransfer(scope.row)"
                            v-auth="'manage.gateActivationCode.transfer'"
                        >
                            转移
                        </el-link>
                        <el-link
                            type="primary"
                            icon="el-icon-edit"
                            @click="gateCodeFromAddEdit(true, scope.row)"
                            v-auth="'manage.gateActivationCode.edit'"
                            >编辑</el-link
                        >
                        <el-link
                            type="danger"
                            icon="el-icon-delete"
                            style="padding-left: 5px"
                            @click="delgateActivationCode(scope.row)"
                            v-auth="'manage.gateActivationCode.del'"
                            >删除</el-link
                        >
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!-- 分页 -->
        <div class="paginationBlock">
            <el-pagination
                style="margin-top: 10px"
                :current-page="pagination.current"
                :page-sizes="[10, 20, 30, 40]"
                :page-size="pagination.size"
                background
                layout="total, sizes, prev, pager, next, jumper"
                :total="pagination.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <!-- 新增编辑表单 -->
        <el-dialog
            :title="dialogTitle"
            width="30%"
            :visible.sync="dialogSchoolVisible"
            @close="cancelForm('ruleForm')"
        >
            <el-form
                :rules="rules"
                ref="ruleForm"
                label-position="right"
                label-width="100px"
                :model="dialogSchoolForm"
            >
                <div v-if="dialogTitle == '新增激活码'">
                    <el-form-item label="学校名称" prop="schoolId">
                        <el-select
                            style="width: 100%"
                            v-model="dialogSchoolForm.schoolId"
                            filterable
                            default-first-option
                            popper-class="selectClass"
                            placeholder="请选择"
                        >
                            <el-option
                                v-for="(item, index) in supplierSchoolList"
                                :key="index"
                                :label="item.name"
                                :value="item.id"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="激活码" prop="authorizeCode">
                        <el-input
                            style="width: 100%"
                            v-model="dialogSchoolForm.authorizeCode"
                            placeholder="请输入激活码"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="品牌" prop="type">
                        <el-select
                            style="width: 100%"
                            v-model="dialogSchoolForm.type"
                            placeholder="请选择品牌"
                        >
                            <el-option label="旷视" :value="1"></el-option>
                            <el-option label="吉为" :value="2"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="生成日期" prop="creationDate">
                        <el-date-picker
                            v-model="dialogSchoolForm.creationDate"
                            type="date"
                            placeholder="选择日期"
                            value-format="yyyy-MM-dd"
                        ></el-date-picker>
                    </el-form-item>
                </div>
                <div v-if="dialogTitle == '编辑激活码'">
                    <el-form-item label="激活码：" prop="authorizeCode">
                        {{ dialogSchoolForm.authorizeCode }}
                    </el-form-item>
                    <el-form-item label="品牌：" prop="type">
                        {{ brandType[dialogSchoolForm.type] }}
                    </el-form-item>
                    <el-form-item label="生成日期：" prop="creationDate">
                        {{ dialogSchoolForm.creationDate }}
                    </el-form-item>
                </div>

                <el-form-item label="备注" prop="remark">
                    <el-input
                        type="textarea"
                        style="width: 100%"
                        v-model="dialogSchoolForm.remark"
                        placeholder="请输入备注"
                        maxlength="200"
                        show-word-limit
                    ></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog_footer">
                <el-button @click="cancelForm('ruleForm')">取 消</el-button>
                <el-button
                    type="primary"
                    @click="gateCodeFromOnSubmit('ruleForm')"
                    >确 定</el-button
                >
            </div>
        </el-dialog>
        <!-- 删除表单 -->
        <el-dialog
            title="删除激活码"
            width="30%"
            :visible.sync="delDialogSchoolVisible"
        >
            <el-form
                :rules="delRules"
                ref="delRulesForm"
                label-position="right"
                label-width="100px"
                :model="delDialogSchoolForm"
            >
                <el-form-item label="激活码：" prop="authorizeCode">
                    {{ delDialogSchoolForm.authorizeCode }}
                </el-form-item>
                <el-form-item label="品牌：" prop="type">
                    {{ brandType[delDialogSchoolForm.type] }}
                </el-form-item>
                <el-form-item label="生成日期：" prop="creationDate">
                    {{ delDialogSchoolForm.creationDate }}
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input
                        type="textarea"
                        style="width: 100%"
                        v-model="delDialogSchoolForm.remark"
                        placeholder="请输入备注"
                        maxlength="200"
                        show-word-limit
                    ></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog_footer">
                <el-button @click="delCancelForm('delRulesForm')"
                    >取 消</el-button
                >
                <el-button
                    type="primary"
                    @click="delCancelFormSub('delRulesForm')"
                    >确 定</el-button
                >
            </div>
        </el-dialog>
        <!-- 上传提示 -->
        <el-dialog
            title="导入提示"
            :visible.sync="dialogVisible"
            width="30%"
            class="abow_dialog"
        >
            <div class="importDialog">
                <div>全部数据：共 {{ arrData }} 条</div>
                <div>正常数据：{{ correctData }} 条</div>
                <div>异常数据：{{ errorData }} 条</div>
                <div>
                    异常提示：
                    <div style="margin-top: 10px" v-html="errorPrompt"></div>
                </div>
            </div>
            <span slot="footer">
                <div class="butFooter">
                    <el-button
                        style="margin: 10px"
                        type="primary"
                        @click="selectAuthorizecodeList"
                        :loading="loadingImport"
                        >确 定</el-button
                    >
                    <el-button
                        style="margin: 10px"
                        @click="dialogVisible = false"
                        >取 消</el-button
                    >
                </div>
            </span>
        </el-dialog>
        <el-dialog
            class="uploadClass"
            title="特别提示"
            :visible.sync="uploadDialog"
            width="30%"
            :before-close="uploadClose"
        >
            <div
                style="
                    margin: -10px 0px 40px 0px;
                    color: #ccc;
                    line-height: 28px;
                "
            >
                导入过程中，
                <p>如发现相同数据，则新数据将覆盖掉现有数据。</p>
                <p>如有异常数据，将忽略异常数据。</p>
            </div>
            <div
                v-loading="uploadLoading"
                element-loading-text="上传加载中，请稍后...."
            ></div>
        </el-dialog>
        <!--  -->
        <el-dialog
            title="激活码转移"
            width="30%"
            :visible.sync="codeTransferOpen"
            @close="cancelReasonCodeTransferForm"
        >
            <el-form
                :rules="activationCodeTransferRules"
                ref="codeTransferSchoolRef"
                label-position="right"
                :model="activationCodeTransfer"
            >
                <el-form-item label="" prop="schoolId">
                    <el-select
                        style="width: 100%"
                        filterable
                        v-model="activationCodeTransfer.schoolId"
                        placeholder="请选择学校"
                    >
                        <el-option
                            v-for="item in supplierSchoolList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="备 注" prop="remark">
                    <el-input
                        type="textarea"
                        style="width: 100%"
                        v-model="activationCodeTransfer.remark"
                        :autosize="{ minRows: 4, maxRows: 10 }"
                        placeholder="请输入备注"
                        maxlength="200"
                        show-word-limit
                    ></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog_footer">
                <el-button @click="cancelReasonCodeTransferForm"
                    >取 消</el-button
                >
                <el-button
                    type="primary"
                    :loading="addLoading"
                    @click="cancelReasonCodeTransferSubmit"
                >
                    确 定
                </el-button>
            </div>
        </el-dialog>
    </el-card>
</template>
<script>
import { ACCESS_TOKEN } from "@/store/mutation-types";
import {
    selectAuthorizecodeList,
    addAuthorizecode,
    updateAuthorizeCode,
    deleteAuthorizeCode,
    reportingExportInfo,
    importAuthorizeCode,
    getUploadProgress,
    postCodeTransfer,
} from "@/api/gateActivationCode";
import { querySchoolInfoList } from "@/api/gateRegistrationCode.js";
export default {
    data() {
        return {
            token: "",
            brandType: {
                1: "旷视",
                2: "吉为",
            },
            gateCodeFrom: {
                type: "",
                authorizeCode: "",
                no: "",
                status: "",
                schoolName: "",
            },
            list: [],
            pagination: {
                current: 1,
                size: 10,
                total: 0,
            },
            dialogSchoolVisible: false,
            delDialogSchoolVisible: false,
            dialogSchoolForm: {
                authorizeCode: "",
                type: "",
                creationDate: "",
                remark: "",
                schoolId: "",
                schoolName: "",
                reasonDeletionName: "",
            },
            delDialogSchoolForm: {
                authorizeCode: "",
                type: "",
                creationDate: "",
                remark: "",
                schoolId: "",
                schoolName: "",
            },
            supplierSchoolList: [],
            dialogTitle: "",
            data: "",
            deldataId: "",
            rules: {
                schoolId: [
                    {
                        required: true,
                        message: "请选择学校名称",
                        trigger: "change",
                    },
                ],
                type: [
                    {
                        type: "date",
                        required: true,
                        message: "请选择品牌",
                        trigger: "change",
                    },
                ],
                authorizeCode: [
                    {
                        required: true,
                        message: "请输入激活码",
                        trigger: "blur",
                    },
                ],
                creationDate: [
                    {
                        required: true,
                        message: "请选择日期",
                        trigger: "change",
                    },
                ],
                codeTransferSchool: [
                    {
                        required: true,
                        message: "请选择转移学校",
                        trigger: "change",
                    },
                ],
                reasonDeletionRemark: [
                    {
                        required: true,
                        message: "请输入备注",
                        trigger: "change",
                    },
                ],
            },
            delRules: {
                remark: [
                    {
                        required: true,
                        message: "请输入备注",
                        trigger: "change",
                    },
                ],
            },
            copyData: "",
            importLoading: false,
            action:
                process.env.VUE_APP_API_BASE_URL +
                "/manage/v3/authorize/code/upload",
            dialogVisible: false,
            errorPrompt: "",
            exportLoading: false,
            addLoading: false,
            errorData: 0,
            correctData: 0,
            arrData: 0,
            importDataId: "",
            loadingImport: false,
            uploadDialog: false,
            uploadLoading: false,
            codeTransferOpen: false,
            activationCodeTransfer: {
                id: "",
                remark: "",
                schoolId: null,
            },
            activationCodeTransferRules: {
                schoolId: [
                    {
                        required: true,
                        message: "请选择学校名称",
                        trigger: "change",
                    },
                ],
                remark: [
                    {
                        required: true,
                        message: "请输入备注",
                        trigger: "change",
                    },
                ],
            },
        };
    },
    methods: {
        handleExport() {
            let a = document.createElement("a");
            document.body.appendChild(a);
            a.href = "/template/激活码模板.xlsx";
            a.download = "激活码模板" + ".xlsx";
            a.style.display = "none";
            a.click();
            document.body.removeChild(a);
        },
        // 学校接口
        searchSchoolList() {
            const obj = {
                current: 1,
                size: 999,
            };
            querySchoolInfoList(obj).then((res) => {
                this.supplierSchoolList = res.data;
            });
        },
        // 导出
        exportData() {
            this.exportLoading = true;
            let obj = {
                ...this.gateCodeFrom,
            };
            reportingExportInfo(obj, "激活码系统列表").finally(() => {
                this.exportLoading = false;
                this.$message.success("导出成功");
            });
        },
        cancelForm(formName) {
            this.dialogSchoolVisible = false;
            this.$refs[formName].resetFields();
            this.selectAuthorizecodeList();
        },
        delCancelForm(formName) {
            this.delDialogSchoolVisible = false;
            this.$refs[formName].resetFields();
            this.selectAuthorizecodeList();
        },
        // 导入
        importChang() {
            this.importLoading = true;
            this.dialogVisible = false;
        },
        imporError() {
            this.importLoading = false;
            this.$message.error("导入失败，请重新导入");
        },
        // 导入成功
        importData(res) {
            if (res.code === 0) {
                this.uploadDialog = true;
                this.uploadLoading = true;
                this.importLoading = false;
                this.getUploadProgress(res.data.fileId);
            } else {
                this.importLoading = false;
                this.$message.error(res.message);
            }
        },
        uploadClose() {
            this.uploadDialog = false;
            this.uploadLoading = false;
        },
        getUploadProgress(id) {
            this.loadingImport = true;
            getUploadProgress({ fileId: id })
                .then((res) => {
                    if (res.data.progress === 100) {
                        this.uploadDialog = false;
                        this.uploadLoading = false;

                        if (res.data.errorCount > 0) {
                            this.importLoading = false;
                            this.dialogVisible = true;
                            this.importDataId = res.data.id;
                            this.errorData = res.data.errorCount; // 错误条数
                            this.correctData = res.data.successCount; // 正确条数
                            this.arrData = res.data.totalRow; // 正确条数
                            let result = "";
                            res.data.errorMsg.map((str) => {
                                result += `${str}<br/><br/>`;
                            });
                            this.errorPrompt = result;
                        } else {
                            this.importLoading = false;
                            this.dialogVisible = false;
                            this.$message.success(res.message);
                            this.selectAuthorizecodeList();
                        }
                    } else {
                        setTimeout(() => {
                            this.getUploadProgress(id);
                        }, 2000);
                    }
                })
                .finally(() => {
                    this.loadingImport = false;
                });
        },
        // 复制
        authorizeCopy(index, row) {
            this.copyData = row.authorizeCode;
            this.copy(this.copyData);
        },
        copy(data) {
            let url = data;
            let oInput = document.createElement("input");
            oInput.value = url;
            document.body.appendChild(oInput);
            oInput.select(); // 选择对象;
            console.log(oInput.value);
            document.execCommand("Copy"); // 执行浏览器复制命令
            this.$message({
                message: "复制成功",
                type: "success",
            });
            oInput.remove();
        },
        // 列表表格
        selectAuthorizecodeList() {
            this.dialogVisible = false;
            const obj = {
                ...this.gateCodeFrom,
                pageNo: this.pagination.current,
                pageSize: this.pagination.size,
            };
            selectAuthorizecodeList(obj).then((res) => {
                this.list = res.data.list;
                this.pagination.current = res.data.pageNo;
                this.pagination.size = res.data.pageSize;
                this.pagination.total = res.data.total;
            });
        },
        // 新增编辑按钮
        gateCodeFromAddEdit(status, data) {
            this.searchSchoolList();
            if (status == true) {
                this.data = data;
                this.dialogSchoolVisible = true;
                this.dialogTitle = "编辑激活码";
                this.dialogSchoolForm = data;
            } else if (status == false) {
                this.dialogTitle = "新增激活码";
                this.dialogSchoolVisible = true;
                this.dialogSchoolForm = {};
            }
        },
        // 删除激活码
        delgateActivationCode(data) {
            this.deldataId = data.id;
            this.delDialogSchoolVisible = true;
            this.delDialogSchoolForm = data;
            delete this.delDialogSchoolForm.remark;
        },
        // 弹框确定按钮
        gateCodeFromOnSubmit(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    if (this.dialogTitle == "新增激活码") {
                        this.addAuthorizecode();
                    } else if (this.dialogTitle == "编辑激活码") {
                        this.updateAuthorizeCode();
                    }
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        // 删除
        delCancelFormSub(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.delAuthorizeCode();
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        // 删除接口
        delAuthorizeCode() {
            const obj = {
                id: this.deldataId,
                remark: this.delDialogSchoolForm.remark,
            };
            deleteAuthorizeCode(obj)
                .then((res) => {
                    this.$message.success(res.message);
                })
                .finally(() => {
                    this.selectAuthorizecodeList();
                    this.delDialogSchoolVisible = false;
                });
        },

        // 编辑接口
        updateAuthorizeCode() {
            this.addLoading = true;
            const obj = {
                id: this.data.id,
                remark: this.dialogSchoolForm.remark,
            };
            updateAuthorizeCode(obj)
                .then((res) => {
                    this.$message.success(res.message);
                })
                .finally(() => {
                    this.selectAuthorizecodeList();
                    this.dialogSchoolVisible = false;
                    this.addLoading = false;
                });
        },
        // 新增接口
        addAuthorizecode() {
            this.addLoading = true;
            const obj = {
                ...this.dialogSchoolForm,
            };
            addAuthorizecode(obj)
                .then((res) => {
                    this.$message.success(res.message);
                })
                .finally(() => {
                    this.selectAuthorizecodeList();
                    this.dialogSchoolVisible = false;
                    this.addLoading = false;
                });
        },
        // 分页
        handleSizeChange(val) {
            this.pagination.current = 1;
            this.pagination.size = val;
            this.selectAuthorizecodeList();
        },
        handleCurrentChange(val) {
            this.pagination.current = val;
            this.selectAuthorizecodeList();
        },
        onSubmit() {
            (this.pagination.current = 1), this.selectAuthorizecodeList();
        },
        handleSearchReset() {
            this.$refs.gateCodeFrom.resetFields();
            this.selectAuthorizecodeList();
        },
        // 计数器
        handleChangeNum(value) {
            console.log(value);
        },

        //  打开激活码转移弹框
        gateCodeFromCodeTransfer(item) {
            this.activationCodeTransfer.id = item.id;
            this.codeTransferOpen = true;
            this.searchSchoolList();
        },

        // 关闭删除激活码转移弹框
        cancelReasonCodeTransferForm() {
            this.codeTransferOpen = false;
            this.$refs.codeTransferSchoolRef.resetFields();
        },
        // 提交删除激活码转移弹框
        cancelReasonCodeTransferSubmit() {
            const _this = this;
            this.$refs.codeTransferSchoolRef.validate((valid) => {
                if (valid) {
                    postCodeTransfer(_this.activationCodeTransfer).then(
                        (res) => {
                            _this.$message.success(res.message);
                            this.cancelReasonCodeTransferForm();
                            this.selectAuthorizecodeList();
                        }
                    );
                } else {
                    return false;
                }
            });
        },
    },
    created() {
        this.selectAuthorizecodeList();
        this.token = "Bearer " + this.$ls.get(ACCESS_TOKEN);
    },
};
</script>

<style lang="scss" scoped>
.importDialog {
    div {
        width: 100%;
        height: 45px;
        display: flex;
        flex-direction: column;
    }
}
.btn_warp {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0px;
    border-top: 1px solid #ebeef5;
}
.paginationBlock {
    margin-top: 16px;
    text-align: right;
}
.download {
    justify-content: center;
    display: flex;
    align-items: center;
    margin: 10px;
    color: #409eff;
    cursor: pointer;
}
</style>
<style lang="scss">
.uploadClass {
    .el-dialog__body {
        height: 160px;
    }
}
.abow_dialog {
    display: flex;
    justify-content: center;
    align-items: Center;
    overflow: hidden;
    .el-dialog {
        margin: 0 auto !important;
        height: 50%;
        overflow: hidden;
        .el-dialog__body {
            position: absolute;
            height: 65%;
            left: 0;
            top: 54px;
            bottom: 0;
            right: 0;
            padding: 30px 30px 0px 30px;
            z-index: 1;
            overflow: hidden;
            overflow-y: auto;
        }
        .el-dialog__footer {
            .butFooter {
                position: absolute;
                right: 0;
                bottom: 20px;
                width: 100%;
                position: absolute;
                right: 0;
                bottom: 20px;
                display: flex;
                justify-content: center;
            }
        }
    }
}
</style>
