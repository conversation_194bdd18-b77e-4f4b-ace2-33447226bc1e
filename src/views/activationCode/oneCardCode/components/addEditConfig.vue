<template>
    <div>
        <el-dialog :title="dialogTitle" width="40vw" :visible.sync="dialogSchoolVisible"
            @close="cancelForm('ruleForm')">
            <el-form :rules="rules" ref="ruleForm" label-position="right" label-width="80px" :model="form">
                <el-form-item label="APPID" prop="appId">
                    <el-input style="width: 100%" v-model="form.appId" placeholder="请输入APPID" :maxlength="500"
                        show-word-limit></el-input>
                </el-form-item>
                <el-form-item label="SDK key" prop="sdkKey">
                    <el-input style="width: 100%" v-model="form.sdkKey" placeholder="请输入SDK key" :maxlength="500"
                        show-word-limit></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog_footer">
                <el-button @click="cancelForm('ruleForm')">取 消</el-button>
                <el-button type="primary" :loading="addLoading" @click="gateCodeFromOnSubmit('ruleForm')">确
                    定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { addConfig, updateConfig, } from "@/api/oneCardCode";
export default {
    name: "AddEditConfig",
    data() {
        return {
            addLoading: false,
            dialogSchoolVisible: false,
            dialogTitle: "",
            form: {},
            rules: {
                appId: [
                    {
                        required: true,
                        message: "请输入APPID",
                        trigger: "blur",
                    },
                ],
                sdkKey: [
                    {
                        required: true,
                        message: "请输入SDK key",
                        trigger: "change",
                    },
                ],
            },
        }
    },
    methods: {
        open(type, data) {
            if (type == 'edit') {
                this.dialogSchoolVisible = true;
                this.dialogTitle = "编辑";
                this.form = data;
            } else if (type == 'add') {
                this.dialogTitle = "新增";
                this.dialogSchoolVisible = true;
                this.form = {};
            }
        },
        // 编辑接口
        updateConfigFn() {
            this.addLoading = true;
            updateConfig(this.form)
                .then((res) => {
                    this.$message.success(res.message);
                })
                .finally(() => {
                    this.$emit("refurbish");
                    this.dialogSchoolVisible = false;
                    this.addLoading = false;
                });
        },
        // 新增接口
        addConfigFn() {
            this.addLoading = true;
            addConfig(this.form)
                .then((res) => {
                    this.$message.success(res.message);
                })
                .finally(() => {
                    this.$emit("refurbish");
                    this.dialogSchoolVisible = false;
                    this.addLoading = false;
                });
        },
        // 弹框确定按钮
        gateCodeFromOnSubmit(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    if (this.dialogTitle == "新增") {
                        this.addConfigFn();
                    } else if (this.dialogTitle == "编辑") {
                        this.updateConfigFn();
                    }
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        cancelForm(formName) {
            this.dialogSchoolVisible = false;
            this.$refs[formName].resetFields();
            this.$emit("refurbish");
        },
    },
}
</script>

<style lang="scss" scoped></style>