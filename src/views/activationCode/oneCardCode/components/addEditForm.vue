<template>
    <div>
        <el-dialog :title="dialogTitle" width="40vw" :visible.sync="dialogSchoolVisible"
            @close="cancelForm('ruleForm')">
            <el-form :rules="rules" ref="ruleForm" label-position="right" label-width="150px" :model="dialogSchoolForm">
                <el-form-item label="学校名称" prop="schoolId">
                    <el-select :disabled="dialogTitle == '编辑'" style="width: 100%" v-model="dialogSchoolForm.schoolId"
                        filterable default-first-option popper-class="selectClass" placeholder="请选择">
                        <el-option v-for="(item, index) in supplierSchoolList" :key="index" :label="item.name"
                            :value="item.id"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="品牌" prop="brandCode">
                    <el-select :disabled="dialogTitle == '编辑'" style="width: 100%" v-model="dialogSchoolForm.brandCode"
                        placeholder="请选择品牌">
                        <el-option :label="item.name" :value="item.code" v-for="item, index in brandCodeList"
                            :key="index"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="APPID/SDK key" prop="configId">
                    <el-select :disabled="dialogTitle == '编辑'" clear="select_class" style="width: 100%;"
                        v-model="dialogSchoolForm.configId" placeholder="请选择APPID/SDK key">
                        <el-option :label="`${item.appId}/${item.sdkKey}`" :value="item.id"
                            v-for="item, index in codeConfigList" :key="index"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="激活码" prop="activationCode">
                    <el-input :disabled="dialogTitle == '编辑'" style="width: 100%"
                        v-model="dialogSchoolForm.activationCode" placeholder="请输入激活码"></el-input>
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input type="textarea" style="width: 100%" v-model="dialogSchoolForm.remark" placeholder="请输入备注"
                        maxlength="200" show-word-limit></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog_footer">
                <el-button @click="cancelForm('ruleForm')">取 消</el-button>
                <el-button type="primary" :loading="addLoading" @click="gateCodeFromOnSubmit('ruleForm')">确
                    定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {
    getBrandCodelist,
    getCodeConfigList,
    addAuthorizecode,
    updateAuthorizeCode,
} from "@/api/oneCardCode";
import { querySchoolInfoList } from "@/api/gateRegistrationCode.js";
export default {
    name: "AddEditForm",
    data() {
        return {
            addLoading: false,
            dialogSchoolVisible: false,
            dialogTitle: "",
            dialogSchoolForm: {},
            supplierSchoolList: [],
            brandCodeList: [],
            codeConfigList: [],
            rules: {
                schoolId: [
                    {
                        required: true,
                        message: "请选择学校名称",
                        trigger: "change",
                    },
                ],
                brandCode: [
                    {
                        required: true,
                        message: "请选择品牌",
                        trigger: "change",
                    },
                ],
                activationCode: [
                    {
                        required: true,
                        message: "请输入激活码",
                        trigger: "blur",
                    },
                ],
                configId: [
                    {
                        required: true,
                        message: "请选择APPID/SDK key",
                        trigger: "change",
                    },
                ],
                remark: [
                    {
                        required: true,
                        message: "请输入备注",
                        trigger: "change",
                    },
                ],
            },
        }
    },
    methods: {
        // 学校接口
        searchSchoolList() {
            const obj = {
                current: 1,
                size: 999,
            };
            querySchoolInfoList(obj).then((res) => {
                this.supplierSchoolList = res.data;
            });
        },
        getCodeConfig() {
            getCodeConfigList().then((res) => {
                this.codeConfigList = res.data;
            })
        },

        getBrandCode() {
            getBrandCodelist().then((res) => {
                this.brandCodeList = res.data;
            })
        },
        open(status, data) {
            this.getCodeConfig()
            this.getBrandCode()
            this.searchSchoolList();
            if (status == true) {
                this.data = data;
                this.dialogSchoolVisible = true;
                this.dialogTitle = "编辑";
                this.dialogSchoolForm = data;
            } else if (status == false) {
                this.dialogTitle = "新增";
                this.dialogSchoolVisible = true;
                this.dialogSchoolForm = {};
            }
        },
        // 编辑接口
        updateAuthorizeCode() {
            this.addLoading = true;
            const obj = {
                id: this.data.id,
                remark: this.dialogSchoolForm.remark,
            };
            updateAuthorizeCode(obj)
                .then((res) => {
                    this.$message.success(res.message);
                })
                .finally(() => {
                    this.$emit("refurbish");
                    this.dialogSchoolVisible = false;
                    this.addLoading = false;
                });
        },
        // 新增接口
        addAuthorizecode() {
            this.addLoading = true;
            const obj = {
                ...this.dialogSchoolForm,
            };
            addAuthorizecode(obj)
                .then((res) => {
                    this.$message.success(res.message);
                })
                .finally(() => {
                    this.$emit("refurbish");
                    this.dialogSchoolVisible = false;
                    this.addLoading = false;
                });
        },
        // 弹框确定按钮
        gateCodeFromOnSubmit(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    if (this.dialogTitle == "新增") {
                        this.addAuthorizecode();
                    } else if (this.dialogTitle == "编辑") {
                        this.updateAuthorizeCode();
                    }
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        cancelForm(formName) {
            this.dialogSchoolVisible = false;
            this.$refs[formName].resetFields();
            this.$emit("refurbish");
        },
    },
}
</script>

<style lang="scss" scoped></style>