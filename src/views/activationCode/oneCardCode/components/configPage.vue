<template>
    <el-card shadow="never" class="configPage">
        <div class="headBox">
            <i class="el-icon-back" @click="goBack"></i>配置
        </div>
        <div class="btn_warp">
            <el-button type="primary" icon="el-icon-plus" v-auth="'manage.oneCardCode.config.add'"
                @click="addEdit('add')">新增</el-button>
        </div>
        <!-- 列表数据 -->
        <div class="content_warp">
            <el-table :data="list" style="width: 100%" ref="table" border
                :header-cell-style="{ background: '#fafafa', color: '#5b5d61' }">
                <el-table-column type="index" label="序号" width="80" align="center"></el-table-column>
                <el-table-column prop="appId" label="APPID" align="center"></el-table-column>
                <el-table-column prop="sdkKey" label="SDK key" align="center"></el-table-column>
                <el-table-column prop="updateTime" label="更新时间" align="center"></el-table-column>
                <el-table-column label="操作" width="170" align="right" fixed="right">
                    <template slot-scope="scope">
                        <el-link type="primary" icon="el-icon-edit" @click="addEdit('edit', scope.row)"
                            v-auth="'manage.oneCardCode.config.edit'">编辑</el-link>
                        <el-link type="danger" icon="el-icon-delete" style="padding-left: 5px"
                            @click="delectConfig(scope.row)" v-auth="'manage.oneCardCode.config.del'">删除</el-link>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!-- 分页 -->
        <div class="paginationBlock">
            <el-pagination style="margin-top: 10px" :current-page="pagination.current" :page-sizes="[10, 20, 30, 40]"
                :page-size="pagination.size" background layout="total, sizes, prev, pager, next, jumper"
                :total="pagination.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </div>
        <add-edit-config ref="addEditConfigRef" @refurbish="getList" />
    </el-card>
</template>

<script>
import { getConfigPage, removeConfig } from "@/api/oneCardCode";
import AddEditConfig from './addEditConfig.vue'
export default {
    components: { AddEditConfig },
    data() {
        return {
            list: [],
            pagination: {
                current: 1,
                size: 10,
                total: 0,
            },

        }
    },
    methods: {
        // 返回
        goBack() {
            this.$emit("back");
        },
        addEdit(type, item = {}) {
            this.$refs.addEditConfigRef.open(type, item)
        },
        delectConfig(row) {
            this.$confirm("是否确定删除此配置吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    removeConfig({ ids: [row.id] }).then(({ message }) => {
                        this.$message.success(message);
                        this.getList();
                    });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消删除",
                    });
                });
        },
        getList() {
            const obj = {
                pageNo: this.pagination.current,
                pageSize: this.pagination.size,
            };
            getConfigPage(obj).then((res) => {
                this.list = res.data.list
                this.pagination.current = res.data.pageNo;
                this.pagination.size = res.data.pageSize;
                this.pagination.total = res.data.total;
            })
        },
        // 分页
        handleSizeChange(val) {
            this.pagination.current = 1;
            this.pagination.size = val;
            this.getList();
        },
        handleCurrentChange(val) {
            this.pagination.current = val;
            this.getList();
        },
    },
    created() {
        this.getList();
    },
}
</script>

<style lang="scss" scoped>
.configPage {
    height: 100%;

    .headBox {
        height: 62px;
        border-bottom: 1px solid #d9d9d9;
        font-weight: 600;
        font-size: 18px;
        color: #262626;
        line-height: 22px;
        display: flex;
        align-items: center;

        .el-icon-back {
            cursor: pointer;
            padding-right: 8px;
            font-size: 24px;
            color: #318afb;
        }
    }

    .btn_warp {
        display: flex;
        justify-content: flex-end;
        margin: 20px 0;
    }

    .paginationBlock {
        margin-top: 16px;
        text-align: right;
    }
}
</style>