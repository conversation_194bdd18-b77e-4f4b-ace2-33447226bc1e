<template>
    <div>
        <el-dialog title="删除激活码" width="30%" :visible.sync="delDialogSchoolVisible">
            <el-form :rules="delRules" ref="delRulesForm" label-position="right" label-width="60px"
                :model="delDialogSchoolForm">
                <div class="delect_tip">确定删除已选激活码？</div>
                <el-form-item label="备注" prop="remark">
                    <el-input type="textarea" style="width: 100%" v-model="delDialogSchoolForm.remark"
                        placeholder="请输入备注" maxlength="200" show-word-limit></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog_footer">
                <el-button @click="delCancelForm('delRulesForm')">取 消</el-button>
                <el-button type="primary" @click="delCancelFormSub('delRulesForm')">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { deleteAuthorizeCode } from "@/api/oneCardCode";
export default {
    data() {
        return {
            delDialogSchoolVisible: false,
            delDialogSchoolForm: {},
            deldataId: "",
            delRules: {
                remark: [
                    {
                        required: true,
                        message: "请输入备注",
                        trigger: "change",
                    },
                ],
            },
        }
    },
    methods: {
        open(data) {
            this.deldataId = data.id;
            this.delDialogSchoolVisible = true;
            this.delDialogSchoolForm = data;
            delete this.delDialogSchoolForm.remark;
        },
        // 删除接口
        delAuthorizeCode() {
            const obj = {
                ids: [this.deldataId],
                remark: this.delDialogSchoolForm.remark,
            };
            deleteAuthorizeCode(obj)
                .then((res) => {
                    this.$message.success(res.message);
                })
                .finally(() => {
                    this.$emit("refurbish");
                    this.delDialogSchoolVisible = false;
                });
        },
        delCancelForm(formName) {
            this.delDialogSchoolVisible = false;
            this.$refs[formName].resetFields();
            this.$emit("refurbish");
        },
        delCancelFormSub(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.delAuthorizeCode();
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
    }
}
</script>

<style lang="scss" scoped>
.delect_tip {
    text-align: center;
    font-size: 30px;
    font-weight: 600;
    padding: 20px 0;
}
</style>