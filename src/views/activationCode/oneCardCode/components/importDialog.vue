<template>
    <div>
        <!-- 上传提示 -->
        <el-dialog title="导入提示" :visible.sync="dialogVisible" width="30%" class="abow_dialog">
            <div class="importDialog">
                <div>全部数据：共 {{ arrData }} 条</div>
                <div>正常数据：{{ correctData }} 条</div>
                <div>异常数据：{{ errorData }} 条</div>
                <div>
                    异常提示：
                    <div style="margin-top: 10px" v-html="errorPrompt"></div>
                </div>
            </div>
            <span slot="footer">
                <div class="butFooter">
                    <el-button style="margin: 10px" type="primary" @click="sumbit" :loading="loadingImport">确
                        定</el-button>
                    <el-button style="margin: 10px" @click="dialogVisible = false">取 消</el-button>
                </div>
            </span>
        </el-dialog>
        <el-dialog class="uploadClass" title="特别提示" :visible.sync="uploadDialog" width="30%"
            :before-close="uploadClose">
            <div style="
                    margin: -10px 0px 40px 0px;
                    color: #ccc;
                    line-height: 28px;
                ">
                导入过程中，
                <p>如发现相同数据，则新数据将覆盖掉现有数据。</p>
                <p>如有异常数据，将忽略异常数据。</p>
            </div>
            <div v-loading="uploadLoading" element-loading-text="上传加载中，请稍后...."></div>
            <div style="margin-top:90px">
                <el-progress v-if="schedule != 0 && schedule !== 100" :percentage="schedule"></el-progress>
            </div>
        </el-dialog>
    </div>
</template>


<script>
import { getUploadProgress, } from "@/api/oneCardCode";
export default {
    data() {
        return {
            dialogVisible: false,
            errorPrompt: "",
            schedule: 0,
            uploadDialog: false,
            uploadLoading: false,
            loadingImport: false,
            errorData: 0,
            correctData: 0,
            arrData: 0,
        }
    },
    methods: {
        getUploadProgress(id) {
            this.loadingImport = true;
            getUploadProgress({ importId: id })
                .then((res) => {
                    this.schedule = res.data.schedule || 0;
                    if (res.data.schedule === 100) {
                        this.uploadDialog = false;
                        this.uploadLoading = false;

                        if (res.data.errorCount > 0) {
                            this.dialogVisible = true;
                            this.importDataId = res.data.id;
                            this.errorData = res.data.errorCount; // 错误条数
                            this.correctData = res.data.successCount; // 正确条数
                            this.arrData = res.data.totalRow; // 正确条数
                            let result = "";
                            res.data.errorMsg.map((str) => {
                                result += `${str}<br/><br/>`;
                            });
                            this.errorPrompt = result;
                        } else {
                            this.dialogVisible = false;
                            this.$message.success(res.message);
                            this.$emit('refurbish')
                        }
                    } else {
                        setTimeout(() => {
                            this.getUploadProgress(id);
                        }, 2000);
                    }
                })
                .finally(() => {
                    this.loadingImport = false;
                });
        },
        openTip(res) {
            if (res.code === 0) {
                this.uploadDialog = true;
                this.uploadLoading = true;
                this.getUploadProgress(res.data);
            } else {
                this.$message.error(res.message);
            }
        },
        sumbit() {
            this.dialogVisible = false;
            this.$emit('refurbish')
        },
        open() {
            this.dialogVisible = false;
        },
        uploadClose() {
            this.uploadDialog = false;
            this.uploadLoading = false;
        },
    }

}
</script>

<style lang="scss" scoped></style>