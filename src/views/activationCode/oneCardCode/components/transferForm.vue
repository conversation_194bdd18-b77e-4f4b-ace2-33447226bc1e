<template>
    <div>
        <el-dialog title="激活码转移" width="30%" :visible.sync="codeTransferOpen" @close="cancelReasonCodeTransferForm">
            <el-form :rules="activationCodeTransferRules" ref="codeTransferSchoolRef" label-position="right"
                :model="activationCodeTransfer">
                <el-form-item label="" prop="schoolId">
                    <el-select style="width: 100%" filterable v-model="activationCodeTransfer.schoolId"
                        placeholder="请选择学校">
                        <el-option v-for="item in supplierSchoolList" :key="item.id" :label="item.name"
                            :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="备 注" prop="remark">
                    <el-input type="textarea" style="width: 100%" v-model="activationCodeTransfer.remark"
                        :autosize="{ minRows: 4, maxRows: 10 }" placeholder="请输入备注" maxlength="200"
                        show-word-limit></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog_footer">
                <el-button @click="cancelReasonCodeTransferForm">取 消</el-button>
                <el-button type="primary" :loading="addLoading" @click="cancelReasonCodeTransferSubmit">
                    确 定
                </el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { postCodeTransfer, } from "@/api/oneCardCode";
import { querySchoolInfoList } from "@/api/gateRegistrationCode.js";
export default {
    data() {
        return {
            codeTransferOpen: false,
            addLoading: false,
            activationCodeTransfer: {
                id: "",
                remark: "",
                schoolId: null,
            },
            supplierSchoolList: [],
            activationCodeTransferRules: {
                schoolId: [
                    {
                        required: true,
                        message: "请选择学校名称",
                        trigger: "change",
                    },
                ],
                remark: [
                    {
                        required: true,
                        message: "请输入备注",
                        trigger: "change",
                    },
                ],
            },

        }
    },
    methods: {
        // 学校接口
        searchSchoolList() {
            const obj = {
                current: 1,
                size: 999,
            };
            querySchoolInfoList(obj).then((res) => {
                this.supplierSchoolList = res.data;
            });
        },
        open(item) {
            this.activationCodeTransfer.id = item.id;
            this.codeTransferOpen = true;
            this.searchSchoolList();
        },
        // 关闭删除激活码转移弹框
        cancelReasonCodeTransferForm() {
            this.codeTransferOpen = false;
            this.$emit("refurbish");
            this.$refs.codeTransferSchoolRef.resetFields();
        },
        // 提交删除激活码转移弹框
        cancelReasonCodeTransferSubmit() {
            const _this = this;
            this.$refs.codeTransferSchoolRef.validate((valid) => {
                if (valid) {
                    postCodeTransfer(_this.activationCodeTransfer).then(
                        (res) => {
                            _this.$message.success(res.message);
                            this.cancelReasonCodeTransferForm();
                        }
                    );
                } else {
                    return false;
                }
            });
        },
    }
}
</script>

<style lang="scss" scoped></style>