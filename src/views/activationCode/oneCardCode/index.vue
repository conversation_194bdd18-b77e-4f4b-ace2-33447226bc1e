<!--
 * @Descripttion: 激活码
 * @version: 1.0.0
 * @Author: jingrou
 * @Date: 2022-01-13 13:52:02
 * @LastEditors: 杨剑兴
 * @LastEditTime: 2024-10-31 09:53:40
-->
<template>
    <div>
        <el-card shadow="never" class="card_warp" v-if="!isHide">
            <!-- 搜索表单 -->
            <el-form
                ref="gateCodeFrom"
                :inline="true"
                :model="gateCodeFrom"
                class="demo-form-inline"
                label-position="right"
            >
                <el-form-item label="学校：" prop="schoolName">
                    <el-input
                        v-model="gateCodeFrom.schoolName"
                        placeholder="请输入学校名称"
                    ></el-input>
                </el-form-item>
                <el-form-item label="APPID：" prop="appId">
                    <el-input
                        v-model="gateCodeFrom.appId"
                        placeholder="请输入APPID"
                    ></el-input>
                </el-form-item>
                <el-form-item label="SDK key：" prop="sdkKey">
                    <el-input
                        v-model="gateCodeFrom.sdkKey"
                        placeholder="请输入sdkKey"
                    ></el-input>
                </el-form-item>
                <el-form-item label="激活码：" prop="activationCode">
                    <el-input
                        v-model="gateCodeFrom.activationCode"
                        placeholder="请输入激活码"
                    ></el-input>
                </el-form-item>
                <el-form-item label="设备IMEI：" prop="imei">
                    <el-input
                        v-model="gateCodeFrom.imei"
                        placeholder="请输入设备IMEI"
                    ></el-input>
                </el-form-item>
                <el-form-item label="激活码状态：" prop="activationStatus">
                    <el-select
                        v-model="gateCodeFrom.activationStatus"
                        placeholder="请选择激活码状态"
                    >
                        <el-option label="全部" value></el-option>
                        <el-option label="未激活" :value="0"></el-option>
                        <el-option label="已激活" :value="1"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="激活时间：" prop="status">
                    <el-date-picker
                        style="width: 100%"
                        @input="changeDatePicker"
                        v-model="gateCodeFrom.timeList"
                        type="datetimerange"
                        range-separator="至"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="onSubmit">查询</el-button>
                    <el-button @click="handleSearchReset">重 置</el-button>
                </el-form-item>
            </el-form>
            <!-- 功能按钮 -->
            <div class="btn_warp">
                <span>激活码管理</span>
                <div style="display: flex">
                    <el-button
                        type="primary"
                        icon="el-icon-plus"
                        v-auth="'manage.oneCardCode.add'"
                        @click="gateCodeFromAddEdit(false)"
                        >新增</el-button
                    >
                    <el-button
                        style="margin-left: 16px"
                        type="primary"
                        plain
                        @click="configFn"
                        v-auth="'manage.oneCardCode.config'"
                        :loading="exportLoading"
                        >配置</el-button
                    >
                    <el-upload
                        class="upload-demo"
                        :headers="{ Authorization: token, platform: 'system' }"
                        :before-upload="importChang"
                        :action="action"
                        :data="{ importType: 3 }"
                        :show-file-list="false"
                        :on-success="importData"
                        name="file"
                    >
                        <el-button
                            icon="el-icon-top"
                            :loading="importLoading"
                            v-auth="'manage.gateActivationCode.Import'"
                            style="margin-left: 16px"
                            type="primary"
                            plain
                            >导入</el-button
                        >
                    </el-upload>
                    <el-button
                        icon="el-icon-bottom"
                        style="margin-left: 16px"
                        type="primary"
                        plain
                        @click="exportData"
                        v-auth="'manage.oneCardCode.export'"
                        :loading="exportLoading"
                        >导出</el-button
                    >
                    <div class="download" @click="handleExport">下载模板</div>
                </div>
            </div>
            <!-- 列表数据 -->
            <div class="content_warp">
                <el-table
                    :data="list"
                    style="width: 100%"
                    ref="table"
                    border
                    :header-cell-style="{
                        background: '#fafafa',
                        color: '#5b5d61',
                    }"
                >
                    <el-table-column
                        type="index"
                        width="50"
                        label="序号"
                        align="center"
                    ></el-table-column>
                    <el-table-column
                        prop="brandName"
                        label="品牌"
                        align="center"
                    ></el-table-column>
                    <el-table-column
                        prop="schoolName"
                        label="学校"
                        align="center"
                    ></el-table-column>
                    <el-table-column
                        prop="appId"
                        width="150"
                        label="APPID"
                        align="center"
                    ></el-table-column>
                    <el-table-column
                        prop="sdkKey"
                        label="SDK key"
                        align="center"
                    ></el-table-column>
                    <el-table-column
                        prop="activationCode"
                        label="激活码"
                        align="center"
                        width="350"
                    >
                        <template slot-scope="scope">
                            <span
                                style="cursor: pointer"
                                @click="authorizeCopy(scope.$index, scope.row)"
                                >{{ scope.row.activationCode }}</span
                            >
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="imei"
                        label="设备IMEI"
                        align="center"
                    ></el-table-column>
                    <el-table-column
                        prop="activationStatus"
                        label="激活状态"
                        align="center"
                    >
                        <template slot-scope="scope">
                            <span>
                                {{
                                    { 0: "未激活", 1: "已激活" }[
                                        scope.row.activationStatus
                                    ] || "-"
                                }}</span
                            >
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="createTime"
                        label="导入时间"
                        align="center"
                    ></el-table-column>
                    <el-table-column
                        prop="activationTime"
                        label="激活时间"
                        align="center"
                    ></el-table-column>
                    <el-table-column
                        prop="remark"
                        align="center"
                        label="备注"
                    ></el-table-column>
                    <el-table-column
                        label="操作"
                        width="170"
                        align="right"
                        fixed="right"
                    >
                        <template slot-scope="scope">
                            <el-link
                                type="primary"
                                icon="el-icon-edit"
                                @click="gateCodeFromCodeTransfer(scope.row)"
                                v-auth="'manage.oneCardCode.transfer'"
                            >
                                转移
                            </el-link>
                            <el-link
                                type="primary"
                                icon="el-icon-edit"
                                @click="gateCodeFromAddEdit(true, scope.row)"
                                v-auth="'manage.oneCardCode.edit'"
                                >编辑</el-link
                            >
                            <el-link
                                type="danger"
                                icon="el-icon-delete"
                                style="padding-left: 5px"
                                @click="delgateActivationCode(scope.row)"
                                v-auth="'manage.oneCardCode.del'"
                                >删除</el-link
                            >
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <!-- 分页 -->
            <div class="paginationBlock">
                <el-pagination
                    style="margin-top: 10px"
                    :current-page="pagination.current"
                    :page-sizes="[10, 20, 30, 40]"
                    :page-size="pagination.size"
                    background
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pagination.total"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </div>
            <!-- 新增编辑表单 -->
            <add-edit-form
                ref="addEditRef"
                @refurbish="selectAuthorizecodeList"
            />

            <!-- 转移表单 -->
            <transfer-form
                ref="transferRef"
                @refurbish="selectAuthorizecodeList"
            />

            <!-- 删除表单 -->
            <delect-form ref="delectRef" @refurbish="selectAuthorizecodeList" />

            <!-- 导入 -->
            <import-dialog
                ref="importDialogRef"
                @refurbish="selectAuthorizecodeList"
            />
        </el-card>
        <config-page v-else @back="isHide = false" ref="configRef" />
    </div>
</template>
<script>
import { ACCESS_TOKEN } from "@/store/mutation-types";
import { getList, reportingExportInfo } from "@/api/oneCardCode";
import { querySchoolInfoList } from "@/api/gateRegistrationCode.js";
import AddEditForm from "./components/addEditForm.vue";
import TransferForm from "./components/transferForm.vue";
import DelectForm from "./components/delectForm.vue";
import ImportDialog from "./components/importDialog.vue";
import ConfigPage from "./components/configPage.vue";
export default {
    components: {
        AddEditForm,
        TransferForm,
        DelectForm,
        ImportDialog,
        ConfigPage,
    },
    data() {
        return {
            isHide: false,
            token: "",
            brandType: {
                1: "旷视",
                2: "吉为",
            },
            gateCodeFrom: {
                timeList: [],
            },
            list: [],
            pagination: {
                current: 1,
                size: 10,
                total: 0,
            },
            supplierSchoolList: [],
            data: "",
            copyData: "",
            importLoading: false,
            action: process.env.VUE_APP_API_BASE_URL + "/unicard/common/import",
            exportLoading: false,
            addLoading: false,
            importDataId: "",
        };
    },
    methods: {
        configFn() {
            this.isHide = true;
            this.$nextTick(() => {
                this.$refs.configRef.getList();
            });
        },
        timeTransformOne(timeList) {
            const startTime = timeList && timeList[0] ? timeList[0] : "";
            const endTime = timeList && timeList[1] ? timeList[1] : "";
            return { startTime, endTime };
        },
        changeDatePicker(data) {
            this.$nextTick(() => {
                //先使用$delete对该属性进行删除,才能重新检测到该数据的变化
                this.$delete(this.gateCodeFrom, "timeList");
                //生效成功更新数据并更新页面
                this.$set(this.gateCodeFrom, "timeList", [data[0], data[1]]);
                const { endTime, startTime } = this.timeTransformOne(data);
                this.gateCodeFrom.startActivationDate = startTime;
                this.gateCodeFrom.endActivationDate = endTime;
            });
        },

        handleExport() {
            let a = document.createElement("a");
            document.body.appendChild(a);
            a.href = "/template/一卡通激活码模版.xlsx";
            a.download = "一卡通激活码模版" + ".xlsx";
            a.style.display = "none";
            a.click();
            document.body.removeChild(a);
        },
        // 学校接口
        searchSchoolList() {
            const obj = {
                current: 1,
                size: 999,
            };
            querySchoolInfoList(obj).then((res) => {
                this.supplierSchoolList = res.data;
            });
        },
        // 导出
        exportData() {
            this.exportLoading = true;
            reportingExportInfo(this.gateCodeFrom, "激活码系统列表").finally(
                () => {
                    this.exportLoading = false;
                    this.$message.success("导出成功");
                }
            );
        },
        imporError() {
            this.importLoading = false;
            this.$message.error("导入失败，请重新导入");
        },
        // 导入
        importChang() {
            this.importLoading = true;
            this.$refs.importDialogRef.open();
        },
        // 导入成功
        async importData(res) {
            await this.$refs.importDialogRef.openTip(res);
            this.importLoading = false;
        },

        // 复制
        authorizeCopy(index, row) {
            this.copyData = row.activationCode;
            this.copy(this.copyData);
        },
        copy(data) {
            let url = data;
            let oInput = document.createElement("input");
            oInput.value = url;
            document.body.appendChild(oInput);
            oInput.select(); // 选择对象;
            console.log(oInput.value);
            document.execCommand("Copy"); // 执行浏览器复制命令
            this.$message({
                message: "复制成功",
                type: "success",
            });
            oInput.remove();
        },
        // 列表表格
        selectAuthorizecodeList() {
            this.dialogVisible = false;
            const obj = {
                ...this.gateCodeFrom,
                pageNo: this.pagination.current,
                pageSize: this.pagination.size,
            };
            getList(obj).then((res) => {
                this.list = res.data.list;
                this.pagination.current = res.data.pageNo;
                this.pagination.size = res.data.pageSize;
                this.pagination.total = res.data.total;
            });
        },
        // 新增编辑按钮
        gateCodeFromAddEdit(status, data) {
            this.$refs.addEditRef.open(status, data);
        },
        // 删除激活码
        delgateActivationCode(data) {
            this.$refs.delectRef.open(data);
        },
        // 分页
        handleSizeChange(val) {
            this.pagination.current = 1;
            this.pagination.size = val;
            this.selectAuthorizecodeList();
        },
        handleCurrentChange(val) {
            this.pagination.current = val;
            this.selectAuthorizecodeList();
        },
        onSubmit() {
            (this.pagination.current = 1), this.selectAuthorizecodeList();
        },
        handleSearchReset() {
            this.gateCodeFrom = {};
            this.$refs.gateCodeFrom.resetFields();
            this.selectAuthorizecodeList();
        },
        // 计数器
        handleChangeNum(value) {
            console.log(value);
        },

        //  打开激活码转移弹框
        gateCodeFromCodeTransfer(item) {
            this.$refs.transferRef.open(item);
        },
    },
    created() {
        this.selectAuthorizecodeList();
        this.token = "Bearer " + this.$ls.get(ACCESS_TOKEN);
    },
};
</script>

<style lang="scss" scoped>
.importDialog {
    div {
        width: 100%;
        height: 45px;
        display: flex;
        flex-direction: column;
    }
}

.btn_warp {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0px;
    border-top: 1px solid #ebeef5;
}

.paginationBlock {
    margin-top: 16px;
    text-align: right;
}

.download {
    justify-content: center;
    display: flex;
    align-items: center;
    margin: 10px;
    color: #409eff;
    cursor: pointer;
}
</style>
<style lang="scss">
.uploadClass {
    .el-dialog__body {
        height: 200px;
    }
}

.el-select-dropdown {
    max-width: 200px;
}

.el-select-dropdown__item {
    width: 100%;
    word-break: break-all;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.abow_dialog {
    display: flex;
    justify-content: center;
    align-items: Center;
    overflow: hidden;

    .el-dialog {
        margin: 0 auto !important;
        height: 50%;
        overflow: hidden;

        .el-dialog__body {
            position: absolute;
            height: 65%;
            left: 0;
            top: 54px;
            bottom: 0;
            right: 0;
            padding: 30px 30px 0px 30px;
            z-index: 1;
            overflow: hidden;
            overflow-y: auto;
        }

        .el-dialog__footer {
            .butFooter {
                position: absolute;
                right: 0;
                bottom: 20px;
                width: 100%;
                position: absolute;
                right: 0;
                bottom: 20px;
                display: flex;
                justify-content: center;
            }
        }
    }
}
</style>
