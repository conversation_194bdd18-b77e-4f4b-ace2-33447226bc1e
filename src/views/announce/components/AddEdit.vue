<template>
    <div>
        <!-- 新增 -->
        <el-dialog
            :title="isEdit ? '编辑公告' : '新增公告'"
            :visible.sync="show"
            :before-close="cancel"
            :wrapper-closable="false"
            top="5vh"
            width="1080px"
        >
            <!-- label-position="top" -->
            <el-form
                ref="ruleForm"
                :model="form"
                :rules="addEditRule"
                label-width="100px"
            >
                <el-form-item label="公告标题：" prop="title">
                    <el-input
                        class="input_padding"
                        v-model="form.title"
                        show-word-limit
                        placeholder="请输入公告标题"
                        maxlength="500"
                    />
                </el-form-item>

                <el-form-item label="公告内容：" prop="announcementContent">
                    <el-input
                        class="input_padding"
                        type="textarea"
                        placeholder="请输入公告内容"
                        v-model="form.announcementContent"
                        maxlength="1000"
                        :autosize="{ minRows: 3, maxRows: 5 }"
                        show-word-limit
                    />
                </el-form-item>
                <el-form-item label="发布对象：" prop="announceTypeList">
                    <el-select
                        v-model="form.announceTypeList"
                        multiple
                        style="width: 100%"
                        placeholder="请选择发布对象（默认全选）"
                    >
                        <el-option
                            v-for="item in targetOption"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </el-form-item>

                <el-form-item label="发布形式：">
                    <div>
                        请选择PC端发布形式
                        <span class="shape_text"
                            >移动端（APP/H5/小程序）发布形式默认为消息推送+更新后首次进入中部弹窗</span
                        >
                    </div>
                    <el-radio-group v-model="form.formType">
                        <el-radio :label="1">
                            <span class="shape">顶部公告</span>
                            <div class="example">
                                <div class="tip">
                                    <span>公告内容</span>
                                    <span>X</span>
                                </div>
                            </div>
                        </el-radio>
                        <el-radio :label="2">
                            <span class="shape">中部公告</span>
                            <div class="example middle">
                                <div class="tip">
                                    <span>公告内容</span>
                                    <span>X</span>
                                </div>
                            </div>
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="公告时间：" prop="timeList">
                    <el-date-picker
                        style="width: 100%"
                        @input="changeDatePicker"
                        v-model="form.timeList"
                        type="datetimerange"
                        range-separator="至"
                        format="yyyy-MM-dd HH:mm"
                        value-format="yyyy-MM-dd HH:mm"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item
                    v-if="isEdit"
                    label="公告状态："
                    prop="status"
                    class="status_radio"
                >
                    <el-radio-group v-model="form.status">
                        <el-radio :label="1">启用</el-radio>
                        <el-radio :label="0">禁用</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <!-- 操作类型 1.保存并启用 2.保存 -->
                <div class="footer">
                    <div v-if="isEdit">
                        <el-button type="primary" @click="submitEditForm"
                            >确定</el-button
                        >
                    </div>
                    <div v-else>
                        <el-button type="primary" @click="submitForm(1)"
                            >发布并启用</el-button
                        >
                        <el-button
                            style="margin: 0px 10px"
                            @click="submitForm(2)"
                            >保 存</el-button
                        >
                    </div>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { getDict } from "@/api/announce.js";
import {
    addEditRule,
    targetOption,
    timeTransformOne,
    timeTransformTwo,
} from "../data";
export default {
    name: "AddEdit",
    data() {
        return {
            targetOption,
            isEdit: false,
            show: false,
            form: {
                announceTypeList: [],
                timeList: [],
                status: 0,
            },
            addEditRule, // 表单验证
        };
    },
    created() {
        getDict(["announce_type"]).then((res) => {
            this.targetOption = res.data[0].list;
        });
    },
    methods: {
        open(status, form) {
            this.isEdit = status;
            this.show = true;
            if (status) {
                this.form = form;
                this.form.timeList = timeTransformTwo(form);
            } else {
                this.form = { announceTypeList: [] };
                this.targetOption.forEach((i) => {
                    this.form.announceTypeList.push(i.value);
                });
            }
        },
        // optType 操作类型 1.保存并启用 2.保存
        submitForm(optType) {
            this.$refs.ruleForm.validate((valid) => {
                if (valid) {
                    this.form.optType = optType;
                    this.$emit("submit", this.form);
                } else {
                    console.log(valid);
                    return false;
                }
            });
        },
        // 编辑确定
        submitEditForm() {
            this.$refs.ruleForm.validate((valid) => {
                if (valid) {
                    this.$emit("submitEdit", this.form);
                } else {
                    console.log(valid);
                    return false;
                }
            });
        },
        cancel() {
            this.$refs.ruleForm.resetFields();
            this.show = false;
        },
        changeDatePicker(data) {
            this.$nextTick(() => {
                //先使用$delete对该属性进行删除,才能重新检测到该数据的变化
                this.$delete(this.form, "timeList");
                //生效成功更新数据并更新页面
                this.$set(this.form, "timeList", [data[0], data[1]]);
                const { endTime, startTime } = timeTransformOne(data);
                this.form.startTime = startTime;
                this.form.endTime = endTime;
            });
        },
    },
};
</script>

<style lang="scss" scoped>
:deep(.el-radio-group) {
    display: flex !important;
}
.status_radio {
    :deep(.el-radio) {
        display: flex;
        height: 40px;
        align-items: center;
    }
}
:deep(.el-dialog__body) {
    height: 670px !important;
    overflow-y: auto !important;
}
.example {
    margin-top: 10px;
    width: 288px;
    height: 162px;
    background: #ffffff;
    border-radius: 2px;
    border: 1px solid #d9d9d9;
    display: flex;
    align-items: flex-start;

    .tip {
        width: 100%;
        height: 33px;
        background: #f9f9f9;
        border-radius: 2px 2px 0px 0px;
        border-bottom: 1px solid #d9d9d9;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0px 20px;
    }
}
.middle {
    .tip {
        width: 144px;
        height: 81px;
        background: #f9f9f9;
        border-radius: 2px;
        border: 1px solid #d9d9d9;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 10px 20px;
    }
    justify-content: center;
    align-items: center;
}
.shape {
    position: absolute;
    top: 4px;
}
.shape_text {
    color: #2cc360;
    padding-left: 10px;
}
.footer {
    display: flex;
    justify-content: flex-start;
}
:deep(.el-dialog__footer) {
    border-top: 1px solid #ccc;
}

:deep(.el-input__count) {
    background: none !important;
}
:deep(.el-input__count-inner) {
    background: none !important;
}
.input_padding {
    :deep(.el-input__inner) {
        padding-right: 64px !important;
    }
    :deep(.el-textarea__inner) {
        padding-right: 51px !important;
    }
}
</style>
