<template>
    <div>
        <!-- 新增 -->
        <el-dialog title="公告详情" :visible.sync="show" :before-close="cancel">
            <div class="item">
                <span class="title">公告标题：</span>
                <span class="text">{{ form.title }}</span>
            </div>
            <div class="item">
                <span class="title">公告内容：</span>
                <span class="text">{{ form.announcementContent }}</span>
            </div>
            <div class="item">
                <span class="title">发布平台：</span>
                <span class="text">{{ form.announceTypeText }}</span>
            </div>
            <div class="item">
                <span class="title">公告位置：</span>
                <span class="text">
                    <div v-if="form.formType === 1">
                        <span class="shape">顶部公告</span>
                        <div class="example">
                            <div class="tip">
                                <span>公告内容</span>
                                <span>X</span>
                            </div>
                        </div>
                    </div>
                    <div v-else>
                        <span class="shape">中部公告</span>
                        <div class="example middle">
                            <div class="tip">
                                <span>公告内容</span>
                                <span>X</span>
                            </div>
                        </div>
                    </div>
                </span>
            </div>
            <div class="split_line"></div>
            <div class="item">
                <span class="title">公告时间：</span>
                <span class="text"
                    >{{ form.startTime }} - {{ form.endTime }}</span
                >
            </div>
            <div class="item">
                <span class="title">公告状态：</span>
                <span class="text"> {{ statusText[form.status] }}</span>
            </div>
            <div class="item">
                <span class="title">创建时间：</span>
                <span class="text">{{ form.createTime }}</span>
            </div>
            <div class="item">
                <span class="title">创建人：</span>
                <span class="text">{{ form.createBy }}</span>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { announceDetail } from "@/api/announce.js";
import { timeTransformTwo, statusText } from "../data";
export default {
    name: "Detail",
    data() {
        return {
            statusText,
            show: false,
            form: {},
        };
    },
    methods: {
        getAnnounceDetail(id) {
            announceDetail({ id }).then((res) => {
                this.form = res.data;
                this.form.announceTypeText =
                    res.data.announceTypeNameList.join("、") || "";
                this.form.timeList = timeTransformTwo(res.data);
            });
        },
        open(form) {
            this.show = true;
            this.getAnnounceDetail(form.id);
        },
        cancel() {
            this.show = false;
        },
    },
};
</script>

<style lang="scss" scoped>
.item {
    display: flex;
    align-items: flex-start;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    padding: 10px 0px;

    .title {
        color: #000000a6;
        min-width: 76px;
    }
    .text {
        color: #000000d9;
    }
}
.split_line {
    border-bottom: 1px dashed #d9d9d9;
}
.example {
    margin-top: 10px;
    width: 288px;
    height: 162px;
    background: #ffffff;
    border-radius: 2px;
    border: 1px solid #d9d9d9;
    display: flex;
    align-items: flex-start;

    .tip {
        width: 100%;
        height: 33px;
        background: #f9f9f9;
        border-radius: 2px 2px 0px 0px;
        border-bottom: 1px solid #d9d9d9;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0px 20px;
    }
}
.middle {
    .tip {
        width: 144px;
        height: 81px;
        background: #f9f9f9;
        border-radius: 2px;
        border: 1px solid #d9d9d9;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 10px 20px;
    }
    justify-content: center;
    align-items: center;
}
</style>
