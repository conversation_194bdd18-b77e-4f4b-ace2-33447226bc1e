export const targetOption = [];

export const statusOption = [
    {
        name: "启用",
        id: 1,
    },
    {
        name: "禁用",
        id: 0,
    },
];
export const statusText = {
    0: "禁用",
    1: "启用",
};

export const statusEditText = {
    0: "启用",
    1: "禁用",
};

export const addEditRule = {
    title: [
        {
            required: true,
            message: "请输入公告标题",
        },
    ],
    announcementContent: [
        {
            required: true,
            message: "请输入公告内容",
        },
    ],
    announceTypeList: [
        {
            required: true,
            message: "请选择发布对象",
            trigger: "change",
        },
    ],
    formType: [
        {
            required: true,
            message: "请选择发布形式",
            trigger: "change",
        },
    ],
    timeList: [
        {
            required: true,
            message: "请选择发布日期时间",
            trigger: "change",
        },
    ],
    status: [
        {
            required: true,
            message: "请选择发布状态",
            trigger: "change",
        },
    ],
};

export const timeTransformOne = (timeList) => {
    const startTime = timeList && timeList[0] ? timeList[0] : "";
    const endTime = timeList && timeList[1] ? timeList[1] : "";
    return { startTime, endTime };
};

export const timeTransformTwo = ({ startTime, endTime }) => {
    const timeList = startTime && endTime ? [startTime, endTime] : [];
    return timeList;
};
