<template>
    <div class="announce">
        <!-- 表单 -->
        <el-form ref="form" :inline="true" :model="form">
            <el-form-item label="公告标题：">
                <el-input v-model="form.title" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="发布对象：">
                <el-select
                    v-model="form.announceType"
                    clearable
                    placeholder="全部"
                >
                    <el-option
                        v-for="item in targetOption"
                        :key="item.id"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="公告状态：">
                <el-select v-model="form.status" clearable placeholder="全部">
                    <el-option
                        v-for="item in statusOption"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="展示时间：" class="login_time_class">
                <el-date-picker
                    clearable
                    @change="changePicker"
                    v-model="form.timeList"
                    type="daterange"
                    range-separator="至"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                >
                </el-date-picker>
            </el-form-item>
            <el-form-item label="更新时间：" class="login_time_class">
                <el-date-picker
                    clearable
                    @change="changeUpdatePicker"
                    v-model="form.updateTime"
                    type="daterange"
                    range-separator="至"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                >
                </el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-button
                    type="primary"
                    @click="handleSearch"
                    icon="el-icon-search"
                    >查询</el-button
                >
                <el-button @click="reset">重置</el-button>
            </el-form-item>
        </el-form>
        <!-- 新增 -->
        <div class="btn">
            <span>公告管理</span>
            <div class="multipartFile">
                <el-button
                    size="medium"
                    icon="el-icon-plus"
                    type="primary"
                    @click="handleAddEdit(false)"
                    v-auth="'mamage.announce.add'"
                    >新增公告</el-button
                >
            </div>
        </div>
        <el-table
            class="table_class"
            :data="tableData"
            border
            :header-cell-style="{ background: '#fafafa', color: '#5b5d61' }"
        >
            <el-table-column
                type="index"
                :index="indexMethod"
                :width="80"
                label="序号"
            />

            <el-table-column
                prop="title"
                label="公告标题"
                show-overflow-tooltip
            />

            <el-table-column
                prop="announceTypeText"
                label="发布对象"
                show-overflow-tooltip
            />
            <el-table-column prop="status" label="公告状态" :width="100">
                <template slot-scope="scope">
                    {{ statusText[scope.row.status] }}
                </template>
            </el-table-column>
            <el-table-column prop="startTime" label="展示时间">
                <template slot-scope="scope">
                    {{ scope.row.startTime }} - {{ scope.row.endTime }}
                </template>
            </el-table-column>
            <el-table-column prop="updateTime" label="更新时间" :width="160" />
            <el-table-column
                prop="operate"
                label="操作"
                align="right"
                fixed="right"
                :width="200"
            >
                <template slot-scope="scope">
                    <div class="operate_class">
                        <el-link
                            v-auth="'mamage.announce.info'"
                            type="primary"
                            :underline="false"
                            @click="handleInfo(scope.row)"
                            >详情</el-link
                        >
                        <el-link
                            v-auth="'mamage.announce.edit'"
                            :underline="false"
                            type="primary"
                            icon="el-icon-edit"
                            :class="
                                scope.row.status === 1
                                    ? 'edit_disable'
                                    : 'edit_enable'
                            "
                            @click="handleAddEdit(true, scope.row)"
                            >编辑</el-link
                        >
                        <el-link
                            v-auth="'mamage.announce.statusEdit'"
                            :underline="false"
                            type="primary"
                            @click="handleStatus(scope.row)"
                        >
                            {{ statusEditText[scope.row.status] }}
                        </el-link>
                        <el-link
                            v-auth="'mamage.announce.delete'"
                            :class="
                                scope.row.status === 1
                                    ? 'edit_disable'
                                    : 'edit_enable'
                            "
                            :underline="false"
                            type="danger"
                            @click="handleDelete(scope.row)"
                        >
                            删除
                            <i class="el-icon-delete" />
                        </el-link>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <el-pagination
            background
            class="pagination"
            :current-page="pagination.pageNo"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="pagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
        <!-- 新增编辑 -->
        <AddEdit
            ref="addEditRef"
            @submit="submitForm"
            @submitEdit="submitEditForm"
        ></AddEdit>
        <!-- 详情 -->
        <Detail ref="detailRef"></Detail>
    </div>
</template>

<script>
import {
    getDict,
    getAnnounceList,
    createAnnounce,
    updateAnnounce,
    deleteAnnounce,
    updateStatus,
} from "@/api/announce.js";
import AddEdit from "./components/AddEdit";
import Detail from "./components/Detail.vue";
import {
    targetOption,
    statusOption,
    timeTransformOne,
    statusText,
    statusEditText,
} from "./data";
export default {
    components: {
        AddEdit,
        Detail,
    },
    data() {
        return {
            statusText,
            statusEditText,
            tableData: [],
            targetOption, // 对象字典
            statusOption, // 状态字典
            // 搜索条件表单
            form: {
                status: null,
                announceType: null,
            },
            // 分页
            pagination: {
                pageSize: 10,
                pageNo: 1,
                total: 10,
            },
            isEdit: false, // 标识是否为编辑
        };
    },
    created() {
        getDict(["announce_type"]).then((res) => {
            this.targetOption = res.data[0].list;
        });
        this.getList();
    },
    methods: {
        // 获取列表
        getList() {
            getAnnounceList({ ...this.pagination, ...this.form }).then(
                (res) => {
                    const { total, pageNo, pageSize } = res.data;
                    this.tableData = res.data.list.map((i) => {
                        return {
                            ...i,
                            announceTypeText:
                                i.announceTypeNameList.join("、") || "",
                        };
                    });
                    this.pagination.total = total;
                    this.pagination.pageNo = pageNo;
                    this.pagination.pageSize = pageSize;
                }
            );
        },
        indexMethod(index) {
            return index + 1;
        },
        // 搜素
        handleSearch() {
            this.pagination.pageNo = 1;
            this.getList();
        },
        // 重置
        reset() {
            this.pagination.pageNo = 1;
            this.form = {};
            this.getList();
        },
        // 新增编辑
        handleAddEdit(isEdit, form = {}) {
            this.isEdit = isEdit;
            // 公告在线中不可编辑
            if (isEdit && form.status === 1) {
                return;
            }
            this.$refs.addEditRef.open(isEdit, form);
        },
        // 分页
        handleSizeChange(val) {
            this.pagination.pageNo = 1;
            this.pagination.pageSize = val;
            this.getList();
        },
        handleCurrentChange(val) {
            this.pagination.pageNo = val;
            this.getList();
        },
        handleDelete({ id, status }) {
            if (status === 1) {
                return;
            }
            this.$confirm("是否确定删除此公告?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    deleteAnnounce({ id }).then(({ message }) => {
                        this.$message.success(message);
                        this.getList();
                    });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消删除",
                    });
                });
        },
        handleInfo(item) {
            this.$refs.detailRef.open(item);
        },
        handleStatus({ id, status }) {
            updateStatus({ id, status: status == 1 ? 0 : 1 }).then(
                ({ message }) => {
                    this.$message.success(message);
                    this.getList();
                }
            );
        },
        changePicker(e) {
            const { endTime, startTime } = timeTransformOne(e);
            this.form.startTime = startTime;
            this.form.endTime = endTime;
        },
        changeUpdatePicker(e) {
            const { endTime, startTime } = timeTransformOne(e);
            this.form.updateStartTime = startTime;
            this.form.updateEndTime = endTime;
        },
        // 新增
        submitForm(form) {
            createAnnounce(form).then((res) => {
                this.$message.success(res.message);
                this.getList();
                this.$refs.addEditRef.cancel();
            });
        },
        // 编辑
        submitEditForm(form) {
            updateAnnounce(form).then((res) => {
                this.$message.success(res.message);
                this.$refs.addEditRef.cancel();
                this.getList();
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.edit_disable {
    :deep(.el-link--inner) {
        color: #ccc !important;
        cursor: not-allowed !important;
    }
    :deep(.el-icon-edit) {
        color: #ccc !important;
        cursor: not-allowed !important;
    }
}

.announce {
    height: 100%;
    background: #fff;
    padding: 20px;
}

.btn {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    padding-top: 16px;
    border-top: 1px solid #ebeef5;
    span {
        margin: 10px;
    }
    .multipartFile {
        display: flex;
    }
    .download {
        justify-content: center;
        display: flex;
        align-items: center;
        margin: 10px;
        a {
            color: #409eff;
        }
    }
}
.pagination {
    margin-top: 16px;
    text-align: right;
}
.table_class {
    width: 100%;
    margin-top: 16px;
    min-height: 500px;
}
.operate_class {
    display: flex;
    justify-content: space-evenly;
}
</style>
