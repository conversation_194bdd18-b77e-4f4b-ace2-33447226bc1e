<template>
	<el-form
		ref="addSchoolRuleForm"
		:model="addSchoolRuleForm"
		:rules="addSchoolRules"
		label-width="70px"
		class="demo-ruleForm"
	>
		<el-form-item label prop="schoolName">
			<el-autocomplete
				v-model="addSchoolRuleForm.schoolName"
				style="width: 90%"
				popper-class="my-autocomplete"
				:fetch-suggestions="querySearch"
				placeholder="请输入要查询的学校"
				@select="handleSelect"
			>
				<i slot="suffix" class="el-icon-edit el-input__icon" />
				<template slot-scope="{ item }">
					<div class="name">{{ item.schoolName }}</div>
				</template>
			</el-autocomplete>
		</el-form-item>

		<el-form-item class="yd-form-footer">
			<el-button
				size="medium"
				type="primary"
				@click="addSchoolSubmitForm('addSchoolRuleForm')"
				>确定</el-button
			>
			<el-button @click="handDrawerleClose">取消</el-button>
		</el-form-item>
	</el-form>
</template>

<script>
import * as api from "@/api/application.js";
import { mapGetters } from "vuex";
export default {
	name: "CreateSchoolForm",
	components: {},
	props: {},
	data() {
		return {
			restaurants: [],
			state: "",
			addSchoolRuleForm: {
				schoolName: "",
				schoolId: "",
				brandApplicationId: ""
			},

			addSchoolRules: {
				schoolName: [
					{
						required: true,
						message: "请输入学校名称",
						trigger: "blur"
					}
				]
			}
		};
	},
	computed: {
		...mapGetters(["brandApplicationId"])
	},
	watch: {},
	created() {
		// this.getsearchSchoolInfo();
	},
	mounted() {
		// this.restaurants = this.getsearchSchoolInfo();
	},
	methods: {
		addSchoolSubmitForm(formName) {
			const that = this;

			this.$refs[formName].validate(valid => {
				if (valid) {
					api["insetVersionSchoolApi"](this.addSchoolRuleForm).then(
						res => {
							const { message, code } = res,
								param = {
									versionType: 2,
									brandApplicationId: this.brandApplicationId,
									...this.pages
								};

							if (code == 200) {
								this.$message({
									showClose: true,
									message,
									type: "success"
								});
								this.$refs["addSchoolRuleForm"].resetFields();

								this.$emit("drawerClose");
								this.$store.dispatch(
									"VersionSchool/insetVersionSchoolInfo",
									param
								);
							}
						}
					);
				} else {
					return false;
				}
			});
		},
		getsearchSchoolInfo() {
			const params = {
				brandApplicationId: this.brandApplicationId,
				schoolName: this.addSchoolRuleForm.schoolName
			};

			return new Promise((resolve, reject) => {
				api["searchSchoolApi"](params).then(res => {
					let data = [];

					res && (data = res.data);
					resolve(data);
				});
			});
		},
		querySearch(queryString, cb) {
			this.addSchoolRuleForm.schoolId = "";
			this.getsearchSchoolInfo().then(res => {
				const results = queryString
					? res.filter(this.createFilter(queryString))
					: res;
				// 调用 callback 返回建议列表的数据

				cb(results);
			});
		},
		createFilter(queryString) {
			return restaurant => {
				return (
					restaurant.schoolName
						.toLowerCase()
						.indexOf(queryString.toLowerCase()) !== -1
				);
			};
		},
		handDrawerleClose() {
			this.$emit("drawerClose");
		},
		handleSelect(item) {
			this.addSchoolRuleForm = {
				schoolName: item.schoolName,
				schoolId: item.id,
				brandApplicationId: this.brandApplicationId
			};
		}
	}
};
</script>

<style scoped lang="scss"></style>
