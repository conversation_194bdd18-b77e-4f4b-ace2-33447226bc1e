<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-08-23 19:55:36
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2021-11-15 09:53:09
-->
<template>
	<el-form
		ref="versionRuleForm"
		:model="versionRuleForm"
		class="demo-ruleForm demo-h-ruleForm"
	>
		<div
			v-for="(item, index) in versionRuleForm.list"
			:key="index"
			class="scrollbar"
		>
			<el-form-item
				class="form-item"
				label
				:prop="`list[${index}].versionName`"
				:rules="{
					required: true,
					message: '请输入版本名称',
					trigger: 'blur'
				}"
			>
				<el-input
					v-model="item.versionName"
					placeholder="请输入版本名称"
					@blur="versionjianFn(item.versionName, index)"
				/>
				<i
					class="el-icon-circle-plus-outline icon"
					@click="addVersionManagerFn"
				/>
				<i
					v-if="versionRuleForm.list.length > 1"
					class="el-icon-remove-outline icon"
					@click="errorVersionManagerFn(item, index)"
				/>
			</el-form-item>
		</div>
		<el-form-item class="yd-form-footer drawer_footer">
			<el-button
				size="medium"
				type="primary"
				@click="versionSubmitForm('versionRuleForm')"
				>确定</el-button
			>
			<el-button @click="handDrawerleClose">取消</el-button>
		</el-form-item>
	</el-form>
</template>

<script>
import { mapGetters } from "vuex";
import * as api from "@/api/application.js";
export default {
	name: "CreateVersionForm",
	components: {},
	props: {
		versionRuleForm: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		return {
			// versionRuleForm: {
			// 	list: [{ versionName: "" }]
			// }
		};
	},
	computed: {
		...mapGetters(["brandApplicationId"])
	},
	watch: {},
	created() {},
	mounted() {},
	methods: {
		handDrawerleClose(start) {
			this.$emit("drawerClose", start == "success" ? start : null);
		},
		versionSubmitForm(formName) {
			this.$refs[formName].validate(valid => {
				if (valid) {
					const listArr = [];

					this.versionRuleForm.list.forEach(element => {
						if (element.id == undefined) {
							listArr.push("-1#&#" + element.versionName);
						} else {
							listArr.push(
								element.id + "#&#" + element.versionName
							);
						}
					});
					const obj = {
							versionNames: listArr.toString(),
							brandApplicationId: this.brandApplicationId
						},
						{ schoolId } = this.$route.query;

					schoolId && (obj.schoolId = schoolId);

					api["batchAddClassificationApi"](obj)
						.then(res => {
							if (res.code == "200") {
								this.$message.success("添加成功");
								this.versionRuleForm.list = [
									{ versionName: "" }
								];
								this.handDrawerleClose("success");
							}
						})
						.catch();
				} else {
					return false;
				}
			});
		},
		errorVersionManagerFn(item, i) {
			if (item.id == undefined) {
				this.versionRuleForm.list.splice(i, 1);
				this.$message.success("删除成功");
				return false;
			}
			const params = {
					id: item.id,
					brandApplicationId: this.brandApplicationId
				},
				{ schoolId } = this.$route.query;

			schoolId && (params.schoolId = schoolId);
			this.$confirm("此操作将永久删除此信息, 是否继续?", "提示", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				type: "warning"
			})
				.then(() => {
					api["delVersionClassificationApi"](params)
						.then(res => {
							if (res.code == "200") {
								this.$message.success("删除成功");
								// this.getversionListFn();
								this.$emit("updatedVersion");
							}
						})
						.catch();
				})
				.catch(() => {
					// this.$message({
					// 	type: "info",
					// 	message: "已取消删除"
					// });
				});
		},

		addVersionManagerFn() {
			const obj = { versionName: "" };

			this.versionRuleForm.list.push(obj);
		},
		versionjianFn(name, i) {
			for (
				let index = 0;
				index < this.versionRuleForm.list.length;
				index++
			) {
				const element = this.versionRuleForm.list[index].versionName;

				if (name == element && i != index) {
					this.$message.error(name + "已存在，请重新输入");
					this.versionRuleForm.list[i].versionName = "";
				}
			}
		}
	}
};
</script>

<style scoped lang="scss">
.form-item {
	::v-deep .el-form-item__content {
		display: flex;
		margin: 0 40px;
	}
}
.el-icon-circle-plus-outline {
	color: #409eff;
}
.el-icon-remove-outline {
	color: #fc5a5a;
}
.icon {
	cursor: pointer;
	font-size: 22px;
	margin-left: 20px;
	margin-top: 5px;
}
</style>
