<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-08-23 19:36:33
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2021-11-15 09:31:07
-->
<template>
	<el-drawer
		:title="title"
		:visible.sync="drawerShow"
		:before-close="handDrawerleClose"
		:wrapper-closable="false"
		class="menu_drawer"
	>
		<slot />
	</el-drawer>
</template>

<script>
export default {
	name: "Drawer",
	components: {},
	props: {
		title: {
			type: String,
			default: () => ""
		},
		drawerShow: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			addSchoolDrawer: false
		};
	},
	computed: {},
	watch: {},
	created() {},
	mounted() {},
	methods: {
		handDrawerleClose() {
			// done();
			this.$emit("drawerClose");
		}
	}
};
</script>

<style scoped lang="scss"></style>
