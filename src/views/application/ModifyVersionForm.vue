<!--
 * @Descripttion: ModifyVersionForm
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-08-24 11:01:49
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2021-11-15 09:31:29
-->
<template>
	<el-form
		ref="UploadFileRuleForm"
		:model="UploadFileRuleForm"
		:rules="rulesUpload"
		label-width="100px"
		class="demo-ruleForm demo-h-ruleForm"
	>
		<el-form-item label="选择版本" prop="versionClassificationId">
			<el-select
				v-model="UploadFileRuleForm.versionClassificationId"
				style="width:400px;"
				placeholder="请选择版本"
			>
				<el-option
					v-for="(item, index) in versionList"
					:key="index"
					:label="item.versionName"
					:value="item.id"
				/>
			</el-select>
		</el-form-item>
		<el-form-item label="版本号" prop="versionNumber">
			<el-input
				v-model="UploadFileRuleForm.versionNumber"
				style="width:400px;"
				oninput="this.value = this.value.replace(/[^0-9]/g, '')"
				placeholder="请输入版本号"
			/>
		</el-form-item>
		<el-form-item label="展示版本" prop="exhibitionVersion">
			<el-input
				v-model="UploadFileRuleForm.exhibitionVersion"
				style="width:400px;"
				placeholder="请输入如：V1.0.0"
			/>
		</el-form-item>
		<el-form-item label="添加文件" prop="addfile" style="height: 60px">
			<el-upload
				ref="uploads"
				:file-list="UploadFileRuleForm.addfile"
				class="avatar-uploader newfile"
				:on-remove="handleRemove"
				action="#"
				:auto-upload="false"
				:http-request="uploadVideoUAvatar"
				:on-change="handleChange"
			>
				<el-button size="small" type="primary">点击上传</el-button>
			</el-upload>
		</el-form-item>
		<el-form-item label="更新类型" prop="updateType">
			<el-select
				v-model="UploadFileRuleForm.updateType"
				style="width:400px;"
				placeholder="请选择更新类型"
			>
				<el-option label="不强制" value="2" />
				<el-option label="强制" value="1" />
			</el-select>
		</el-form-item>

		<el-form-item label="更新提示" prop="updateTips">
			<el-input
				v-model="UploadFileRuleForm.updateTips"
				type="textarea"
				style="width:400px;"
				:rows="10"
				placeholder="在这里输入更新提示内容"
			/>
		</el-form-item>
		<el-form-item class="drawer_footer yd-form-footer">
			<el-button
				type="primary"
				@click="submitUploadFileForm('UploadFileRuleForm')"
				>确定</el-button
			>
			<el-button @click="handDrawerleClose">取消</el-button>
		</el-form-item>
	</el-form>
</template>

<script>
import * as api from "@/api/application.js";
import { mapGetters } from "vuex";
import { getUploadFileOSSpath } from "@/utils/oss";
export default {
	name: "ModifyVersionForm",
	components: {},
	props: {
		modifyVersion: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},

	data() {
		return {
			file: "",
			sys_id: "",
			versionRuleForm: {
				list: [{ versionName: "" }]
			},
			versionList: [],
			UploadFileRuleForm: {
				updateTips: "",
				updateType: "",
				addfile: [],
				exhibitionVersion: "",
				versionNumber: "",
				versionClassificationId: []
			},
			rulesUpload: {
				versionClassificationId: [
					{
						required: true,
						message: "请选择版本",
						trigger: "change"
					}
				],
				versionNumber: [
					{
						required: true,
						message: "请输入版本号",
						trigger: "blur"
					}
				],
				exhibitionVersion: [
					{
						required: true,
						message: "请输入展示版本如：V1.0.0",
						trigger: "blur"
					}
				],
				updateType: [
					{
						required: true,
						message: "请选择更新类型",
						trigger: "change"
					}
				],
				addfile: [
					{
						required: true,
						message: "请上传文件",
						trigger: "change"
					}
				]
			},
			pages: {
				current: 1,
				size: 10,
				total: 0
			}
		};
	},
	computed: {
		...mapGetters(["brandApplicationId"])
	},

	watch: {
		modifyVersion(val) {
			this.UploadFileRuleForm = {};
			const {
				id,
				fileName,
				fileUrl,
				updateTips,
				updateType,
				addfile,
				exhibitionVersion,
				versionNumber,
				versionClassificationId
			} = val;

			if (id) {
				this.UploadFileRuleForm = {
					id,
					updateTips,
					updateType,
					addfile,
					exhibitionVersion,
					versionNumber,
					versionClassificationId
				};
				this.UploadFileRuleForm.addfile = [
					{
						name: fileName,
						fileUrl
					}
				];
			}
			this.UploadFileRuleForm.versionSchoolId =
				this.$route.query.schoolId || "";
			this.getversionListFn();
		}
	},
	created() {
		const {
			id,
			fileUrl,
			fileName,
			updateTips,
			updateType,
			addfile,
			exhibitionVersion,
			versionNumber,
			versionClassificationId
		} = this.modifyVersion;

		if (id) {
			this.UploadFileRuleForm = {
				id,
				updateTips,
				updateType,
				addfile,
				exhibitionVersion,
				versionNumber,
				versionClassificationId
			};
			this.UploadFileRuleForm.addfile = [
				{
					name: fileName,
					fileUrl
				}
			];
		}
		this.UploadFileRuleForm.versionSchoolId =
			this.$route.query.schoolId || "";
		this.getversionListFn();
	},
	mounted() {},
	methods: {
		//获取版本
		getversionListFn() {
			api["queryClassificListApi"]({
				brandApplicationId: this.brandApplicationId,
				schoolId: this.$route.query.schoolId || ""
			})
				.then(res => {
					if (res.code == "200") {
						this.versionList = res.data;
						if (res.data.length > 0) {
							this.versionRuleForm.list = res.data;
						} else {
							this.versionRuleForm.list = [{ versionName: "" }];
						}
					}
				})
				.catch();
		},
		handleChange(file, fileList) {
			this.file = file;
			if (fileList.length > 1) {
				fileList.splice(0, 1);
			}
			this.UploadFileRuleForm.addfile = fileList;
		},
		uploadVideoUAvatar(item) {
			this.file = item;
			// this.UploadFileRuleForm.append("file", item.file);
		},
		handleRemove(file, fileList) {
			this.UploadFileRuleForm.addfile = [];
		},

		handDrawerleClose(start) {
			this.$emit("drawerClose", start == "success" ? start : null);
			this.$refs["UploadFileRuleForm"].resetFields();
		},
		submitUploadFileForm(formName) {
			const that = this,
				schoolId = this.$route.query.schoolId;

			this.$refs[formName].validate(async valid => {
				if (valid) {
					const loading = this.$loading({
							lock: true,
							text: "上传中-0%",
							spinner: "el-icon-loading",
							background: "rgba(0, 0, 0, 0.7)"
						}),
						FormDatas = new FormData();

					for (const i in that.UploadFileRuleForm) {
						if (i == "addfile") {
							// for (var j in that.UploadFileRuleForm[i]) {
							// 	if (that.UploadFileRuleForm.id) {
							// 		that.UploadFileRuleForm[i][0]["raw"] &&
							// 			FormDatas.append(
							// 				"file",
							// 				this.UploadFileRuleForm[i][j].raw
							// 			);
							// 	} else {
							// 		FormDatas.append(
							// 			"file",
							// 			this.UploadFileRuleForm[i][j].raw
							// 		);
							// 	}
							// }
						} else {
							this.UploadFileRuleForm[i] &&
								FormDatas.append(i, this.UploadFileRuleForm[i]);
						}
					}
					const params = {
							versionType: schoolId ? 2 : 1,
							...this.pages,
							schoolId,
							brandApplicationId: this.brandApplicationId
						},
						URL = `VersionSchool/${
							schoolId
								? "insetVersionSchoolInfo"
								: "quetyRegistrationCodeInfo"
						}`;

					if (that.file) {
						const url  = await getUploadFileOSSpath(
							that.file.raw,
							'appVersion',
							n => {
								loading.text = `上传中-${n}%`;
							}
						);

						if (url) {
							FormDatas.append("fileUrl", url);
							FormDatas.append("fileName", that.file.name);
							this.file = "";
						} else {
							return this.$message.error('上传失败');
						}
					}

					if (that.UploadFileRuleForm.id) {
						api["updateVersionManagementApi"](FormDatas)
							.then(res => {
								if (res.code == 200) {
									this.$emit("drawerClose");
									this.$store.dispatch(URL, params);
									this.$message.success("修改成功");
									this.$refs[
										"UploadFileRuleForm"
									].resetFields();
								}
								loading.close();
							})
							.catch(err => {
								loading.close();
							});
					} else {
						api["insertVersionManagementApi"](FormDatas)
							.then(res => {
								if (res.code == 200) {
									this.$emit("drawerClose");
									this.$store.dispatch(URL, params);
									this.$message.success("上传成功");
									this.$refs[
										"UploadFileRuleForm"
									].resetFields();
								}
								loading.close();
							})
							.catch(err => {
								loading.close();
							});
					}
				} else {
					return false;
				}
			});
		}
	}
};
</script>

<style lang="scss">
.avatar-uploader {
	overflow: initial;
}
@import "./allmanagement.scss";
</style>
