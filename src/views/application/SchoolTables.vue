<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-08-23 21:19:19
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2021-11-15 09:31:53
-->
<template>
	<div>
		<div class="schoolForm">
			<el-form
				ref="ruleForm"
				inline
				:model="ruleForm"
				label-width="100px"
				class="demo-ruleForm"
			>
				<el-form-item prop="schoolName">
					<el-input
						v-model="ruleForm.schoolName"
						style="width:300px;margin-right:10px;"
						placeholder="请输入学校名称"
					/>
				</el-form-item>
				<el-form-item>
					<el-button
						type="primary"
						icon="el-icon-search"
						@click="headerInquiry"
						>查询</el-button
					>
					<el-button @click="resetForm('ruleForm')">重置</el-button>
				</el-form-item>
			</el-form>
		</div>
		<div class="btn">
			<span>按学校管理</span>
			<div>
				<el-button
					type="primary"
					class="addSchool"
					icon="el-icon-plus"
					@click="addSchoolFn"
					>新增学校</el-button
				>
			</div>
		</div>
		<div class="from_list">
			<el-table
				:data="schoolTableData"
				border
				:header-cell-style="{ background: '#fafafa', color: '#5b5d61' }"
				:cell-style="cellStyleFun"
				style="width: 100%"
			>
				<el-table-column label="序号" width="50">
					<template slot-scope="scope">
						<span v-text="scope.$index + 1" />
					</template>
				</el-table-column>

				<el-table-column
					align="center"
					v-for="(item, idx) in schoolTable"
					:key="idx"
					:prop="item.prop"
					:label="item.label"
				/>

				<el-table-column
					label="操作"
					width="100px"
					align="right"
					fixed="right"
				>
					<template slot-scope="scope">
						<el-link
							type="primary"
							icon="el-icon-view"
							plain
							size="mini"
							@click="seeVersionFn(scope.row)"
							>查看</el-link
						>
						<!-- <el-button
              type="danger"
              plain
              size="mini"
              @click="delFn(scope.row)"
						>删除</el-button>-->
					</template>
				</el-table-column>
			</el-table>
		</div>
		<div class="paginationBlock">
			<el-pagination
				v-if="pages.total > 10"
				background
				style="margin-top:20px;"
				:current-page="pages.current"
				:page-sizes="[10, 20, 30, 50]"
				:page-size="pages.size"
				layout="total, sizes, prev, pager, next, jumper"
				:total="pages.total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
		</div>
	</div>
</template>

<script>
import * as api from "@/api/application.js";
import { mapGetters } from "vuex";
export default {
	name: "SchoolTable",
	components: {},
	props: {},
	data() {
		return {
			page: { current: 1, size: 10, total: 0 },
			versionType: 2,
			ruleForm: {
				schoolName: ""
			},
			schoolName: "",
			schoolTable: [
				{ prop: "schoolName", label: "学校名称" },
				{ prop: "versionName", label: "版本名称" },
				{ prop: "versionNumber", label: "最新版本" },
				{ prop: "fileName", label: "最新版本文件" },
				{ prop: "updateTips", label: "最新版本更新提示" }
			]
		};
	},
	computed: {
		...mapGetters(["brandApplicationId", "schoolTableData", "pages"])
	},
	watch: {
		brandApplicationId(value, olValue) {
			if (value !== olValue) {
				this.getSchoolListInfo();
			}
		}
	},
	created() {
		this.getSchoolListInfo();
	},
	mounted() {},
	methods: {
		getSchoolListInfo() {
			const params = {
				versionType: 2,
				brandApplicationId: this.brandApplicationId,
				schoolName: this.ruleForm.schoolName,
				...this.pages
			};

			this.$store.dispatch(
				"VersionSchool/insetVersionSchoolInfo",
				params
			);
		},
		thStyleFun() {
			return "text-align:center";
		},
		cellStyleFun() {
			return "text-align:center";
		},
		addSchoolFn() {
			this.$emit("createSchool");
		},
		headerInquiry() {
			this.getSchoolListInfo();
		},
		resetForm(formName) {
			this.$refs[formName].resetFields();
			this.headerInquiry();
		},
		seeVersionFn(row) {
			this.$store.state.VersionSchool.pages = this.page;
			this.$router.push({
				path: "/internalApplication/versionManagementChild",
				query: {
					schoolId: row.versionSchoolId,
					sys_id: this.brandApplicationId
				}
			});
		},

		delFn(row) {
			const params = {
				versionType: 2,
				brandApplicationId: this.brandApplicationId,
				schoolId: row.versionSchoolId,
				id: row.versionSchoolId
			};

			this.$confirm("此操作将永久删除此信息, 是否继续?", "提示", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				type: "warning"
			})
				.then(() => {
					const { current } = this.pages;

					if (current > this.schoolTableData.length) {
						this.pages.current = 1;
					}
					api["delVersionSchoolApi"](params)
						.then(res => {
							if (res.code == 200) {
								this.$message.success("删除成功");
								this.headerInquiry();
							}
						})
						.catch();
				})
				.catch(() => {
					// this.$message({
					// 	type: "info",
					// 	message: "已取消删除"
					// });
				});
		},
		handleSizeChange(val) {
			this.pages.size = val;
			this.getSchoolListInfo();
		},
		handleCurrentChange(val) {
			this.pages.current = val;
			this.getSchoolListInfo();
		}
	}
};
</script>

<style scoped lang="scss">
.schoolForm {
	.addSchool {
		height: 36px;
	}
	margin-top: 20px;
	display: flex;
}
.btn {
	display: flex;
	justify-content: space-between;
	margin-bottom: 16px;
	padding-top: 16px;
	border-top: 1px solid #ebeef5;
	span {
		margin: 10px;
	}
}
.paginationBlock {
	margin-top: 16px;
	text-align: right;
}
</style>
