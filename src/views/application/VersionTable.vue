<!--
 * @Descripttion:VersionTable
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-08-23 21:18:54
 * @LastEditors: 杨剑兴
 * @LastEditTime: 2024-06-11 16:21:15
-->
<template>
	<div>
		<div class="version_table_form">
			<el-form
				ref="ruleForm"
				inline
				:model="ruleForm"
				label-width="100px"
				class="demo-ruleForm"
			>
				<el-form-item prop="versionName">
					<el-input
						v-model="ruleForm.versionName"
						style="width:300px;margin-right:10px;"
						placeholder="请输入版本名称"
					/>
				</el-form-item>
				<el-form-item>
					<el-button
						type="primary"
						icon="el-icon-search"
						@click="queryFn()"
						>查询</el-button
					>
				</el-form-item>
				<el-button @click="resetForm('ruleForm')">重置</el-button>
			</el-form>
		</div>
		<div class="btn">
			<span>按版本管理</span>
			<div>
				<el-button
					type="primary"
					icon="el-icon-top"
					class="upload"
					plain
					@click="UploadFileFn()"
					>上传文件</el-button
				>
			</div>
		</div>
		<div class="from_list">
			<el-table
				:data="versionTableData"
				border
				style="width: 100%"
				:header-cell-style="{ background: '#fafafa', color: '#5b5d61' }"
				:cell-style="cellStyleFun"
			>
				<el-table-column label="序号" width="50">
					<template slot-scope="scope">
						<span v-text="scope.$index + 1" />
					</template>
				</el-table-column>

				<el-table-column
					align="center"
					v-for="(item, idx) in versionTableColumn"
					:key="idx"
					show-overflow-tooltip
					:prop="item.prop"
					:label="item.label"
					:formatter="formatter"
				/>

				<el-table-column
					label="操作"
					width="240px"
					align="right"
					fixed="right"
				>
					<template slot-scope="scope">
						<el-link
							style="margin-left:10px"
							icon="el-icon-bottom"
							type="primary"
							plain
							size="mini"
							@click="downloadFn(scope.row.fileUrl)"
							>下载</el-link
						>
						<el-link
							style="margin-left:10px"
							icon="el-icon-edit"
							type="primary"
							plain
							size="mini"
							@click="ediuFn(scope.row)"
							>修改</el-link
						>
						<el-link
							style="margin-left:10px"
							type="danger"
							plain
							size="mini"
							@click="delFn(scope.row.id)"
						>
							删除
							<i class="el-icon-delete" />
						</el-link>
					</template>
				</el-table-column>
			</el-table>
		</div>
		<div class="paginationBlock">
			<el-pagination
				v-if="pages.total > 10"
				background
				style="margin-top:20px;"
				:current-page="pages.current"
				:page-sizes="[10, 20, 30, 50]"
				:page-size="pages.size"
				layout="total, sizes, prev, pager, next, jumper"
				:total="pages.total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
		</div>
	</div>
</template>

<script>
import { mapGetters } from "vuex";
import {
	queryClassificListApi,
	delVersionManagementApi
} from "@/api/application.js";
export default {
	name: "VersionTable",
	components: {},
	props: {},
	data() {
		return {
			versionRuleForm: {
				list: [{ versionName: "" }]
			},
			ruleForm: { versionName: "" },
			versionName: "",
			sys_id: "",
			// versionTableData: [],
			// pages: {
			// 	current: 1,
			// 	size: 10,
			// 	total: 0
			// },
			updateType: ["强制", "不强制"],
			versionTableColumn: [
				{ prop: "versionName", label: "版本名称" },
				{ prop: "versionNumber", label: "版本号" },
				// { prop: "fileUrl", label: "文件地址" },
				{ prop: "createdDateTime", label: "上传时间" },
				{ prop: "updateType", label: "更新类型" },
				{ prop: "updateTips", label: "更新提示" }
			]
		};
	},
	computed: {
		...mapGetters([
			"versionNavigationList",
			"brandApplicationId",
			"versionTableData",
			"pages"
		])
	},
	watch: {
		brandApplicationId(value, olValue) {
			if (value !== olValue) {
				this.initListFn();
			}
		}
	},
	created() {},
	mounted() {
		this.initListFn();
	},

	methods: {
		thStyleFun() {
			return "text-align:center";
		},
		cellStyleFun() {
			return "text-align:center";
		},
		downloadFn(url) {
			if (url.indexOf("http") != -1) {
				window.location.href = url;
			} else {
				this.$message.error("此路径错误");
			}
		},

		ediuFn(item) {
			item.sys_id = this.brandApplicationId;
			const params = {
				drawerShow: true,
				drawerTitle: "修改版本管理",
				item
			};

			this.$emit("modifyVersion", params);
		},
		delFn(id) {
			this.$confirm("此操作将永久删除此信息, 是否继续?", "提示", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				type: "warning"
			})
				.then(() => {
					delVersionManagementApi({ id })
						.then(res => {
							const { current } = this.pages;

							if (current > this.versionTableData.length) {
								this.pages.current = 1;
							}
							if (res.code == "200") {
								this.$message.success("删除成功");
								this.initListFn();
							}
						})
						.catch();
				})
				.catch(() => {
					this.$message({
						type: "info",
						message: "已取消删除"
					});
				});
		},
		formatter(row, column, cellValue, index) {
			if (row.updateType == cellValue) {
				cellValue = this.updateType[cellValue - 1];
			}
			return cellValue;
		},

		//获取版本
		getversionListFn() {
			queryClassificListApi({
				brandApplicationId: this.brandApplicationId
			})
				.then(res => {
					if (res.code == "200") {
						this.versionList = res.data;
						if (res.data.length > 0) {
							this.versionRuleForm.list = res.data;
						} else {
							this.versionRuleForm.list = [{ versionName: "" }];
						}
					}
				})
				.catch();
		},
		UploadFileFn() {
			const params = {
				drawerShow: true,
				drawerTitle: "上传文件"
			};

			this.$emit("modifyVersion", params);
		},
		queryFn() {
			this.initListFn();
		},
		resetForm(formName) {
			this.$refs[formName].resetFields();
			this.initListFn();
		},

		handleSizeChange(val) {
			this.pages.size = val;
			this.initListFn();
		},
		handleCurrentChange(val) {
			this.pages.current = val;
			this.initListFn();
		},
		initListFn() {
			const params = {
				versionType: 1,
				brandApplicationId: this.brandApplicationId,
				versionName: this.ruleForm.versionName,
				...this.pages
			};

			this.$store.dispatch(
				"VersionSchool/quetyRegistrationCodeInfo",
				params
			);
		}
	}
};
</script>

<style scoped lang="scss">
.version_table_form {
	margin-top: 20px;
	display: flex;
	justify-content: space-between;
	.upload {
		height: 36px;
	}
}
.btn {
	display: flex;
	justify-content: space-between;
	margin-bottom: 16px;
	padding-top: 16px;
	border-top: 1px solid #ebeef5;
	span {
		margin: 10px;
	}
}
.paginationBlock {
	margin-top: 16px;
	text-align: right;
}
</style>
