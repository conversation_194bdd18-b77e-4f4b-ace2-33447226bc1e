<!--
 * @Descripttion: 应用添加，编辑
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-04-01 15:16:45
 * @LastEditors: jingrou
 * @LastEditTime: 2022-07-21 10:35:04
-->

<template>
    <el-dialog
        :title="`${isEdit ? '编辑应用' : '添加应用'}`"
        :visible.sync="visible"
        direction="rtl"
        width="780px"
        top="10vh"
        :modal-append-to-body="false"
    >
        <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="120px"
            class="drawer_form"
        >
            <el-form-item label="应用名称：" prop="name">
                <el-input v-model.trim="form.name" />
            </el-form-item>
            <el-form-item label="应用提供商" prop="supplierId">
                <el-select v-model="form.supplierId" placeholder>
                    <el-option
                        v-for="item in supplierList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    ></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="应用介绍：" prop="introduction">
                <el-input
                    v-model.trim="form.introduction"
                    type="textarea"
                    :rows="3"
                    maxlength="200"
                    show-word-limit
                    placeholder="输入应用介绍"
                />
            </el-form-item>

            <el-form-item label="应用分组：" prop="groupId">
                <el-select v-model="form.groupId" placeholder="请选择">
                    <el-option
                        v-for="(i, j) in groupList"
                        :key="`group_${j}`"
                        :label="i.name"
                        :value="i.id"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="应用类型：" prop="type">
                <el-radio-group v-model="form.type">
                    <el-radio label="0">H5应用</el-radio>
                    <el-radio label="1">原生应用</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="App路径：" prop="path">
                <el-input v-model.trim="form.path" />
            </el-form-item>
            <el-form-item label="云平台链接：" prop="backstagePath">
                <el-input v-model.trim="form.backstagePath" />
            </el-form-item>
            <el-form-item label="应用可见：" prop="showTypes">
                <el-checkbox-group v-model="form.showTypes">
                    <el-checkbox :label="0">App端展示</el-checkbox>
                    <el-checkbox :label="1">云平台展示</el-checkbox>
                </el-checkbox-group>
            </el-form-item>
            <el-form-item label="图标：" prop="img">
                <div class="uploadBox" @click="dialogVisible = true">
                    <img
                        :src="form.img"
                        alt
                        v-if="form.img"
                        style="height: 100%; width: auto"
                    />
                    <i class="el-icon-plus uploadBox_icon" v-else></i>
                </div>
            </el-form-item>
            <el-form-item label="排序：" prop="sort">
                <el-input-number
                    v-model="form.sort"
                    controls-position="right"
                    :min="1"
                    :max="9999"
                />
            </el-form-item>
            <el-form-item label="可见范围：" prop="num">
                <!-- <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox> -->
                <el-checkbox-group v-model="form.num">
                    <el-checkbox
                        v-for="city in cities"
                        :key="city.label"
                        :label="city.value"
                        >{{ city.label }}</el-checkbox
                    >
                </el-checkbox-group>
            </el-form-item>
            <!-- 新加接口链接 -->
            <el-form-item label="接口链接:" prop="documentLink">
                <el-input v-model.trim="form.documentLink" />
            </el-form-item>
        </el-form>
        <div class="drawer_form__footre">
            <el-button @click="resetForm">取消</el-button>
            <el-button type="primary" :loading="loading" @click="submitForm()"
                >确定</el-button
            >
        </div>

        <el-dialog
            title="图片剪裁"
            :visible.sync="dialogVisible"
            append-to-body
            :modal-append-to-body="false"
            width="780px"
        >
            <div class="cropper-content">
                <Cropper @cancel="dialogVisible = false" @upload="upload" />
            </div>
            <!-- <div slot="footer" class="dialog-footer">
				<el-button @click="dialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="finish" :loading="loading">确认</el-button>
			</div>-->
        </el-dialog>
    </el-dialog>
</template>
<script>
import {
    createApply,
    getApplyInfo,
    updateApply,
    uploadApplyLogo,
} from "@/api/apply.js";
import { supplierName } from "@/api/supplier.js";
import { getToken } from "@/utils/auth";

import Cropper from "@/components/Cropper/index";
const cityOptions = [
    { label: "家长", value: "0" },
    { label: "教职工", value: "4" },
];

export default {
    components: {
        Cropper,
    },
    props: {
        isEdit: {
            type: Boolean,
            default: false,
        },
        groupList: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            dialogVisible: false,
            supplierList: [],
            isIndeterminate: false,
            checkAll: false,
            cities: cityOptions,
            visible: false,
            loading: false,
            form: {
                supplierId: "",
                name: "",
                groupId: null,
                type: "0",
                sort: null,
                path: "",
                img: "",
                num: [],
                backstagePath: "",
                introduction: "",
                showTypes: [],
                documentLink: "",
            },
            rules: {
                name: [
                    {
                        required: true,
                        message: "请输入应用名称",
                        trigger: "blur",
                    },
                    {
                        min: 1,
                        max: 20,
                        message: "长度在 1 到 20 个字符",
                        trigger: "blur",
                    },
                ],
                supplierId: [
                    {
                        required: true,
                        message: "请选择供应商",
                        trigger: "change",
                    },
                ],
                introduction: [
                    {
                        required: true,
                        message: "请输入应用介绍",
                        trigger: "blur",
                    },
                    {
                        min: 1,
                        max: 200,
                        message: "长度在 1 到 200 个字符",
                        trigger: "blur",
                    },
                ],
                groupId: [
                    {
                        required: true,
                        message: "请选择应用分组",
                        trigger: "change",
                    },
                ],
                type: [
                    {
                        required: true,
                        message: "请选择应用类型",
                        trigger: "change",
                    },
                ],
                path: [
                    {
                        required: true,
                        message: "请输入应用路径",
                        trigger: "blur",
                    },
                ],
                backstagePath: [
                    {
                        required: true,
                        message: "请输入后台应用路径",
                        trigger: "blur",
                    },
                ],
                img: [
                    {
                        required: true,
                        message: "请上传应用图标",
                        trigger: "change",
                    },
                ],
                sort: [
                    {
                        required: true,
                        message: "请输入排序",
                        trigger: "change",
                        type: "number",
                    },
                    {
                        min: 1,
                        max: 9999,
                        message: "排序区间 1 到 9999 个",
                        trigger: "blur",
                        type: "number",
                    },
                ],
                num: [
                    {
                        required: true,
                        message: "请选择应用可见范围",
                        trigger: "change",
                    },
                ],
                documentLink: [
                    {
                        required: true,
                        message: "请输入接口链接",
                        trigger: "blur",
                    },
                ],
            },
            imageUrl: "",
            uploadURL:
                process.env.VUE_APP_BASE_API +
                "/backstage/cloud-backstage/application/toOss",
            // headers: {
            // 	Authorization: getToken(),
            // },
        };
    },
    created() {
        this.supplierName();
    },
    methods: {
        upload(blob, name, cb) {
            const _this = this,
                file = new File([blob], name, { type: blob.type }),
                params = new FormData();

            params.append("file", file);
            uploadApplyLogo(params)
                .then((res) => {
                    if (res.code == 200) {
                        _this.form.img = res.data;
                        _this.dialogVisible = false;
                    }
                })
                .finally(() => {
                    cb();
                });
        },
        // 供应商
        supplierName() {
            supplierName().then(({ data, code, msg }) => {
                if (code === 200) {
                    this.supplierList = data;
                } else {
                    this.$message.error(msg);
                }
            });
        },
        handleAvatarChange(file) {
            this.option.img = URL.createObjectURL(file.raw);
            this.dialogVisible = true;
        },
        handleAvatarSuccess(res, file) {
            const { code, data, msg } = res;

            if (code === 200) {
                this.form.img = data;
                this.option.img = data;
            } else {
                this.form.img = "";
                this.$message.error(msg);
            }
        },
        handleAvatarError(res) {},
        beforeAvatarUpload(file) {
            const isLt2M = file.size / 1024 / 1024 < 2;

            if (!isLt2M) {
                this.$message.error("上传头像图片大小不能超过 2MB!");
            }
            return isLt2M;
        },
        // handleCheckAllChange(val) {
        // 	this.form.num = val ? cityOptions.map(i => i.value) : [];
        // 	this.isIndeterminate = false;
        // },
        handleCheckedCitiesChange(value) {
            const checkedCount = value.length;

            this.checkAll = checkedCount === this.cities.length;
            this.isIndeterminate =
                checkedCount > 0 && checkedCount < this.cities.length;
        },
        open() {
            this.visible = true;
            this.$nextTick(() => {
                this.$refs.form.resetFields();
            });
            return this;
        },
        submitForm() {
            this.$refs.form.validate(async (valid) => {
                if (valid) {
                    this.loading = true;
                    const collective = ({ code, msg }) => {
                            if (code === 200) {
                                this.$message.success(msg);
                                this.$emit("updata");
                                this.resetForm();
                            } else {
                                this.$message.error(msg);
                            }
                        },
                        finallyF = () => {
                            this.loading = false;
                        };

                    if (this.isEdit) {
                        updateApply({ ...this.form, id: this.id })
                            .then(collective)
                            .finally(finallyF);
                    } else {
                        // await this.$refs.upload.submit();
                        createApply(this.form)
                            .then(collective)
                            .finally(finallyF);
                    }
                } else {
                    return false;
                }
            });
        },
        getApplyInfo(id) {
            this.id = id;
            getApplyInfo({ id }).then(({ code, data, message }) => {
                if (code === 200) {
                    const {
                        name,
                        sort,
                        groupId,
                        type,
                        path,
                        num,
                        img,
                        backstagePath,
                        introduction,
                        showTypes,
                        supplierId,
                        documentLink,
                    } = data;

                    this.form.name = name;
                    this.form.supplierId = supplierId;
                    this.form.groupId = groupId;
                    this.form.sort = sort;
                    this.form.type = type;
                    this.form.path = path;
                    this.form.num = num || [];
                    this.form.img = img;
                    this.form.backstagePath = backstagePath;
                    this.form.introduction = introduction;
                    this.form.showTypes = showTypes || [];
                    this.form.documentLink = documentLink;
                } else {
                    this.$message.error(message);
                }
            });
        },
        resetForm() {
            this.$refs.form.resetFields();
            this.visible = false;
        },
    },
};
</script>

<style lang="scss" scoped>
.drawer_form {
    height: 580px;
    overflow-y: auto;
    position: relative;
    padding: 0 20px;
    margin-bottom: 70px;
    &__footre {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        text-align: right;
        padding: 20px;
        border-top: 1px solid #dcdfe6;
    }
    ::v-deep .avatar-uploader .el-upload {
        border: 1px dashed #dcdfe6;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }
    .avatar-uploader .el-upload:hover {
        border-color: #409eff;
    }
    .avatar-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 104px;
        height: 104px;
        line-height: 104px;
        text-align: center;
    }
    .avatar {
        width: 104px;
        height: 104px;
        display: block;
    }
}

.uploadBox {
    border: 1px dashed #dcdfe6;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;

    display: flex;
    text-align: center;
    align-items: center;
    justify-content: center;
    text-align: center;
    width: 104px;
    height: 104px;
    .uploadBox_icon {
        font-size: 28px;
        color: #8c939d;
        width: 104px;
        height: 104px;
        line-height: 104px;
        text-align: center;
    }
}
</style>
