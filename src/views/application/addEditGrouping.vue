<!--
 * @Descripttion: 应用分组添加，编辑
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-04-01 15:16:45
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2021-11-15 09:28:14
-->

<template>
	<el-drawer
		:title="`${isEdit ? '编辑应用分组' : '添加应用分组'}`"
		:visible.sync="visible"
		direction="rtl"
	>
		<el-form
			:model="form"
			:rules="rules"
			ref="form"
			label-width="100px"
			class="drawer_form"
		>
			<el-form-item label="分组名称：" prop="name">
				<el-input v-model.trim="form.name"></el-input>
			</el-form-item>
			<el-form-item label="排序：" prop="sort">
				<el-input-number
					v-model="form.sort"
					controls-position="right"
					:min="1"
					:max="9999"
				></el-input-number>
				<p style="color:#ccc">序号影响分组的排序，序号越大，排序越前</p>
			</el-form-item>
			<div class="drawer_form__footre">
				<el-button
					type="primary"
					:loading="loading"
					@click="submitForm()"
					>确定</el-button
				>
				<el-button @click="resetForm">取消</el-button>
			</div>
		</el-form>
	</el-drawer>
</template>
<script>
import {
	createApplygroup,
	getApplygroupInfo,
	updateApplygroup
} from "@/api/apply.js";
export default {
	props: {
		isEdit: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			visible: false,
			loading: false,
			form: {
				name: "",
				sort: null
			},
			rules: {
				name: [
					{
						required: true,
						message: "请输入分组名称",
						trigger: "blur"
					},
					{
						min: 1,
						max: 6,
						message: "长度在 1 到 6 个字符",
						trigger: "blur"
					}
				],
				sort: [
					{
						required: true,
						message: "请输入排序数值",
						trigger: "change",
						type: "number"
					},
					{
						min: 1,
						max: 9999,
						message: "排序区间 1 到 9999 个",
						trigger: "blur",
						type: "number"
					}
				]
			},
			id: null
		};
	},
	created() {},
	methods: {
		open() {
			this.visible = true;
			return this;
		},
		submitForm() {
			this.$refs.form.validate(valid => {
				if (valid) {
					this.loading = true;
					const collective = ({ code, msg }) => {
							if (code === 200) {
								this.$message.success(msg);
								this.$emit("updata");
								this.resetForm();
							} else {
								this.$message.error(msg);
							}
						},
						finallyF = () => {
							this.loading = false;
						};

					if (this.isEdit) {
						updateApplygroup({ ...this.form, id: this.id })
							.then(collective)
							.finally(finallyF);
					} else {
						createApplygroup(this.form)
							.then(collective)
							.finally(finallyF);
					}
				} else {
					return false;
				}
			});
		},
		getApplygroupInfo(id) {
			this.id = id;
			getApplygroupInfo({ id }).then(({ code, data, message }) => {
				if (code === 200) {
					const { name, sort } = data;

					this.form.name = name;
					this.form.sort = sort;
				} else {
					this.$message.error(message);
				}
			});
		},
		resetForm() {
			this.$refs.form.resetFields();
			this.visible = false;
		}
	}
};
</script>

<style lang="scss" scoped>
.drawer_form {
	height: 100%;
	overflow-y: auto;
	position: relative;
	padding: 0 20px;
	&__footre {
		position: absolute;
		left: 0;
		right: 0;
		bottom: 0;
		text-align: center;
		padding: 20px 0;
		border-top: 1px solid #dcdfe6;
	}
}
</style>
