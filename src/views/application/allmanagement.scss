.menu_left_content {
    float: left;
    width: 12%;
    height: 800px;
    text-align: center;
    background: #fff;
    box-shadow: 1px 2px 4px 0px rgba(214, 214, 214, 0.17);
    border-radius: 4px;
    margin: 0px 2%;
}

.menu_right_content {
    float: left;
    width: 84%;
    background: #fff;
    box-shadow: 1px 2px 4px 0px rgba(214, 214, 214, 0.17);
    border-radius: 4px;
    height: 800px;
}

.add_application {
    margin: 30px auto 20px;
    width: 120px;
}

.add_menu {
    margin: 0px 32px;
    width: 120px;
}

//添加菜单
.application_name {
    color: #333;
    font-size: 20px;
    margin: 36px 0px 24px 32px;
    font-weight: bold;
}

.menu_drawer {
    position: fixed;
}

.yd-form-footer {
    width: 100%;
    text-align: center;
    border-top: 1px solid #dcdfe6;
    position: absolute;
    padding-top: 20px;
    bottom: 0px;
    background: #fff;
    margin: 0;
    padding: 20px 0;
    .el-form-item__content {
        margin-left: 0 !important;
    }
}

.menu_ruleForm {
    width: 530px;
    margin: 0px auto;
}

.drawer_title {
    width: 100%;
    height: 40px;
    line-height: 40px;
    background-color: #f4f8fc;
    color: #333;
    padding-left: 20px;
    margin-bottom: 20px;
}

.add_btn {
    float: right;
    font-size: 22px;
    line-height: 40px;
    color: #2c8aff;
    cursor: pointer;
    margin-right: 10px;
}

.remove_btn {
    font-size: 22px;
    display: block;
    cursor: pointer;
    color: #dddddd;
}

.second_right_btn {
    float: right;
    margin: 55px 10px 0px 20px;
}

.second_list {
    border-bottom: 10px solid #f4f8fc;
    overflow: hidden;
    padding-bottom: 5px;
    margin-bottom: 20px;
}

.second_newlist {
    max-height: 470px;
    overflow-y: auto;
}

.avatar-uploader {
    height: 38px;
}

.avatar-uploader .el-upload {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    height: 36px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.avatar-uploader .el-upload:hover {
    border-color: #409eff;
}

.avatar-uploader-icon {
    font-size: 20px;
    color: #dcdfe6;
    width: 36px;
    height: 36px;
    line-height: 36px;
    text-align: center;
}

.avatar {
    width: 36px;
    height: 36px;
    display: inline-block;
}

.menu_table {
    margin-top: 20px;
    height: 650px;
    overflow-y: auto;
    .el-table th {
        background-color: #f5f7fa;
    }
    .ediu_style {
        color: #2c8aff;
        margin-right: 20px;
        cursor: pointer;
    }
    .delete_style {
        cursor: pointer;
    }
}

.app_uploader {
    width: 100px;
    height: 100px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
}

.app_uploader_icon {
    font-size: 20px;
    color: #dcdfe6;
    line-height: 100px;
    display: block;
    width: 100px;
    text-align: center;
}

//班牌管理
.modeular_type {
    display: inline-block;
    span {
        font-size: 16px;
        color: #333;
        padding-right: 15px;
        cursor: pointer;
    }
}

.w_font {
    font-weight: bolder;
    color: #333;
}

.tab_box {
    margin: 0px 0px 20px 33px;
    .el-radio-group {
        box-shadow: 1px 2px 4px 0px rgba(214, 214, 214, 0.17);
    }
    .el-radio-button__inner {
        border: none;
    }
}

.ModularList_box {
    width: 97%;
    height: 620px;
    overflow-y: auto;
    margin: 0px auto;
}

.ModularList_ul {
    margin-top: 20px;
}

.ModularList_li {
    float: left;
    padding: 10px 15px;
    border: 1px solid #DCDFE6;
    color: #333333;
    font-size: 14px;
    cursor: pointer;
    border-radius: 4px;
    margin: 0px 10px;
    position: relative;
}

.ModularList_li:hover {
    border: 1px solid #EBF0F6;
    background-color: #EBF0F6;
}

.one {
    border: 1px solid #EBF0F6;
    background-color: #EBF0F6;
}

.error_btn {
    font-size: 16px;
    position: absolute;
    right: -7px;
    top: -7px;
    color: #2C8AFF;
    cursor: pointer;
}

.clearfix {
    clear: both;
}

.size_tabs {
    margin: 20px 0px;
    span {
        padding: 30px 15px;
        font-size: 15px;
        color: #333;
        cursor: pointer;
    }
    span:hover {
        color: #2C8AFF;
    }
}

.photo_modular_list {
    overflow: hidden;
    margin-top: 20px;
    margin-left: 16px;
}

.add_pmodular {
    width: 320px;
    height: 200px;
    border: 1px solid #DCDFE6;
    border-radius: 4px;
    float: left;
    text-align: center;
    margin-right: 20px;
    margin-bottom: 20px;
    i {
        color: #C0C4CC;
        font-size: 42px;
        margin-top: 60px;
    }
    span {
        color: #C0C4CC;
        font-size: 16px;
        display: block;
        margin-top: 15px;
    }
}

.add_photo {
    width: 320px;
    float: left;
    margin-right: 20px;
    margin-bottom: 20px;
}

.add_m {
    width: 320px;
    height: 200px;
    border: 1px solid #DCDFE6;
    border-radius: 4px;
    float: left;
    overflow: hidden;
    background-color: #DCDFE6;
    display: flex;
    align-items: center;
    padding: 10px;
    img {
        margin: 0px auto;
    }
}

.add_m_btn {
    width: 100%;
    overflow: hidden;
    span {
        color: #909399;
        cursor: pointer;
        display: block;
        float: left;
        cursor: pointer;
        line-height: 30px;
    }
    span:nth-child(2) {
        display: block;
        float: right;
    }
}

.model_uploader {
    width: 100%;
    border: 1px solid #DCDFE6;
    border-radius: 4px;
    height: 200px;
    overflow: hidden;
    text-align: center;
}

.model_img {
    height: 200px;
}

.model_icon {
    i {
        font-size: 40px;
        color: #DCDFE6;
        margin-top: 58px;
    }
    span {
        font-size: 16px;
        color: #DCDFE6;
        display: block;
    }
}

.add_Verification {
    margin: 0px 32px;
    width: 144px;
}

.demo-h-ruleForm {
    margin-bottom: 85px;
    overflow-y: auto;
    height: calc(100vh - 155px);
}