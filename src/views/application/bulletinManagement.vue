<template>
  <div class="height100">
    <MyIframe :src="src" :isPostMessage="true"></MyIframe>
  </div>
</template>

<script>
import MyIframe from "@/components/Iframe/index";
import Vue from "vue";
import { ACCESS_TOKEN } from "@/store/mutation-types";
export default {
  components: {
    MyIframe,
  },
  data() {
    return {
      src: "",
    };
  },

  created() {
    const token = `Bearer ${Vue.ls.get(ACCESS_TOKEN)}`;
    const color = localStorage.getItem("COLOR_THEME");
    // 看板模版管理 ${process.env.VUE_APP_API_BASE_BOARD}
    this.src = `${process.env.VUE_APP_API_BASE_BOARD}/#/dataCenter?attr=template&token=${token}&colorPrimary=${color}&platform=manage`
  },
};
</script>

<style lang="scss" scoped>
@import "@/style/element-variables.scss";

.iframe_warp {
  width: 100%;
  height: calc(100vh - 100px);
  background-color: $--color-white;

  .my_iframe {
    width: 100%;
    height: 100%;
  }
}
</style>