<!--
 * @Descripttion: 
 * @version: 1.0.0
 * @Author: jingrou
 * @Date: 2022-06-14 18:30:43
 * @LastEditors: jingrou
 * @LastEditTime: 2023-02-21 09:38:03
-->
<template>
    <div class="menu_content">
        <div>
            <el-radio-group v-model="classRadio" @change="classRadioChange">
                <el-radio-button :label="1">模块尺寸</el-radio-button>
                <el-radio-button :label="0">验证方式</el-radio-button>
            </el-radio-group>
        </div>
        <div class="fnButton">
            <span v-if="classRadio === 1">
                <span
                    :style="direction == 0 ? 'font-weight: 600' : ''"
                    @click="modeularFn(0)"
                    >横版</span
                >
                <span
                    :style="direction == 1 ? 'font-weight: 600' : ''"
                    @click="modeularFn(1)"
                    >竖版</span
                >
            </span>
            <div>
                <el-button
                    v-auth="'manage.classManagement.addStyle'"
                    type="primary"
                    v-show="classRadio === 1"
                    @click="templateStyleFn"
                    icon="el-icon-plus"
                >
                    添加模块风格
                </el-button>
                <el-button
                    type="primary"
                    v-auth="'manage.classManagement.addSize'"
                    v-show="classRadio === 1"
                    @click="addSizeFn"
                    icon="el-icon-plus"
                >
                    添加模块尺寸
                </el-button>
                <el-button
                    type="primary"
                    v-auth="'manage.classManagement.addType'"
                    @click="addNameFn"
                    v-show="classRadio === 1"
                    icon="el-icon-plus"
                >
                  添加模块分类
                </el-button>
                <el-button
                    type="primary"
                    @click="addNameFn"
                    v-show="classRadio === 0"
                    v-auth="'manage.classManagement.addVerify'"
                    icon="el-icon-plus"
                >
                  添加验证方式
                </el-button>
            </div>
        </div>
        <div v-loading="loading">
            <!-- 验证方式 -->
            <div class="classList" v-if="classRadio === 0">
                <div
                    class="classListLi"
                    v-for="item in validationList"
                    :key="item.id"
                    v-show="item.name"
                >
                    {{ item.name }}
                    <div
                        class="deleteClass el-icon-error"
                        @click="deleteValidation(item)"
                    ></div>
                </div>
            </div>
            <!-- 模板分类 -->
            <div class="classList" v-if="classRadio === 1">
                <div
                    class="classListLi"
                    :id="'classListLi' + index"
                    v-for="(item, index) in moduleEntityList"
                    :key="item.id"
                    :style="
                        classifyId == item.id
                            ? 'background-color: #EBF0F6;border: 1px solid #EBF0F6'
                            : ''
                    "
                    v-show="item.name"
                    @click="moduleEntitChang(item, index)"
                >
                    {{ item.name }}
                    <div
                        @click="deleteModuleEntity(item)"
                        class="deleteClass el-icon-error"
                    ></div>
                </div>
            </div>
            <!-- 尺寸 -->
            <div class="size_tabs" v-if="classRadio === 1">
                <span
                    v-for="item in sizeList"
                    :key="item.id"
                    :style="
                        size.width == item.width && size.height == item.high
                            ? 'color:#2C8AFF'
                            : ''
                    "
                    @click="sizeTabsChange(item)"
                    >{{ item.width + "x" + item.high }}</span
                >
            </div>
            <div v-if="classRadio === 1">
                <el-select
                    style="width: 300px"
                    v-model="layoutStyleId"
                    placeholder="请选择"
                    @change="getModuleList"
                >
                    <el-option
                        v-for="(item, index) in queryStyleList"
                        :key="item.id + index"
                        :label="item.styleName"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
            </div>
            <!-- 模板列表 -->
            <div class="moduleList" v-if="classRadio === 1">
                <div
                    class="addImage"
                    style="display: flex; flex-direction: column"
                    v-if="sizeList.length > 0"
                    @click="addModule(false)"
                >
                    <i class="el-icon-plus"></i>
                    <span style="color: #ccc; margin-top: 5px"> 新增模块</span>
                </div>
                <div
                    v-for="item in moduleList"
                    :key="item.id"
                    class="addImage images"
                >
                    <div
                        style="
                            width: 320px;
                            height: 200px;
                            overflow: hidden;
                            justify-content: center;
                            display: flex;
                            align-items: center;
                        "
                    >
                        <img
                            :src="item.images"
                            :style="
                                item.width > item.high
                                    ? 'width:100%;'
                                    : 'height:100%;'
                            "
                            alt=""
                        />
                    </div>

                    <div class="imageBotton">
                        <span @click="addModule(true, item)">编辑</span>
                        <span @click="removeModule(item)">删除</span>
                    </div>
                </div>
            </div>
        </div>

        <div
            v-if="
                classRadio === 1 &&
                (sizeList.length <= 0 || moduleEntityList.length <= 0)
            "
            style="margin-top: 30px"
        >
            <el-empty description="暂无数据"></el-empty>
        </div>
        <div
            style="margin-top: 30px"
            v-if="classRadio === 0 && validationList.length <= 0"
        >
            <el-empty description="暂无数据"></el-empty>
        </div>
        <!-- 添加模块分类或验证方式 -->
        <el-drawer
            :title="classRadio === 1 ? '添加模块分类' : '添加验证方式'"
            :visible.sync="addNameVisible"
            :before-close="close"
            width="30%"
        >
            <!-- 添加模块 -->
            <el-form :model="addNameForm" ref="addNameForm" class="formClass">
                <el-form-item
                    :label="classRadio === 1 ? '模块名称：' : '验证方式：'"
                    prop="name"
                    :inline="true"
                    label-width="auto"
                    :rules="{
                        required: true,
                        message: '请输入',
                        trigger: 'blur',
                    }"
                >
                    <el-input
                        maxlength="10"
                        show-word-limit
                        style="width: 250px"
                        v-model.trim="addNameForm.name"
                        :placeholder="
                            classRadio === 1
                                ? '请输入模块名称'
                                : '请输入验证方式'
                        "
                    />
                </el-form-item>
                <el-form-item class="form-footer">
                    <div class="footerForm">
                        <el-button plain @click="close">取消</el-button>
                        <el-button
                            type="primary"
                            :loading="typeLoading"
                            @click="submitForm('addNameForm')"
                            >确定</el-button
                        >
                    </div>
                </el-form-item>
            </el-form>
        </el-drawer>

        <!-- 添加模块尺寸 -->
        <el-drawer
            title="添加模块尺寸"
            :visible.sync="addSizeVisible"
            :before-close="closeSzie"
            width="40%"
        >
            <el-form :model="addSizeForm" ref="addSizeForm" class="formClass">
                <el-form-item
                    label="横版/竖版："
                    prop="sizeDirection"
                    :inline="true"
                    class="demo-ruleForm"
                    label-width="auto"
                    :rules="{
                        required: true,
                        message: '请选择',
                        trigger: 'blur',
                    }"
                >
                    <el-radio-group
                        @change="radioDirection"
                        v-model="addSizeForm.sizeDirection"
                    >
                        <el-radio :label="0">横版</el-radio>
                        <el-radio :label="1">竖版</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item
                    label="模块列表："
                    prop="classifyId"
                    :inline="true"
                    class="demo-ruleForm"
                    label-width="auto"
                    :rules="{
                        required: true,
                        message: '请选择',
                        trigger: 'blur',
                    }"
                >
                    <el-select
                        v-model="addSizeForm.classifyId"
                        placeholder="请选择"
                    >
                        <el-option
                            v-for="item in classifyList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item
                    label="模块长度："
                    prop="sizeLength"
                    :inline="true"
                    class="demo-ruleForm"
                    label-width="auto"
                    :rules="{
                        required: true,
                        message: '请输入',
                        trigger: 'blur',
                    }"
                >
                    <el-input-number
                        :min="1"
                        laceholder="请输入模块长度"
                        v-model="addSizeForm.sizeLength"
                    ></el-input-number>
                </el-form-item>
                <el-form-item
                    label="模块高度："
                    prop="sizeWidth"
                    :inline="true"
                    class="demo-ruleForm"
                    label-width="auto"
                    :rules="{
                        required: true,
                        message: '请输入',
                        trigger: 'blur',
                    }"
                >
                    <el-input-number
                        :min="1"
                        laceholder="请输入模块宽度"
                        v-model="addSizeForm.sizeWidth"
                    ></el-input-number>
                </el-form-item>
                <el-form-item class="form-footer">
                    <div class="footerForm">
                        <el-button plain @click="closeSzie">取消</el-button>
                        <el-button
                            type="primary"
                            :loading="sizeLoading"
                            @click="submitSizeForm('addSizeForm')"
                            >确定</el-button
                        >
                    </div>
                </el-form-item>
            </el-form>
        </el-drawer>

        <!-- 添加编辑模块 -->
        <el-drawer
            :title="addModulTitle"
            :visible.sync="addModuleVisible"
            :before-close="closeAddModule"
            width="574px"
        >
            <el-form
                :model="addModuleForm"
                class="formClass"
                ref="addModuleForm"
                label-width="100px"
            >
                <el-form-item
                    label="模块尺寸："
                    prop="layoutSizeId"
                    class="demo-ruleForm"
                    :rules="{
                        required: true,
                        message: '请选择模块尺寸',
                        trigger: 'change',
                    }"
                >
                    <el-select
                        style="width: 300px"
                        :disabled="ediuType"
                        v-model="addModuleForm.layoutSizeId"
                        placeholder="请选择"
                    >
                        <el-option
                            v-for="item in sizeList"
                            :key="item.id"
                            :label="item.width + '  x  ' + item.high"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item
                    label="样式："
                    :rules="{
                        required: true,
                        message: '请上传文件地址',
                        trigger: 'blur',
                    }"
                    prop="images"
                >
                    <el-upload
                        class="upload-demo"
                        :headers="{
                            Authorization: token,
                            platform: 'system',
                        }"
                        :action="action"
                        :show-file-list="false"
                        :file-list="filelist"
                        :on-success="importSuccess"
                        :on-error="importError"
                        name="file"
                        :multiple="false"
                        accept=".jpg, .jpeg, .png, .JPG, .JPEG"
                    >
                        <div class="addFile">
                            <i
                                v-if="addModuleForm.images === ''"
                                class="el-icon-plus"
                            ></i>
                            <img
                                class="model_img"
                                :src="addModuleForm.images"
                                alt=""
                            />
                        </div>
                    </el-upload>
                </el-form-item>
                <el-form-item
                    label="tag标识："
                    prop="tag"
                    :inline="true"
                    class="demo-ruleForm"
                    :rules="{
                        required: true,
                        message: '请输入tag标识',
                        trigger: 'blur',
                    }"
                >
                    <el-input
                        :disabled="ediuType"
                        style="width: 300px"
                        v-model.trim="addModuleForm.tag"
                        placeholder="在这里输入当前尺寸的唯一标识"
                    />
                    <p style="color: #c0c4cc">
                        提示：使用横竖版、模块名称、尺寸内容首字母填写
                    </p>
                </el-form-item>
                <el-form-item
                    label="说明:"
                    :rules="{
                        required: true,
                        message: '请输入该模块尺寸的说明信息',
                        trigger: 'blur',
                    }"
                    prop="descriptive"
                >
                    <el-input
                        style="width: 300px"
                        v-model.trim="addModuleForm.descriptive"
                        placeholder="在这里输入该模块尺寸的说明信息"
                    />
                </el-form-item>
                <el-form-item
                    label="模块风格："
                    prop="layoutStyleId"
                    class="demo-ruleForm"
                    :rules="{
                        required: true,
                        message: '请选择模块风格',
                        trigger: 'change',
                    }"
                >
                    <el-select
                        style="width: 300px"
                        v-model="addModuleForm.layoutStyleId"
                        placeholder="请选择"
                    >
                        <el-option
                            v-for="(item, index) in styleList"
                            :key="item.id + index"
                            :label="item.styleName"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <div class="form-footer">
                    <div class="footerForm">
                        <el-button plain @click="closeAddModule"
                            >取消</el-button
                        >
                        <el-button
                            type="primary"
                            :loading="moduleLoading"
                            @click="addModuleFormSubmit('addModuleForm')"
                            >确定</el-button
                        >
                    </div>
                </div>
            </el-form>
        </el-drawer>
        <!-- 添加模块风格 -->
        <TemplateStyle
            :drawerStatus="drawerStatus"
            @closeDrawer="closeDrawerFn"
            @okStyleFn="okStyleFn"
            :list="styleList"
            :styleOkLoading="styleOkLoading"
            @deleteStyle="deleteStyleFn"
        />
    </div>
</template>

<script>
import {
    getBrandApplicationList,
    addClassify,
    getClassifyList,
    getValidationList,
    deleteModuleEntity,
    removeValidation,
    addBrandLayoutSize,
    getLayoutSize,
    getModuleList,
    createLayoutModule,
    getLayoutModuleDetail,
    updateLayoutModule,
    deleteLayoutModule,
    addEditStyleModule,
    styleModuleList,
    deleteStyleModule,
} from "@/api/application.js";
import { ACCESS_TOKEN } from "@/store/mutation-types";
import TemplateStyle from "@/components/TemplateStyle";
export default {
    components: {
        TemplateStyle,
    },
    data() {
        return {
            layoutStyleId: "",
            styleOkLoading: false,
            drawerStatus: false,
            loading: false,
            direction: 0,
            token: "",
            action: process.env.VUE_APP_API_BASE_URL + "/manage/file/upload",
            classRadio: 1, // 0:验证，1：模块
            addNameVisible: false,
            addNameForm: {
                name: "",
            },
            addSizeVisible: false,
            addSizeForm: {
                sizeDirection: 0,
                sizeLength: "",
                sizeWidth: "",
                classifyId: "",
            },
            sys_Id: "", // 系统ID 默认1
            classifyList: [], // 添加尺寸模板
            moduleEntityList: [], // 模板分类列表
            validationList: [], // 验证方式列表
            sizeList: [], // 尺寸列表
            moduleList: [], // 模板列表
            addModuleVisible: false,
            addModulTitle: "添加模块",
            addModuleForm: {
                layoutSizeId: "",
                images: "",
                tag: "",
                descriptive: "",
                layoutStyleId: "",
            },
            filelist: [],
            classifyId: "",
            size: {
                width: "",
                height: "",
            },
            ediuType: false,
            sizeId: "",
            sizeLoading: false,
            moduleLoading: false,
            typeLoading: false,
            styleList: [],
            queryStyleList: [],
        };
    },
    created() {
        this.token = "Bearer " + this.$ls.get(ACCESS_TOKEN);
        this.getBrandApplicationList();
        this.styleModuleListFn();
    },
    methods: {
        // 点击新增模板风格
        templateStyleFn() {
            this.drawerStatus = true;
            this.styleModuleListFn();
        },
        // 模板风格列表
        styleModuleListFn() {
            styleModuleList({ styleName: "" }).then((res) => {
                this.styleList = res.data;
                const allArr = [
                    {
                        id: "",
                        styleName: "全部",
                    },
                ];
                this.queryStyleList = allArr.concat(res.data);
                this.layoutStyleId = "";
            });
        },
        okStyleFn(list) {
            this.styleOkLoading = true;
            addEditStyleModule({ list })
                .then((res) => {
                    this.$message.success(res.message);
                    this.closeDrawerFn();
                    this.styleModuleListFn();
                })
                .finally(() => {
                    this.styleOkLoading = false;
                });
        },
        deleteStyleFn(id) {
            deleteStyleModule({ id }).then((res) => {
                this.$message.success(res.message);
                this.styleModuleListFn();
            });
        },
        // 取消
        closeDrawerFn() {
            this.drawerStatus = false;
        },
        importSuccess(res) {
            if (res.data) {
                this.filelist = res.data;
                this.addModuleForm.images = res.data[0].url;
            } else {
                this.$message.error(res.message);
            }
        },
        importError(res) {
            this.$message.error(res.message);
        },
        // 系统ID
        getBrandApplicationList() {
            getBrandApplicationList().then((res) => {
                if (res.data.length > 0) {
                    this.sys_Id = res.data[0].id;
                } else {
                    this.sys_Id = 1; // 系统id 如果后端没返回 默认为1
                }
                this.modeularFn(0);
            });
        },
        classRadioChange() {
            switch (this.classRadio) {
                case 1:
                    this.modeularFn(0);
                    break;
                case 0:
                    this.verificationFn();
                    break;

                default:
                    break;
            }
        },
        // 添加模块分类
        addNameFn() {
            this.addNameVisible = true;
        },
        // 取消
        close() {
            this.addNameVisible = false;
            this.$refs["addNameForm"].resetFields();
        },
        // 确定
        submitForm(formName) {
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    this.typeLoading = true;
                    let obj = {
                        direction: this.modeulartype,
                        sysId: this.sys_Id,
                        type: this.classRadio,
                        ...this.addNameForm,
                    };
                    addClassify(obj)
                        .then((res) => {
                            this.addNameVisible = false;
                            this.$message.success(res.message);
                            this.close();
                            this.classRadioChange();
                            this.modeularFn(this.modeulartype);
                            this.verificationFn();
                        })
                        .finally(() => {
                            this.typeLoading = false;
                        });
                } else {
                    return false;
                }
            });
        },
        // 模板分类列表
        modeularFn(type, classifyId) {
            this.loading = true;
            this.direction = type;
            let obj = {
                sysId: this.sys_Id,
                type: this.classRadio,
            };
            getClassifyList(obj)
                .then((res) => {
                    this.moduleEntityList = res.data;
                    this.classifyList = res.data;
                    this.classifyId = classifyId ? classifyId : res.data[0].id;
                    if (res.data && res.data.length > 0) {
                        this.getLayoutSize();
                    }
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        // 删除模板分类
        deleteModuleEntity(item) {
            this.$confirm("此操作将永久删除该模板分类，是否继续？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    deleteModuleEntity({ id: item.id }).then((res) => {
                        this.$message.success(res.message);
                        this.modeularFn(this.direction);
                    });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消删除",
                    });
                });
        },
        // 验证方式列表
        verificationFn() {
            let obj = {
                sysId: this.sys_Id,
                type: this.classRadio,
            };
            getValidationList(obj).then((res) => {
                this.validationList = res.data;
                this.loading = false;
            });
        },
        // 删除验证方式
        deleteValidation(item) {
            this.$confirm("此操作将永久删除该验证方式，是否继续？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    removeValidation({ id: item.id }).then((res) => {
                        this.$message.success(res.message);
                        this.verificationFn();
                    });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消删除",
                    });
                });
        },
        // 获取模块列表
        modeularFnTwo(type) {
            this.direction = type;
            let obj = {
                sysId: this.sys_Id,
                type: this.classRadio,
            };
            getClassifyList(obj).then((res) => {
                this.classifyList = res.data;
            });
        },
        radioDirection() {
            this.direction = this.addSizeForm.sizeDirection;
            this.modeularFn(this.direction);
        },
        // 点击添加尺寸
        addSizeFn() {
            this.addSizeVisible = true;
            this.direction = this.addSizeForm.sizeDirection;
            this.modeularFnTwo(this.direction);
        },
        // 确定添加尺寸
        submitSizeForm(formName) {
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    this.sizeLoading = true;
                    let obj = {
                        width: this.addSizeForm.sizeLength,
                        high: this.addSizeForm.sizeWidth,
                        classifyId: this.addSizeForm.classifyId,
                        direction: this.addSizeForm.sizeDirection,
                    };
                    addBrandLayoutSize(obj)
                        .then((res) => {
                            this.$message.success(res.message);
                            this.addSizeVisible = false;
                            this.$refs[formName].resetFields();
                            this.modeularFn(
                                this.direction,
                                this.addSizeForm.classifyId
                            );
                        })
                        .finally(() => {
                            this.sizeLoading = false;
                        });
                } else {
                    return false;
                }
            });
        },
        // 取消
        closeSzie() {
            this.addSizeVisible = false;
            this.$refs["addSizeForm"].resetFields();
        },
        // 尺寸列表
        getLayoutSize(item) {
            let obj = {
                classifyId: this.classifyId,
                direction: this.direction,
            };
            getLayoutSize(obj).then((res) => {
                this.sizeList = res.data;
                if (res.data.length > 0) {
                    this.size.width = res.data[0].width;
                    this.size.height = res.data[0].high;
                    this.sizeId = res.data[0].id;
                    this.getModuleList(res.data[0]);
                } else {
                    this.moduleList = [];
                }
            });
        },
        // 点击模板分类
        moduleEntitChang(item, index) {
            this.classifyId = item.id;
            this.getLayoutSize(item);
        },
        // 点击尺寸
        sizeTabsChange(item) {
            this.sizeId = item.id;
            this.size.width = item.width;
            this.size.height = item.high;
            this.loading = true;
            this.getModuleList(item);
        },
        // 模块列表
        getModuleList(item) {
            // if (item) {
            let obj = {
                classifyId: this.classifyId,
                direction: this.direction,
                high: this.size.height,
                layoutSizeId: this.sizeId,
                width: this.size.width,
                layoutStyleId: this.layoutStyleId,
            };
            getModuleList(obj).then((res) => {
                this.moduleList = res.data;
                this.loading = false;
            });
            // } else {
            //     this.moduleList = [];
            // }
        },
        // 添加模板
        addModule(isEdit, item) {
            this.addModuleVisible = true;
            this.ediuType = isEdit;
            this.styleModuleListFn();
            if (isEdit === false) {
                this.addModuleForm.layoutSizeId = this.sizeId;
                this.addModulTitle = "添加模块";
                this.addModuleForm.descriptive = "";
            } else if (isEdit === true) {
                this.addModulTitle = "编辑模块";
                this.echoModul(item);
            }
        },
        // 回显编辑模板
        echoModul(item) {
            getLayoutModuleDetail({ id: item.id }).then((res) => {
                this.addModuleForm = res.data;
            });
        },
        // 新增模板接口
        createLayoutModule() {
            this.moduleLoading = true;
            let obj = {
                ...this.addModuleForm,
                classifyId: this.classifyId,
                direction: this.direction,
            };
            createLayoutModule(obj)
                .then((res) => {
                    this.$message.success(res.message);
                    this.closeAddModule();
                })
                .finally(() => {
                    this.moduleLoading = false;
                });
        },
        // 编辑模板接口
        updateLayoutModule() {
            this.moduleLoading = true;
            let obj = {
                ...this.addModuleForm,
                direction: this.direction,
            };
            updateLayoutModule(obj)
                .then((res) => {
                    this.$message.success(res.message);
                    this.closeAddModule();
                })
                .finally(() => {
                    this.moduleLoading = false;
                });
        },

        // 点击确定
        addModuleFormSubmit(formName) {
            this.$refs[formName].validate(async (valid) => {
                if (valid) {
                    if (this.addModulTitle === "添加模块") {
                        this.createLayoutModule();
                    } else if (this.addModulTitle === "编辑模块") {
                        this.updateLayoutModule();
                    }
                } else {
                    return false;
                }
            });
        },
        closeAddModule() {
            this.addModuleVisible = false;
            this.$refs["addModuleForm"].resetFields();
            this.getModuleList();
        },
        // 删除模板
        removeModule(item) {
            this.$confirm("此操作将永久删除该模板，是否继续？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    deleteLayoutModule({ id: item.id }).then((res) => {
                        this.$message.success(res.message);
                        this.getModuleList();
                    });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消删除",
                    });
                });
        },
    },
};
</script>

<style lang="scss" scoped>
.menu_content {
    overflow: hidden;
    background-color: #ffffff;
    padding: 20px;
    @import "./allmanagement.scss";
}

.fnButton {
    margin: 18px 0px;
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: space-between;

    span {
        font-size: 16px;
        color: #333;
        cursor: pointer;
        padding-left: 15px;
    }
}

.classList {
    width: 100%;
    display: flex;
    flex-wrap: wrap;

    .classListLi {
        position: relative;
        height: 14px;
        padding: 10px 15px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        color: #333;
        font-size: 14px;
        margin: 5px 10px;
        cursor: pointer;

        .deleteClass {
            display: none;
            position: absolute;
            top: -5px;
            right: -5px;
            color: #1e90ff;
        }
    }

    .classListLi:hover {
        background-color: #ebf0f6;

        .deleteClass {
            display: block;
        }
    }
}

.moduleList {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    cursor: pointer;

    .images {
        background: #dcdfe6;
        position: relative;
    }

    .addImage {
        width: 320px;
        height: 200px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 14px;
        padding: 5px;

        i {
            font-size: 50px;
            color: #ccc;
        }

        // img {
        //     width: 100%;
        // }
        .imageBotton {
            color: #909399;
            width: 100%;
            position: absolute;
            bottom: -20px;
            left: 0px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            span {
                cursor: pointer;
            }
        }
    }
}

.addFile {
    overflow: hidden;
    width: 336px;
    height: 200px;
    border: 1px solid #dcdfe6;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 5px;

    i {
        font-size: 50px;
        color: #ccc;
    }

    // img {
    //     width: 100%;
    // }
}
</style>
<style lang="scss">
.formClass {
    .el-form-item {
        margin-left: 22px;
    }

    .form-footer {
        z-index: 2;
        text-align: center;
        height: 64px;
        position: absolute;
        bottom: 0px;
        background: #fff;
        width: 100%;
        margin: 0px !important;

        .footerForm {
            display: flex;
            width: 100%;
            justify-content: center;
            align-items: center;
            height: 64px;
            border-top: 1px solid #eee;
        }
    }
}
</style>
