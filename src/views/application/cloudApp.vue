<template>
  <div class="cloud-app-container">
    <!-- 二级设置页面 -->
    <cloud-app-setting v-if="showSettingPage" @back="backToMain" />

    <!-- 主页面 -->
    <div v-else class="main-page">
      <div class="page-header">
        <h1 class="page-title">云平台应用管理</h1>
        <div class="header-actions">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索应用名称"
            prefix-icon="el-icon-search"
            class="search-input"
            clearable
          ></el-input>
          <el-button type="primary" class="primaryBlue" @click="goToSetting">
            应用设置
          </el-button>
        </div>
      </div>

      <div class="content-wrapper">
        <!-- 左侧分类导航 -->
        <div class="category-sidebar">
          <div
            class="category-item"
            :class="{ active: activeCategory === '' }"
            @click="selectCategory('')"
          >
            全部应用
          </div>
          <div
            v-for="(category, index) in appData"
            :key="index"
            class="category-item"
            :class="{ active: activeCategory === category.categoryName }"
            @click="selectCategory(category.categoryName)"
          >
            {{ category.categoryName }}
          </div>
        </div>

        <!-- 右侧应用列表 -->
        <div class="app-content">
          <div class="app-grid">
            <div
              v-for="(app, index) in filteredApps"
              :key="index"
              class="app-card"
            >
              <div class="app-icon">
                <img :src="app.logo" :alt="app.name" @error="handleImgError" />
              </div>
              <div class="app-info">
                <div class="app-name">{{ app.name || '应用名称' }}</div>
                <div class="app-tags">
                  <span v-if="app.appTypes && app.appTypes.includes('1')">
                    h5应用
                  </span>
                  <span v-if="app.appTypes && app.appTypes.includes('2')">
                    app应用
                  </span>
                  <span v-if="app.appTypes && app.appTypes.includes('3')">
                    小程序
                  </span>
                  <span v-if="app.types && app.types.includes('1')">/</span>
                  <span v-if="app.types && app.types.includes('1')">
                    web应用
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <el-empty
            v-if="filteredApps.length === 0"
            description="暂无应用"
          ></el-empty>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CloudAppSetting from './cloudAppSetting.vue';

export default {
  name: 'CloudApp',
  components: {
    CloudAppSetting
  },
  data() {
    return {
      showSettingPage: false, // 是否显示设置页面
      searchKeyword: '', // 搜索关键词
      activeCategory: '', // 当前选中的分类
      appData: [
        {
          apps: [
            {
              appTypes: ['2', '1', '3'],
              logo: 'https://alicdn.1d1j.cn/announcement/20220822/3732b7c13be74079b7bec6bf40547c82.',
              name: '电子学生证',
              types: ['1', '2', '5', '6']
            },
            {
              appTypes: ['2', '1', '3'],
              logo: 'https://alicdn.1d1j.cn/announcement/20220822/e7dd87a6fbee49a684634df260e6f26f.',
              name: '打卡',
              types: ['1', '5', '6']
            },
            {
              appTypes: ['1', '2', '3'],
              logo: 'https://cloudcdn.yyide.com/evalActivity/eval/activity/tiyu.png',
              name: '新的评价',
              types: ['1', '5']
            },
            {
              appTypes: ['1', '2', '3'],
              logo: 'https://alicdn.1d1j.cn/announcement/20230327/9cd0087ddbe94b9bb29b0f81817ef047.',
              name: '收集表',
              types: ['2', '5', '6', '1']
            },
            {
              appTypes: ['1', '2', '3'],
              logo: 'https://cloud-yide.oss-cn-shenzhen.aliyuncs.com/vote/voteOptioModule/QWaBxk5eKDKpraNdmM65.png',
              name: '投票活动',
              types: ['1', '2', '5', '6']
            },
            {
              appTypes: ['1', '2', '3'],
              logo: 'https://alicdn.1d1j.cn/announcement/20240401/c1b37b49139647c49840177b1cdbd50d.',
              name: '评价系统',
              types: ['1', '2', '5']
            },
            {
              appTypes: ['1', '2', '3'],
              logo: 'https://alicdn.1d1j.cn/announcement/20240905/901726839f644a3081d2636075e4bd3b.',
              name: '活动报名',
              types: ['1', '2', '5']
            }
          ],
          categoryName: '校园活动',
          schoolId: ''
        },
        {
          apps: [
            {
              appTypes: ['2'],
              logo: 'https://alicdn.1d1j.cn/announcement/20250604/9b255357dac34204a52e0820354796e5.',
              name: '教师发展档案',
              types: ['1']
            },
            {
              appTypes: ['2', '1'],
              logo: 'https://alicdn.1d1j.cn/announcement/20240822/9ab8635f7a724e579a4d86682740a5b3.',
              name: '成绩管理',
              types: ['1', '2', '6', '5']
            }
          ],
          categoryName: '成绩管理',
          schoolId: ''
        }
      ]
    };
  },
  computed: {
    // 过滤后的应用列表
    filteredApps() {
      let apps = [];

      // 根据分类筛选
      if (this.activeCategory === '') {
        // 全部应用
        this.appData.forEach(category => {
          apps = apps.concat(category.apps);
        });
      } else {
        // 指定分类
        const category = this.appData.find(
          item => item.categoryName === this.activeCategory
        );
        if (category) {
          apps = category.apps;
        }
      }

      // 根据搜索关键词过滤
      if (this.searchKeyword) {
        apps = apps.filter(app =>
          app.name && app.name.includes(this.searchKeyword)
        );
      }

      return apps;
    }
  },
  methods: {
    // 选择分类
    selectCategory(categoryName) {
      this.activeCategory = categoryName;
    },
    // 跳转到设置页面
    goToSetting() {
      this.showSettingPage = true;
    },
    // 返回主页面
    backToMain() {
      this.showSettingPage = false;
    },
    // 图片加载失败处理
    handleImgError(e) {
      e.target.src = require('@/assets/images/app.png');
    }
  }
};
</script>

<style lang="scss" scoped>
.cloud-app-container {
  min-height: calc(100vh - 100px);
  background: #f5f7fa;


  // 主页面样式
  .main-page {
    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
      background: #fff;
      padding: 20px 24px;
      border-radius: 4px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);

      .page-title {
        font-size: 20px;
        font-weight: 600;
        color: #303942;
        margin: 0;
        border-left: 4px solid #4877fb;
        padding-left: 12px;
      }

      .header-actions {
        display: flex;
        align-items: center;
        gap: 16px;

        .search-input {
          width: 260px;
        }
      }
    }

    .content-wrapper {
      display: flex;
      gap: 24px;
      min-height: 600px;

      // 左侧分类导航
      .category-sidebar {
        width: 200px;
        background: #fff;
        border-radius: 4px;
        padding: 8px 0;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
        height: fit-content;

        .category-item {
          padding: 12px 24px;
          cursor: pointer;
          color: #606266;
          font-size: 14px;
          transition: all 0.3s;
          position: relative;

          &:hover {
            background: #f5f7fa;
            color: #4877fb;
          }

          &.active {
            background: #ecf2ff;
            color: #4877fb;
            font-weight: 500;

            &::before {
              content: '';
              position: absolute;
              left: 0;
              top: 50%;
              transform: translateY(-50%);
              width: 3px;
              height: 16px;
              background: #4877fb;
              border-radius: 0 2px 2px 0;
            }
          }
        }
      }

      // 右侧应用内容
      .app-content {
        flex: 1;
        background: #fff;
        border-radius: 4px;
        padding: 24px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);

        .app-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
          gap: 24px;

          .app-card {
            display: flex;
            align-items: center;
            padding: 20px;
            border: 1px solid #e8ebef;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;

            &:hover {
              box-shadow: 0 4px 12px rgba(72, 119, 251, 0.15);
              border-color: #4877fb;
              transform: translateY(-2px);
            }

            .app-icon {
              width: 56px;
              height: 56px;
              border-radius: 12px;
              overflow: hidden;
              margin-right: 16px;
              flex-shrink: 0;
              background: #f5f7fa;
              display: flex;
              align-items: center;
              justify-content: center;

              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }

            .app-info {
              flex: 1;
              min-width: 0;

              .app-name {
                font-size: 16px;
                font-weight: 500;
                color: #303942;
                margin-bottom: 8px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }

              .app-tags {
                font-size: 12px;
                color: #909399;
                display: flex;
                flex-wrap: wrap;
                gap: 4px;

                span {
                  &:not(:last-child) {
                    margin-right: 4px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

// 覆盖 element-ui 空状态样式
::v-deep .el-empty {
  padding: 60px 0;
}
</style>

