<template>
    <div class="cloud-app-setting">
        <div class="setting-header">
            <el-button type="text" @click="goBack" class="back-btn">
                <i class="el-icon-arrow-left"></i> 返回
            </el-button>
            <h2>应用设置</h2>
        </div>

        <div class="setting-content">
            <!-- 第一级切换：学段 -->
            <div class="level-tabs">
                <div
                    v-for="item in schoolLevels"
                    :key="item.value"
                    class="level-tab-item"
                    :class="{ active: activeLevel === item.value }"
                    @click="changeLevel(item.value)"
                >
                    {{ item.label }}
                </div>
            </div>

            <!-- 第二级切换：角色 -->
            <div class="role-tabs">
                <div
                    v-for="item in roles"
                    :key="item.code"
                    class="role-tab-item"
                    :class="{ active: activeRole === item.code }"
                    @click="changeRole(item.code)"
                >
                    {{ item.name }}
                </div>
            </div>

            <!-- 应用分配区域 -->
            <div class="app-assign-container">
                <div class="app-assign-left">
                    <div class="section-title">应用/系统：</div>
                    <!-- 搜索区域 -->
                    <div class="search-form">
                        <span class="search-label">应用名称：</span>
                        <el-input
                            v-model="searchKeyword"
                            placeholder="请输入应用名称"
                            class="search-input"
                            clearable
                        ></el-input>
                        <el-button
                            type="primary"
                            class="search-btn"
                            @click="handleSearch"
                        >
                            搜索
                        </el-button>
                        <el-button class="reset-btn" @click="handleReset">
                            重置
                        </el-button>
                    </div>
                    <div class="app-list">
                        <div
                            v-for="(app, index) in filteredAllApps"
                            :key="index"
                            class="app-box"
                        >
                            <img
                                class="app-icon"
                                :src="app.logo"
                                :alt="app.name"
                                @error="handleImgError"
                            />
                            <div class="app-name">{{ app.name }}</div>
                            <div class="deploy-badge" v-if="app.isDist">
                                已配置
                            </div>
                            <!-- 未选中状态显示未选图标 -->
                            <img
                                v-if="!app.isDist"
                                class="action-icon"
                                @click="addApp(app)"
                                src="@/assets/images/pitchIcon.png"
                                alt="未选"
                                title="点击选择"
                            />
                            <!-- 已选中状态显示已选图标 -->
                            <img
                                v-if="app.isDist"
                                class="action-icon"
                                @click="addApp(app)"
                                src="@/assets/images/agreen.png"
                                alt="已选"
                                title="点击取消选择"
                            />
                        </div>
                    </div>
                </div>

                <div class="app-assign-right">
                    <div class="section-title">已配应用：</div>
                    <div class="app-list">
                        <div
                            v-for="(app, index) in assignedApps"
                            :key="index"
                            class="app-box"
                        >
                            <img
                                class="app-icon"
                                :src="app.logo"
                                :alt="app.name"
                                @error="handleImgError"
                            />
                            <div class="app-name">{{ app.name }}</div>
                            <img
                                class="action-icon"
                                @click="removeApp(app)"
                                src="@/assets/images/del.png"
                                alt="删除"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部页脚 -->
        <div class="footer-actions">
            <el-button @click="handleCancel" size="medium">
                取消
            </el-button>
            <el-button
                type="primary"
                @click="handleConfirm"
                size="medium"
                :loading="confirmLoading"
                :disabled="!hasChanges"
            >
                确认
            </el-button>
        </div>
    </div>
</template>

<script>
import { listAppIdentityCode, listAppIdentityApp, updateBySchoolType } from "@/api/apply";
export default {
    name: "CloudAppSetting",
    data() {
        return {
            // 学段分类
            schoolLevels: [
                { label: "K12", value: "1" },
                { label: "大学", value: "2" },
            ],
            activeLevel: "1", // 当前选中的学段
            // 角色列表
            roles: [],
            activeRole: "", // 当前选中的角色
            // 搜索关键词
            searchKeyword: "",
            // 所有应用列表（包含已分配和未分配）
            allApps: [],
            // 原始应用状态（用于取消时恢复）
            originalAppStates: [],
            // 是否有变更
            hasChanges: false,
            // 确认按钮加载状态
            confirmLoading: false,
        };
    },
    computed: {
        // 过滤后的所有应用列表（根据搜索关键词）
        filteredAllApps() {
            if (!this.searchKeyword) {
                return this.allApps;
            }
            return this.allApps.filter(
                (app) => app.name && app.name.includes(this.searchKeyword)
            );
        },
        // 已分配的应用
        assignedApps() {
            return this.allApps.filter((app) => app.isDist);
        },
    },
    created() {
        this.getSchoolListInfo();
    },

    methods: {
        // 返回上一页
        goBack() {
            this.$emit("back");
        },

        // 根据学段从接口获取获取身份列表
        getSchoolListInfo() {
            listAppIdentityCode({
                schoolType: this.activeLevel,
            }).then((res) => {
                this.roles = res.data;
                // 默认选择第一个身份 获取数据
                this.activeRole = res.data[0].code;
                this.changeRole(res.data[0].code);
            });
        },

        // 切换学段
        changeLevel(level) {
            this.activeLevel = level;
            this.getSchoolListInfo();
        },

        // 切换角色
        changeRole(code) {
            this.activeRole = code;
            listAppIdentityApp({
                identityCode: this.activeRole,
                schoolType: this.activeLevel,
            }).then((res) => {
                this.allApps = res.data;
                // 保存原始状态
                this.saveOriginalStates();
                // 重置变更状态
                this.hasChanges = false;
            });
        },

        // 搜索应用
        handleSearch() {
            // 过滤逻辑已在计算属性 filteredAllApps 中实现
            console.log("搜索关键词:", this.searchKeyword);
        },

        // 重置搜索
        handleReset() {
            this.searchKeyword = "";
        },

        // 切换应用选择状态
        addApp(app) {
            // 切换应用的分配状态
            app.isDist = !app.isDist;
            // 检查是否有变更
            this.checkChanges();
        },

        // 保存原始状态
        saveOriginalStates() {
            this.originalAppStates = this.allApps.map(app => ({
                id: app.id,
                isDist: app.isDist
            }));
        },

        // 检查是否有变更
        checkChanges() {
            this.hasChanges = this.allApps.some(app => {
                const original = this.originalAppStates.find(orig => orig.id === app.id);
                return original && original.isDist !== app.isDist;
            });
        },
        // 移除应用
        removeApp(app) {
            this.$confirm("确认删除该系统/应用吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    app.isDist = false;
                    this.$message({
                        type: "success",
                        message: "删除成功!",
                    });
                    // TODO: 调用接口删除数据
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消",
                    });
                });
        },
        // 取消操作
        handleCancel() {
            if (this.hasChanges) {
                this.$confirm("确认取消所有更改吗？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                .then(() => {
                    this.restoreOriginalStates();
                })
                .catch(() => {
                    // 用户取消了取消操作
                });
            } else {
                this.$emit("back");
            }
        },

        // 确认操作
        handleConfirm() {
            if (!this.hasChanges) {
                this.$message.warning("没有任何更改");
                return;
            }

            this.confirmLoading = true;

            // 获取已选中的应用ID列表
            const selectedAppIds = this.allApps
                .filter(app => app.isDist)
                .map(app => app.id);

            const params = {
                appIds: selectedAppIds,
                identityCode: this.activeRole,
                schoolType: this.activeLevel
            };

            updateBySchoolType(params)
                .then((res) => {
                    this.$message.success("应用分配成功");
                    // 更新原始状态
                    this.saveOriginalStates();
                    this.hasChanges = false;
                })
                .catch((error) => {
                    this.$message.error("应用分配失败：" + (error.message || "未知错误"));
                })
                .finally(() => {
                    this.confirmLoading = false;
                });
        },

        // 恢复原始状态
        restoreOriginalStates() {
            this.allApps.forEach(app => {
                const original = this.originalAppStates.find(orig => orig.id === app.id);
                if (original) {
                    app.isDist = original.isDist;
                }
            });
            this.hasChanges = false;
        },

        // 图片加载失败处理
        handleImgError(e) {
            e.target.src = require("@/assets/images/app.png");
        },
    },
};
</script>

<style lang="scss" scoped>
.cloud-app-setting {
    background: #fff;
    border-radius: 4px;
    min-height: calc(100vh - 150px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
    position: relative;
    padding-bottom: 80px; // 为底部页脚留出空间

    .setting-header {
        height: 62px;
        border-bottom: 1px solid #d9d9d9;
        line-height: 62px;
        padding: 0 24px;
        display: flex;
        align-items: center;
        gap: 12px;

        .back-btn {
            font-size: 16px;
            font-weight: 600;
            color: rgba(0, 0, 0, 0.85);
            padding: 0;

            &:hover {
                color: #4877fb;
            }

            i {
                padding-right: 8px;
            }
        }

        h2 {
            font-size: 16px;
            font-weight: 600;
            color: rgba(0, 0, 0, 0.85);
            margin: 0;
        }
    }

    .setting-content {
        padding: 24px;

        // 第一级切换（学段）
        .level-tabs {
            display: flex;
            gap: 24px;
            margin-bottom: 24px;
            border-bottom: 2px solid #f0f0f0;

            .level-tab-item {
                padding: 12px 24px;
                font-size: 16px;
                font-weight: 500;
                color: #666;
                cursor: pointer;
                position: relative;
                transition: all 0.3s;

                &:hover {
                    color: #4877fb;
                }

                &.active {
                    color: #4877fb;

                    &::after {
                        content: "";
                        position: absolute;
                        bottom: -2px;
                        left: 0;
                        right: 0;
                        height: 2px;
                        background: #4877fb;
                    }
                }
            }
        }

        // 第二级切换（角色）
        .role-tabs {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin-bottom: 24px;

            .role-tab-item {
                padding: 8px 20px;
                font-size: 14px;
                color: #666;
                background: #f5f7fa;
                border: 1px solid #e8ebef;
                border-radius: 4px;
                cursor: pointer;
                transition: all 0.3s;

                &:hover {
                    color: #4877fb;
                    border-color: #4877fb;
                    background: #ecf2ff;
                }

                &.active {
                    color: #fff;
                    background: #4877fb;
                    border-color: #4877fb;
                }
            }
        }

        // 应用分配容器
        .app-assign-container {
            display: flex;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            min-height: 500px;

            .app-assign-left,
            .app-assign-right {
                flex: 1;
                padding: 16px;
            }

            .app-assign-left {
                border-right: 1px solid #d9d9d9;
            }

            .section-title {
                padding-bottom: 16px;
                font-size: 14px;
                font-weight: 500;
                color: rgba(0, 0, 0, 0.85);
            }

            // 搜索表单
            .search-form {
                display: flex;
                align-items: center;
                margin-bottom: 20px;
                padding: 16px;
                background: #fafafa;
                border-radius: 4px;

                .search-label {
                    font-size: 14px;
                    color: rgba(0, 0, 0, 0.85);
                    white-space: nowrap;
                    margin-right: 8px;
                }

                .search-input {
                    width: 200px;
                    margin-right: 12px;

                    ::v-deep .el-input__inner {
                        height: 32px;
                        line-height: 32px;
                    }
                }

                .search-btn,
                .reset-btn {
                    height: 32px;
                    padding: 0 16px;
                    font-size: 14px;
                }

                .search-btn {
                    margin-right: 8px;
                }
            }

            .app-list {
                display: flex;
                flex-wrap: wrap;

                .app-box {
                    padding-top: 15px;
                    width: 100px;
                    height: 100px;
                    margin-right: 20px;
                    margin-bottom: 20px;
                    border: 1px dashed #d9d9d9;
                    text-align: center;
                    position: relative;
                    transition: all 0.3s;
                    cursor: pointer;

                    &:hover {
                        border-color: #4877fb;
                        box-shadow: 0 2px 8px rgba(72, 119, 251, 0.15);
                    }

                    .app-icon {
                        height: 46px;
                        width: 46px;
                        object-fit: cover;
                    }

                    .app-name {
                        padding-top: 12px;
                        font-size: 14px;
                        font-weight: 500;
                        color: #262626;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        padding-left: 4px;
                        padding-right: 4px;
                    }

                    .action-icon {
                        position: absolute;
                        cursor: pointer;
                        top: 0;
                        right: 0;
                        height: 20px;
                        width: 20px;
                        transition: all 0.3s;
                        &:hover {
                            transform: scale(1.1);
                        }
                    }

                    .deploy-badge {
                        background: #2e8aff;
                        font-weight: 400;
                        color: #ffffff;
                        font-size: 12px;
                        padding: 2px 6px;
                        border-radius: 0 0 0 5px;
                        position: absolute;
                        top: 0;
                        right: 0;
                    }
                }
            }
        }
    }

    // 底部页脚样式
    .footer-actions {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 60px;
        background: #fff;
        border-top: 1px solid #e8e8e8;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 16px;
        z-index: 1000;
        box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.08);

        .el-button {
            min-width: 100px;
            height: 36px;
        }
    }
}
</style>
