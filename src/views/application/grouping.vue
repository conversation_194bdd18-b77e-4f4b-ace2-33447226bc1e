<!--
 * @Descripttion: 应用分组
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-04-01 14:59:54
 * @LastEditors: jingrou
 * @LastEditTime: 2022-07-21 10:36:35
-->
<template>
    <div class="card_warp">
        <!-- <el-card shadow="never" class="card_warp"> -->
        <div class="search_warp">
            <el-form :inline="true" :model="searchform" ref="searchform">
                <el-form-item label="分组名称：">
                    <el-input
                        v-model="searchform.name"
                        placeholder="请输入分组名称"
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button
                        icon="el-icon-search"
                        type="primary"
                        @click="handleSearch"
                        >查 询</el-button
                    >
                    <el-button @click="handleSearchReset">重 置</el-button>
                </el-form-item>
            </el-form>
        </div>
        <div class="btn_warp">
            <span>应用分组</span>
            <div>
                <el-button
                    type="primary"
                    icon="el-icon-plus"
                    size="small"
                    @click="handleAddEdit(false)"
                    >新增分组</el-button
                >
            </div>
        </div>
        <div v-loading="loading">
            <div class="content_warp">
                <el-table
                    :data="list"
                    style="width: 100%"
                    border
                    :header-cell-style="{
                        background: '#fafafa',
                        color: '#5b5d61',
                    }"
                >
                    <el-table-column
                        type="index"
                        align="center"
                        width="50px"
                        label="序号"
                    ></el-table-column>
                    <el-table-column
                        prop="name"
                        label="分组名称"
                        show-overflow-tooltip
                    ></el-table-column>
                    <el-table-column
                        prop="sort"
                        label="排序"
                        width="100"
                        align="center"
                    ></el-table-column>
                    <el-table-column
                        label="操作"
                        align="right"
                        width="180"
                        fixed="right"
                    >
                        <template slot-scope="scope">
                            <el-link
                                @click="handleAddEdit(true, scope.row)"
                                icon="el-icon-edit"
                                type="primary"
                                size="small"
                                >编辑</el-link
                            >
                            <el-link
                                style="margin-left: 10px"
                                type="danger"
                                size="small"
                                @click="handleRemoveHitn(scope.row)"
                            >
                                删除
                                <i class="el-icon-delete" />
                            </el-link>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <el-pagination
                style="margin-top: 16px; text-align: right"
                @current-change="handleCurrentChange"
                background
                :current-page.sync="pagination.current"
                :page-size="pagination.size"
                layout="total, prev, pager, next"
                :total="pagination.total"
            >
                <span>总共 {{ pagination.total }} 条记录</span>
            </el-pagination>
        </div>
        <!--  -->
        <el-dialog
            title="确定删除该应用分组吗？"
            :visible.sync="dialogVisible"
            width="400px"
            :modal-append-to-body="false"
        >
            <span>删除后将无法使用该应用分组</span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="handleRemove"
                    >确 定</el-button
                >
            </span>
        </el-dialog>
        <!--  -->
        <addEdit-grouping
            ref="addEditGrouping"
            :isEdit="isEdit"
            @updata="getApplygroupList"
        />
        <!-- </el-card> -->
    </div>
</template>
<script>
import AddEditGrouping from "./addEditGrouping";
import { getApplygroupList, deleteApplyGroup } from "@/api/apply.js";
export default {
    data() {
        return {
            list: [],
            pagination: {
                size: 10,
                current: 1,
                total: 0,
            },
            searchform: {
                naem: "",
            },
            dialogVisible: false,
            isEdit: false,
            loading: false,
            id: null,
        };
    },
    components: { AddEditGrouping },
    created() {
        this.getApplygroupList();
    },
    methods: {
        getApplygroupList(isSearch) {
            this.loading = true;
            const params = {
                size: this.pagination.size,
                current: this.pagination.current,
            };

            if (isSearch === "search") {
                params["name"] = this.searchform.name;
            }
            getApplygroupList(params)
                .then(({ code, data, message }) => {
                    if (code === 200) {
                        const { records, total, size, current } = data;

                        this.pagination.total = total;
                        this.pagination.size = size;
                        this.pagination.current = current;
                        this.list = records;
                    } else {
                        this.$message.error(message);
                    }
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        handleRemoveHitn(row) {
            this.id = row.id;
            this.dialogVisible = true;
        },
        // 删除
        handleRemove() {
            deleteApplyGroup(this.id).then(({ code, msg }) => {
                if (code === 200) {
                    this.$message.success(msg);
                    this.dialogVisible = false;
                    this.getApplygroupList();
                } else {
                    this.dialogVisible = false;
                    this.$message.error(msg);
                }
            });
        },
        // 添加编辑
        handleAddEdit(isEdit, row) {
            this.isEdit = isEdit;
            this.$refs.addEditGrouping.open();
            isEdit && this.$refs.addEditGrouping.getApplygroupInfo(row.id);
        },
        // 搜索
        handleSearch() {
            this.pagination.current = 1;
            this.getApplygroupList("search");
        },
        // 重置
        handleSearchReset() {
            this.$refs.searchform.resetFields();
            this.searchform.name = null;
            this.pagination.current = 1;
            this.getApplygroupList();
        },
        handleCurrentChange(current) {
            this.pagination.current = current;
            this.getApplygroupList("search");
        },
    },
};
</script>

<style lang="scss" scoped>
.card_warp {
    background-color: #fff;
    padding: 20px;
    .content_warp {
        margin: 16px 0;
    }
    .btn_warp {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;
        padding-top: 16px;
        border-top: 1px solid #ebeef5;
        span {
            margin: 10px;
        }
        div {
            display: flex;
        }
    }
}
</style>
