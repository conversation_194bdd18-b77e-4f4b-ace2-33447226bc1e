<!--
 * @Descripttion: 应用添加，编辑
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-04-01 14:49:45
 * @LastEditTime: 2022-07-21 10:36:42
-->

<template>
    <el-card shadow="never" class="card_warp">
        <div class="search_warp">
            <el-form :inline="true" :model="searchform" ref="searchform">
                <el-form-item label="应用名称：">
                    <el-input
                        v-model="searchform.name"
                        placeholder="应用名称"
                    ></el-input>
                </el-form-item>
                <el-form-item label="分组：">
                    <el-select
                        v-model="searchform.groupId"
                        placeholder="请选择"
                    >
                        <el-option
                            :label="item.name"
                            :value="item.id"
                            v-for="(item, index) in groupList"
                            :key="'group' + index"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="应用提供商：">
                    <el-select
                        placeholder="请选择"
                        v-model="searchform.supplierId"
                    >
                        <el-option
                            v-for="item in supplierList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="应用状态：">
                    <el-select
                        v-model="searchform.sign"
                        placeholder="请选择"
                        style="width: 120px"
                    >
                        <el-option label="未上架" value="0"></el-option>
                        <el-option label="上架" value="1"></el-option>
                        <el-option label="下架" value="2"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item style="margin-left: 56px">
                    <el-button type="primary" @click="handleSearch"
                        >查 询</el-button
                    >
                    <el-button @click="handleSearchReset">重 置</el-button>
                </el-form-item>
            </el-form>
        </div>
        <div class="btn_warp" v-if="showBtn">
            <span>应用列表</span>
            <div>
                <el-button
                    type="primary"
                    icon="el-icon-plus"
                    size="small"
                    @click="handleAddEdit(false)"
                    >新增应用</el-button
                >
                <el-button
                    v-if="showBtn"
                    size="small"
                    style="margin-left: 16px"
                    @click="$router.push('/application/grouping')"
                    >应用分组管理</el-button
                >
            </div>
        </div>
        <div class="content_warp" v-loading="loading">
            <div
                v-if="!showBtn"
                style="
                    padding: 12px 24px;
                    background-color: #e6f7ff;
                    border: 1px solid #91d5ff;
                "
            >
                已选择 {{ multipleSelection.length }} 个项
            </div>
            <el-table
                :data="list"
                style="width: 100%"
                @selection-change="handleSelectionChange"
                ref="table"
                border
                :header-cell-style="{ background: '#fafafa', color: '#5b5d61' }"
            >
                <el-table-column
                    type="selection"
                    width="55"
                    v-if="!showBtn"
                    align="center"
                ></el-table-column>
                <el-table-column
                    type="index"
                    width="50"
                    label="序号"
                    align="center"
                ></el-table-column>
                <el-table-column
                    prop="id"
                    label="应用编号"
                    show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                    prop="name"
                    label="应用名称"
                    show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                    prop="supplierName"
                    label="提供商"
                    show-overflow-tooltip
                ></el-table-column>
                <!-- <el-table-column prop="introduction" label="应用介绍" show-overflow-tooltip></el-table-column> -->
                <el-table-column prop="type" label="类型" align="center">
                    <template slot-scope="scope">
                        <span>
                            {{
                                scope.row.type == "0"
                                    ? "H5应用"
                                    : scope.row.type == "1"
                                    ? "原生应用"
                                    : ""
                            }}
                        </span>
                    </template>
                </el-table-column>
                <!-- <el-table-column prop="path" label="App路径" show-overflow-tooltip></el-table-column>
				<el-table-column prop="backstagePath" label="云平台路径" show-overflow-tooltip></el-table-column>-->
                <el-table-column
                    prop="showType"
                    label="应用可见"
                    show-overflow-tooltip
                >
                    <template slot-scope="scope">
                        <div v-for="item in scope.row.showTypes" :key="item">
                            {{
                                item == "0"
                                    ? "APP端显示"
                                    : item == "1"
                                    ? "云平台显示"
                                    : ""
                            }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="sign" label="应用状态" align="center">
                    <template slot-scope="scope">
                        {{
                            scope.row.sign == "0"
                                ? "未上架"
                                : scope.row.sign == "1"
                                ? "上架"
                                : scope.row.sign == "2"
                                ? "下架"
                                : ""
                        }}
                    </template>
                </el-table-column>
                <el-table-column prop="img" label="图标" align="center">
                    <template slot-scope="scope">
                        <img
                            :src="scope.row.img"
                            alt
                            style="width: auto; height: 35px"
                        />
                    </template>
                </el-table-column>
                <el-table-column
                    prop="groupName"
                    label="应用分组"
                    align="center"
                ></el-table-column>
                <el-table-column prop="owner" label="权限" align="center">
                    <template slot-scope="scope">
                        <div v-for="item in scope.row.num" :key="item">
                            {{
                                item == "0" ? "家长" : item == "4" ? "老师" : ""
                            }}
                        </div>
                    </template>
                </el-table-column>

                <!-- <el-table-column prop="sort" label="排序" align="center"></el-table-column> -->
                <el-table-column
                    prop="documentLink"
                    label="接口链接"
                    align="center"
                ></el-table-column>
                <el-table-column
                    label="操作"
                    width="220"
                    v-if="showBtn"
                    align="right"
                    fixed="right"
                >
                    <template slot-scope="scope">
                        <el-link
                            type="primary"
                            icon="el-icon-edit"
                            @click="handleAddEdit(true, scope.row)"
                            >编辑</el-link
                        >
                        <el-dropdown type="text" size="small">
                            <el-button
                                type="text"
                                :style="
                                    scope.row.sign == '0'
                                        ? 'color: #74bf74'
                                        : scope.row.sign == '1'
                                        ? 'color: blue'
                                        : scope.row.sign == '2'
                                        ? 'color: red'
                                        : ''
                                "
                                class="el-dropdown-link"
                            >
                                {{
                                    scope.row.sign == "0"
                                        ? "未上架"
                                        : scope.row.sign == "1"
                                        ? "上架"
                                        : scope.row.sign == "2"
                                        ? "下架"
                                        : ""
                                }}
                            </el-button>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item
                                    v-for="item in dropdownList"
                                    :key="item.value"
                                    :command="item.value"
                                    @click.native="
                                        dropdownClick(item.value, scope.row)
                                    "
                                    >{{ item.name }}</el-dropdown-item
                                >
                            </el-dropdown-menu>
                        </el-dropdown>

                        <el-button
                            type="text"
                            size="small"
                            style="margin-left: 10px"
                            @click="versionEditing(scope.row)"
                            >版本编辑</el-button
                        >

                        <el-link
                            type="danger"
                            size="small"
                            @click="handleRemoveHitn(scope.row)"
                            style="margin-left: 10px"
                        >
                            删除
                            <i class="el-icon-delete" />
                        </el-link>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <el-pagination
            style="text-align: right; margin-top: 16px"
            @current-change="handleCurrentChange"
            background
            :current-page.sync="pagination.current"
            :page-size="pagination.size"
            layout="total, prev, pager, next"
            :total="pagination.total"
        >
            <span>总共 {{ pagination.total }} 条记录</span>
        </el-pagination>
        <!--  -->
        <addEdit-apply
            ref="addEditApply"
            :isEdit="isEdit"
            @updata="getApplyList"
            :groupList="groupList"
        />
        <!-- 版本编辑弹窗 -->
        <el-dialog
            title="版本编辑"
            :visible.sync="versionEditingVisible"
            @close="close"
            width="480px"
            :modal-append-to-body="false"
        >
            <el-form
                :model="versionEditingForm"
                ref="versionEditingRef"
                :rules="versionEditingRules"
                label-width="120px"
            >
                <el-form-item label="版本号:" prop="number">
                    <el-input
                        v-model.trim="versionEditingForm.number"
                        autocomplete="off"
                        placeholder="请输入版本号"
                        style="width: 220px"
                    ></el-input>
                </el-form-item>
                <el-form-item label="更新时间:" prop="updateTime">
                    <el-date-picker
                        v-model.trim="versionEditingForm.updateTime"
                        type="date"
                        placeholder="选择日期"
                        value-format="yyyy-MM-dd"
                        value="yyyy-MM-dd"
                    ></el-date-picker>
                </el-form-item>

                <el-form-item label="版本描述:" class="textarea" prop="content">
                    <el-input
                        type="textarea"
                        :rows="6"
                        placeholder="请输入内容"
                        v-model.trim="versionEditingForm.content"
                    ></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="versionEditingVisible = false"
                    >取 消</el-button
                >
                <el-button type="primary" @click="sureVersionEditing"
                    >确 定</el-button
                >
            </div>
        </el-dialog>
    </el-card>
</template>
<script>
import AddEditApply from "./addEditApply";
import {
    getApplyList,
    deleteApply,
    getApplygroupList,
    versionUpdate,
} from "@/api/apply.js";
import { supplierName, updateApplicationStatus } from "@/api/supplier.js";
export default {
    components: { AddEditApply },
    props: {
        showBtn: {
            type: Boolean,
            default: true,
        },
        schoolId: {
            default: null,
        },
    },
    data() {
        return {
            editionEditId: "",
            versionEditingVisible: false,
            versionEditingForm: {
                number: "",
                updateTime: "",
                content: "",
            },
            supplierList: [],
            dropdownList: [
                { value: "0", name: "未上架" },
                { value: "1", name: "上架" },
                { value: "2", name: "下架" },
            ],
            list: [],
            groupList: [],
            pagination: {
                size: 10,
                current: 1,
                total: 0,
            },
            searchform: {
                naem: "",
                groupId: null,
                supplierId: "",
                sign: "",
            },
            dialogVisible: false,
            isEdit: false,
            loading: false,
            id: null,
            multipleSelection: [],
            versionEditingRules: {
                number: [
                    { required: true, message: "请输入", trigger: "blur" },
                ],
                updateTime: [
                    { required: true, message: "请选择", trigger: "change" },
                ],
                content: [
                    { required: true, message: "请输入", trigger: "blur" },
                ],
            },
        };
    },
    created() {
        this.getApplyList();
        this.getApplygroupList();
        this.supplierNameId();
    },
    methods: {
        // 修改状态
        updateApplicationStatus(dropState, command) {
            const obj = {
                id: command.id,
                sign: dropState,
            };

            updateApplicationStatus(obj).then(({ code, msg, data }) => {
                if (code == 200) {
                    this.getApplyList();
                }
            });
        },
        handleCommand(command) {},
        dropdownClick(dropState, command) {
            this.updateApplicationStatus(dropState, command);
        },
        // 供应商
        supplierNameId() {
            supplierName().then(({ data, code, msg }) => {
                if (code == 200) {
                    this.supplierList = data;
                } else {
                    this.$message.error(msg);
                }
            });
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
            this.$emit("selectionChange", val);
        },
        getApplygroupList() {
            const params = {
                size: 999,
                current: 1,
            };

            getApplygroupList(params).then(({ code, data, message }) => {
                if (code == 200) {
                    const { records } = data;

                    this.groupList = records;
                } else {
                    this.$message.error(message);
                }
            });
        },
        getApplyList(isSearch) {
            this.loading = true;
            const params = {
                size: this.pagination.size,
                current: this.pagination.current,
                schoolId: this.schoolId,
            };

            if (isSearch === "search") {
                params["name"] = this.searchform.name;
                params["groupId"] = this.searchform.groupId;
                params["supplierId"] = this.searchform.supplierId;
                params["sign"] = this.searchform.sign;
            }
            getApplyList(params)
                .then(({ code, data, message }) => {
                    if (code === 200) {
                        const { records, total, size, current } = data;

                        this.pagination.total = total;
                        this.pagination.size = size;
                        this.pagination.current = current;
                        this.list = records;
                    } else {
                        this.$message.error(message);
                    }
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        handleRemoveHitn(row) {
            this.id = row.id;
            this.dialogVisible = true;
            this.$confirm("删除后将无法使用该应用", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    deleteApply(this.id).then(({ code, msg }) => {
                        if (code === 200) {
                            this.$message.success(msg);
                            this.dialogVisible = false;
                            this.getApplyList();
                        }
                    });
                })
                .catch(() => {});
        },
        // 添加编辑
        handleAddEdit(isEdit, row) {
            this.isEdit = isEdit;
            this.$refs.addEditApply.open();
            isEdit && this.$refs.addEditApply.getApplyInfo(row.id);
        },
        // 搜索
        handleSearch() {
            this.pagination.current = 1;
            this.getApplyList("search");
        },
        // 重置
        handleSearchReset() {
            this.$refs.searchform.resetFields();
            this.searchform = {};
            this.pagination.current = 1;
            this.getApplyList();
        },
        handleCurrentChange(current) {
            this.pagination.current = current;
            this.getApplyList("search");
        },
        clearSelection() {
            this.$refs.table.clearSelection();
        },
        // 版本编辑弹窗
        versionEditing(row) {
            this.editionEditId = row.id;
            this.versionEditingVisible = true;
        },
        // 确定版本编辑
        sureVersionEditing() {
            this.$refs.versionEditingRef.validate((valid) => {
                if (valid) {
                    this.versionEditingList();
                }
            });
        },
        // 版本编辑接口
        versionEditingList() {
            const obj = {
                number: this.versionEditingForm.number,
                updateTime: this.versionEditingForm.updateTime,
                content: this.versionEditingForm.content,
                applicationId: this.editionEditId,
            };

            versionUpdate(obj).then((res) => {
                if (res.code == 200) {
                    this.$message.success(res.msg);
                    this.getApplyList();
                    this.versionEditingVisible = false;
                    this.versionEditingForm.content = "";
                }
            });
        },
        // 关闭弹窗的回调
        close() {
            this.$refs.versionEditingRef.resetFields();
            this.versionEditingVisible = false;
        },
    },
};
</script>

<style lang="scss" scoped>
.card_warp {
    .el-dropdown-link {
        margin-left: 9px;
    }
    .mmm {
        display: flex !important;
        margin-left: 20px;
    }
    .btn_warp {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 0px;
        border-top: 1px solid #ebeef5;
    }
    .el-dropdown {
        width: 50px;
        text-align: center;
    }
}
</style>
