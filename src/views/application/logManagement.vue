<!-- 日志管理 -->
<template>
    <el-card shadow="never" class="log_management">
        <div class="manage_text">
            <!-- <ul class="log_ma_left">
				<li
					v-for="(item, index) in appList"
					:key="index"
					@click="onAppSelect(index, item.id)"
					:class="[appIndex == index ? 'on_clik_r' : '']"
				>{{ item.name }}</li>
			</ul>-->
            <!-- <div class="menu_left_content"> -->
            <!-- 添加应用 -->
            <!-- <NewMenu
          style="margin-top:20px;"
          @childByValue="childByValue"
        />
			</div>-->
            <!-- <div style="height: 90%; width:100%" > -->
            <div class="date_screen">
                <el-date-picker
                    v-model="dateScreen"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :picker-options="pickerOptions"
                    :on-click="onDateScreen()"
                />
                <el-button type="primary" @click.native="getLogManagement"
                    >查询</el-button
                >
                <el-button size="medium" @click="searchResetForm"
                    >重置</el-button
                >
            </div>
            <div
                style="
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 16px 0;
                    border-top: 1px solid #ebeef5;
                "
            >
                <span>日志管理</span>
                <div></div>
            </div>
            <div style="width: 100%" v-loading="loading">
                <el-table
                    :data="tableData"
                    max-height="600"
                    border
                    style="width: 100%"
                    :header-cell-style="{
                        background: '#fafafa',
                        color: '#5b5d61',
                    }"
                >
                    <el-table-column label="序号" width="50">
                        <template slot-scope="scope">
                            <span v-text="scope.$index + 1" />
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="versionName"
                        align="center"
                        label="版本名称"
                    />
                    <el-table-column
                        prop="versionNumber"
                        align="center"
                        label="版本号"
                    />
                    <el-table-column
                        prop="equipmentSerialNumber"
                        label="设备号"
                        align="center"
                    />
                    <el-table-column prop="createdDateTime" align="center">
                        <template slot="header">
                            <span class="el-dropdown-link">上传时间</span>
                        </template>
                        <template slot-scope="{ row: { createdDateTime } }">
                            {{ createdDateTime }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="status" width="200" align="center">
                        <template slot="header">
                            <el-dropdown>
                                <span class="el-dropdown-link">
                                    处理状态
                                    <i
                                        class="el-icon-arrow-down el-icon--right"
                                    />
                                </span>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item
                                        v-for="(item, index) in filters"
                                        :key="index"
                                        @click.native="onScreen(item.value)"
                                        >{{ item.text }}</el-dropdown-item
                                    >
                                </el-dropdown-menu>
                            </el-dropdown>
                        </template>
                        <template slot-scope="{ row: { status } }">
                            <span v-if="+status === 0" style="color: #67c23a"
                                >未处理</span
                            >
                            <span v-else>已处理</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="操作"
                        prop="status"
                        align="right"
                        fixed="right"
                    >
                        <template slot-scope="scope">
                            <el-link
                                type="primary"
                                icon="el-icon-view"
                                size="small"
                                @click="onDownload(scope.row)"
                                >查看</el-link
                            >

                            <el-link
                                style="margin-left: 10px"
                                type="primary"
                                icon="el-icon-edit"
                                v-if="scope.row.status == 0"
                                size="small"
                                @click="onDispose(scope.row)"
                                >处理</el-link
                            >
                            <el-link
                                type="primary"
                                style="margin-left: 10px"
                                icon="el-icon-document"
                                v-else
                                size="small"
                                @click="openText(scope.row)"
                                >处理详情</el-link
                            >
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="pagin_ation">
                <el-pagination
                    background
                    layout="prev, pager, next"
                    :total="total"
                    :page-size="10"
                    @current-change="handleCurrentChange"
                />
            </div>
            <!-- </div> -->
        </div>

        <!-- 处理描述弹窗 -->
        <el-dialog
            title="处理描述"
            :visible.sync="dialogVisible"
            width="30%"
            :modal-append-to-body="false"
        >
            <el-input
                v-model="textarea"
                type="textarea"
                :rows="5"
                placeholder="请输入处理描述内容"
                maxlength="450"
                show-word-limit
            />
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="onDialogVisible"
                    >确 定</el-button
                >
            </span>
        </el-dialog>
        <!-- 处理详情 -->
        <el-dialog
            :modal-append-to-body="false"
            title="处理详情"
            :visible.sync="dialogVisible2"
            width="30%"
        >
            <div>{{ particularsText }}</div>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="dialogVisible2 = false"
                    >确 定</el-button
                >
            </span>
        </el-dialog>
    </el-card>
</template>
<script>
import NewMenu from "@/components/NewMenu";
import menu from "@/utils/menu";
import {
    versionManagementQuetyVersionLogList,
    versionManagementQuetyUpdateVersionLog,
} from "@/api/apply.js";
import { applicationListApi } from "@/api/application.js";
export default {
    components: {
        // NewMenu,
    },
    data() {
        return {
            loading: false,
            sys_id: "",
            addText: "",
            appList: [],
            appIndex: 0,
            appId: "",
            tableData: [],
            total: 0, //总页数
            current: 1, //当前页数
            dialogVisible: false,
            dialogVisible2: false,
            content: "",
            textarea: "",
            particularsText: "",
            textUrl: "",
            filters: [
                { text: "全部", value: "" },
                { text: "未处理", value: 0 },
                { text: "已处理", value: 1 },
            ],
            screen: "",
            dateScreen: "",
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                },
            },
            startTime: "",
            endTime: "",
            // sys_id: "",
        };
    },
    created() {
        const vm = this;

        this.initFn();
        // 用$on事件来接收参数
        menu.$on("text", (data) => {
            vm.addText = data.text;
            vm.sys_id = data.id;
        });
    },
    mounted() {
        this.getAppLicatList();
    },
    methods: {
        searchResetForm() {
            (this.dateScreen = ""), this.getAppLicatList();
        },
        initFn() {
            const thata = this;

            applicationListApi()
                .then((res) => {
                    if (res.code == "200") {
                        this.sys_id = res.data[0].id;
                        thata.$store.commit(
                            "VersionSchool/VERSION_NAVIGATION_LIST",
                            res.data
                        );
                    }
                })
                .catch();
        },
        //应用列表
        getAppLicatList() {
            this.loading = true;
            applicationListApi().then((res) => {
                if (res.code == 200) {
                    this.appList = res.data;
                    this.appId = res.data[0].id;
                    this.getLogManagement(res.data[0].id);
                }
            });
        },
        onAppSelect(index, id) {
            this.appIndex = index;
            this.appId = id;
            this.getLogManagement(id);
        },
        //时间选择
        onDateScreen() {
            if (this.dateScreen) {
                this.startTime = this.dateFormat(this.dateScreen[0]); //startDate
                this.endTime = this.dateFormat(this.dateScreen[1]); //endDate
            }
        },
        dateFormat(dateData) {
            let date = new Date(dateData),
                y = date.getFullYear(),
                m = date.getMonth() + 1;

            m = m < 10 ? "0" + m : m;
            let d = date.getDate();

            d = d < 10 ? "0" + d : d;
            const time = y + "-" + m + "-" + d;

            return time;
        },
        onScreen(index) {
            this.screen = index;
            this.getLogManagement();
        },
        //版本日志列表
        getLogManagement(id) {
            const params = {
                brandApplicationId: this.appId,
                current: this.current,
                size: 10,
                status: this.screen,
            };

            if (this.dateScreen) {
                params.endDate = this.endTime;
                params.startDate = this.startTime;
            }
            versionManagementQuetyVersionLogList(params).then((res) => {
                if (res.code == 200) {
                    this.tableData = res.data.records;
                    this.total = res.data.total;
                    this.loading = false;
                }
            });
        },
        //分页
        handleCurrentChange(val) {
            this.current = val;
            this.getLogManagement();
        },
        //处理
        onDispose(text) {
            this.dialogVisible = true;
            this.content = text;
        },
        onDialogVisible() {
            if (!this.textarea) {
                this.$message({
                    showClose: true,
                    message: "请输入处理内容",
                    type: "error",
                });
                return;
            }
            const param = {
                id: this.content.id,
                status: 1,
                content: this.textarea,
            };

            versionManagementQuetyUpdateVersionLog(param).then((res) => {
                if (res.code == 200) {
                    this.$message({
                        showClose: true,
                        message: "处理成功",
                        type: "success",
                    });
                    this.dialogVisible = false;
                    this.textarea = "";
                    this.getLogManagement();
                }
            });
        },
        //处理详情
        openText(text) {
            this.particularsText = text.content;
            this.dialogVisible2 = true;
        },
        //下载
        onDownload(text) {
            window.open(text.fileUrl);
        },
        childByValue(item) {
            this.addText = item.text;
            this.sys_id = item.id;
            this.getLogManagement();
        },
    },
};
</script>
<style lang="scss" scoped>
.log_management {
    .menu_left_content {
        float: left;
        width: 12%;
        height: calc(90vh);
        text-align: center;
        background: #fff;
        box-shadow: 1px 2px 4px 0px rgba(214, 214, 214, 0.17);
        border-radius: 4px;
        margin: 0px 2%;
    }
    .date_screen {
        margin: 20px 0px;
        .el-button--medium {
            margin-left: 20px;
        }
    }
    .manage_text {
        .log_ma_left {
            width: 10%;
            margin: 0 10px;
            background: #fff;
            min-height: 600px;
            padding: 10px 5px;
            li {
                height: 40px;
                line-height: 40px;
                text-align: center;
                cursor: pointer;
                border: 1px solid #eee;
                border-radius: 6px;
                margin-top: 10px;
            }
            .on_clik_r {
                background: #3a8ee6;
                color: #fff;
                box-shadow: darkgrey 1px 5px 20px 5px;
                border: none;
            }
        }
    }
    .pagin_ation {
        margin: 20px;
        text-align: right;
    }
}
</style>
