<template>
    <!-- 菜单管理 -->
    <div class="menu_content">
        <!-- <div class="menu_left_content"> -->
        <!-- 添加应用 -->
        <!-- <el-button type="primary" class="add_application" @click="addApplicationFn()">
				<i class="el-icon-plus" style="margin-right:10px;"></i>添加应用
		</el-button>-->
        <!-- <NewMenu style="margin-top: 20px" @childByValue="childByValue" />
		</div>-->
        <div class="menu_right_content">
            <!-- <div class="application_name">{{ addText }}</div> -->
            <div class="btn_warp">
                <span>菜单管理</span>
                <!-- 添加菜单 -->
                <div>
                    <el-button
                        type="primary"
                        class="add_menu"
                        @click="addMenuFn()"
                    >
                        <i
                            class="el-icon-plus"
                            style="margin-right: 10px"
                        />添加菜单
                    </el-button>
                </div>
            </div>
            <div class="menu_table" v-loading="loading">
                <el-table
                    :data="menuTableData"
                    height="600"
                    border
                    style="width: 95%; margin: 0px auto"
                    class="elTable"
                    :header-cell-style="{
                        background: '#fafafa',
                        color: '#5b5d61',
                    }"
                >
                    <el-table-column prop label width="50">
                        <template>
                            <div>
                                <img
                                    style="
                                        margin: 0px auto;
                                        width: 12px;
                                        display: block;
                                    "
                                    src="@/assets/images/yid_btn.png"
                                    alt
                                />
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        v-for="(item, index) in col"
                        :key="`col_${index}`"
                        :prop="dropCol[index].prop"
                        :label="item.label"
                    />
                    <el-table-column
                        prop
                        label="操作"
                        width="200"
                        align="right"
                        fixed="right"
                    >
                        <template slot-scope="scope">
                            <div>
                                <el-link
                                    type="primary"
                                    icon="el-icon-edit"
                                    class="ediu_style"
                                    @click="ediuFn(scope.row)"
                                    >修改</el-link
                                >
                                <el-button
                                    type="text"
                                    icon="el-icon-setting"
                                    class="delete_style"
                                    style="margin-left: -10px"
                                    :style="
                                        scope.row.delInd == '0'
                                            ? 'color:#fc5a5a;'
                                            : 'color:#2c8aff;'
                                    "
                                    @click="deleteFn(scope.row)"
                                >
                                    {{
                                        scope.row.delInd == "0"
                                            ? "禁用"
                                            : "启动"
                                    }}
                                </el-button>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <!-- 添加菜单与修改菜单 -->
        <el-drawer
            :title="menuTitle"
            :visible.sync="menuDrawer"
            :before-close="handleClose"
            :wrapper-closable="false"
            class="menu_drawer"
        >
            <el-form
                ref="ruleForm"
                :model="ruleForm"
                :rules="rules"
                :label-position="labelPosition"
                label-width="100px"
                class="menu_ruleForm"
            >
                <div class="drawer_title">
                    <span>一级菜单</span>
                </div>
                <el-form-item label="一级菜单:" prop="firstlevelmenu">
                    <el-input
                        v-model.trim="ruleForm.firstlevelmenu"
                        placeholder="请输入菜单名称"
                    />
                </el-form-item>
                <el-form-item label="路径:" prop="firstroute">
                    <el-input
                        v-model.trim="ruleForm.firstroute"
                        placeholder="请输入路径"
                    />
                </el-form-item>
                <div class="drawer_title" style="margin-top: 25px">
                    <span>二级菜单</span>
                    <i
                        class="el-icon-circle-plus add_btn"
                        @click="addDrawerFn()"
                    />
                </div>
                <div class="second_newlist">
                    <draggable
                        v-model="ruleForm.ruleList"
                        chosen-class="chosen"
                        force-fallback="true"
                        group="people"
                        animation="1000"
                        @start="onStart"
                        @end="onEnd"
                    >
                        <transition-group :key="1">
                            <div
                                v-for="(item, index) in ruleForm.ruleList"
                                :key="'1' + index"
                                class="second_list"
                            >
                                <div style="float: left">
                                    <el-form-item
                                        label="二级菜单:"
                                        :prop="`ruleList[${index}].name`"
                                        :rules="{
                                            required: true,
                                            message: '请输入菜单名称',
                                            trigger: 'blur',
                                        }"
                                    >
                                        <el-input
                                            v-model.trim="item.name"
                                            placeholder="请输入菜单名称"
                                            style="width: 360px; float: left"
                                        />
                                    </el-form-item>
                                    <el-form-item
                                        label="路径:"
                                        :prop="`ruleList[${index}].component`"
                                        :rules="{
                                            required: true,
                                            message: '请输入路径',
                                            trigger: 'blur',
                                        }"
                                    >
                                        <el-input
                                            v-model.trim="item.component"
                                            style="width: 360px"
                                            placeholder="请输入路径"
                                        />
                                    </el-form-item>
                                    <el-form-item
                                        label="标识:"
                                        :prop="`ruleList[${index}].permission`"
                                        :rules="{
                                            required: true,
                                            message: '请输入标识',
                                            trigger: 'blur',
                                        }"
                                    >
                                        <el-input
                                            v-model.trim="item.permission"
                                            style="width: 360px"
                                            placeholder="请输入标识"
                                        />
                                    </el-form-item>
                                </div>
                                <div class="second_right_btn">
                                    <img
                                        src="@/assets/images/yid_btn.png"
                                        style="
                                            margin-bottom: 10px;
                                            margin-left: 7px;
                                        "
                                        alt
                                    />
                                    <div
                                        :style="
                                            item.delInd == '0'
                                                ? 'color:#fc5a5a;font-size:14px;'
                                                : 'font-size:14px;'
                                        "
                                        @click="deleteFn(item, index)"
                                    >
                                        {{
                                            item.delInd == "0" ? "禁用" : "启动"
                                        }}
                                    </div>
                                    <!-- <i class="el-icon-remove remove_btn" v-if="index!=0" @click="removeFn(index)"></i> -->
                                </div>
                            </div>
                        </transition-group>
                    </draggable>
                </div>

                <el-form-item class="yd-form-footer">
                    <el-button
                        :loading="buttonLoading"
                        size="medium"
                        type="primary"
                        @click="submitForm('ruleForm')"
                        >确定</el-button
                    >
                </el-form-item>
            </el-form>
        </el-drawer>
        <!-- 添加应用 -->
        <el-drawer
            title="添加应用"
            :visible.sync="applicationDrawer"
            :before-close="addhandleClose"
            :wrapper-closable="false"
            class="menu_drawer"
        >
            <el-form
                ref="applicationForm"
                :model="applicationForm"
                :rules="apprules"
                :label-position="labelPosition"
                label-width="100px"
                class="menu_ruleForm"
            >
                <div class="drawer_title">
                    <span>应用信息</span>
                </div>
                <el-form-item label="应用名称:" prop="applicationName">
                    <el-input
                        v-model.trim="applicationForm.applicationName"
                        placeholder="请输入应用名称"
                    />
                </el-form-item>
                <div class="drawer_title" style="margin-top: 25px">
                    <span>应用图标</span>
                </div>
                <el-form-item label="应用图标:" prop="applicationBtn" />
                <el-form-item class="yd-form-footer">
                    <el-button
                        size="medium"
                        type="primary"
                        @click="applicationSubmitForm('applicationForm')"
                        >确定</el-button
                    >
                </el-form-item>
            </el-form>
        </el-drawer>
    </div>
</template>

<script>
import NewMenu from "@/components/NewMenu";
import menu from "@/utils/menu";
import Sortable from "sortablejs";
import draggable from "vuedraggable";
import {
    menuListApi,
    menuSaveApi,
    menuDeleteApi,
    menuSortMenuApi,
    applicationListApi,
} from "@/api/application.js";
import pinyin from "js-pinyin";
export default {
    name: "Menus",
    components: {
        // NewMenu,
        draggable,
    },
    inject: ["reload"],
    data() {
        return {
            buttonLoading: false,
            loading: false,
            sys_id: "",
            addText: "",
            menuDrawer: false,
            menuTitle: "",
            ruleForm: {
                firstlevelmenu: "",
                imageUrl: "",
                firstroute: "",
                ruleList: [
                    { name: "", component: "", delInd: "0", permission: "" },
                ],
            },
            rules: {
                firstlevelmenu: [
                    {
                        required: true,
                        message: "请输入一级菜单",
                        trigger: "blur",
                    },
                ],
                firstroute: [
                    {
                        required: true,
                        message: "请输入路径",
                        trigger: "blur",
                    },
                ],
            },
            labelPosition: "right",
            menuTableData: [],
            col: [
                {
                    label: "一级菜单",
                    prop: "name",
                },
                {
                    label: "二级菜单",
                    prop: "childName",
                },
            ],
            dropCol: [
                {
                    label: "一级菜单",
                    prop: "name",
                },
                {
                    label: "二级菜单",
                    prop: "childName",
                },
            ],
            //添加应用
            applicationDrawer: false,
            applicationForm: {
                applicationName: "",
                applicationBtn: "",
            },
            apprules: {
                applicationName: [
                    {
                        required: true,
                        message: "请输入应用名称",
                        trigger: "blur",
                    },
                ],
                applicationBtn: [
                    {
                        required: true,
                        message: "请上传应用图标",
                        trigger: "blur",
                    },
                ],
            },
            // sys_id: "",
            icon: "",
            iconChild: "",
            childIndex: "",
            newId: "",
            faIndex: "",
            newdelInd: "",
            eiduValue: "0",
        };
    },
    created() {
        this.loading = true;
        const vm = this;
        // 用$on事件来接收参数

        menu.$on("text", (data) => {
            vm.addText = data.name;
            vm.sys_id = data.id;
            this.menuListFn();
        });
    },
    mounted() {
        this.rowDrop();
        this.initFn();
    },
    methods: {
        // szj start
        initFn() {
            const thata = this;

            applicationListApi()
                .then((res) => {
                    if (res.code == "200") {
                        this.sys_id = res.data[0].id;
                        thata.$store.commit(
                            "VersionSchool/VERSION_NAVIGATION_LIST",
                            res.data
                        );
                        this.menuListFn();
                    }
                })
                .catch();
        },
        //行拖拽
        rowDrop() {
            const tbody = document.querySelector(
                    ".el-table__body-wrapper tbody"
                ),
                _this = this;

            Sortable.create(tbody, {
                onEnd({ newIndex, oldIndex }) {
                    const currRow = _this.menuTableData[oldIndex],
                        obj = {
                            id: currRow.id,
                            sort: newIndex,
                            applicationId: _this.sys_id,
                            pid: currRow.pid,
                        };

                    menuSortMenuApi(obj)
                        .then((res) => {
                            if (res.code == "200") {
                                //_this.menuListFn();
                            }
                        })
                        .catch();
                },
            });
        },
        addMenuFn() {
            this.menuDrawer = true;
            this.menuTitle = "添加菜单";
            this.ruleForm.ruleList = [
                { name: "", component: "", delInd: "0", permission: "" },
            ];
            this.ruleForm.firstlevelmenu = "";
            this.ruleForm.firstroute = "";
        },
        handleClose(done) {
            this.addcelFn();
            this.menuListFn();
            done();
        },
        addhandleClose(done) {
            done();
            this.$refs["applicationForm"].resetFields();
            this.applicationForm.applicationName = "";
            this.applicationForm.applicationBtn = "";
        },
        addcelFn() {
            this.ruleForm.firstlevelmenu = "";
            this.ruleForm.firstroute = "";
            this.ruleForm.imageUrl = "";
            this.ruleForm.ruleList = [];
        },
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    let result;

                    pinyin.setOptions({ checkPolyphone: false, charCase: 0 });
                    const char = pinyin.getCamelChars(
                        this.ruleForm.firstlevelmenu
                    );

                    result = this.ruleForm.ruleList[0].component.substring(
                        this.ruleForm.ruleList[0].component.indexOf("/") + 1,
                        this.find(this.ruleForm.ruleList[0].component, "/", 1)
                    );
                    if (this.eiduValue == "0") {
                        this.buttonLoading = true;
                        var obj = {
                            delInd: "0",
                            type: "M",
                            id: this.newId,
                            name: this.ruleForm.firstlevelmenu,
                            icon: char,
                            path: this.ruleForm.firstroute,
                            permission: "",
                            component: "layout",
                            hidden: false,
                            applicationId: this.sys_id,
                            children: [],
                            sort: this.menuTableData.length + 1,
                        };

                        for (
                            let index = 0;
                            index < this.ruleForm.ruleList.length;
                            index++
                        ) {
                            var charChild = pinyin.getCamelChars(
                                    this.ruleForm.ruleList[index].name
                                ),
                                arrObj = {
                                    delInd: "0",
                                    pid: "",
                                    type: "C",
                                    name: this.ruleForm.ruleList[index].name,
                                    icon: charChild,
                                    path: this.ruleForm.ruleList[index]
                                        .component,
                                    permission:
                                        this.ruleForm.ruleList[index]
                                            .permission,
                                    component:
                                        this.ruleForm.ruleList[index].component,
                                    hidden: false,
                                    applicationId: this.sys_id,
                                    children: [],
                                    sort: index,
                                    id: this.ruleForm.ruleList[index].id,
                                };

                            obj.children.push(arrObj);
                        }
                    } else if (this.eiduValue == "1") {
                        this.buttonLoading = true;
                        var obj = {
                            delInd: this.newdelInd,
                            type: "M",
                            id: this.newId,
                            name: this.ruleForm.firstlevelmenu,
                            icon: char,
                            path: this.ruleForm.firstroute,
                            permission: "",
                            component: "layout",
                            hidden: false,
                            applicationId: this.sys_id,
                            children: [],
                            sort: this.faIndex,
                        };

                        for (
                            let index = 0;
                            index < this.ruleForm.ruleList.length;
                            index++
                        ) {
                            var charChild = pinyin.getCamelChars(
                                    this.ruleForm.ruleList[index].name
                                ),
                                arrObj = {
                                    delInd: this.ruleForm.ruleList[index]
                                        .delInd,
                                    pid: "",
                                    type: "C",
                                    name: this.ruleForm.ruleList[index].name,
                                    icon: charChild,
                                    path: this.ruleForm.ruleList[index]
                                        .component,
                                    permission:
                                        this.ruleForm.ruleList[index]
                                            .permission,
                                    component:
                                        this.ruleForm.ruleList[index].component,
                                    hidden: false,
                                    applicationId: this.sys_id,
                                    children: [],
                                    sort: index,
                                    id: this.ruleForm.ruleList[index].id,
                                };

                            obj.children.push(arrObj);
                        }
                    }

                    menuSaveApi(obj)
                        .then((res) => {
                            if (res.code == "200") {
                                this.menuDrawer = false;
                                this.menuListFn();
                                this.addcelFn();
                                this.faIndex = "";
                                this.buttonLoading = false;
                            } else {
                                this.$message.error(res.msg);
                            }
                        })
                        .catch();
                } else {
                    return false;
                }
            });
        },
        find(str, cha, num) {
            let x = str.indexOf(cha);

            for (let i = 0; i < num; i++) {
                x = str.indexOf(cha, x + 1);
            }
            return x;
        },
        removeFn(index) {
            this.ruleForm.ruleList.splice(index, 1);
        },
        addDrawerFn(index) {
            const obj = {
                name: "",
                component: "",
                delInd: "0",
                permission: "",
            };

            this.ruleForm.ruleList.push(obj);
        },
        childByValue(item) {
            this.addText = item.name;
            this.sys_id = item.id;
        },
        deleteFn(item, i) {
            let title;

            if (item.delInd == "0") {
                title = "此操作将禁用该菜单，是否继续？";
            } else {
                title = "此操作将重启该菜单，是否继续？";
            }
            this.$confirm(title, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    menuDeleteApi(item.id)
                        .then((res) => {
                            if (res.code == "200") {
                                this.menuListFn();
                                if (item.delInd == "1") {
                                    item.delInd = "0";
                                } else {
                                    item.delInd = "1";
                                }

                                this.$message({
                                    type: "success",
                                    message: "操作成功!",
                                });
                            } else {
                                this.$message({
                                    type: "error",
                                    message: "操作失败!",
                                });
                            }
                        })
                        .catch();
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消",
                    });
                });
        },
        ediuFn(item) {
            this.menuDrawer = true;
            this.menuTitle = "修改菜单";
            this.ruleForm.firstlevelmenu = item.name;
            if (item.component == "layout") {
                this.ruleForm.firstroute = item.path;
            }
            this.newId = item.id;
            this.ruleForm.ruleList = item.children;
            this.faIndex = item.sort;
            this.newdelInd = item.delInd;
            this.eiduValue = "1";
        },
        //添加应用
        addApplicationFn() {
            this.applicationDrawer = true;
        },
        applicationSubmitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    alert("submit!2");
                } else {
                    return false;
                }
            });
        },
        //开始拖拽事件
        onStart(arr) {},
        //拖拽结束事件
        onEnd(arr) {},
        menuListFn() {
            this.loading = true;
            const obj = {
                applicationId: this.sys_id,
            };

            menuListApi(obj)
                .then((res) => {
                    if (res.code == "200") {
                        this.menuTableData = res.data;
                        this.loading = false;
                        for (
                            let index = 0;
                            index < this.menuTableData.length;
                            index++
                        ) {
                            const childList =
                                    this.menuTableData[index].children,
                                childName = [];

                            for (
                                let index = 0;
                                index < childList.length;
                                index++
                            ) {
                                childName.push(childList[index].name);
                            }
                            this.$set(
                                this.menuTableData[index],
                                "childName",
                                childName.join("、")
                            );
                        }
                    }
                })
                .catch((err) => {});
        },
    },
};
</script>

<style lang="scss">
.menu_content {
    height: 800px;
    overflow: hidden;
    background-color: #ffffff;
    padding: 20px;
    @import "./allmanagement.scss";
}
.menu_content .menu_right_content {
    width: 100%;
    -webkit-box-shadow: none;
    box-shadow: none;
    // padding: 20px;
}
.btn_warp {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    // padding-top: 16px;
    // border-top: 1px solid #ebeef5;
    span {
        margin: 10px;
    }
    div {
        display: flex;
    }
}
.elTable {
    width: 100% !important;
    margin: none !important;
}
</style>
