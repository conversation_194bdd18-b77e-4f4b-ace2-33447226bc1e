<template>
    <div id="app">
        <div
            slot="content"
            style="width: 600px; padding: 0 40px; margin: 0 auto"
        >
            <div
                class="checkbox-table"
                v-for="(item, indexkey) in menu"
                :key="item.id"
            >
                <template>
                    <el-checkbox
                        class="check1"
                        style="font-weight: 600; margin-bottom: 15px"
                        v-model="menusIds"
                        :label="item.id"
                        @change="handleCheck(1, indexkey)"
                    >
                        {{ item.name }}
                    </el-checkbox>
                    <div style="margin-bottom: 20px">
                        <div
                            v-for="list in item.children"
                            class="line-check"
                            :key="list.id"
                            style="
                                display: inline-block;
                                margin-left: 20px;
                                margin-bottom: 20px;
                            "
                        >
                            <el-checkbox
                                class="check2"
                                @change="handleCheck(2, indexkey)"
                                v-model="menusIds"
                                :label="list.id"
                            >
                                {{ list.name }}
                            </el-checkbox>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            menu: [
                {
                    id: 1,
                    name: "课程管理",
                    ename: "course-manage",
                    children: [
                        {
                            id: 2,
                            name: "课程模板",
                        },
                        {
                            id: 3,
                            name: "课程列表",
                        },
                        {
                            id: 4,
                            name: "已归档课程",
                        },
                    ],
                },
                {
                    id: 5,
                    name: "内容",
                    ename: "content",
                    children: [
                        {
                            id: 6,
                            name: "广告位",
                        },
                        {
                            id: 7,
                            name: "关于我们",
                        },
                    ],
                },
                {
                    id: 8,
                    name: "用户",
                    ename: "user",
                    children: [
                        {
                            id: 9,
                            name: "用户管理",
                        },
                        {
                            id: 10,
                            name: "主办方",
                        },
                        {
                            id: 11,
                            name: "组织者",
                        },
                    ],
                },
            ],
            menusIds: [],
        };
    },
    methods: {
        // 处理选择事件
        handleCheck(type, a = 0) {
            // 多选框
            let self = this;
            if (type == 2) {
                // 二级菜单点击
                let index = 0;
                self.menu[a].children.map((item) => {
                    if (self.menusIds.indexOf(item.id) > -1) {
                        index += 1;
                    }
                });
                if (index > 0) {
                    if (self.menusIds.indexOf(self.menu[a].id) < 0) {
                        self.menusIds.push(self.menu[a].id);
                    }
                } else {
                    if (self.menusIds.indexOf(self.menu[a].id) > 0) {
                        self.menusIds.splice(
                            self.menusIds.indexOf(self.menu[a].id),
                            1
                        );
                    }
                }
            } else {
                if (self.menusIds.indexOf(self.menu[a].id) > -1) {
                    self.menu[a].children.map((item) => {
                        if (self.menusIds.findIndex((n) => n == item.id) < 0) {
                            self.menusIds.push(item.id);
                        }
                    });
                } else {
                    self.menu[a].children.map((item) => {
                        if (self.menusIds.findIndex((n) => n == item.id) > -1) {
                            self.menusIds.splice(
                                self.menusIds.findIndex((n) => n == item.id),
                                1
                            );
                        }
                    });
                }
            }
        },
        //编辑某个菜单
        // handleEdit(obj) {
        //     let self = this;
        //     self.obj = obj;
        //     let array = obj.menuIds ? obj.menuIds.split(',') : []
        //     let arr = [];
        //     array.map(item => {
        //         arr.push(parseInt(item));
        //     })
        //     this.menusIds = arr;
        // },
        // 获取所有菜单列表
        // async _allMenuApi() {
        //     let res = await fetchRights();
        //     if (res.code == 200) {
        //         this.menu = res.data.allMenus;
        //     }
        // }
    },
};
</script>

<style lang="scss" scoped></style>
