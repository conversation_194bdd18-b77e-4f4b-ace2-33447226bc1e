<!--
 * @Descripttion: 
 * @version: 1.0.0
 * @Author: jingrou
 * @Date: 2022-06-14 18:30:43
 * @LastEditors: jingrou
 * @LastEditTime: 2023-03-06 18:02:23
-->
<template>
    <div class="open_manage">
        <el-form :inline="true" :model="searchform" ref="searchform" @submit.native.prevent>
            <el-form-item label="学校名称：">
                <el-input v-model.trim="searchform.name" placeholder="请输入学校名称"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button icon="el-icon-search" type="primary" @click="onFindData">查 询</el-button>
                <el-button @click="searchResetForm">重 置</el-button>
            </el-form-item>
        </el-form>
        <div class="open_button">
            <span>开通管理</span>
            <div class="btn"></div>
        </div>
        <div class="from_list">
            <el-table :data="dataList" border style="width: 100%"
                :header-cell-style="{ background: '#fafafa', color: '#5b5d61' }">
                <el-table-column label="序号" width="60" align="center" type="index" />
                <el-table-column prop="name" label="学校名称" />
                <el-table-column prop="id" label="学校编号" width="200px" />
                <el-table-column label="操作" align="right" fixed="right">
                    <template slot-scope="scope">
                        <el-button icon="el-icon-notebook-2" type="text" style="color: #ccc">平台菜单</el-button>
                        <el-link style="margin-left: 10px" icon="el-icon-crop" type="primary" v-auth="'manage.openingManagement.size'"
                            @click="onOpenModule(scope.$index, scope.row)">模块尺寸</el-link>
                        <el-link style="margin-left: 10px" icon="el-icon-document-checked" type="danger"
                            v-auth="'manage.openingManagement.verify'" @click="onOpenVerify(scope.$index, scope.row)">验证方式</el-link>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!-- 分页 -->
        <div class="paginationBlock">
            <el-pagination style="margin-top: 10px" :current-page="pagination.pageNo" :page-sizes="[10, 20, 30, 40]"
                :page-size="pagination.pageSize" background layout="total, sizes, prev, pager, next, jumper"
                :total="pagination.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </div>
        <!-- 模块尺寸 -->
        <el-drawer :title="'开通模块尺寸 - ' + schoolName" :visible.sync="moduleDrawer" :before-close="closeSize">
            <div class="layoutStyleClass">
                <div class="layoutStyleTitle"><span>*</span> 模块风格：</div>
                <el-select style="width: 300px" v-model="layoutStyleId" placeholder="请选择" @change="layoutStyleFn">
                    <el-option v-for="item, index in styleList" :key="item.id + index" :label="item.styleName"
                        :value="item.id">
                    </el-option>
                </el-select>
            </div>
            <div class="drawer_content">
                <el-tree class="tree" ref="treeS" :data="modelSizeList" show-checkbox default-expand-all node-key="id"
                    highlight-current :default-checked-keys="defaultKeysSize" :props="defaultProps" />
                <div class="drawer_footer">
                    <el-button @click="closeSize">取消</el-button>
                    <el-button @click="onSizeConfirm" type="primary" :loading="modelSizeloading">确定</el-button>
                </div>
            </div>
        </el-drawer>
        <!-- 验证方式 -->
        <el-drawer :visible.sync="verifyDrawer" :title="'开通验证方式 - ' + schoolName">
            <div class="drawer_content">
                <el-tree class="tree" ref="treeV" :data="terraceVerify" show-checkbox default-expand-all node-key="id"
                    highlight-current :props="defaultProps" :default-checked-keys="defaultKeyVerify" />
                <div class="drawer_footer">
                    <el-button @click="verifyDrawer = false">取消</el-button>
                    <el-button @click="onVerifyConfirm" type="primary" :loading="verifyloading">确定</el-button>
                </div>
            </div>
        </el-drawer>
    </div>
</template>

<script>
import {
    openSchoolList,
    openSchoolModule,
    createModuleInfo,
    openSchoolValidation,
    createValidationInfo,
} from "@/api/opening.js";
import { styleModuleList } from "@/api/application.js";

export default {
    data() {
        return {
            layoutStyleId: '',
            searchform: {
                name: "",
            },
            dataList: [],
            pagination: {
                pageSize: 10,
                pageNo: 1,
                total: 0,
            },
            schoolName: "",
            schoolId: "",
            defaultProps: {
                children: "children",
                label: "name",
            },
            // 模块尺寸
            moduleDrawer: false,
            modelSizeloading: false,
            modelSizeList: [],
            defaultKeysSize: [],
            parentLevelList: [],
            sizeParameterId: [],
            // 验证方式
            verifyDrawer: false,
            verifyloading: false,
            terraceVerify: [],
            defaultKeyVerify: [],
            styleList: [],
            verifyParameterId: [],
        };
    },
    created() {
        this.styleModuleListFn()
        this.openSchoolList();
    },
    methods: {
        // 模板风格列表
        styleModuleListFn() {
            styleModuleList({ styleName: '' }).then((res) => {
                this.layoutStyleId = res.data[0].id
                this.styleList = res.data
            })
        },
        // 开通列表
        openSchoolList() {
            let obj = {
                ...this.pagination,
                ...this.searchform,
            };
            openSchoolList(obj).then((res) => {
                this.dataList = res.data.list;
                this.pagination.pageNo = res.data.pageNo;
                this.pagination.pageSize = res.data.pageSize;
                this.pagination.total = res.data.total;
            });
        },
        //模块尺寸列表=---------------------------
        onOpenModule(index, item) {
            this.moduleDrawer = true;
            this.index = index;
            this.schoolName = item.name;
            this.getModuleList(item.id);
        },
        layoutStyleFn() {
            this.getModuleList(this.schoolId)
        },
        getModuleList(id) {
            this.schoolId = id;
            const obj = { schoolId: id, layoutStyleId: this.layoutStyleId };
            openSchoolModule(obj).then((res) => {
                let { hbList, sbList } = res.data;
                if (hbList.length) {
                    hbList.map((item, index) => {
                        this.parentLevelList.push(item);
                        item.brandLayoutModuleList.map((i, p) => {
                            if (i.selected) {
                                this.defaultKeysSize.push(i.id);
                            }
                            this.$set(i, "name", i.width + "*" + i.high + " " + (i.descriptive || ""));
                            return i;
                        });
                        this.$set(item, "children", item.brandLayoutModuleList);
                        return item;
                    });
                }
                if (sbList.length) {
                    sbList.map((item, index) => {
                        this.parentLevelList.push(item);
                        item.brandLayoutModuleList.map((i, p) => {
                            if (i.selected) {
                                this.defaultKeysSize.push(i.id);
                            }
                            this.$set(
                                i,
                                "name",
                                i.width +
                                "*" +
                                i.high +
                                "   " +
                                (i.descriptive || "")
                            );
                            return i;
                        });
                        this.$set(item, "children", item.brandLayoutModuleList);
                        return item;
                    });
                }
                this.modelSizeList = [
                    {
                        name: "电子班牌",
                        children: [
                            { name: "横版", children: res.data.hbList },
                            {
                                name: "竖版",
                                children: res.data.sbList,
                            },
                        ],
                    },
                ];
            });
        },
        closeSize() {
            this.moduleDrawer = false
            this.sizeParameterId = [];
            this.defaultKeysSize = [];
            this.parentLevelList = [];
        },
        // 确定
        onSizeConfirm() {
            this.modelSizeloading = true;
            const res = this.$refs.treeS.getCheckedNodes(); //子级
            res.forEach((item) => {
                this.sizeParameterId.push(item);
            });
            const father = [];
            this.parentLevelList.map((item, index) => {
                this.sizeParameterId.map((u, w) => {
                    const ect = {};
                    ect.id = u.id;
                    ect.direction = u.direction;
                    ect.images = u.images;
                    ect.width = u.width;
                    ect.high = u.high;
                    ect.tag = u.tag;
                    ect.descriptive = u.descriptive;
                    ect.secPageBackTime =  u.secPageBackTime || '';
                    ect.url = u.url || '';
                    ect.layoutStyleId = u.layoutStyleId;
                    if (u.classifyId == item.id) {
                        const obj = {};
                        obj.id = item.id;
                        obj.name = item.name;
                        obj.sysId = item.sysId;
                        obj.type = item.type;
                        obj.schoolId = this.schoolId;
                        obj.layoutModuleList = [ect];
                        father.push(obj);
                    }
                });
            });
            for (let i = 0; i < father.length; i++) {
                if (father[i].id == undefined) {
                    father.splice(i, 1);
                }
                for (let j = i + 1; j < father.length; j++) {
                    if (father[i].id === father[j].id) {
                        const k = father[j].layoutModuleList[0];

                        if (
                            father[i].layoutModuleList.find((c) => c.id == k.id)
                        ) {
                        } else {
                            father[i].layoutModuleList.push(k);
                        }
                        father.splice(j, 1);
                        j--;
                    }
                }
            }
            const params = { list: father, schoolId: this.schoolId, type: 1, layoutStyleId: this.layoutStyleId };
            createModuleInfo(params)
                .then((res) => {
                    this.$message.success(res.message);
                    this.moduleDrawer = false;
                })
                .finally(() => {
                    this.modelSizeloading = false
                    this.sizeParameterId = [];
                    this.defaultKeysSize = [];
                    this.parentLevelList = [];
                });
        },
        // 验证方式-------------------------------------
        onOpenVerify(index, item) {
            this.verifyDrawer = true;
            this.schoolId = item.id;
            this.schoolName = item.name;
            openSchoolValidation({ schoolId: item.id }).then((res) => {
                if (res.data.hbList.length) {
                    res.data.hbList.map((item, index) => {
                        if (item.selected) {
                            this.defaultKeyVerify.push(item.id);
                        }
                    });
                }
                this.terraceVerify = [
                    { name: "电子班牌", children: res.data.hbList },
                ];
            });
        },
        onVerifyConfirm() {
            this.verifyloading = true;
            const res = this.$refs.treeV.getCheckedNodes();
            res.forEach((item) => {
                this.verifyParameterId.push(item);
            });
            const list = [];
            if (this.verifyParameterId.length) {
                this.verifyParameterId.map((item, index) => {
                    const obj = {};
                    obj.id = item.id;
                    (obj.name = item.name),
                    (obj.type = item.type),
                    (obj.sysId = item.sysId),
                    (obj.direction = item.direction),
                    // ( obj.secPageBackTime = item.secPageBackTime || '')
                    // (obj.url = item.url || '')
                    (obj.schoolId = this.schoolId),
                    list.push(obj);
                });
            }
            const params = {
                list,
                schoolId: this.schoolId,
                type: 0, // 0 为验证方式  1 为平台菜单
            };
            createValidationInfo(params)
                .then((res) => {
                    this.$message.success(res.message);
                    this.verifyParameterId = [];
                    this.defaultKeyVerify = [];
                    this.verifyDrawer = false;
                })
                .finally(() => {
                    this.verifyloading = false;
                });
        },
        // 分页
        handleSizeChange(val) {
            this.pagination.pageNo = 1;
            this.pagination.pageSize = val;
            this.openSchoolList();
        },
        handleCurrentChange(val) {
            this.pagination.pageNo = val;
            this.openSchoolList();
        },
        // 搜索
        onFindData() {
            this.pagination.pageNo = 1;
            this.openSchoolList();
        },
        // 重置
        searchResetForm() {
            this.searchform.name = "";
            this.openSchoolList();
        },
    },
};
</script>

<style lang="scss" scoped>
.layoutStyleClass {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin: 0px 0px 12px 12px;

    .layoutStyleTitle {
        width: 100px;

        span {
            color: red;
        }
    }
}

.drawer_footer {
    width: 100%;
    background: #fff;
    height: 70px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-top: 1px solid #eee;
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 2;
}

.drawer_content {
    margin: 12px 0px 90px 12px;
}

.open_manage {
    background-color: #fff;
    padding: 20px;

    .open_button {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;
        padding-top: 16px;
        border-top: 1px solid #ebeef5;

        span {
            margin: 10px;
        }
    }

    .open_paging {
        // margin: 20px;
        // text-align: right;
        // width: 90%;
        margin-top: 16px;
        text-align: right;
    }

    .open_manage_name_seek {
        display: flex;
        height: 58px;

        .school_name {
            line-height: 36px;
        }

        .el-input--medium {
            width: 200px;
        }

        .el-input__inner {
            width: 200px;
        }

        margin-left: 10px;
    }

    .from_list {
        margin: 20px 10px;
    }

    .el-drawer__wrapper .el-drawer__open .el-drawer .el-drawer__body.el-drawer__body .right_open_drawer {
        .open_title {
            font-size: 20px;
        }

        .el-drawer__body {
            position: relative;
            height: 92%;
        }

        .open_school_name {
            .xiala_open {
                margin-top: 20px;
                height: 600px;
                overflow: auto;
            }
        }

        .confirm {
            width: 74px;
            margin: 50px auto;
        }
    }
}
</style>
