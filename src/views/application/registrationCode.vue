<template>
    <!-- 注册码管理 -->
    <div class="registration_code_manage">
        <div class="registration_code_seek">
            <div class="left_sdh_y">
                <div class="school_name">
                    <p>注册码:</p>
                    <el-input
                        v-model="input_code"
                        placeholder="请输入注册码"
                    ></el-input>
                </div>
                <div class="school_name">
                    <p>设备序列号:</p>
                    <el-input
                        v-model="input_name"
                        placeholder="请输入设备序列号"
                    ></el-input>
                </div>
                <div class="school_name">
                    <p>学校名称:</p>
                    <el-input
                        v-model="officeName"
                        placeholder="请输入学校名称"
                    ></el-input>
                </div>
                <div class="school_name">
                    <p>设备类型：</p>
                    <el-select
                        clear="el_select_class"
                        v-model="type"
                        style="width: 120px"
                    >
                        <el-option label="全部" value />
                        <el-option label="班牌" value="1" />
                        <el-option label="闸机" value="2" />
                    </el-select>
                </div>
                <div style="margin-left: 30px">
                    <el-button
                        type="primary"
                        icon="el-icon-search"
                        @click="onFindData"
                        >查询</el-button
                    >
                    <el-button size="medium" @click="searchResetForm"
                        >重置</el-button
                    >
                </div>
            </div>
            <div class="right_export">
                <span>注册码管理</span>
                <div>
                    <el-button
                        type="primary"
                        icon="el-icon-plus"
                        @click="onDrawer()"
                        >新增</el-button
                    >
                    <el-button
                        type="primary"
                        plain
                        icon="el-icon-bottom"
                        @click="onExportText"
                        >导出</el-button
                    >
                </div>
            </div>
        </div>
        <div class="from_list" v-loading="loading">
            <el-table
                :data="tableData"
                border
                :header-cell-style="{ background: '#fafafa', color: '#5b5d61' }"
                :cell-style="cellStyleFun"
                style="width: 100%"
            >
                <el-table-column label="序号" width="50">
                    <template slot-scope="scope">
                        <span v-text="scope.$index + 1"></span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="registrationCode"
                    label="注册码"
                    align="center"
                ></el-table-column>
                <el-table-column
                    prop="officeName"
                    label="学校名称"
                    align="center"
                ></el-table-column>
                <el-table-column
                    prop="equipmentSerialNumber"
                    label="设备序列号"
                    align="center"
                ></el-table-column>
                <el-table-column
                    prop="type"
                    label="设备类型"
                    align="center"
                    show-overflow-tooltip
                >
                    <template slot-scope="scope">
                        {{
                            scope.row.type == "1"
                                ? "班牌"
                                : scope.row.type == "2"
                                ? "闸机"
                                : ""
                        }}
                    </template>
                </el-table-column>
                <el-table-column prop="statu" align="center">
                    <template slot="header">
                        <el-dropdown>
                            <span class="el-dropdown-link">
                                注册码状态
                                <i
                                    class="el-icon-arrow-down el-icon--right"
                                ></i>
                            </span>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item
                                    v-for="(item, index) in filters"
                                    :key="index"
                                    @click.native="onStatusSelect(item.value)"
                                    >{{ item.text }}</el-dropdown-item
                                >
                            </el-dropdown-menu>
                        </el-dropdown>
                    </template>
                    <template slot-scope="scope">
                        <el-tag
                            :type="
                                scope.row.status == 2 ? 'primary' : 'success'
                            "
                            disable-transitions
                            >{{ scope.row.statu }}</el-tag
                        >
                    </template>
                </el-table-column>
                <el-table-column
                    label="备注"
                    align="center"
                    prop="remarks"
                ></el-table-column>
                <el-table-column
                    label="操作"
                    align="right"
                    fixed="right"
                    width="150px"
                >
                    <template slot-scope="scope">
                        <el-link
                            icon="el-icon-delete"
                            type="danger"
                            v-if="scope.row.status == 1"
                            @click="onOpenDrawer(scope)"
                            >注销</el-link
                        >
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!-- 侧边窗 -->
        <div>
            <el-drawer :visible.sync="drawer" :with-header="false">
                <div class="right_open_drawer">
                    <div class="add_registration_code">新增注册码</div>
                    <div class="from_code">
                        <div class="school_name_list">
                            学校：
                            <input
                                v-model="school_name"
                                placeholder="输入学校名称"
                                @click="onInputName"
                            />
                            <ul
                                class="associate_list"
                                v-if="restaurants.length && isShow"
                            >
                                <li
                                    v-for="(item, index) in restaurants"
                                    :key="index"
                                    @click="
                                        onschoolName(item.schoolName, item.id)
                                    "
                                >
                                    <div style="margin-right: 30px">
                                        {{ item.schoolName }}
                                    </div>
                                    <!-- <div>{{ item.schoolArea }}</div> -->
                                </li>
                            </ul>
                        </div>
                        <div>
                            数量：
                            <input
                                v-model="amount"
                                placeholder="仅可输入数字"
                            />
                        </div>
                        <div>
                            备注：
                            <input
                                v-model="remark"
                                placeholder="填写注册码备注信息"
                            />
                        </div>
                        <div>
                            选择设备：
                            <el-select v-model="addType" class="addType">
                                <el-option label="班牌" :value="1" />
                                <el-option label="闸机" :value="2" />
                            </el-select>
                        </div>
                    </div>
                    <div class="sty_confirm" @click="addRegistrationCode">
                        <el-button type="primary">确定</el-button>
                    </div>
                </div>
            </el-drawer>
        </div>
        <div class="paging_list">
            <el-pagination
                @current-change="handleCurrentChange"
                :current-page="page"
                background
                :page-size="pageSize"
                layout="prev, pager, next"
                :total="totalAll"
            ></el-pagination>
        </div>
    </div>
</template>

<script>
import {
    quetyRegistrationCodeList,
    insetRegistrationCode,
    updateRegistrationCode,
    applicationSearchLikeName,
} from "@/api/apply.js";
import axios from "axios";
import { Message } from "element-ui";
import { getToken } from "@/utils/auth";
export default {
    name: "opening",
    data() {
        return {
            loading: false,
            pageSize: 10,
            input: "",
            drawer: false,
            amount: "",
            remark: "",
            addType: 1,
            tableData: [],
            totalAll: 0,
            isShow: true,
            page: 1,
            input_code: "", //搜索注册码
            input_name: "", //搜索设备号
            officeName: "", //搜索学校名称
            type: "",
            school_name: "", //学校名称
            restaurants: [], //学校名称
            school_id: "",
            vaule: "",
            status: "",
            filters: [
                { text: "全部", value: "" },
                { text: "未使用", value: 0 },
                { text: "已使用", value: 1 },
                { text: "已注销", value: 2 },
            ],
        };
    },
    created() {},
    mounted() {
        this.getData();
    },
    watch: {
        school_name(curVal, oldVal) {
            clearTimeout(this.timeout);
            this.timeout = setTimeout(() => {
                this.getSchoolList(curVal);
            }, 200);
        },
    },
    methods: {
        searchResetForm() {
            (this.input_code = ""), //搜索注册码
                (this.input_name = ""), //搜索设备号
                (this.officeName = ""), //搜索学校名称
                (this.type = ""),
                (this.page = 1);
            this.getData();
        },
        thStyleFun() {
            return "text-align:center";
        },
        cellStyleFun() {
            return "text-align:center";
        },
        handleCurrentChange(val) {
            this.page = val;
            this.getData();
        },
        onDrawer() {
            this.drawer = true;
            (this.school_name = ""), (this.amount = ""), (this.remark = "");
        },
        //注销
        onOpenDrawer(rem) {
            const params = { status: 2, id: rem.row.id };

            updateRegistrationCode(params).then((res) => {
                if (res.code === 200) {
                    this.$message({
                        showClose: true,
                        message: "注销成功",
                        type: "success",
                    });
                    this.getData();
                }
            });
        },
        onStatusSelect(index) {
            (this.status = index), (this.page = 1);
            this.getData();
        },
        //查找
        onFindData() {
            this.page = 1;
            this.getData();
        },
        //注册码列表查询
        getData() {
            this.loading = true;
            const params = {
                size: 10,
                current: this.page,
            };

            if (this.input_code) {
                params.registrationCode = this.input_code;
            }
            if (this.input_name) {
                params.equipmentSerialNumber = this.input_name;
            }
            if (this.officeName) {
                params.officeName = this.officeName;
            }
            if (this.type) {
                params.type = this.type;
            }
            params.status = this.status;
            quetyRegistrationCodeList(params).then((res) => {
                if (res.code === 200) {
                    this.tableData = res.data.records.map((item) => {
                        this.$set(
                            item,
                            "statu",
                            item.status == 0
                                ? "未使用"
                                : item.status == 1
                                ? "已使用"
                                : "已注销"
                        );
                        return item;
                    });
                    this.totalAll = res.data.total;
                    this.page = res.data.current;
                    this.loading = false;
                }
            });
        },
        //导出
        onExportText() {
            const params = {
                    registrationCode: this.input_code,
                    equipmentSerialNumber: this.input_name,
                    officeName: this.officeName,
                    status: this.status,
                    type: this.type,
                },
                apiUrl =
                    process.env.VUE_APP_BASE_API +
                    "/backstage/cloud-backstage/registrationCode/exceleRegistrationCode"; // // api的base_url

            axios({
                url: apiUrl,
                method: "get",
                params,
                responseType: "blob",
                headers: {
                    Authorization: "Bearer " + getToken(),
                    platform: "system",
                },
            })
                .then((res) => {
                    const link = document.createElement("a"),
                        blob = new Blob([res.data], {
                            type: "application/vnd.ms-excel",
                        });

                    link.style.display = "none";
                    link.href = URL.createObjectURL(blob);
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                })
                .catch((error) => {
                    Message({
                        message: error.message,
                        type: "error",
                        duration: 3 * 1000,
                    });
                });
        },
        //新增 学校名称查找 applicationSearchLikeName
        getSchoolList() {
            this.restaurants = [];

            if (!this.school_name) {
                return;
            }

            const obj = { schoolName: this.school_name };

            applicationSearchLikeName(obj).then((res) => {
                if (res.code === 200) {
                    //  this.restaurants=
                    const list = [];

                    if (res.data.length) {
                        res.data.map((item, index) => {
                            const obj = {};

                            obj.id = item.id;
                            obj.schoolName = item.schoolName;
                            obj.schoolArea = item.schoolArea;
                            list.push(obj);
                        });
                        this.restaurants = list;
                    }
                }
            });
        },
        onschoolName(name, id) {
            this.isShow = false;
            this.school_name = name;
            this.restaurants = [];
            this.school_id = id;
        },
        onInputName() {
            this.isShow = true;
            this.school_id = "";
        },
        //添加注册码
        isPositiveNum(s) {
            //是否为正整数
            const re = /^[0-9]*[1-9][0-9]*$/;

            return re.test(s);
        },
        addRegistrationCode() {
            if (!this.school_id) {
                this.$message({
                    showClose: true,
                    message: "请选择正确的学校",
                    type: "error",
                });
                return;
            }
            if (!this.isPositiveNum(this.amount)) {
                this.$message({
                    showClose: true,
                    message: "数量必须是小于100的正整数",
                    type: "error",
                });
                return;
            }
            if (this.amount > 100) {
                this.$message({
                    showClose: true,
                    message: "数量不得大于100",
                    type: "error",
                });
                return;
            }
            let params;

            if (
                this.remark &&
                this.amount &&
                this.school_name &&
                this.addType
            ) {
                params = {
                    remarks: this.remark,
                    num: this.amount,
                    officeId: this.school_id,
                    officeName: this.school_name,
                    type: this.addType,
                };
            } else {
                this.$message({
                    showClose: true,
                    message: "请输入数量或备注，学校名称",
                    type: "error",
                });
            }
            insetRegistrationCode(params).then((res) => {
                if (res.code === 200) {
                    this.$message({
                        showClose: true,
                        message: "新增成功",
                        type: "success",
                    });
                    this.school_id = "";
                    this.getData();
                    this.drawer = false;
                    this.this.school_name = "";
                    this.amount = "";
                    this.remark = "";
                    this.addType = "";
                }
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.registration_code_manage {
    background-color: #fff;
    padding: 20px;
    .registration_code_seek {
        // width: 80%;
        // height: 35px;
        // display: flex;
        // justify-content: space-between;

        // margin-left: 10px;
        .right_export {
            display: flex;
            justify-content: space-between;
            margin-bottom: 16px;
            padding-top: 16px;
            border-top: 1px solid #ebeef5;
            span {
                margin: 10px;
            }
            // float: right;
        }
    }
    // .from_list {
    // margin: 20px 15px;
    // }
    .el-drawer__wrapper
        .el-drawer__open
        .el-drawer
        .el-drawer__body.el-drawer__body
        .right_open_drawer {
        margin: 100px 60px;
        .add_registration_code {
            font-size: 20px;
            font-weight: bold;
        }
        .from_code {
            margin: 20px 0;
            .school_name_list {
                position: relative;
            }
            div {
                margin-bottom: 20px;
                input {
                    width: 80%;
                    outline: none;
                    height: 50px;
                    border: 1px solid #eee;
                    text-align: center;
                }

                .associate_list {
                    width: 80%;
                    position: absolute;
                    right: 38px;
                    background: #fff;
                    height: 400px;
                    overflow: auto;
                    z-index: 2;
                    li {
                        padding: 0 10px;
                        height: 35px;
                        line-height: 35px;
                        display: flex;
                        cursor: pointer;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                    li:hover {
                        color: #4e6ef2;
                    }
                }
            }
        }
        .sty_confirm {
            width: 100px;
            margin: 300px auto;
        }
    }
    .paging_list {
        // width: 80%;
        // text-align: right;
        // margin: 20px auto;
        margin-top: 16px;
        text-align: right;
    }
}
</style>
<style lang="scss">
.addType {
    height: 50px;
    .el-input--medium .el-input__inner {
        height: 50px !important;
        width: 333px !important;
        position: relative;
        z-index: 1;
    }
}
.left_sdh_y {
    // width: 90%;
    display: flex;
    height: 58px;
    .school_name {
        width: 280px;
        line-height: 36px;
        display: flex;
        justify-content: flex-start;
        margin-left: 10px;
        // margin-left: 20px;
        // p {
        //     width: 100px;
        // }
        .el-input {
            width: 70%;
        }
    }
    .el-input--medium {
        width: 50%;
        margin-left: 10px;
        .el-input__inner {
            width: 200px;
            margin-right: 20px;
        }
    }
}
</style>
