<!--
 * @Descripttion: 供应商管理
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-08-19 09:58:05
 * @LastEditors: jingrou
 * @LastEditTime: 2022-06-14 11:16:11
-->

<template>
    <div class="supplier">
        <div class="supplierClass">
            <div class="search_warp">
                <el-form ref="ruleForm" :inline="true" :model="searchform">
                    <el-form-item label>
                        <el-input
                            v-model="searchform.content"
                            placeholder="请输入内容"
                            class="input-with-select"
                        >
                            <el-select
                                slot="prepend"
                                v-model="select"
                                placeholder="请选择"
                            >
                                <el-option label="应用提供商公司名" value="1" />
                                <el-option label="电话" value="2" />
                                <el-option label="对接人姓名" value="3" />
                            </el-select>
                        </el-input>
                    </el-form-item>

                    <el-form-item label="提供商状态">
                        <el-select
                            v-model="searchform.status"
                            placeholder="请选择"
                        >
                            <!-- <el-option label="启用" value="0" /> -->
                            <el-option label="启用" value="1" />
                            <el-option label="停用" value="2" />
                        </el-select>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="onSubmit('ruleForm')"
                            >查询</el-button
                        >
                        <el-button @click="resetForm('ruleForm')"
                            >重置</el-button
                        >
                    </el-form-item>
                </el-form>
            </div>
            <div class="btn_warp">
                <span>提供商管理</span>
                <div>
                    <el-button
                        type="primary"
                        @click="supplierAddForm"
                        icon="el-icon-plus"
                        >新增应用提供商</el-button
                    >
                </div>
            </div>
            <!-- 表格 -->
            <div v-loading="loading">
                <el-table
                    :data="list"
                    border
                    max-height="600"
                    style="width: 100%"
                    :header-cell-style="{
                        background: '#fafafa',
                        color: '#5b5d61',
                        height: '48px',
                    }"
                >
                    <el-table-column
                        prop
                        align="center"
                        label="编号"
                        type="index"
                        :index="indexMethod"
                        :resizable="false"
                        width="50"
                    />
                    <el-table-column
                        prop="name"
                        align="center"
                        label="应用提供商公司名"
                    />
                    <el-table-column
                        prop="type"
                        align="center"
                        label="应用提供商属性"
                    >
                        <template slot-scope="scope">
                            <div>
                                {{
                                    scope.row.type == "0"
                                        ? "内部提供商"
                                        : scope.row.type == "1"
                                        ? "第三方提供商"
                                        : ""
                                }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="address"
                        align="center"
                        label="公司地址"
                        :resizable="false"
                        :show-overflow-tooltip="true"
                    />
                    <el-table-column
                        prop="supplierPhone"
                        align="center"
                        label="公司电话"
                    />
                    <el-table-column
                        prop="supplierEmail"
                        align="center"
                        label="公司邮箱"
                    />
                    <el-table-column
                        prop="brokerName"
                        label="对接人"
                        align="center"
                    />
                    <el-table-column
                        prop="brokerPhone"
                        label="对接人电话"
                        align="center"
                    />
                    <!-- <el-table-column
				prop="brokerEmail"
				label="对接人邮箱"
				align="center"
					/>-->
                    <!-- <el-table-column prop="img" label="营业执照" align="center" /> -->
                    <el-table-column prop="status" label="状态" align="center">
                        <template slot-scope="scope">
                            <div>
                                {{ scope.row.status == "2" ? "停用" : "启用" }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="操作"
                        width="280"
                        align="right"
                        fixed="right"
                    >
                        <template slot-scope="scope">
                            <el-link
                                type="primary"
                                size="small"
                                @click="handleSchool(scope.row)"
                                >服务商相关联学校</el-link
                            >
                            <el-link
                                style="margin-left: 10px"
                                type="primary"
                                icon="el-icon-edit"
                                size="small"
                                @click="handleAddEdit(true, scope.row)"
                                >编辑</el-link
                            >
                            <el-button
                                type="text"
                                size="small"
                                :style="
                                    scope.row.status == '2'
                                        ? 'color: #74bf74'
                                        : 'color: red'
                                "
                                class="el-dropdown-link"
                                @click="dropdownClick(scope.row)"
                            >
                                {{ scope.row.status == "2" ? "启用" : "停用" }}
                                <!-- <el-dropdown-menu slot="dropdown">
								<el-dropdown-item
									v-for="item in dropdownList"
									:key="item.value"
									:command="item.value"
									@click.native="
										dropdownClick(item.value, scope.row)
									"
									>{{ item.name }}</el-dropdown-item
								>
								</el-dropdown-menu>-->
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <!-- 分页 -->
            <div class="paginationBlock">
                <el-pagination
                    style="margin-top: 10px"
                    :current-page="pagination.current"
                    :page-sizes="[10, 20, 30, 40]"
                    :page-size="pagination.size"
                    background
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pagination.total"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </div>
            <!-- 表单 -->
            <el-drawer
                :visible.sync="addfrom"
                title="应用提供商管理"
                direction="rtl"
            >
                <el-form
                    ref="ruleForm"
                    :model="supplierForms"
                    :rules="rules"
                    label-width="150px"
                    class="demo-ruleForm"
                >
                    <el-form-item
                        label="应用提供商公司名:"
                        prop="corporateName"
                    >
                        <el-input
                            v-model="supplierForms.corporateName"
                            placeholder="请输入应用提供商公司名"
                            maxlength="50"
                            show-word-limit
                        />
                    </el-form-item>
                    <el-form-item
                        label="应用提供商属性:"
                        prop="providerProperties"
                    >
                        <el-select
                            v-model="supplierForms.providerProperties"
                            placeholder="第三方提供商"
                        >
                            <el-option
                                v-for="(item, index) in newProviderProperties"
                                :key="index"
                                :label="item"
                                :value="index"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="公司地址:" prop="companyAddress">
                        <el-input
                            v-model="supplierForms.companyAddress"
                            placeholder="请输入公司详细地址"
                            maxlength="50"
                            show-word-limit
                        />
                    </el-form-item>
                    <el-form-item label="公司电话:" prop="companyPhone">
                        <el-input
                            v-model="supplierForms.companyPhone"
                            placeholder="请输入公司电话"
                        />
                    </el-form-item>
                    <el-form-item label="公司邮箱:" prop="companyMailbox">
                        <el-input
                            v-model="supplierForms.companyMailbox"
                            placeholder="请输入公司邮箱"
                        />
                    </el-form-item>

                    <el-form-item label="上传营业执照:">
                        <el-upload
                            action="/"
                            list-type="picture"
                            :on-change="uploadChange"
                            :before-remove="uploadRemove"
                            :file-list="fileList"
                            :auto-upload="false"
                            :on-preview="handlePictureCardPreview"
                            :limit="2"
                            accept="image/png, image/jpeg"
                        >
                            <el-button size="small" type="primary">
                                {{
                                    !this.fileList.length
                                        ? "点击上传"
                                        : "更换附件"
                                }}
                            </el-button>
                        </el-upload>
                        <el-dialog append-to-body :visible.sync="dialogVisible">
                            <img width="100%" :src="supplierForms.img" alt />
                        </el-dialog>
                    </el-form-item>

                    <el-form-item label="对接人:" prop="abutmentMan">
                        <el-input
                            v-model="supplierForms.abutmentMan"
                            placeholder="请输入对接人"
                        />
                    </el-form-item>
                    <el-form-item label="对接人电话:" prop="abutmentManPhone">
                        <el-input
                            v-model="supplierForms.abutmentManPhone"
                            placeholder="请输入对接人电话"
                        />
                    </el-form-item>
                    <el-form-item label="对接人邮箱:" prop="abutmentManMailbox">
                        <el-input
                            v-model="supplierForms.abutmentManMailbox"
                            placeholder="请输入对接人邮箱"
                        />
                    </el-form-item>

                    <el-form-item>
                        <el-button
                            type="primary"
                            @click="submitForm('ruleForm')"
                            >确定</el-button
                        >
                        <el-button @click="addfrom = false">取消</el-button>
                    </el-form-item>
                </el-form>
            </el-drawer>
        </div>
        <!-- 相关学校选择 -->
        <el-dialog
            title="服务商相关学校"
            width="20%"
            :visible.sync="dialogSchoolVisible"
        >
            <el-form :model="dialogSchoolForm">
                <el-form-item>
                    <el-select
                        style="width: 100%"
                        v-model="dialogSchoolForm.supplierSchool"
                        multiple
                        filterable
                        default-first-option
                        popper-class="selectClass"
                        placeholder="请选择"
                    >
                        <el-option
                            v-for="(item, index) in supplierSchoolList"
                            :key="index"
                            :label="item.schoolName"
                            :value="item.id"
                        ></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog_footer">
                <el-button @click="dialogSchoolVisible = false"
                    >取 消</el-button
                >
                <el-button type="primary" @click="dialogSchool"
                    >确 定</el-button
                >
            </div>
        </el-dialog>
    </div>
</template>
<script>
import {
    createSupplier,
    supplierList,
    editEcho,
    editSupplier,
    updateStatus,
    relationSchool,
} from "@/api/supplier.js";

import { getUploadFileOSSpath } from "@/utils/oss";
import { searchSchoolList } from "@/api/application.js";
export default {
    data() {
        return {
            id: "",
            status: "",
            inputShow: true,
            loading: false,
            newProviderProperties: ["内部提供商", "第三方提供商"],
            dialogVisible: false,
            fileList: [],
            addsupplierFrom: "",
            addfrom: false,
            pagination: {
                current: 1,
                size: 10,
                total: 0,
            },
            list: [],
            searchform: {
                content: "",
                status: "",
            },
            select: "1",
            supplierForms: {
                id: "",
                corporateName: "",
                providerProperties: 1,
                companyAddress: "",
                companyPhone: "",
                companyMailbox: "",
                abutmentMan: "",
                abutmentManPhone: "",
                abutmentManMailbox: "",
                businessLicense: "",
                img: "",
                status: "",
            },
            rules: {
                corporateName: [
                    {
                        required: true,
                        message: "请输入应用提供商公司名",
                        trigger: "change",
                    },
                ],
                providerProperties: [
                    {
                        required: true,
                        message: "请选择应用提供商属性",
                        trigger: "change",
                    },
                ],
                companyAddress: [
                    {
                        required: true,
                        message: "请输入公司地址",
                        trigger: "change",
                    },
                ],
                companyPhone: [
                    {
                        required: true,
                        message: "请输入公司电话",
                        trigger: "change",
                    },
                ],
                companyMailbox: [
                    {
                        required: true,
                        message: "请输入公司邮箱",
                        ttrigger: "change",
                    },
                    {
                        pattern:
                            /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/,
                        message: "请输入正确的邮箱",
                        trigger: "blur",
                    },
                ],
                abutmentMan: [
                    {
                        required: true,
                        message: "请输入对接人",
                        trigger: "change",
                    },
                ],
                abutmentManPhone: [
                    {
                        required: true,
                        message: "请输入对接人电话",
                        trigger: "change",
                    },
                ],
                abutmentManMailbox: [
                    {
                        pattern:
                            /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/,
                        message: "请输入正确的邮箱",
                        trigger: "blur",
                    },
                ],
            },
            // dropdownList: [
            // 	{ value: "0", name: "启用" },
            // 	{ value: "1", name: "启用" },
            // 	{ value: "2", name: "停用" },
            // ],
            dialogSchoolVisible: false,
            dialogSchoolForm: {
                supplierSchool: "",
            },
            supplierSchoolList: [],
            listQuery: {
                current: 1,
                size: 999,
            },
            supplierId: "",
        };
    },
    created() {
        this.loadsupplierList();
        this.searchSchoolList();
    },
    methods: {
        // 点击提供商相关学校
        handleSchool(row) {
            this.dialogSchoolForm.supplierSchool =
                row.managementSchoolList || [];
            this.dialogSchoolVisible = true;
            this.supplierId = row.id;
            this.searchSchoolList();
        },
        // 学校接口
        searchSchoolList() {
            const obj = {
                current: this.listQuery.current,
                size: this.listQuery.size,
            };

            searchSchoolList(obj).then((res) => {
                if (res.code == 200) {
                    this.supplierSchoolList = res.data.records;
                }
            });
        },
        // 学校表单确定按钮
        dialogSchool() {
            const obj = {
                schoolIdList: this.dialogSchoolForm.supplierSchool,
                supplierId: this.supplierId,
            };

            relationSchool(obj).then((res) => {
                if (res.code == 200) {
                    this.$message.success(res.msg);
                    this.loadsupplierList();
                    this.dialogSchoolVisible = false;
                }
            });
        },

        handlePictureCardPreview(file) {
            this.supplierForms.img = file.url;
            this.dialogVisible = true;
        },
        // 修改状态
        updateStatus() {
            const obj = {
                id: this.id,
                status: this.status,
            };

            updateStatus(obj).then(({ code, msg, data }) => {
                if (code == 200) {
                    this.loadsupplierList();
                }
            });
        },
        handleCommand(command) {},
        dropdownClick(row) {
            this.id = row.id;
            if (row.status == "2") {
                this.status = "1";
                this.$confirm("您确定要启用该公司吗？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(() => {
                        this.updateStatus();
                    })
                    .catch(() => {});
            } else if (row.status == "1") {
                this.status = "2";
                this.$confirm("您确定要停用该公司吗？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(() => {
                        this.updateStatus();
                    })
                    .catch(() => {});
            } else if (row.status == "0") {
                this.status = "2";
                this.$confirm("您确定要停用该公司吗？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(() => {
                        this.updateStatus();
                    })
                    .catch(() => {});
            }
        },
        // 上传图片
        async uploadChange(file, fileList) {
            const  url  = await getUploadFileOSSpath(file.raw,
                'supplier'
            );

            if (url) {
                if (fileList.length > 0) {
                    this.fileList = [fileList[fileList.length - 1]];
                }
                this.supplierForms.img = url;
            } else {
                this.$message.error('上传失败');
                this.supplierForms.img = "";
            }
        },
        uploadRemove(file) {
            file.url = "";
            this.supplierForms.img = "";
        },
        // 回显
        editEcho(item) {
            this.addfrom = true;
            const obj = {
                id: item.id,
            };

            this.supplierForms.id = item.id;
            editEcho(obj).then(({ code, msg, data }) => {
                if (code == 200) {
                    const {
                        name,
                        type,
                        address,
                        supplierPhone,
                        supplierEmail,
                        brokerName,
                        brokerPhone,
                        brokerEmail,
                        img,
                        status,
                    } = data;

                    this.supplierForms.corporateName = name;
                    this.supplierForms.providerProperties = type - 0;
                    this.supplierForms.companyAddress = address;
                    this.supplierForms.companyPhone = supplierPhone;
                    this.supplierForms.companyMailbox = supplierEmail;
                    this.supplierForms.abutmentMan = brokerName;
                    this.supplierForms.abutmentManPhone = brokerPhone;
                    this.supplierForms.abutmentManMailbox = brokerEmail;
                    this.supplierForms.status = status;
                    if (img == "") {
                        this.fileList = [];
                    } else {
                        this.fileList = [{ url: img, name: img }];
                    }
                }
            });
        },

        // 编辑
        editSupplier() {
            const obj = {
                id: this.supplierForms.id,
                name: this.supplierForms.corporateName,
                type: this.supplierForms.providerProperties,
                address: this.supplierForms.companyAddress,
                supplierPhone: this.supplierForms.companyPhone,
                supplierEmail: this.supplierForms.companyMailbox,
                brokerName: this.supplierForms.abutmentMan,
                brokerPhone: this.supplierForms.abutmentManPhone,
                brokerEmail: this.supplierForms.abutmentManMailbox,
                img: this.supplierForms.img,
            };

            editSupplier(obj).then(({ code, msg, data }) => {
                if (code == 200) {
                    this.addfrom = false;
                    this.loadsupplierList();
                    this.$message.success(msg);
                }
            });
        },

        // 表格列表
        loadsupplierList() {
            this.loading = true;
            let params = {};

            if (this.select) {
                const obj1 = [
                    {
                        key: "name",
                        value: "1",
                    },
                    {
                        key: "phone",
                        value: "2",
                    },
                    {
                        key: "brokerName",
                        value: "3",
                    },
                ].find((i) => i.value == this.select);

                params = {
                    [obj1.key]: this.searchform.content,
                };
            }
            const obj = {
                ...params,
                status: this.searchform.status,
                size: this.pagination.size,
                current: this.pagination.current,
            };

            supplierList(obj)
                .then(({ data, code, msg }) => {
                    if (code === 200) {
                        const { records, total, size, current } = data;
                        this.pagination.total = total;
                        this.pagination.size = size;
                        this.pagination.current = current;
                        this.list = records;
                        this.loading = false;
                    } else {
                        this.$message.error(msg);
                    }
                })
                .finally(() => {
                    this.tableloading = false;
                });
        },
        // 编号
        indexMethod(index) {
            return index + 1;
        },
        // 新增
        supplierAddForm() {
            this.addsupplierFrom = "1";
            this.fileList = [];
            this.addfrom = true;
            this.resetForm("ruleForm");
        },

        // 新增
        createSupplier() {
            const obj = {
                name: this.supplierForms.corporateName,
                type: this.supplierForms.providerProperties,
                address: this.supplierForms.companyAddress,
                supplierPhone: this.supplierForms.companyPhone,
                supplierEmail: this.supplierForms.companyMailbox,
                brokerName: this.supplierForms.abutmentMan,
                brokerPhone: this.supplierForms.abutmentManPhone,
                brokerEmail: this.supplierForms.abutmentManMailbox,
                img: this.supplierForms.img,
            };

            createSupplier(obj).then(({ code, msg, data }) => {
                if (code === 200) {
                    this.addfrom = false;
                    this.$message.success(msg);
                    this.loadsupplierList();
                }
            });
        },
        // 分页
        handleSizeChange(val) {
            this.pagination.current = 1;
            this.pagination.size = val;
            this.loadsupplierList();
        },
        handleCurrentChange(val) {
            this.pagination.current = val;
            this.loadsupplierList();
        },

        // 表格编辑回显
        handleAddEdit(isEdit, row) {
            // this.supplierForms = true;
            this.addsupplierFrom = "2";
            this.editEcho(row);
        },
        // 重置
        resetForm(formName) {
            this.$refs[formName].resetFields();
            this.searchform.content = "";
            this.searchform.status = "";
            this.fileList = [];
            this.loadsupplierList();
        },
        // 新增编辑
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    if (this.addsupplierFrom == 1) {
                        this.createSupplier();
                    } else if (this.addsupplierFrom == 2) {
                        this.editSupplier();
                    }
                }
            });
        },
        // 查询
        onSubmit() {
            this.loadsupplierList();
        },
    },
};
</script>

<style lang="scss">
.supplier {
    padding: 20px;
    background-color: #fff;
    .supplierClass {
        .has-gutter tr th {
            background-color: #ebeef5;
            height: 48px;
        }
        .btn_warp {
            display: flex;
            justify-content: space-between;
            margin-bottom: 16px;
            padding-top: 16px;
            border-top: 1px solid #ebeef5;
            span {
                margin: 10px;
            }
            div {
                display: flex;
            }
        }
        .paginationBlock {
            margin-top: 16px;
            text-align: right;
        }
        .el-select .el-input {
            width: 160px;
        }
        .input-with-select .el-input-group__prepend {
            background-color: #fff;
        }
        .el-dropdown-link {
            cursor: pointer;
            color: #409eff;
            padding-left: 10px;
        }
        .el-upload-list__item {
            transition: none !important;
        }
    }
}
.selectClass {
    max-width: 200px;
}
.dialog_footer {
    display: flex;
    justify-content: center;
}
.el-drawer__body {
    padding-right: 20px;
}
</style>
