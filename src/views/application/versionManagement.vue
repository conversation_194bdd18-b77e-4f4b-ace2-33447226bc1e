<template>
    <!-- 注册码管理 -->
    <div class="versionManagement">
        <!-- <div class="menu_left_content">
      添加应用
      <NewMenu
        style="margin-top:20px;"
        @childByValue="childByValue"
      />
		</div>-->

        <div class="menu_right_content menu_right_w" v-loading="loading">
            <div>
                <el-button-group>
                    <el-button v-for="(item, idx) in versionSchoolTab" :key="idx"
                        :type="versionSchoolState == idx ? 'primary' : ''" @click="headerVersionSchoolState(idx)">{{
        item
}}</el-button>

                </el-button-group>
                <el-button v-if="versionSchoolState == '0'" type="primary"
                    style="float: right" plain icon="el-icon-document-copy" @click="addVersionFn()">版本管理</el-button>
            </div>
            <!-- 新增学校 -->
            <div v-if="ewMenutrue">
                <!-- <div>
		    <SchoolTables v-if="versionSchoolState" @createSchool="headerCreateSchool"/>
			<VersionTable v-if="!versionSchoolState" @modifyVersion="headerModifyVersion"/>
				</div>-->
                <SchoolTables v-if="versionSchoolState" @createSchool="headerCreateSchool" />
                <VersionTable v-else @modifyVersion="headerModifyVersion" />
            </div>
            <Drawer :title="drawerTitle" :drawer-show="drawerShow" @drawerClose="headerDrawerClose">
                <ModifyVersionForm v-if="modify" :modify-version="modifyVersionParams"
                    @drawerClose="headerDrawerClose" />
                <template v-else>
                    <CreateSchoolForm v-if="versionSchoolState" @drawerClose="headerDrawerClose" />
                    <CreateVersionForm v-else :version-rule-form="versionRuleForm" @updatedVersion="getversionListFn"
                        @drawerClose="headerDrawerClose" />
                </template>
            </Drawer>
        </div>
    </div>
</template>

<script>
import NewMenu from "@/components/NewMenu";
import Drawer from "./Drawer";

import VersionTable from "./VersionTable";
import CreateSchoolForm from "./CreateSchoolForm";
import CreateVersionForm from "./CreateVersionForm";
import ModifyVersionForm from "./ModifyVersionForm";
import SchoolTables from "./SchoolTables";

import * as api from "@/api/application.js";
export default {
    name: "Opening",
    components: {
        // NewMenu,
        Drawer,
        VersionTable,
        CreateSchoolForm,
        CreateVersionForm,
        ModifyVersionForm,
        SchoolTables,
    },
    data() {
        return {
            loading: false,
            // szj start
            ewMenutrue: false,
            sys_id: "",
            modifyVersionParams: {},
            modify: false,
            versionSchoolTab: ["按版本管理", "按学校管理"],
            // versionSchoolTab: ["按版本管理"],
            versionSchoolState: 0,
            drawerShow: false,
            drawerTitle: "新增学校",
            updateType: ["强制", "不强制"],
            versionTable: [
                { prop: "versionName", label: "版本名称" },
                { prop: "versionNumber", label: "版本号" },
                { prop: "fileUrl", label: "文件地址" },
                { prop: "createdDateTime", label: "上传时间" },
                { prop: "updateType", label: "更新类型" },
                { prop: "updateTips", label: "更新提示" },
            ],

            schoolTable: [
                { prop: "schoolName", label: "学校名称" },
                { prop: "versionName", label: "版本名称" },
                { prop: "newVersion", label: "最新版本" },
                { prop: "newFile", label: "最新版本文件" },
                { prop: "remarks", label: "最新版本更新提示" },
            ],
            // szj end
            pages: {
                current: 1,
                size: 10,
                total: 0,
            },
            versionRuleForm: {
                list: [{ versionName: "" }],
            },
            versionList: [],
        };
    },
    created() {
        this.loading = true;
        // menu.$on("text", data => {
        // 	this.sys_id = data.id;
        // });
        // 左侧导航
        this.initFn();
        // 头部权限设置
        // if(this.$route.meta.btnIds.includes("edition_manage","school_manage")){
        // 	this.versionSchoolState = 0
        // }else if(this.$route.meta.btnIds.includes("edition_manage")){
        // 	this.versionSchoolState = 0
        // }else if(this.$route.meta.btnIds.includes("school_manage")){
        // 	this.versionSchoolState = 1
        // }
    },
    mounted() {
        if (this.$route.query.schoolId) {
            this.versionSchoolState = 1;
            this.drawerTitle = "新增学校";
        }
    },
    methods: {
        // szj start
        async initFn() {
            const thata = this;

            await api["applicationListApi"]()
                .then((res) => {
                    if (res.code == "200") {
                        this.sys_id = res.data[0].id;
                        thata.$store.commit(
                            "VersionSchool/VERSION_NAVIGATION_LIST",
                            res.data
                        );
                        this.ewMenutrue = true;
                        this.loading = false;
                    }
                })
                .catch();
        },
        headerDrawerClose() {
            this.drawerShow = false;
        },
        headerVersionSchoolState(idx) {
            this.$router.push({
                path: this.$route.path,
                query: "",
            });

            this.versionSchoolState = idx;
            this.drawerTitle = idx ? "新增学校" : "版本管理";
            this.$store.state.VersionSchool.pages = this.pages;
            this.loading = false;
        },

        formatter(row, column, cellValue, index) {
            if (row.updateType == cellValue) {
                cellValue = this.updateType[cellValue - 1];
            }
            return cellValue;
        },
        headerCreateSchool() {
            this.drawerShow = true;
            this.modify = false;
            this.drawerTitle = "新增学校";
        },
        headerModifyVersion(data) {
            const { drawerTitle, drawerShow, item } = data;

            this.drawerShow = drawerShow;
            this.drawerTitle = drawerTitle;
            this.modifyVersionParams = item || { sys_id: this.sys_id };
            this.modify = true;
        },
        // szj end
        //版本管理
        addVersionFn() {
            this.modify = false;
            this.getversionListFn();
            this.drawerTitle = "版本管理";
            this.drawerShow = true;
        },
        //获取版本
        getversionListFn() {
            const { schoolId } = this.$route.query,
                params = {
                    brandApplicationId: this.sys_id,
                };

            schoolId && (params.schoolId = schoolId);
            api["queryClassificListApi"](params)
                .then((res) => {
                    if (res.code == "200") {
                        this.versionList = res.data;
                        if (res.data.length > 0) {
                            this.versionRuleForm.list = res.data;
                        } else {
                            this.versionRuleForm.list = [{ versionName: "" }];
                        }
                    }
                })
                .catch();
        },
        childByValue(item) {
            const { id, text } = item;

            this.addText = text;
            this.$store.state.VersionSchool.brandApplicationId = id;
            this.sys_id = id;
            // this.$router.push({ path: this.$route.path });
            // this.$forceUpdate();
        },
    },
    computed: {
        // showTable(){
        // 	return ["edition_manage","school_manage"][this.versionSchoolState]
        // }
    },
};
</script>

<style lang="scss">
.versionManagement {
    overflow: hidden;
    // padding-top: 20px;
    background-color: #f5f6fa;

    .menu_left_content {
        float: left;
        // width: 260px;
        width: 12%;
        height: calc(90vh);
        text-align: center;
        background: #fff;
        box-shadow: 1px 2px 4px 0px rgba(214, 214, 214, 0.17);
        border-radius: 4px;
        margin: 0px 2%;
    }

    .menu_right_w {
        padding: 30px;
    }

    .from_list {
        margin-top: 20px;
        // height: 580px;
        overflow-y: auto;
    }

    .paging_list {
        margin-top: 20px;
    }

    .icon {
        cursor: pointer;
        font-size: 22px;
        margin-left: 20px;
        margin-top: 5px;
    }

    .el-icon-circle-plus-outline {
        color: #409eff;
    }

    .el-icon-remove-outline {
        color: rgb(252, 90, 90);
    }

    @import "./allmanagement.scss";

    .menu_right_content {
        float: left;
        width: 100%;
        background: #fff;
        box-shadow: 1px 2px 4px 0px rgba(214, 214, 214, 0.17);
        border-radius: 4px;
        height: calc(100vh - 110px);
        overflow: auto;
    }
}

.el-drawer__body {
    overflow: hidden !important;
}
</style>
