<template>
    <div class="version_management_child from_list">
        <div style="margin-bottom: 20px">
            <el-button type="primary" plain @click="CreateVersionFn()"
                >版本管理</el-button
            >
            <el-button type="primary" plain @click="UploadFileFn()"
                >上传文件</el-button
            >
            <el-button
                type="primary"
                style="float: right"
                plain
                @click="callBackFn()"
                >返回</el-button
            >
        </div>
        <el-table
            :data="schoolTableData"
            border
            :header-cell-style="thStyleFun"
            :cell-style="cellStyleFun"
            style="width: 100%"
        >
            <el-table-column width="80px" label="序号">
                <template slot-scope="scope">
                    <span v-text="scope.$index + 1" />
                </template>
            </el-table-column>
            <el-table-column
                v-for="(item, idx) in schoolChildTableColumn"
                :key="idx"
                :prop="item.prop"
                :label="item.label"
                :formatter="formatter"
            />
            <el-table-column label="操作" width="260px">
                <template slot-scope="scope">
                    <el-button
                        type="primary"
                        plain
                        size="mini"
                        @click="downloadFn(scope.row.fileUrl)"
                        >下载</el-button
                    >
                    <el-button size="mini" @click="ediuFn(scope.row)"
                        >修改</el-button
                    >
                    <el-button
                        type="danger"
                        plain
                        size="mini"
                        @click="delFn(scope.row.id)"
                        >删除</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            v-if="pages.total > 10"
            background
            style="margin-top: 20px"
            :current-page="pages.current"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="pages.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pages.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />

        <!-- 抽屉 -->
        <Drawer
            :title="drawerTitle"
            :drawer-show="drawerShow"
            @drawerClose="headerDrawerClose"
        >
            <ModifyVersionForm
                v-show="!versionSchoolState"
                :modify-version="modifyVersionParams"
                @drawerClose="headerDrawerClose"
            />
            <CreateVersionForm
                v-show="versionSchoolState"
                :version-rule-form="versionRuleForm"
                @updatedVersion="getversionListFn"
                @drawerClose="headerDrawerClose"
            />
        </Drawer>
    </div>
</template>

<script>
import * as api from "@/api/application.js";
import { mapGetters } from "vuex";
import Drawer from "./Drawer";
import ModifyVersionForm from "./ModifyVersionForm";
import CreateVersionForm from "./CreateVersionForm";

export default {
    name: "VersionManagementChild",
    components: { Drawer, ModifyVersionForm, CreateVersionForm },
    props: {},
    data() {
        return {
            page: {
                current: 1,
                size: 10,
                total: 0,
            },
            drawerTitle: "",
            versionSchoolState: false,
            drawerShow: false,
            versionType: 2,
            modifyVersionParams: {},
            updateType: ["强制", "不强制"],
            schoolChildTableColumn: [
                { label: "学校名称", prop: "schoolName" },
                { label: "版本名称", prop: "versionName" },
                { label: "版本号", prop: "versionNumber" },
                { label: "文件地址", prop: "fileUrl" },
                { label: "上传时间", prop: "createdDateTime" },
                { label: "更新类型", prop: "updateType" },
                { label: "更新提示", prop: "updateTips" },
            ],

            versionRuleForm: {
                list: [{ versionName: "" }],
            },
        };
    },
    computed: {
        ...mapGetters(["brandApplicationId", "schoolTableData", "pages"]),
    },
    watch: {},
    created() {
        this.getSchoolListInfo();
    },
    mounted() {},
    methods: {
        formatter(row, column, cellValue, index) {
            if (row.updateType === cellValue) {
                cellValue = this.updateType[cellValue - 1];
            }
            return cellValue;
        },
        downloadFn(url) {
            if (url.indexOf("http") != -1) {
                window.location.href = url;
            } else {
                this.$message.error("此路径错误");
            }
        },
        headerDrawerClose() {
            this.drawerShow = false;
        },
        ediuFn(item) {
            this.drawerTitle = "修改文件";
            this.drawerShow = true;
            this.modifyVersionParams = item;
        },

        delFn(id) {
            const { schoolId } = this.$route.query,
                params = {
                    id,
                    schoolId,
                    versionType: 2,
                    ...this.pages,
                    brandApplicationId: this.brandApplicationId,
                };

            this.$confirm("此操作将永久删除此信息, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    api["delVersionManagementApi"](params)
                        .then((res) => {
                            const { current } = this.pages;

                            if (current > this.schoolTableData.length) {
                                this.pages.current = 1;
                            }
                            if (res.code == 200) {
                                this.$message.success("删除成功");
                                this.getSchoolListInfo();
                            }
                        })
                        .catch();
                })
                .catch(() => {});
        },
        getSchoolListInfo() {
            const { schoolId } = this.$route.query,
                params = {
                    versionType: 2,
                    schoolId,
                    ...this.pages,
                    brandApplicationId: this.brandApplicationId,
                };

            this.$store.dispatch(
                "VersionSchool/insetVersionSchoolInfo",
                params
            );
        },
        thStyleFun() {
            return "text-align:center";
        },
        cellStyleFun() {
            return "text-align:center";
        },
        callBackFn() {
            this.$store.state.VersionSchool.pages = this.page;
            const schoolId = this.$route.query.schoolId;

            this.$router.push({
                path: "/internalApplication/versionManagement",
                query: { schoolId },
            });
        },
        CreateVersionFn() {
            this.drawerTitle = "版本管理";
            this.drawerShow = true;
            this.versionSchoolState = true;
            this.getversionListFn();
        },
        //获取版本  - 學校
        getversionListFn() {
            const { schoolId } = this.$route.query,
                params = {
                    brandApplicationId: this.brandApplicationId,
                };

            schoolId && (params.schoolId = schoolId);
            api["queryClassificListApi"](params)
                .then((res) => {
                    const { code, data } = res;

                    if (code == "200") {
                        if (data.length > 0) {
                            this.versionRuleForm.list = data;
                        } else {
                            this.versionRuleForm.list = [{ versionName: "" }];
                        }
                    }
                })
                .catch();
        },
        UploadFileFn() {
            this.drawerTitle = "上传文件";
            this.drawerShow = true;
            this.versionSchoolState = false;
            this.modifyVersionParams = {};
        },

        handleCurrentChange(val) {
            this.pages.current = val;
            this.getSchoolListInfo();
        },
        handleSizeChange(val) {
            this.pages.size = val;
            this.getSchoolListInfo();
        },
    },
};
</script>

<style scoped lang="scss">
.version_management_child {
    padding: 20px;
    overflow-y: auto;
    height: calc(100vh - 95px);
}
</style>
