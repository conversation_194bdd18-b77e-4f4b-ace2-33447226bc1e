<template>
    <div class="acsAccountPage">
        <div class="pageTypeBox">
            <el-radio-group v-model="pageType" @input="pageTypeinput">
                <el-radio-button label="1">学校管理</el-radio-button>
                <el-radio-button label="2">设备管理</el-radio-button>
            </el-radio-group>
        </div>
        <div v-if="pageType === '1'">
            <div>
                <el-form :model="searchForm" status-icon :inline="true">
                    <el-form-item label="学校名称:" prop="name">
                        <el-input
                            v-model.trim="searchForm.name"
                            placeholder="请输入学校名称"
                        />
                    </el-form-item>
                    <el-form-item>
                        <el-button
                            type="primary"
                            @click="query"
                            icon="el-icon-search"
                            >查询</el-button
                        >
                        <el-button @click="searchResetForm">重置</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <div class="addAccount">
                <el-button type="primary" @click="addSchool">新增</el-button>
            </div>
            <!-- table部分 -->
            <div class="tableBox">
                <el-table :data="tableData" style="width: 100%">
                    <el-table-column label="序号" type="index">
                    </el-table-column>
                    <el-table-column prop="name" label="学校名称">
                    </el-table-column>
                    <el-table-column prop="connectionLimit" label="连接数">
                    </el-table-column>
                    <el-table-column prop="deviceTypes" label="第三方设备类型">
                    </el-table-column>
                    <el-table-column prop="deviceCount" label="设备数量">
                    </el-table-column>
                </el-table>
                <!-- 分页 -->
                <div>
                    <el-pagination
                        background
                        class="pagination"
                        :current-page="pagination.pageNo"
                        :page-sizes="[10, 20, 30, 50]"
                        :page-size="pagination.pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="pagination.total"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                    />
                </div>
            </div>
        </div>
        <div v-if="pageType === '2'">
            <div>
                <el-tabs v-model="activeType" @tab-click="handleClick">
                    <el-tab-pane label="自助借还书机" name="6" />
                    <el-tab-pane label="查询机" name="22" />
                    <el-tab-pane label="馆员工作站" name="20" />
                    <el-tab-pane label="盘点车" name="23" />
                    <el-tab-pane label="手持盘点仪" name="27" />
                    <el-tab-pane label="安全门" name="24" />
                    <el-tab-pane label="微型图书馆" name="26" />
                </el-tabs>
            </div>

            <div>
                <el-form
                    :model="typeSearchForm"
                    status-icon
                    :inline="true"
                    label-width="auto"
                >
                    <el-form-item label="学校名称:" prop="schoolName">
                        <el-input
                            v-model.trim="typeSearchForm.schoolName"
                            placeholder="请输入"
                        />
                    </el-form-item>
                    <el-form-item label="供应商:" prop="vendorName">
                        <el-input
                            v-model.trim="typeSearchForm.vendorName"
                            placeholder="请输入"
                        />
                    </el-form-item>
                    <el-form-item label="设备名称:" prop="name">
                        <el-input
                            v-model.trim="typeSearchForm.name"
                            placeholder="请输入"
                        />
                    </el-form-item>
                    <el-form-item>
                        <el-button
                            type="primary"
                            @click="typeQuery"
                            icon="el-icon-search"
                            >查询</el-button
                        >
                        <el-button @click="typeReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <div class="addAccount">
                <el-button type="primary" @click="addAcsDevice">新增</el-button>
            </div>
            <!-- table部分 -->
            <div class="tableBox">
                <el-table
                    :data="typeTableData"
                    style="width: 100%"
                    v-loading="listLoading"
                >
                    <el-table-column prop="index" label="序号">
                    </el-table-column>
                    <el-table-column prop="schoolName" label="学校名称">
                    </el-table-column>
                    <el-table-column prop="vendorName" label="设备供应商">
                    </el-table-column>
                    <el-table-column prop="name" label="设备名称">
                    </el-table-column>
                    <el-table-column prop="acsUsername" label="ACS账号">
                        <template slot-scope="scope">
                            <el-button
                                v-if="scope.row.acsUsername"
                                @click="lookacsUsername(scope.row)"
                                type="text"
                                >查看</el-button
                            >

                            <span v-else>-</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="createTime" label="创建时间">
                    </el-table-column>
                    <el-table-column
                        prop="operation"
                        label="操作"
                        width="260px"
                        align="center"
                    >
                        <template slot-scope="scope">
                            <el-link
                                type="primary"
                                :disabled="!!scope.row.acsUsername"
                                @click="generateaccount(scope.row)"
                                >生成账号</el-link
                            >
                            <el-link
                                type="primary"
                                style="margin-left: 20px"
                                @click="editItem(scope.row)"
                                >编辑</el-link
                            >
                            <el-link
                                type="danger"
                                style="margin-left: 20px"
                                @click="delItem(scope.row)"
                                >删除</el-link
                            >
                        </template>
                    </el-table-column>
                </el-table>
                <!-- 分页 -->
                <div>
                    <el-pagination
                        background
                        class="pagination"
                        :current-page="typePagination.pageNo"
                        :page-sizes="[10, 20, 30, 50]"
                        :page-size="typePagination.pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="typePagination.total"
                        @size-change="typeHandleSizeChange"
                        @current-change="typeHandleCurrentChange"
                    />
                </div>
            </div>
        </div>
        <!-- 新增学校的弹窗 -->
        <el-dialog
            title="新增"
            :visible.sync="schoolVisible"
            @close="abolish('formInline')"
            width="500px"
        >
            <div>
                <el-form
                    :model="formInline"
                    label-width="auto"
                    ref="formInline"
                >
                    <el-form-item
                        label="学校名称："
                        prop="schoolObj"
                        :rules="[{ required: true, message: '请选择学校' }]"
                    >
                        <el-select
                            style="width: 100%"
                            filterable
                            value-key="id"
                            v-model="formInline.schoolObj"
                            placeholder="请选择"
                        >
                            <el-option
                                v-for="item in schoolOptions"
                                :key="item.id"
                                :label="item.name"
                                :value="item"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="abolish('formInline')">取 消</el-button>
                <el-button type="primary" @click="conserve('formInline')"
                    >保 存</el-button
                >
            </span>
        </el-dialog>
        <!-- 设备新增的的弹窗 -->
        <el-dialog
            :title="facilityObj.id ? '编辑' : '新增'"
            :visible.sync="dialogVisible"
            width="500px"
            @close="countermand('facilityObj')"
        >
            <div>
                <el-form
                    :model="facilityObj"
                    label-width="auto"
                    ref="facilityObj"
                >
                    <el-form-item
                        label="学校名称："
                        prop="schoolId"
                        :rules="[{ required: true, message: '请选择学校' }]"
                    >
                        <el-select
                            style="width: 100%"
                            filterable
                            :disabled="facilityObj.id"
                            v-model="facilityObj.schoolId"
                            placeholder="请选择"
                        >
                            <el-option
                                v-for="item in schoolOptionsList"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        label="供应商名称："
                        prop="vendorName"
                        :rules="[
                            { required: true, message: '请输入供应商名称' },
                        ]"
                    >
                        <el-input
                            v-model.trim="facilityObj.vendorName"
                            maxlength="20"
                            show-word-limit
                            placeholder="请输入"
                        ></el-input>
                    </el-form-item>
                    <el-form-item
                        label="设备名称："
                        prop="name"
                        :rules="[{ required: true, message: '请输入设备名称' }]"
                    >
                        <el-input
                            v-model.trim="facilityObj.name"
                            maxlength="20"
                            show-word-limit
                            placeholder="请输入"
                        ></el-input>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="countermand('facilityObj')">取 消</el-button>
                <el-button type="primary" @click="preserve('facilityObj')"
                    >保 存</el-button
                >
            </span>
        </el-dialog>
        <!-- 查看账号的弹窗 -->
        <el-dialog
            title="ACS账号"
            :visible.sync="acsDialogVisible"
            width="500px"
            @close="closeAcs"
        >
            <div id="acsUsernameRef">
                <div style="padding-bottom: 20px">
                    ACS账号：<span>{{ this.userObj.acsUsername }}</span>
                </div>
                <div>
                    密码：<span>{{ this.userObj.acsPassword }}</span>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="closeAcs">关 闭</el-button>
                <el-button type="primary" @click="copyAcs">复 制</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import {
    getSchoolList,
    acsSchoolcreate,
    acsSchoolfindPaged,
    acsSchoolfindMany,
    acsDevicecreate,
    acsDeviceFindPaged,
    createUserName,
    acsDevicedeleteById,
    acsDevicefindById,
    acsDeviceupdateById,
} from "@/api/device.js";
export default {
    name: "AcsAccount",
    data() {
        return {
            schoolOptions: [],
            schoolOptionsList: [],
            schoolVisible: false,
            listLoading: false,
            acsDialogVisible: false,
            dialogVisible: false,
            activeType: "6",
            pageType: "1",
            searchForm: {},
            typeSearchForm: {},
            formInline: {},
            userObj: {},
            facilityObj: {
                schoolId: "",
                vendorName: "",
                name: "",
            },
            pagination: {
                pageSize: 10,
                pageNo: 1,
                total: 0,
            },
            typePagination: {
                pageSize: 10,
                pageNo: 1,
                total: 0,
            },
            tableData: [],
            typeTableData: [],
        };
    },
    created() {
        this.reqAcsSchoolfindPaged();
        this.reqSchoolList();
        this.reqacsSchoolfindMany();
    },
    mounted() {},
    computed: {},
    methods: {
        reqAcsSchoolfindPaged() {
            acsSchoolfindPaged({
                pageNo: this.pagination.pageNo,
                pageSize: this.pagination.pageSize,
                ...this.searchForm,
            }).then((res) => {
                this.pagination.total = res.data.total;
                this.tableData = res.data.list;
            });
        },
        reqacsDeviceFindPaged() {
            this.listLoading = true;
            acsDeviceFindPaged({
                pageNo: this.typePagination.pageNo,
                pageSize: this.typePagination.pageSize,
                ...this.typeSearchForm,
                type: this.activeType,
            })
                .then((res) => {
                    this.typeTableData = res.data.list;
                    this.typePagination.total = res.data.total;
                })
                .finally(() => {
                    this.listLoading = false;
                });
        },
        handleSizeChange(val) {
            this.pagination.pageSize = val;
            this.reqAcsSchoolfindPaged();
        },
        reqacsSchoolfindMany() {
            acsSchoolfindMany().then((res) => {
                this.schoolOptionsList = res.data;
            });
        },
        handleCurrentChange(val) {
            this.pagination.pageNo = val;
            this.reqAcsSchoolfindPaged();
        },
        reqSchoolList() {
            getSchoolList().then((res) => {
                this.schoolOptions = res.data.map((i) => ({
                    name: i.name,
                    id: i.id,
                }));
            });
        },

        query() {
            this.reqAcsSchoolfindPaged();
        },
        searchResetForm() {
            this.searchForm = {};
            this.reqAcsSchoolfindPaged();
        },

        addSchool() {
            this.schoolVisible = true;
        },
        abolish(formName) {
            this.$refs[formName].resetFields();
            this.formInline = {};
            this.schoolVisible = false;
        },
        conserve(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    acsSchoolcreate({
                        ...this.formInline.schoolObj,
                    }).then((res) => {
                        this.schoolVisible = false;
                        this.formInline = {};
                        this.reqAcsSchoolfindPaged();
                        this.$message.success("操作成功");
                    });
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        handleClick(val) {
            this.typePagination.pageNo = 1;
            this.typePagination.pageSize = 10;
            this.reqacsDeviceFindPaged();
        },

        addAcsDevice() {
            this.dialogVisible = true;
        },
        countermand(formName) {
            this.$refs[formName].resetFields();
            this.facilityObj = {
                schoolId: "",
                vendorName: "",
                name: "",
            };
            this.dialogVisible = false;
        },
        preserve(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    const apiUrl = this.facilityObj.id
                        ? acsDeviceupdateById
                        : acsDevicecreate;
                    apiUrl({
                        ...this.facilityObj,
                        type: this.activeType,
                    }).then((res) => {
                        this.dialogVisible = false;
                        this.reqacsDeviceFindPaged();
                        this.$message.success("操作成功");
                    });
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        pageTypeinput(val) {
            this.reqacsSchoolfindMany();
            if (val === "2") {
                this.reqacsDeviceFindPaged();
            }
        },
        typeQuery() {
            this.reqacsDeviceFindPaged();
        },
        typeReset() {
            this.typeSearchForm = {};
            this.reqacsDeviceFindPaged();
        },
        typeHandleSizeChange(val) {
            this.typePagination.pageSize = val;
            this.reqacsDeviceFindPaged();
        },
        typeHandleCurrentChange(val) {
            this.typePagination.pageNo = val;
            this.reqacsDeviceFindPaged();
        },
        generateaccount(data) {
            createUserName({
                id: data.id,
            }).then((res) => {
                this.reqacsDeviceFindPaged();
            });
        },

        delItem(data) {
            this.$confirm("确认要删除该设备吗？删除后ACS账号不可登录", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    acsDevicedeleteById({
                        id: data.id,
                    }).then(() => {
                        this.reqacsDeviceFindPaged();
                    });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消",
                    });
                });
        },

        editItem(data) {
            this.dialogVisible = true;
            this.facilityObj.id = data.id;
            this.facilityObj.schoolId = data.schoolId;
            this.facilityObj.name = data.name;
            this.facilityObj.vendorName = data.vendorName;
        },
        lookacsUsername(data) {
            console.log("data", data);
            acsDevicefindById({
                id: data.id
            }).then((res) => {
                this.userObj =res.data;
                this.acsDialogVisible = true;
            })
        },
        closeAcs() {
            this.userObj = {};
            this.acsDialogVisible = false;
        },
        copyText(text) {
            if (typeof text !== "string")
                throw new Error(`the content must be of type string`);
            // 是否支持 navigator.clipboard 属性
            const isClipboardApiSupported =
                window.navigator && window.navigator.clipboard;
            if (isClipboardApiSupported) {
                window.navigator.clipboard.writeText(text);
            } else {
                const textarea = document.createElement("textarea");
                textarea.readOnly = true;
                textarea.value = text;
                textarea.style.position = "absolute";
                textarea.style.top = "-9999px";
                textarea.style.left = "-9999px";
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand("copy");
                textarea.remove();
            }
        },
        copyAcs() {
            let divDOM = document.getElementById("acsUsernameRef");
            let text = divDOM.textContent;
            this.copyText(text);
            this.$message.success("复制成功");
        },
    },
};
</script>

<style lang="scss" scoped>
.acsAccountPage {
    background-color: #fff;
    padding: 20px;
    .pageTypeBox {
        padding-bottom: 20px;
    }
    .addAccount {
        padding-bottom: 20px;
    }
}
.pagination {
    margin-top: 16px;
    text-align: right;
}

:deep(.el-table th.el-table__cell) {
    background-color: #fafafa !important;
}
</style>
