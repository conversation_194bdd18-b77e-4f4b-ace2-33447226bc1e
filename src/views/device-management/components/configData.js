export const placeholder = {
    1: "人脸机",
    2: "班牌",
    3: "考勤机",
    4: "中性版班牌",
    5: "一体机",
    6: "自助借还书机",
    7: "访客机",
    8: "门禁机",
    9: "会议机",
    20: "馆员工作站",
    22: "查询机",
    23: "盘点车",
    24: "安全门",
    26: "微型图书馆",
    27: "手持盘点仪",
    35: "智慧点餐机"
};

// 人脸机
const faceHeader = [
    { title: "场地", value: "siteName" },
    { title: "学校名称", value: "schoolName" },
    { title: "方向", value: "machineDirection", filter: machineFilter },
    { title: "人脸机名称", value: "machineName" },
    { title: "管理员", value: "administratorName" },
    {
        title: "状态",
        value: "machineStatus",
        filter: (val) => statusFilter(val),
    },
    { title: "人脸机序列号", value: "no" },
    { title: "创建时间", value: "updateTime" },
    {
        title: "操作",
        list: [
            {
                name: "查看详情",
                auth: "*",
            },
            {
                name: "设备监控",
                auth: "*",
            },
        ],
        width: "300px",
        isShow: btnShow,
    },
];

// 班牌
const classHeader = [
    { title: "设备名称", value: "machineName" },
    { title: "学校名称", value: "schoolName" },
    { title: "设备编号", value: "no" },
    { title: "创建时间", value: "updateTime" },
    {
        title: "状态",
        value: "machineStatus",
        filter: (val) => statusFilter(val),
    },
    {
        title: "操作",
        // list: ["查看详情", "设备监控"],
        list: [
            {
                name: "查看详情",
                auth: "*",
            },
            {
                name: "设备监控",
                auth: "*",
            },
        ],
        width: "300px",
        isShow: btnShow,
    },
];

// 考勤机
const attendanceHeader = [
    { title: "场地", value: "siteName" },
    { title: "学校名称", value: "schoolName" },
    { title: "考勤机名称", value: "machineName" },
    {
        title: "状态",
        value: "machineStatus",
        filter: (val) => statusFilter(val),
    },
    { title: "考勤机序列号", value: "no" },
    { title: "创建时间", value: "updateTime" },
    {
        title: "操作",
        list: [
            {
                name: "查看详情",
                auth: "*",
            },
        ],
        width: "200px",
        isShow: () => true,
    },
];

// 一体机
const allInOneHeader = [
    { title: "场地", value: "siteName" },
    { title: "学校名称", value: "schoolName" },
    { title: "一体机名称", value: "machineName" },
    {
        title: "状态",
        value: "machineStatus",
        filter: (val) => statusFilter(val),
    },
    { title: "一体机序列号", value: "no" },
    { title: "创建时间", value: "updateTime" },
    {
        title: "操作",
        list: [
            {
                name: "查看详情",
                auth: "*",
            },
        ],
        width: "200px",
        isShow: () => true,
    },
];

// 自助借还书机
const bookHeader = [
    { title: "场地", value: "siteName" },
    { title: "学校名称", value: "schoolName" },
    { title: "借还书机名称", value: "machineName" },
    { title: "管理员", value: "administratorName" },
    {
        title: "状态",
        value: "machineStatus",
        filter: (val) => statusFilter(val),
    },
    { title: "序列号", value: "no" },
    { title: "创建时间", value: "updateTime" },
    {
        title: "操作",
        list: [
            {
                name: "查看详情",
                auth: "*",
            },
        ],
        width: "200px",
        isShow: () => true,
    },
];

export const deviceHeader = [
    { title: "学校名称", value: "schoolName" },
    {
        title: "设备类型名称",
        value: "equipmentNames",
        filter: (val) => changeString(val),
        width: 400,
    },
    { title: "修改时间", value: "updateTime" },
    { title: "修改人", value: "updateBy" },
    { title: "备注", value: "remark" },
    {
        title: "操作",
        // list: ["编辑", "删除"],
        list: [
            {
                name: "编辑",
                auth: "manage.deviceType.edit",
            },
            {
                name: "删除",
                auth: "manage.deviceType.del",
            },
        ],
        width: "200px",
        isShow: () => true,
    },
];

export const maintainHeader = [
    { title: "设备类型", value: "equipmentName" },
    { title: "设备品牌", value: "brandName" },
    {
        title: "设备属性",
        value: "deviceMode",
        filter: (val) => deviceModetext(val),
    },
    { title: "设备型号", value: "deviceManufacturer" },
    {
        title: "使用对象",
        value: "tenantType",
        filter: (val) => tenantTypetext(val),
    },
    { title: "备注说明", value: "remarks" },
    { title: "创建人", value: "createBy" },
    { title: "创建时间", value: "createTime" },
    // {
    //     title: "操作",
    //     list: [
    //         {
    //             name: "删除",
    //             auth: "*",
    //         },
    //     ],
    //     width: "200px",
    //     isShow: () => true,
    // },
];

// 访客机
export const visitorHeader = [
    // 心在滴血
    { title: "场地", value: "siteName" },
    { title: "学校名称", value: "schoolName" },
    {
        title: "访客机名称",
        value: "machineName",
        // filter: (val) => changeString(val),
        width: 400,
    },
    {
        title: "状态",
        value: "machineStatus",
        filter: (val) => statusFilter(val),
    },
    { title: "访客机序列号", value: "no" },
    { title: "创建时间", value: "updateTime" },
    {
        title: "操作",
        list: [
            {
                name: "查看详情",
                auth: "*",
            },
        ],
        width: "200px",
        isShow: () => true,
    },
    // {
    //     title: "操作",
    //     list: ["编辑", "删除"],
    //     width: "200px",
    //     isShow: () => true,
    // },
];

// 门禁机
export const accessControlHeader = [
    // 心在滴血
    { title: "场地", value: "siteName" },
    { title: "学校名称", value: "schoolName" },
    {
        title: "门禁机名称",
        value: "machineName",
        // filter: (val) => changeString(val),
        width: 400,
    },
    {
        title: "状态",
        value: "machineStatus",
        filter: (val) => statusFilter(val),
    },
    { title: "门禁机序列号", value: "no" },
    { title: "创建时间", value: "updateTime" },
    {
        title: "操作",
        list: [
            {
                name: "查看详情",
                auth: "*",
            },
        ],
        width: "200px",
        isShow: () => true,
    },
    // {
    //     title: "操作",
    //     list: ["编辑", "删除"],
    //     width: "200px",
    //     isShow: () => true,
    // },
];

// 会议机
export const conferenceMachinelHeader = [
    // 心在滴血
    { title: "场地", value: "siteName" },
    { title: "学校名称", value: "schoolName" },
    {
        title: "会议机名称",
        value: "machineName",
        // filter: (val) => changeString(val),
        width: 400,
    },
    {
        title: "状态",
        value: "machineStatus",
        filter: (val) => statusFilter(val),
    },
    { title: "会议机序列号", value: "no" },
    { title: "创建时间", value: "updateTime" },
    {
        title: "操作",
        list: [
            {
                name: "查看详情",
                auth: "*",
            },
        ],
        width: "200px",
        isShow: () => true,
    },
];

export const guanHeader = [
    { title: "场地", value: "siteName" },
    { title: "学校名称", value: "schoolName" },
    {
        title: "馆员工作站名称",
        value: "machineName",
        // filter: (val) => changeString(val),
        width: 400,
    },
    {
        title: "状态",
        value: "machineStatus",
        filter: (val) => statusFilter(val),
    },
    { title: "馆员工作站序列号", value: "no" },
    { title: "创建时间", value: "updateTime" },
    {
        title: "操作",
        list: [
            {
                name: "查看详情",
                auth: "*",
            },
        ],
        width: "200px",
        isShow: () => true,
    },
];

export const chaHeader = [
    { title: "场地", value: "siteName" },
    { title: "学校名称", value: "schoolName" },
    {
        title: "查询机名称",
        value: "machineName",
        // filter: (val) => changeString(val),
        width: 400,
    },
    {
        title: "状态",
        value: "machineStatus",
        filter: (val) => statusFilter(val),
    },
    { title: "查询机序列号", value: "no" },
    { title: "创建时间", value: "updateTime" },
    {
        title: "操作",
        list: [
            {
                name: "查看详情",
                auth: "*",
            },
        ],
        width: "200px",
        isShow: () => true,
    },
];

export const panHeader = [
    { title: "场地", value: "siteName" },
    { title: "学校名称", value: "schoolName" },
    {
        title: "盘点车名称",
        value: "machineName",
        // filter: (val) => changeString(val),
        width: 400,
    },
    {
        title: "状态",
        value: "machineStatus",
        filter: (val) => statusFilter(val),
    },
    { title: "盘点车序列号", value: "no" },
    { title: "创建时间", value: "updateTime" },
    {
        title: "操作",
        list: [
            {
                name: "查看详情",
                auth: "*",
            },
        ],
        width: "200px",
        isShow: () => true,
    },
];

export const anquanHeader = [
    { title: "场地", value: "siteName" },
    { title: "学校名称", value: "schoolName" },
    {
        title: "安全门名称",
        value: "machineName",
        // filter: (val) => changeString(val),
        width: 400,
    },
    {
        title: "状态",
        value: "machineStatus",
        filter: (val) => statusFilter(val),
    },
    { title: "安全门序列号", value: "no" },
    { title: "创建时间", value: "updateTime" },
    {
        title: "操作",
        list: [
            {
                name: "查看详情",
                auth: "*",
            },
        ],
        width: "200px",
        isShow: () => true,
    },
];

export const weiHeader = [
    { title: "场地", value: "siteName" },
    { title: "学校名称", value: "schoolName" },
    {
        title: "微型图书馆名称",
        value: "machineName",
        // filter: (val) => changeString(val),
        width: 400,
    },
    {
        title: "状态",
        value: "machineStatus",
        filter: (val) => statusFilter(val),
    },
    { title: "微型图书馆序列号", value: "no" },
    { title: "创建时间", value: "updateTime" },
    {
        title: "操作",
        list: [
            {
                name: "查看详情",
                auth: "*",
            },
        ],
        width: "200px",
        isShow: () => true,
    },
];

export const shouHeader = [
    { title: "场地", value: "siteName" },
    { title: "学校名称", value: "schoolName" },
    {
        title: "手持盘点仪名称",
        value: "machineName",
        // filter: (val) => changeString(val),
        width: 400,
    },
    {
        title: "状态",
        value: "machineStatus",
        filter: (val) => statusFilter(val),
    },
    { title: "手持盘点仪序列号", value: "no" },
    { title: "创建时间", value: "updateTime" },
    {
        title: "操作",
        list: [
            {
                name: "查看详情",
                auth: "*",
            },
        ],
        width: "200px",
        isShow: () => true,
    },
];

export const zhihuiHeader = [
    { title: "场地", value: "siteName" },
    { title: "学校名称", value: "schoolName" },
    { title: "设备编号", value: "no" },
    { title: "创建时间", value: "updateTime" },
    {
        title: "状态",
        value: "machineStatus",
        filter: (val) => statusFilter(val),
    },
    {
        title: "操作",
        // list: ["查看详情", "设备监控"],
        list: [
            {
                name: "查看详情",
                auth: "*",
            },
        ],
        width: "300px",
        isShow: btnShow,
    },
]

function machineFilter(val) {
    if (val === "1" || val === "2") {
        return val === "1" ? "出" : "入";
    } else {
        return "--";
    }
}

function statusFilter(val) {
    return val === 1 ? "在线" : "离线";
}

function changeString(arr) {
    return arr && arr.length ? arr.join(",") : "";
}

function deviceModetext(val) {
    return val === 1 ? "自研" : "非自研";
}

function tenantTypetext(val) {
    return val === 1 ? "学校" : "服务商";
}

function btnShow(index, row) {
    if (index === 1 && row.machineStatus !== 1) {
        return false;
    } else {
        return true;
    }
}

// 列表的table 表头
export const headerGroup = {
    1: faceHeader,
    2: classHeader,
    3: attendanceHeader,
    4: classHeader,
    5: allInOneHeader,
    6: bookHeader,
    7: visitorHeader,
    8: accessControlHeader,
    9: conferenceMachinelHeader,
    20: guanHeader,
    22: chaHeader,
    23: panHeader,
    24: anquanHeader,
    26: weiHeader,
    27: shouHeader,
    35: zhihuiHeader
};
