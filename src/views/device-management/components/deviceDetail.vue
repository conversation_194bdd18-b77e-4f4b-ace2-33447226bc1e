<template>
    <div class="container_dialog_box">
        <el-dialog
            title="详情信息"
            :visible.sync="dialogProps.dialogVisible"
            width="600px"
            @open="open"
            :before-close="close"
        >
            <div class="msg_box">
                <div>
                    <span>设备id：</span
                    ><span class="item_right">{{ msgData.machineInfoId }}</span>
                </div>
                <div>
                    <span>学校id：</span
                    ><span class="item_right">{{ msgData.schoolId }}</span>
                </div>
                <div>
                    <div class="title">运行信息</div>
                    <div>
                        <span>创建时间：</span
                        ><span class="item_right">{{
                            msgData.operationInfoDTO.createTime | changFormat
                        }}</span>
                    </div>
                    <!-- <div>
                        <span>类型：</span
                        ><span class="item_right">{{
                            msgData.operationInfoDTO.infoType | filterType
                        }}</span>
                    </div> -->
                    <div v-if="msgData.operationInfoDTO.infoList.length">
                        <!-- <div class="title">属性集合</div> -->
                        <div
                            v-for="(item, index) in msgData.operationInfoDTO
                                .infoList"
                            :key="index"
                        >
                            <span>{{ item.infoKey }}：</span
                            ><span class="item_right">{{
                                item.infoValue
                            }}</span>
                        </div>
                    </div>
                </div>
                <div>
                    <div class="title">系统信息</div>
                    <div>
                        <span>创建时间：</span
                        ><span class="item_right">{{
                            msgData.systemInfoDTO.createTime | changFormat
                        }}</span>
                    </div>
                    <!-- <div>
                        <span>类型：</span
                        ><span class="item_right">{{
                            msgData.systemInfoDTO.infoType | filterType
                        }}</span>
                    </div> -->
                    <div v-if="msgData.systemInfoDTO.infoList.length">
                        <!-- <div class="title">属性集合</div> -->
                        <div
                            v-for="(item, index) in msgData.systemInfoDTO
                                .infoList"
                            :key="index"
                        >
                            <span>{{ item.infoKey }}：</span
                            ><span class="item_right">{{
                                item.infoValue
                            }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="close">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import { getDetail } from "@/api/device.js";
import { format } from "@/utils/util.js";
export default {
    name: "DeviceDetail",
    props: {
        dialogProps: {
            type: Object,
            default: () => ({
                dialogVisible: false,
                schoolId: "",
                machineInfoId: "",
            }),
        },
    },
    data() {
        return {
            msgData: {
                machineInfoId: "",
                schoolId: "",
                operationInfoDTO: {
                    createTime: "",
                    infoList: [],
                    infoType: "",
                },
                systemInfoDTO: {
                    createTime: "",
                    infoList: [],
                    infoType: "",
                },
            },
        };
    },
    filters: {
        filterType(val) {
            if (val) {
                return val == "1" ? "定制" : "通用包";
            }
            return val;
        },
        changFormat(val) {
            if (val) {
                return format(val, "YYYY-MM-DD");
            }
            return val;
        },
    },
    methods: {
        close() {
            this.dialogProps.dialogVisible = false;
            this.msgData = {
                machineInfoId: "",
                schoolId: "",
                operationInfoDTO: {
                    createTime: "",
                    infoList: [],
                    infoType: "",
                },
                systemInfoDTO: {
                    createTime: "",
                    infoList: [],
                    infoType: "",
                },
            };
        },
        open() {
            this.getDetail(
                this.dialogProps.schoolId,
                this.dialogProps.machineInfoId
            );
        },
        getDetail(schoolId, machineInfoId) {
            const params = {
                schoolId,
                machineInfoId,
            };
            getDetail(params).then((res) => {
                const baseData = {
                    createTime: "",
                    infoList: [],
                    infoType: "",
                };
                res.data.operationInfoDTO =
                    res.data.operationInfoDTO || baseData;
                res.data.systemInfoDTO = res.data.systemInfoDTO || baseData;
                this.msgData = res.data;
            });
        },
    },
};
</script>
<style lang="scss" scoped>
.container_dialog_box {
    :deep .el-dialog__body {
        padding: 5px 20px;
    }
    .msg_box {
        font-size: 16px;
        line-height: 28px;
        color: #333;
        .item_right {
            margin-left: 20px;
        }
        .title {
            font-weight: 600;
            margin: 10px 0;
        }
    }
}
</style>
