<template>
    <div>
        <el-dialog
            :title="`${dialogProps.isAdd ? '新增' : '编辑'}设备类型`"
            :visible.sync="dialogProps.dialogVisible"
            width="500px"
            @open="open"
            :before-close="close"
        >
            <el-form
                ref="formRef"
                :model="form"
                status-icon
                :inline="true"
                label-width="auto"
            >
                <el-form-item label="学校名称:" prop="queryName">
                    <el-select
                        v-model="form.schoolId"
                        placeholder="请选择学校名称"
                        :disabled="!dialogProps.isAdd"
                        filterable
                    >
                        <el-option
                            v-for="(item, index) in schoolList"
                            :key="index"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="设备类型:" prop="name">
                    <el-select
                        v-model="form.equipmentTypes"
                        placeholder="请选择设备类型"
                        multiple
                    >
                        <el-option
                            v-for="(item, index) in typeList"
                            :key="index"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>

            <span slot="footer" class="dialog-footer">
                <el-button @click="close">取消</el-button>
                <el-button type="primary" @click="confirm">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import { addType, updateType } from "@/api/device.js";
export default {
    name: "DeviceTypeDialog",
    props: {
        dialogProps: {
            type: Object,
            default: () => ({
                dialogVisible: false,
                isAdd: true,
            }),
        },
        schoolList: {
            type: Array,
            default: () => [],
        },
        typeList: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            form: {
                schoolId: "",
                equipmentTypes: [],
            },
        };
    },
    methods: {
        close() {
            Object.keys(this.form).forEach((i) => (this.form[i] = ""));
            this.dialogProps.dialogVisible = false;
        },
        open() {
            if (!this.dialogProps.isAdd) {
                this.form = { ...this.dialogProps.formData };
            }
        },
        async confirm() {
            const params = this.form;
            try {
                if (this.dialogProps.isAdd) {
                    await this.addType(params);
                } else {
                    await this.updateType(params);
                }
                this.close();
                this.$emit("confirm");
            } catch (e) {
                this.close();
                this.$emit("confirm");
            }
        },

        // 新增
        addType(params) {
            return new Promise((res) => {
                addType(params).then(() => {
                    this.$message.success("设备新增成功");
                    res();
                });
            });
        },

        // 编辑
        updateType(params) {
            return new Promise((res) => {
                updateType(params).then(() => {
                    this.$message.success("设备更新成功");
                    res();
                });
            });
        },
    },
};
</script>
<style lang="scss"></style>
