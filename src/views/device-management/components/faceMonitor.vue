
<template>
    <div v-loading="loading" class="monitor">
        <el-image style="width: 100%" :src="src"></el-image>
    </div>
</template>

<script>
import mqtt from "mqtt";
let client;
let timer;
const options = {
    clean: true, // true: 清除会话, false: 保留会话
    connectTimeout: 40000, // 超时时间
    clientId: "emq_web_01",
    username: "yide",
    password: "m8IFxdsRMkEFfaO1",
};
const connectUrl = process.env.VUE_APP_API_BASE_MQTT;
export default {
    props: ["deviceId"],
    data() {
        return {
            loading: true,
            src: "https://cloud-yide.oss-cn-shenzhen.aliyuncs.com/itENDJ_QQ%E6%88%AA%E5%9B%BE20230621181310.png",
        };
    },
    computed: {},
    mounted() {
        client = mqtt.connect(connectUrl, options);
        timer = setInterval(this.send, 5000);
        this.send();
        client.subscribe(`device_web/deviceImages/${this.deviceId}`);
        client.on("error", (error) => {
            console.log("连接失败:", error);
        });

        client.on("message", (topic, message) => {
            let obj = JSON.parse(message.toString());
            this.loading = false;
            this.src = "data:image/jpg;base64," + obj.remark;
        });
    },
    watch: {},
    methods: {
        send() {
            console.log("发送");
            client.publish(
                `device_web/push/${this.deviceId}`,
                JSON.stringify({ eventCode: "web_10000" })
            );
            console.log(this.deviceId);
            
        },
    },
    components: {},
    destroyed() {
        clearInterval(timer);
        client.end();
    },
};
</script>
<style lang="scss" scoped>
.monitor {
    height: 890px;
    overflow-y: auto;
}
</style>
