<template>
    <div>
        <el-dialog
            :title="`${dialogProps.isAdd ? '新增' : '编辑'}设备`"
            :visible.sync="dialogProps.dialogVisible"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            width="800px"
            @open="open"
            :before-close="close"
        >
            <el-form
                ref="formRef"
                :model="form"
                status-icon
                :inline="true"
                label-width="auto"
            >
                <el-form-item label="设备类型:" prop="equipmentType">
                    <el-select
                        v-model="form.equipmentType"
                        placeholder="请选择设备类型"
                    >
                        <el-option
                            v-for="(item, index) in equipmentTypesList"
                            :key="index"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <div
                    class="brandInfos"
                    v-for="(item, index) in form.brandInfos"
                >
                    <el-form-item label="设备品牌:" prop="queryName">
                        <el-input
                            v-model="item.brandName"
                            placeholder="请输入内容"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="品牌标识:" prop="queryName">
                        <el-input
                            v-model="item.deviceManufacturer"
                            placeholder="请输入内容"
                        ></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button
                            v-if="index === 0 && dialogProps.isAdd"
                            type="primary"
                            @click="addBrand"
                            >添加</el-button
                        >
                        <el-button
                            v-if="index !== 0 && dialogProps.isAdd"
                            type="danger"
                            @click="deleteBrand(index)"
                            >删除</el-button
                        >
                    </el-form-item>
                </div>
                <el-form-item label="使用对象:" prop="tenantType">
                    <el-radio-group v-model="form.tenantType">
                        <el-radio :label="1">学校</el-radio>
                        <el-radio :label="2">服务商</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="close">取消</el-button>
                <el-button type="primary" @click="confirm">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import { createDevBrand, upDevBrand, equipmentTypeList } from "@/api/device.js";
export default {
    name: "DeviceTypeDialog",
    props: {
        dialogProps: {
            type: Object,
            default: () => ({
                dialogVisible: false,
                isAdd: true,
            }),
        },
        typeList: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            equipmentTypesList: [],
            form: {
                brandInfos: [
                    {
                        brandName: "",
                        deviceManufacturer: "",
                    },
                ],
            },
        };
    },
    mounted() {
        this.getequipmentTypeList();
    },
    methods: {
        // 获取类型列表
        getequipmentTypeList() {
            equipmentTypeList().then((res) => {
                this.equipmentTypesList = res.data;
            });
        },
        // 添加品牌
        addBrand() {
            this.form.brandInfos.push({
                brandName: "",
                deviceManufacturer: "",
            });
        },
        deleteBrand(index) {
            this.form.brandInfos.splice(index, 1);
        },
        close() {
            // Object.keys(this.form).forEach((i) => (this.form[i] = ""));
            this.form = {
                brandInfos: [
                    {
                        brandName: "",
                        deviceManufacturer: "",
                    },
                ],
            };
            this.dialogProps.dialogVisible = false;
        },
        open() {
            if (!this.dialogProps.isAdd) {
                this.form = { ...this.dialogProps.formData };
            }
        },
        async confirm() {
            const params = this.form;
            const upParams = {
                id: this.form.id,
                equipmentType: this.form.equipmentType,
                brandName: this.form.brandInfos[0].brandName,
                deviceManufacturer: this.form.brandInfos[0].deviceManufacturer,
                tenantType: this.form.tenantType,
            };

            try {
                if (this.dialogProps.isAdd) {
                    await this.addType(params);
                } else {
                    await this.updateType(upParams);
                }
                this.close();
                this.$emit("confirm");
            } catch (e) {
                this.close();
                this.$emit("confirm");
            }
        },

        // 新增
        addType(params) {
            return new Promise((res) => {
                createDevBrand(params).then(() => {
                    this.$message.success("新增成功");
                    res();
                });
            });
        },

        // 编辑
        updateType(upParams) {
            return new Promise((res) => {
                upDevBrand(upParams).then(() => {
                    this.$message.success("更新成功");
                    res();
                });
            });
        },
    },
};
</script>
<style lang="scss">
.brandInfos {
    display: flex;
}
</style>
