<template>
    <div class="table-com">
        <el-table
            v-loading="listLoading"
            align="center"
            fit
            :key="JSON.stringify(header)"
            :data="responseBody.list || []"
            element-loading-text="加载中"
            style="width: 100%"
            class="queryTable"
            :max-height="height"
            empty-text="暂无数据"
            :highlight-current-row="true"
            @row-click="rowClick"
            @selection-change="handleSelectionChange"
        >
            <el-table-column v-if="isSelection" type="selection" width="55" />
            <el-table-column
                label="序号"
                align="center"
                width="80"
                fixed="left"
            >
                <template slot-scope="scope"
                    >{{
                        scope.$index +
                        1 +
                        responseBody.pageSize * (responseBody.pageNo - 1)
                    }}
                </template>
            </el-table-column>
            <el-table-column
                v-for="column in header"
                :key="column.index"
                :align="column.align || 'center'"
                :width="column.width"
                :min-width="column.minwidth"
                :fixed="column.fixed || false"
                :label="column.title"
                :show-overflow-tooltip="column.showAll ? false : true"
            >
                <template slot-scope="scope">
                    <span v-if="column.list">
                        <el-button
                            v-for="(item, index) in column.list"
                            v-auth="item.auth"
                            v-if="column.isShow(index, scope.row)"
                            :key="index"
                            type="text"
                            class="throttle"
                            style="padding: 0 5px"
                            @click="
                                cellClick(
                                    column,
                                    scope.row,
                                    $event,
                                    scope.$index,
                                    item
                                )
                            "
                            >{{ (item.name) | nullFilter }}</el-button
                        >
                    </span>
                    <span v-if="column.filter">
                        {{
                            column.filter(scope.row[column.value]) | nullFilter
                        }}
                    </span>
                    <span
                        v-if="!column.list && !column.filter"
                        :title="scope.row[column.value]"
                    >
                        {{ scope.row[column.value] | nullFilter }}
                    </span>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>
<script>
export default {
    // 操作栏多个按钮的表格组件
    name: "TableList",
    filters: {
        nullFilter(val) {
            return val || "--";
        },
    },
    props: {
        responseBody: {
            type: Object,
            default: function () {
                return {
                    data: [],
                };
            },
        },
        isSelection: {
            type: Boolean,
            default: false,
        },
        header: {
            type: Array,
            default: () => [],
        },
        listLoading: Boolean,
        queryRow: {
            type: Number,
            default: 1,
        },
    },
    data() {
        return {
            height: 600,
            trueHeight:
                document.body.clientHeight * 0.75 -
                267 -
                (this.queryRow - 1) * 60,
            flag: 0,
        };
    },
    created() {
        this.trueHeight > this.height && (this.height = this.trueHeight);
    },
    mounted() {
        window.onresize = () => {
            this.trueHeight =
                document.body.clientHeight * 0.75 -
                180 -
                (this.queryRow - 1) * 60;
            this.trueHeight > this.height && (this.height = this.trueHeight);
        };
    },
    methods: {
        // 表格点击事件
        cellClick(column, data, e, index, btn) {
            data.btnText = btn;
            this.$emit("cellClick", { column, data, e, index, btn });
        },
        // 行点击事件
        rowClick(row, column) {
            this.$emit("rowClick", row);
        },
        // 复选框事件
        handleSelectionChange(selection) {
            this.$emit("handleSelectionChange", selection);
        },
    },
};
</script>
<style scoped>
.throttle:active {
    animation: none;
}
@keyframes throttle {
    from {
        pointer-events: none;
    }

    to {
        pointer-events: all;
    }
}
</style>
