<template>
    <div class="device-type">
        <el-radio-group
            v-model="configType"
            style="margin-bottom: 10px"
            @change="elChange"
        >
            <el-radio-button label="2">按学校配置</el-radio-button>
            <el-radio-button label="1">按设备配置</el-radio-button>
        </el-radio-group>
        <el-tabs v-model="active" @tab-click="handleClick">
            <el-tab-pane label="班牌" name="2" />
            <el-tab-pane label="人脸机" name="1" />
        </el-tabs>

        <template v-if="active == 2">
            <div class="yd_title">
                {{ configType == 1 ? "班牌列表" : "学校列表" }}
            </div>
            <el-form
                ref="searchFormRef"
                @submit.native.prevent
                :model="searchForm"
                status-icon
                :inline="true"
                label-width="auto"
            >
                <el-form-item
                    label="设备名称"
                    prop="queryName"
                    v-if="configType == 1"
                >
                    <el-input
                        class="yd_input"
                        v-model="searchForm.queryName"
                        placeholder="请输入设备名称"
                    />
                </el-form-item>
                <el-form-item label="学校名称" prop="schoolName">
                    <el-input
                        class="yd_input"
                        v-model="searchForm.schoolName"
                        placeholder="请输入学校名称"
                    />
                </el-form-item>
                <el-form-item
                    label="设备状态"
                    prop="machineStatus"
                    v-if="configType == 1"
                >
                    <el-select
                        clearable
                        v-model="searchForm.machineStatus"
                        placeholder="请选择"
                    >
                        <el-option label="在线" value="1" />
                        <el-option label="离线" value="0" />
                    </el-select>
                </el-form-item>
                <!-- <el-form-item
                label="主板品牌"
                prop="motherboardBrand"
                v-if="configType == 1"
            >
                <el-select
                    clearable
                    v-model="searchForm.motherboardBrand"
                    placeholder="请选择"
                >
                    <el-option
                        v-for="item in dict['motherboard_brand']"
                        :key="'motherboard_brand' + item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
            </el-form-item> -->
                <el-form-item
                    label="刷卡器品牌"
                    prop="cardReaderBrand"
                    v-if="configType == 1"
                >
                    <el-select
                        clearable
                        v-model="searchForm.cardReaderBrand"
                        placeholder="请选择"
                    >
                        <el-option
                            v-for="item in dict['card_reader_brand']"
                            :key="'card_reader_brand' + item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item
                    :style="{ float: configType == 1 ? 'right' : 'none' }"
                >
                    <el-button
                        type="primary"
                        @click="BatchConfig"
                        v-auth="'manage.configuration.batch'"
                        :disabled="multipleSelection.length == 0"
                        v-if="configType == 1"
                        >批量配置</el-button
                    >
                    <el-button
                        type="primary"
                        @click="query"
                        icon="el-icon-search"
                        >查询</el-button
                    >
                    <el-button @click="searchResetForm">重置</el-button>
                </el-form-item>
            </el-form>

            <div>
                <el-table
                    :data="list"
                    :key="configType"
                    :border="true"
                    @selection-change="handleSelectionChange"
                    :header-cell-style="{
                        background: '#fafafa',
                        color: '#d2d2d2',
                    }"
                >
                    <el-table-column
                        type="selection"
                        width="55"
                        v-if="configType == 1"
                    >
                    </el-table-column>
                    <el-table-column
                        type="index"
                        label="序号"
                        width="50"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column prop="schoolName" label="学校名称">
                    </el-table-column>
                    <el-table-column
                        prop="machineName"
                        label="设备名称"
                        v-if="configType == 1"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="no"
                        label="设备编号"
                        v-if="configType == 1"
                    >
                    </el-table-column>
                    <!-- <el-table-column prop="motherboardBrandCode" label="主板品牌">
                </el-table-column> -->
                    <el-table-column
                        prop="cardReaderBrandCode"
                        label="刷卡器品牌"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="createDate"
                        label="创建时间"
                        v-if="configType == 1"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="machineStatus"
                        width="100"
                        label="状态"
                        align="center"
                        v-if="configType == 1"
                    >
                        <template slot-scope="scope">
                            {{ scope.row.machineStatus == 1 ? "在线" : "离线" }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="address"
                        label="操作"
                        width="260px"
                        align="center"
                    >
                        <template slot-scope="scope">
                            <el-link
                                type="primary"
                                :disabled="!scope.row.isConfig"
                                @click="editConfig(scope.row, true)"
                                >查看配置</el-link
                            >
                            <el-link
                                type="primary"
                                style="margin-left: 20px"
                                v-auth="'manage.configuration.edit'"
                                @click="editConfig(scope.row, false)"
                                >修改配置</el-link
                            >
                            <el-link
                                type="danger"
                                style="margin-left: 20px"
                                :disabled="!scope.row.isConfig"
                                v-auth="'manage.configuration.del'"
                                @click="delConfig(scope.row, false)"
                                >删除配置</el-link
                            >
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <el-pagination
                background
                style="margin-top: 16px; text-align: right"
                :page-sizes="[10, 20, 30, 50]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
            <el-dialog
                :title="title"
                :visible.sync="dialogVisible"
                width="400px"
                top="30px"
                :before-close="handleClose"
            >
                <el-form
                    ref="form"
                    :model="formItem"
                    label-width="100px"
                    class="formItem"
                    :disabled="formDisabled"
                >
                    <!-- <div>
                    <p>主板</p>
                    <el-form-item label="品牌">
                        <el-select
                            clearable
                            v-model="formItem.motherboardBrand"
                            placeholder="请选择"
                        >
                            <el-option
                                v-for="item in dict['motherboard_brand']"
                                :key="'motherboard_brand' + item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="型号">
                        <el-select
                            clearable
                            v-model="formItem.motherboardModel"
                            placeholder="请选择"
                        >
                            <el-option
                                v-for="item in dict['motherboard_model']"
                                :key="'motherboard_model' + item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </div> -->
                    <div>
                        <p>刷卡器</p>
                        <el-form-item label="品牌">
                            <el-select
                                clearable
                                v-model="formItem.cardReaderBrand"
                                placeholder="请选择"
                            >
                                <el-option
                                    v-for="item in dict['card_reader_brand']"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="频率">
                            <el-select
                                clearable
                                v-model="formItem.cardReaderFrequency"
                                placeholder="请选择"
                            >
                                <el-option
                                    v-for="item in dict[
                                        'card_reader_frequency'
                                    ]"
                                    :key="'card_reader_frequency' + item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="串口">
                            <el-select
                                clearable
                                v-model="formItem.cardReaderSerialPort"
                                placeholder="请选择"
                            >
                                <el-option
                                    v-for="item in dict[
                                        'card_reader_serial_port'
                                    ]"
                                    :key="
                                        'card_reader_serial_port' + item.value
                                    "
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </div>
                    <div>
                        <p>相机</p>
                        <el-form-item label="角度">
                            <el-select
                                clearable
                                v-model="formItem.cameraAngle"
                                placeholder="请选择"
                            >
                                <el-option
                                    v-for="item in dict['camera_angle']"
                                    :key="'camera_angle' + item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="人脸预览角度">
                            <el-select
                                clearable
                                v-model="formItem.cameraPreview"
                                placeholder="请选择"
                            >
                                <el-option
                                    v-for="item in dict['camera_angle']"
                                    :key="'cameraPreview' + item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </div>
                    <div>
                        <p>其它</p>
                        <el-form-item label="主题">
                            <el-select
                                clearable
                                v-model="formItem.theme"
                                placeholder="请选择"
                            >
                                <el-option
                                    v-for="item in dict['brand_theme']"
                                    :key="'brand_theme' + item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="多人脸识别">
                            <el-switch
                                v-model="formItem.faceStatus"
                                active-text="开"
                                inactive-text="关"
                            />
                        </el-form-item>
                    </div>
                </el-form>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="dialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="save" v-if="!formDisabled"
                        >确 定</el-button
                    >
                </span>
            </el-dialog>
        </template>
        <!-- 学校人脸机 -->
        <SchoolFaceTemplate v-if="configType == 2 && active == 1" />

        <DeviceFaceTemplate v-if="configType == 1 && active == 1" />
    </div>
</template>

<script>
import { Axios } from "@/utils/axios";
import SchoolFaceTemplate from "./schoolFaceTemplate.vue";
import DeviceFaceTemplate from "./deviceFaceTemplate.vue";
export default {
    name: "configuration",
    components: { SchoolFaceTemplate, DeviceFaceTemplate },
    data() {
        return {
            formItem: {
                faceStatus: false,
            },
            multipleSelection: [],
            formDisabled: true,
            active: "2",
            dict: {},
            title: "修改学校配置",
            configType: "2",
            activeName: "学校列表",
            total: 0,
            list: [],
            listLoading: false,
            searchForm: {
                pageNo: 1,
                queryName: "",
                schoolName: "",
                pageSize: 10,
                machineStatus: null,
                motherboardBrand: null,
                cardReaderBrand: null,
            },
            dialogVisible: false,
        };
    },
    created() {
        this.getDict();
    },
    mounted() {
        this.getList();
    },
    computed: {},
    methods: {
        formInit() {
            this.searchForm = {
                pageNo: 1,
                queryName: "",
                schoolName: "",
                pageSize: 10,
                machineStatus: null,
                motherboardBrand: null,
                cardReaderBrand: null,
            };
            this.$refs.searchFormRef.resetFields();
        },
        delConfig(item) {
            this.$confirm("此操作将永久删除该配置, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    Axios.get(
                        `/manage/machineInfo/deleteMachineConfig?id=${item.id}`
                    ).then((res) => {
                        this.getList();
                    });
                })
                .catch(() => {});
        },
        BatchConfig() {
            if (this.multipleSelection.length == 0) {
                this.$message("请至少选择一台设备");
                return false;
            }
            this.title = "批量配置";
            this.formDisabled = false;
            this.formItem = { batchConfig: true };
            this.dialogVisible = true;
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        elChange(e) {
            this.formInit();
            this.getList();
        },
        save() {
            const ids = this.formItem.batchConfig
                ? this.multipleSelection.map((i) => i.id)
                : [this.formItem.id];
            Axios.post("/manage/machineInfo/updateSchoolMachineConfig", {
                ids,
                equipmentType: this.active,
                configType: this.configType,
                motherboardBrand: this.formItem.motherboardBrand,
                cardReaderBrand: this.formItem.cardReaderBrand,
                cardReaderFrequency: this.formItem.cardReaderFrequency,
                cardReaderSerialPort: this.formItem.cardReaderSerialPort,
                cameraAngle: this.formItem.cameraAngle,
                cameraPreview: this.formItem.cameraPreview,
                motherboardModel: this.formItem.motherboardModel,
                theme: this.formItem.theme,
                faceStatus: this.formItem.faceStatus,
            }).then((res) => {
                this.getList();
                this.dialogVisible = false;
            });
        },
        getDict() {
            Axios.post("/manage/systemDict/get", [
                "motherboard_brand",
                "motherboard_model",
                "card_reader_brand",
                "brand_theme",
                "card_reader_frequency",
                "card_reader_serial_port",
                "camera_angle",
            ]).then((res) => {
                console.log(res.data);
                let arr = res.data;
                arr.forEach((i) => {
                    this.dict[i.type] = i.list;
                });
            });
        },
        editConfig(item, flag) {
            flag ? (this.title = "查看配置") : (this.title = "修改配置");
            this.formDisabled = flag;
            this.formItem = item;
            if (!this.formItem.faceStatus) {
                this.formItem.faceStatus = false;
            }
            this.dialogVisible = true;
        },
        getList() {
            if (this.configType == 2) {
                Axios.post("/manage/machineInfo/pageSchoolConfig", {
                    equipmentType: this.active,
                    ...this.searchForm,
                }).then((res) => {
                    this.list = res.data.list;
                    this.total = res.data.total;
                });
            } else {
                Axios.post("/manage/machineInfo/pageMachineConfig", {
                    equipmentType: this.active,
                    ...this.searchForm,
                }).then((res) => {
                    this.list = res.data.list;
                    this.total = res.data.total;
                });
            }
        },
        handleClose(done) {
            done();
        },
        handleClick() {},
        // 查询
        query() {
            this.searchForm.pageNo = 1;
            this.getList();
        },
        // 重置
        searchResetForm() {
            this.formInit();
            this.getList();
        },
        // 分页
        handleSizeChange(val) {
            this.searchForm.pageNo = 1;
            this.searchForm.pageSize = val;
            this.getList();
        },
        handleCurrentChange(val) {
            this.searchForm.pageNo = val;
            this.getList();
        },
    },
};
</script>

<style lang="scss" scoped>
.formItem {
    p {
        margin-bottom: 10px;
    }
    .el-select {
        width: 100%;
    }
}
.device-type {
    background-color: #fff;
    padding: 10px 20px;
    .yd_title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 10px;
    }
    .yd_input {
        width: 220px;
    }
}
</style>
