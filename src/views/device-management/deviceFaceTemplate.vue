<template>
    <div>
        <div class="yd_title">
            人脸机列表
        </div>
        <el-form
                ref="searchFormRef"
                @submit.native.prevent
                :model="searchForm"
                status-icon
                :inline="true"
                label-width="auto"
            >
            <el-form-item label="设备名称" prop="queryName">
                <el-input
                    class="yd_input"
                    v-model="searchForm.queryName"
                    placeholder="请输入设备名称"
                />
            </el-form-item>
            <el-form-item label="学校名称" prop="schoolName">
                    <el-input
                        class="yd_input"
                        v-model="searchForm.schoolName"
                        placeholder="请输入学校名称"
                    />
                </el-form-item>
                <el-form-item
                    label="设备状态"
                    prop="machineStatus"
                    v-if="configType == 1"
                >
                    <el-select
                        clearable
                        v-model="searchForm.machineStatus"
                        placeholder="请选择"
                    >
                        <el-option label="在线" value="1" />
                        <el-option label="离线" value="0" />
                    </el-select>
                </el-form-item>
            <el-form-item>
                <el-button
                        type="primary"
                        @click="BatchConfig"
                        v-auth="'manage.configuration.batch'"
                        :disabled="multipleSelection.length == 0"
                        >批量配置</el-button
                    >
                <el-button
                    type="primary"
                    @click="query"
                    icon="el-icon-search"
                    >查询</el-button
                >
                <el-button @click="searchResetForm">重置</el-button>
            </el-form-item>
        </el-form>
        <div>
            <el-table
                :data="list"
                :key="configType"
                :border="true"
                @selection-change="handleSelectionChange"
                :header-cell-style="{
                    background: '#fafafa',
                    color: '#d2d2d2',
                }"
            >
            <el-table-column
                        type="selection"
                        width="55"
                    />
                <el-table-column
                    type="index"
                    label="序号"
                    width="50"
                    align="center"
               />
                <el-table-column prop="schoolName" label="学校名称" />
                <el-table-column prop="machineName" label="设备名称" />
                <el-table-column prop="no" label="设备编号" />
                <el-table-column prop="createDate" label="创建时间" />
                <el-table-column
                        prop="machineStatus"
                        width="100"
                        label="状态"
                        align="center"
                    >
                        <template slot-scope="scope">
                            {{ scope.row.machineStatus == 1 ? "在线" : "离线" }}
                        </template>
                    </el-table-column>
                <el-table-column
                    prop="address"
                    label="操作"
                    width="260px"
                    align="center"
                >
                    <template slot-scope="scope">
                        <el-link
                            type="primary"
                            :disabled="!scope.row.isConfig"
                            @click="editConfig(scope.row, true)"
                            >查看配置</el-link
                        >
                        <el-link
                            type="primary"
                            style="margin-left: 20px"
                            @click="editConfig(scope.row, false)"
                            >修改配置</el-link
                        >
                        <el-link
                            type="danger"
                            style="margin-left: 20px"
                            :disabled="!scope.row.isConfig"
                            @click="delConfig(scope.row, false)"
                            >删除配置</el-link
                        >
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <el-pagination
                background
                style="margin-top: 16px; text-align: right"
                :page-sizes="[10, 20, 30, 50]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="pagination.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
            <el-dialog
                :title="dialogTitle"
                :visible.sync="dialogVisible"
                width="400px"
                top="30px"
                :before-close="handleClose"
            >
            <el-form
                    ref="form"
                    :model="formItem"
                    label-width="100px"
                    class="formItem"
                >
                <el-form-item label="抓拍上传">
                    <el-switch
                        v-model="formItem.captureFace"
                        active-text="开"
                        inactive-text="关"
                    />
                    </el-select>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                    <el-button @click="dialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="dialogSave"   v-if="!formDisabled"
                        >确 定</el-button
                    >
                </span>
        </el-dialog>
    </div>
</template>
<script>
import { Axios } from "@/utils/axios";
export default {
    name: "schoolFaceTemplate",
    components: {},
    data() {
        return {
            active: 1,
            list: [],
            configType: 1,
            searchForm: {
                queryName: "",
                machineStatus: null,
                schoolName: "",
            },
            pagination: {
                pageNo: 1,
                pageSize: 10,
                total: 0,
            },
            formItem: {
                id:'',  
                captureFace: false,
            },
            dialogVisible: false,
            formDisabled: false,
            dialogTitle:'',
            multipleSelection: [],
        };
    },
    mounted() {
        this.getList();
    },
    methods: { 
        getList() {
           const parasm = {equipmentType: this.active, ...this.searchForm, ...this.pagination,configType:this.configType };
            Axios.post("/manage/machineInfo/pageMachineConfig", parasm).then(({data}) => {
                const {list, total,pageNo, pageSize } = data;
                this.list = list;
                this.pagination.pageNo = pageNo;
                this.pagination.pageSize = pageSize;
                this.pagination.total = total;
            });
        },
        editConfig(item, flag) {
            this.dialogVisible = true;
            flag ? (this.dialogTitle = "查看配置") : (this.dialogTitle = "修改配置");
            this.formItem.id = item.id;
            this.formDisabled = flag;
            this.formItem.captureFace = item.captureFace
        },
        delConfig(item) {
            this.$confirm("此操作将永久删除该配置, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    Axios.get(
                        `/manage/machineInfo/deleteMachineConfig?id=${item.id}`
                    ).then((res) => {
                        this.getList();
                    });
                })
                .catch(() => {});
        },
          // 查询
        query() {
            this.pagination.pageNo = 1;
            this.pagination.pageSize = 10;
            this.getList();
        },
        // 重置
        searchResetForm() {
            this.searchForm.queryName =  ''
            this.searchForm.schoolName =  ''
            this.searchForm.machineStatus =  null
            this.getList();
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
           // 分页
        handleSizeChange(val) {
            this.pagination.pageNo = 1;
            this.pagination.pageSize = val;
            this.getList();
        },
        handleCurrentChange(val) {
            this.pagination.pageNo = val;
            this.getList();
        },
        handleClose(done) {
            done();
        },
        // 提交抓拍
        dialogSave() {
            this.dialogVisible = false;
            const ids= this.batchConfig? this.multipleSelection.map((i) => i.id):[this.formItem.id];
           const parasm = {  
                configType: this.configType, captureFace: this.formItem.captureFace ,equipmentType: this.active, ids};
            Axios.post("/manage/machineInfo/updateSchoolMachineConfig", parasm).then((res) => {
                this.getList();
                this.dialogVisible = false;
                this.$message(res.message);
            });
        },
        BatchConfig() {
            if (this.multipleSelection.length == 0) {
                this.$message("请至少选择一台设备");
                return false;
            }
            this.dialogTitle = "批量配置";
            this.formDisabled = false;
            this.batchConfig =true
            this.dialogVisible = true;
        },
    }
};
</script>


<style lang="scss" scoped>
.yd_title {
        font-size: 18px;
        font-weight: 600;
        margin: 10px 0;
    }
</style>
