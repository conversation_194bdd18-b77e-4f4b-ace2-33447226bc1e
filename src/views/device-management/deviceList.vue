<template>
    <div class="device-type">
        <el-tabs v-model="active" @tab-click="handleClick">
            <el-tab-pane label="人脸机" name="1" />
            <el-tab-pane label="班牌" name="2" />
            <el-tab-pane label="考勤机" name="3" />
            <el-tab-pane label="中性版班牌" name="4" />
            <el-tab-pane label="一体机" name="5" />
            <el-tab-pane label="自助借还书机" name="6" />
            <el-tab-pane label="访客机" name="7" />
            <el-tab-pane label="门禁机" name="8" />
            <el-tab-pane label="会议机" name="9" />
            <el-tab-pane label="馆员工作站" name="20" />
            <el-tab-pane label="查询机" name="22" />
            <el-tab-pane label="盘点车" name="23" />
            <el-tab-pane label="安全门" name="24" />
            <el-tab-pane label="微型图书馆" name="26" />
            <el-tab-pane label="手持盘点仪" name="27" />
            <el-tab-pane label="智慧点餐机" name="35" />
        </el-tabs>
        <div class="yd_title">{{ activeName }}</div>
        <el-form
            ref="searchFormRef"
            :model="searchForm"
            status-icon
            :inline="true"
            label-width="auto"
        >
            <el-form-item label="设备名称:" prop="queryName">
                <el-input
                    class="yd_input"
                    v-model="searchForm.queryName"
                    :placeholder="placeholderText"
                />
            </el-form-item>
            <el-form-item label="学校名称:" prop="schoolName">
                <el-input
                    class="yd_input"
                    v-model="searchForm.schoolName"
                    placeholder="请输入学校名称"
                />
            </el-form-item>
            <el-form-item label="设备状态:" prop="machineStatus">
                <el-select
                    v-model="searchForm.machineStatus"
                    placeholder="请选择设备状态"
                >
                    <el-option label="在线" value="1"> </el-option>
                    <el-option label="离线" value="2"> </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="query" icon="el-icon-search"
                    >查询</el-button
                >
                <el-button @click="searchResetForm">重置</el-button>
            </el-form-item>
        </el-form>
        <!--  table列表 -->
        <table-list
            :header="header"
            :listLoading="listLoading"
            :responseBody="responseBody"
            @cellClick="cellClick"
        />

        <el-pagination
            background
            style="margin-top: 16px; text-align: right"
            :current-page="responseBody.pageNo"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="responseBody.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="responseBody.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
        <el-dialog
            title="设备监控"
            :visible.sync="dialogVisible"
            width="800px"
            :before-close="handleClose"
        >
            <monitor v-if="dialogVisible" :deviceId="deviceId"></monitor>
        </el-dialog>
        <el-dialog
            class="faceMonitor"
            title="设备监控"
            :visible.sync="faceDialogVisible"
            width="540px"
            top="0"
            :before-close="handleClose"
        >
            <faceMonitor
                v-if="faceDialogVisible"
                :deviceId="deviceId"
            ></faceMonitor>
        </el-dialog>
        <device-detail :dialogProps="dialogProps" />
    </div>
</template>

<script>
import TableList from "./components/tableList.vue";
import DeviceDetail from "./components/deviceDetail.vue";
import { placeholder, headerGroup } from "./components/configData.js";
import { getList } from "@/api/device.js";
import monitor from "@/components/monitor.vue";
import faceMonitor from "./components/faceMonitor.vue";
export default {
    name: "DeviceList",
    components: { TableList, DeviceDetail, monitor, faceMonitor },
    data() {
        return {
            active: "1",
            activeName: "人脸机列表",
            deviceId: "2852f9186d57",
            responseBody: {
                list: [],
                pageNo: 1,
                pageSize: 10,
                total: 0,
            },
            listLoading: false,
            searchForm: {
                queryName: "",
                machineStatus: "",
                schoolName: "",
            },
            dialogVisible: false,
            faceDialogVisible: false,
            dialogProps: {
                dialogVisible: false,
            },
        };
    },
    created() {},
    mounted() {
        this.query();
    },
    computed: {
        placeholderText() {
            return `请输入${placeholder[this.active]}序列号/${
                placeholder[this.active]
            }名称`;
        },
        header() {
            return headerGroup[this.active];
        },
    },
    methods: {
        handleClose(done) {
            done();
        },
        handleClick() {
            this.activeName = placeholder[this.active] + "列表";
            this.searchResetForm();
        },
        // 查询
        query() {
            this.responseBody.pageNo = 1;
            this.fetchDeviceTypeList();
        },
        // 查询设备列表接口
        async fetchDeviceTypeList() {
            this.listLoading = true;
            const params = {
                ...this.responseBody,
                ...this.searchForm,
                type: this.active,
            };
            delete params.list;
            try {
                const res = await getList(params);
                this.responseBody = res.data;
                this.listLoading = false;
            } catch (e) {
                this.listLoading = false;
                // （只加了这个 别的没动...pai）
                this.responseBody = {
                    list: [],
                    pageNo: 1,
                    pageSize: 10,
                    total: 0,
                };
            }
        },
        // 重置
        searchResetForm() {
            this.$refs.searchFormRef.resetFields();
            this.query();
        },
        // 分页
        handleSizeChange(val) {
            this.responseBody.pageNo = 1;
            this.responseBody.pageSize = val;
            this.fetchDeviceTypeList();
        },
        handleCurrentChange(val) {
            this.responseBody.pageNo = val;
            this.fetchDeviceTypeList();
        },

        cellClick({ data, btn }) {
            if (btn.name == "设备监控") {
                if (data.deviceType === 1) {
                    this.deviceId = data.no;
                    this.faceDialogVisible = true;
                } else {
                    this.deviceId = data.no;
                    this.dialogVisible = true;
                }
            } else {
                // 设备详情
                this.dialogProps = {
                    dialogVisible: true,
                    schoolId: data.schoolId,
                    machineInfoId: data.id,
                };
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.device-type {
    background-color: #fff;
    padding: 20px;

    .yd_title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 10px;
    }

    .yd_input {
        width: 320px;
    }
}

.faceMonitor {
    .el-dialog__body {
        padding: 0px 20px !important;
    }
}

.faceMonitor {
    :deep(.el-dialog__body) {
        padding: 0px 20px !important;
    }
}
</style>
