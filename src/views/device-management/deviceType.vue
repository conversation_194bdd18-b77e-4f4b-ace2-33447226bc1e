<!-- 废弃了 -->
<!-- 废弃了 -->
<!-- 废弃了 -->
<!-- 废弃了 -->
<template>
    <div class="device-type">
        <el-form
            ref="searchFormRef"
            :model="searchForm"
            status-icon
            :inline="true"
            label-width="auto"
        >
            <el-form-item label="学校名称:" prop="schoolName">
                <el-input
                    v-model="searchForm.schoolName"
                    placeholder="请输入学校名称"
                />
            </el-form-item>
            <el-form-item label="设备类型:" prop="equipmentType">
                <el-select
                    v-model="searchForm.equipmentType"
                    placeholder="请选择设备类型"
                >
                    <el-option
                        v-for="(item, index) in equipmentList"
                        :key="index"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="query" icon="el-icon-search"
                    >查询</el-button
                >
                <el-button @click="searchResetForm">重置</el-button>
            </el-form-item>
        </el-form>

        <div class="yd_btn">
            <el-button
                type="primary"
                @click="addType"
                v-auth="'manage.deviceType.add'"
                >新增</el-button
            >
            <el-button
                type="danger"
                @click="delectSelect"
                v-auth="'manage.deviceType.del'"
                >删除</el-button
            >
        </div>

        <table-list
            isSelection
            :header="header"
            :listLoading="listLoading"
            :responseBody="responseBody"
            @cellClick="cellClick"
            @handleSelectionChange="handleSelectionChange"
        />

        <el-pagination
            background
            style="margin-top: 16px; text-align: right"
            :current-page="responseBody.pageNo"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="responseBody.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="responseBody.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />

        <DeviceTypeDialog
            :dialogProps="dialogProps"
            :typeList="equipmentList"
            :schoolList="schoolList"
            @confirm="query"
        />
    </div>
</template>

<script>
import TableList from "./components/tableList.vue";
import DeviceTypeDialog from "./components/deviceTypeDialog.vue";
import { deviceHeader } from "./components/configData.js";
import {
    getTypeList,
    deleteType,
    getSchoolList,
    getEquipmentTypeList,
} from "@/api/device.js";
export default {
    name: "DeviceType",
    components: { TableList, DeviceTypeDialog },
    data() {
        return {
            responseBody: {
                list: [],
                pageNo: 1,
                pageSize: 10,
                total: 0,
            },
            listLoading: false,
            searchForm: {
                schoolName: "",
                equipmentType: "",
            },
            header: deviceHeader,
            equipmentList: [],
            selectList: [],
            dialogProps: {
                dialogVisible: false,
                isAdd: true,
            },
            schoolList: [],
        };
    },
    created() {
        this.getEquipmentTypeList();
        this.getSchoolList();
    },
    mounted() {
        this.query();
    },
    methods: {
        // 查询
        query() {
            this.responseBody.pageNo = 1;
            this.fetchDeviceTypeList();
        },
        // 设备类型列表
        async fetchDeviceTypeList() {
            const params = {
                ...this.responseBody,
                ...this.searchForm,
            };
            delete params.list;

            let res = {};
            res = await getTypeList(params);
            this.responseBody = res.data;
        },
        // 重置
        searchResetForm() {
            this.$refs.searchFormRef.resetFields();
            this.query();
        },
        // 分页
        handleSizeChange(val) {
            this.responseBody.pageNo = 1;
            this.responseBody.pageSize = val;
            this.fetchDeviceTypeList();
        },
        handleCurrentChange(val) {
            this.responseBody.pageNo = val;
            this.fetchDeviceTypeList();
        },

        // 获取设备类型
        getEquipmentTypeList() {
            getEquipmentTypeList().then((res) => {
                this.equipmentList = res.data;
            });
        },
        cellClick({ data, btn }) {
            if (btn.name === "编辑") {
                const formData = {
                    schoolId: data.schoolId,
                    equipmentTypes: data.equipmentTypes,
                };
                this.dialogProps = {
                    dialogVisible: true,
                    isAdd: false,
                    formData,
                };
            } else {
                this.$confirm("确认删除？")
                    .then((_) => {
                        this.delectType([data.schoolId]);
                    })
                    .catch((_) => {});
            }
        },

        delectType(id) {
            deleteType({ schoolIds: id }).then((res) => {
                this.$message.success("设备删除成功");
                this.query();
            });
        },

        handleSelectionChange(select) {
            this.selectList = select;
        },

        addType() {
            this.dialogProps = {
                dialogVisible: true,
                isAdd: true,
            };
        },
        async delectSelect() {
            if (this.selectList.length === 0) {
                return this.$message.warning("请先选择需要删除的设备");
            } else {
                const Ids = this.selectList.map((i) => i.schoolId);
                this.$confirm("确认删除？")
                    .then((_) => {
                        this.delectType(Ids);
                    })
                    .catch((_) => {});
            }
        },

        getSchoolList() {
            getSchoolList().then((res) => {
                this.schoolList = res.data.map((i) => ({
                    name: i.name,
                    id: i.id,
                }));
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.device-type {
    background-color: #fff;
    padding: 20px;
    .yd_title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 10px;
    }
    .yd_btn {
        text-align: right;
        margin-bottom: 20px;
    }
}
</style>
