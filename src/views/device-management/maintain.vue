<!-- 经过一个迭代后这个地方由原来的设备类型改为设备型号了 -->
<!-- 经过一个迭代后这个地方由原来的设备类型改为设备型号了 -->
<!-- 需求就是只允许查看这里的数据和删除 新增的数据统一由后台直接录入进去 -->

<template>
    <div class="device-type">
        <el-form
            ref="searchFormRef"
            :model="searchForm"
            status-icon
            :inline="true"
            label-width="auto"
        >
            <el-form-item label="设备型号:" prop="deviceManufacturer">
                <el-input
                    v-model:value="searchForm.deviceManufacturer"
                    placeholder="请输入设备型号"
                />
            </el-form-item>
            <el-form-item label="设备类型:" prop="equipmentType">
                <el-select
                    v-model="searchForm.equipmentType"
                    placeholder="请选择设备类型"
                >
                    <el-option
                        v-for="(item, index) in equipmentList"
                        :key="index"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <!-- <el-form-item label="设备属性:" prop="deviceMode">
                <el-select
                    v-model="searchForm.deviceMode"
                    placeholder="请选择设备属性"
                >
                    <el-option
                        v-for="(item, index) in propertyOpt"
                        :key="index"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
            </el-form-item> -->
            <el-form-item label="创建时间:" prop="createTime">
                <el-date-picker
                    value-format="yyyy-MM-dd"
                    value="yyyy-MM-dd"
                    v-model="searchForm.createTime"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                >
                </el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="query" icon="el-icon-search"
                    >查询</el-button
                >
                <el-button @click="searchResetForm">重置</el-button>
            </el-form-item>
        </el-form>

        <!-- <div class="yd_btn">
            <el-button type="danger" @click="delectSelect">批量删除</el-button>
        </div> -->

        <table-list
            isSelection
            :header="header"
            :listLoading="listLoading"
            :responseBody="responseBody"
            @cellClick="cellClick"
            @handleSelectionChange="handleSelectionChange"
        />

        <el-pagination
            background
            style="margin-top: 16px; text-align: right"
            :current-page="responseBody.pageNo"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="responseBody.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="responseBody.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />

        <mainTainDialog
            :dialogProps="dialogProps"
            :typeList="equipmentList"
            :schoolList="schoolList"
            @confirm="query"
        />

        <!-- 打开设备详情的 Dialog  -->
        <el-dialog
            width="500px"
            title="设备详情"
            :visible.sync="infoDialogVisible"
        >
            <div class="infoBox">
                <div class="infoItem">
                    设备类型：<span>{{ infoObj.equipmentName }}</span>
                </div>
                <div>
                    设备品牌：<span>{{ infoObj.brandName }}</span>
                </div>
            </div>
            <div class="infoBox">
                <div class="infoItem">
                    创建人：<span>{{ infoObj.createBy }}</span>
                </div>
                <div>
                    创建时间：<span>{{ infoObj.createTime }}</span>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import TableList from "./components/tableList.vue";
import mainTainDialog from "./components/mainTainDialog.vue";
import { maintainHeader } from "./components/configData.js";
import {
    selectPageDevBrand,
    deleteDevBrand,
    equipmentTypeList,
} from "@/api/device.js";
export default {
    name: "DeviceType",
    components: { TableList, mainTainDialog },
    data() {
        return {
            propertyOpt: [
                {
                    id: 1,
                    name: "自研",
                },
                {
                    id: 2,
                    name: "非自研",
                },
            ],
            infoObj: {},
            responseBody: {
                list: [],
                pageNo: 1,
                pageSize: 10,
                total: 0,
            },
            infoDialogVisible: false,
            listLoading: false,
            searchForm: {
                equipmentType: "",
                createTime: [],
            },
            header: maintainHeader,
            equipmentList: [],
            selectList: [],
            dialogProps: {
                dialogVisible: false,
                isAdd: true,
            },
            schoolList: [],
        };
    },
    created() {
        this.getEquipmentTypeList();
    },
    mounted() {
        this.query();
    },
    methods: {
        // 查询
        query() {
            this.responseBody.pageNo = 1;
            this.fetchDeviceTypeList();
        },
        // 设备类型列表
        async fetchDeviceTypeList() {
            const params = {
                ...this.responseBody,
                ...this.searchForm,
                createStartDate: this.searchForm.createTime.length
                    ? this.searchForm.createTime[0]
                    : "",
                createEndDate: this.searchForm.createTime.length
                    ? this.searchForm.createTime[1]
                    : "",
            };
            delete params.list;

            let res = {};
            res = await selectPageDevBrand(params);
            this.responseBody = res.data;
        },
        // 重置
        searchResetForm() {
            this.$refs.searchFormRef.resetFields();
            this.query();
        },
        // 分页
        handleSizeChange(val) {
            this.responseBody.pageNo = 1;
            this.responseBody.pageSize = val;
            this.fetchDeviceTypeList();
        },
        handleCurrentChange(val) {
            this.responseBody.pageNo = val;
            this.fetchDeviceTypeList();
        },

        // 获取设备类型
        getEquipmentTypeList() {
            equipmentTypeList().then((res) => {
                this.equipmentList = res.data;
            });
        },
        cellClick({ data, btn }) {
            switch (btn.name) {
                case "编辑":
                    console.log(data, "编辑11111111111111111");
                    const formData = {
                        id: data.id,
                        equipmentType: data.equipmentType,
                        tenantType: data.tenantType,
                        brandInfos: [
                            {
                                brandName: data.brandName,
                                deviceManufacturer: data.deviceManufacturer,
                            },
                        ],
                    };
                    this.dialogProps = {
                        dialogVisible: true,
                        isAdd: false,
                        formData,
                    };
                    break;
                case "删除":
                    this.$confirm("确认删除？")
                        .then((_) => {
                            this.delectType([data.id]);
                        })
                        .catch((_) => {});
                    break;
                case "详情":
                    console.log(data, "12121312321");
                    this.infoObj = data;
                    this.infoDialogVisible = true;
                    break;
                default:
                    break;
            }
        },

        delectType(id) {
            deleteDevBrand(id).then((res) => {
                this.$message.success("设备删除成功");
                this.query();
            });
        },

        handleSelectionChange(select) {
            this.selectList = select;
        },

        addType() {
            this.dialogProps = {
                dialogVisible: true,
                isAdd: true,
            };
        },
        async delectSelect() {
            if (this.selectList.length === 0) {
                return this.$message.warning("请先选择需要删除的设备");
            } else {
                const Ids = this.selectList.map((i) => i.id);
                this.$confirm("确认删除？")
                    .then((_) => {
                        this.delectType(Ids);
                    })
                    .catch((_) => {});
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.device-type {
    background-color: #fff;
    padding: 20px;
    .yd_title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 10px;
    }
    .yd_btn {
        margin-bottom: 20px;
    }
}

.infoBox {
    display: flex;
    align-items: center;
    padding-bottom: 20px;
    // justify-content: space-around;
    .infoItem {
        padding-right: 100px;
    }
}
</style>
