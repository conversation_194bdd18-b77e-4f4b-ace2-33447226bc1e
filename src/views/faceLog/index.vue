<template>
    <div class="faceLogPage">
        <div class="inquireBox">
            <el-form
                ref="versionForm"
                :model="parameterObj"
                :inline="true"
                label-width="auto"
            >
                <el-form-item label="学校ID：">
                    <el-input
                        v-model="parameterObj.schoolId"
                        :maxlength="19"
                        @blur="parameterObj.schoolId=parameterObj.schoolId.replace(/[^\d]/g,'')"
                        placeholder="请输入"
                    />
                </el-form-item>
                <el-form-item label="用户名：">
                    <el-input
                        v-model="parameterObj.userName"
                        placeholder="请输入"
                    />
                </el-form-item>
                <el-form-item label="设备MAC：">
                    <el-input
                        v-model="parameterObj.deviceMac"
                        placeholder="请输入"
                    />
                </el-form-item>
                <el-form-item label="身份类型：">
                    <el-select
                        v-model="parameterObj.identity"
                        placeholder="请选择"
                    >
                        <el-option
                            placeholder="请选择"
                            v-for="item in options"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="状态：">
                    <el-select
                        v-model="parameterObj.status"
                        placeholder="请选择"
                    >
                        <el-option
                            placeholder="请选择"
                            v-for="item in statusOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item>
                    <el-button
                        type="primary"
                        icon="el-icon-search"
                        @click="searchBtn"
                        >查询</el-button
                    >
                    <el-button @click="resetBtn">重置</el-button>
                </el-form-item>
            </el-form>
        </div>
        <div class="tableBox">
            <el-table
                :data="tableData"
                :border="true"
                :header-cell-style="{
                    background: '#f5f7fa',
                    color: '#5b5d61',
                }"
            >
                <el-table-column label="序号" width="50px">
                    <template slot-scope="scope">
                        <span v-text="scope.$index + 1"></span>
                    </template>
                </el-table-column>
                <el-table-column prop="deviceName" label="设备名称">
                </el-table-column>
                <el-table-column prop="deviceType" label="设备类型">
                </el-table-column>
                <el-table-column prop="deviceMac" label="设备MAC">
                </el-table-column>
                <el-table-column prop="deviceSite" label="设备场地">
                </el-table-column>
                <el-table-column prop="userName" label="人脸用户名称">
                </el-table-column>
                <el-table-column prop="userId" label="人脸用户ID">
                </el-table-column>
                <el-table-column prop="faceUrl" label="人脸识别图片">
                    <template slot-scope="scope">
                        <el-image
                            style="width: 100px; height: 100px"
                            :src="scope.row.faceUrl"
                            :preview-src-list="[scope.row.faceUrl]"
                        >
                        </el-image>
                    </template>
                </el-table-column>
                <el-table-column prop="similarity" label="识别分值">
                </el-table-column>
                <el-table-column prop="identifyTime" label="识别时间">
                </el-table-column>
                <el-table-column prop="identity" label="身份类型">
                    <template slot-scope="scope">
                        <span>{{ getidentity(scope.row.identity) }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="schoolId" label="学校id">
                </el-table-column>
                <el-table-column prop="status" label="状态">
                    <template slot-scope="scope">
                        <span>{{
                            scope.row.status === 1 ? "失败" : "成功"
                        }}</span>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <el-pagination
                background
                style="margin-top: 16px; text-align: right"
                :current-page="pagination.pageNo"
                :page-sizes="[10, 20, 30, 50]"
                :page-size="pagination.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="pagination.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
    </div>
</template>
<script>
import { getfaceIdentifyLogPage } from "@/api/pushMessage.js";
export default {
    name: "FaceLog",
    data() {
        return {
            data: "",
            pagination: {
                pageNo: 1,
                pageSize: 10,
                total: 0,
            },
            parameterObj: {},
            tableData: [],
            options: [
                {
                    value: 1,
                    label: "学生",
                },
                {
                    value: 2,
                    label: "教职工",
                },
                {
                    value: 3,
                    label: "外部人员",
                },
            ],
            statusOptions: [
                {
                    value: 1,
                    label: "失败",
                },
                {
                    value: 2,
                    label: "成功",
                },
            ],
        };
    },
    created() {
        this.reqfaceIdentifyLogPage();
    },
    methods: {
        searchBtn() {
            this.pagination.pageNo = 1;
            this.reqfaceIdentifyLogPage();
        },
        resetBtn() {
            this.pagination.pageNo = 1;
            this.pagination.pageSize = 10;
            this.parameterObj = {};
            this.reqfaceIdentifyLogPage();
        },
        reqfaceIdentifyLogPage() {
            const params = {
                ...this.pagination,
                ...this.parameterObj,
            };
            getfaceIdentifyLogPage(params).then((res) => {
                console.log("res", res);
                this.tableData = res.data.list;
                this.pagination.total = res.data.total;
            });
        },
        getidentity(key) {
            const statusObj = {
                1: "学生",
                2: "教职工",
                3: "外部人员",
                default: "-",
            };
            return statusObj[key] || statusObj.default;
        },
        // 分页
        handleSizeChange(val) {
            this.pagination.pageNo = 1;
            this.pagination.pageSize = val;
            this.reqfaceIdentifyLogPage();
        },
        handleCurrentChange(val) {
            this.pagination.pageNo = val;
            this.reqfaceIdentifyLogPage();
        },
    },
};
</script>
<style lang="scss" scoped>
.faceLogPage {
    background-color: #fff;
    padding: 20px;
}
</style>
