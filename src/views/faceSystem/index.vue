<template>
    <el-card shadow="never">
        <!-- 表单 -->
        <el-form ref="searchform" :inline="true" :model="searchform">
            <el-form-item label="学校：">
                <el-input v-model="searchform.school" />
            </el-form-item>
            <el-form-item label="品牌：">
                <el-select v-model="searchform.brand">
                    <el-option label="旷视" value="旷视" />
                </el-select>
            </el-form-item>
            <el-form-item label="激活码：">
                <el-input v-model="searchform.number" />
            </el-form-item>
            <el-form-item label="MAC：">
                <el-input v-model="searchform.MAC" />
            </el-form-item>
            <el-form-item label="剩余激活次数：">
                <el-select v-model="searchform.frequency" style="width: 120px">
                    <el-option label="全部" value />
                    <el-option label="可使用" value="1" />
                    <el-option label="不可使用" value="0" />
                </el-select>
            </el-form-item>
            <el-form-item label="激活状态：" class="activateStateClass">
                <el-select
                    v-model="searchform.activateState"
                    style="width: 120px"
                >
                    <el-option label="全部" value />
                    <el-option label="已启用" value="1" />
                    <el-option label="禁用" value="2" />
                </el-select>
            </el-form-item>

            <el-form-item>
                <el-button
                    type="primary"
                    style="width: 100px; margin-left: 16px"
                    @click="handleSearch"
                    icon="el-icon-search"
                    >查询</el-button
                >
                <el-button size="medium" @click="searchResetForm('searchform')"
                    >重置</el-button
                >
            </el-form-item>
        </el-form>
        <!-- 新增 导出 -->
        <div class="btn">
            <span>人脸激活码使用记录统计表</span>
            <div class="multipartFile">
                <el-button
                    size="medium"
                    icon="el-icon-plus"
                    type="primary"
                    @click="handleAdd(false)"
                    >新增</el-button
                >
                <!-- 上传模板 -->
                <el-upload
                    class="upload-demo"
                    :headers="{ Authorization: token, platform: 'system' }"
                    :action="action"
                    :show-file-list="false"
                    :on-success="importSuccess"
                    :on-error="importError"
                    name="multipartFile"
                >
                    <el-button
                        icon="el-icon-top"
                        size="medium"
                        style="margin-left: 16px"
                        type="primary"
                        plain
                        >导入</el-button
                    >
                </el-upload>

                <el-button
                    icon="el-icon-bottom"
                    size="medium"
                    style="margin-left: 16px"
                    @click="exportData"
                    type="primary"
                    plain
                    >导出</el-button
                >
                <div class="download">
                    <a
                        href="https://file.1d1j.cn/%E4%BA%BA%E8%84%B8%E6%8E%88%E6%9D%83%E6%A8%A1%E7%89%88.xlsx"
                        >下载模板</a
                    >
                </div>
            </div>
        </div>
        <!-- 上传错误提示 -->
        <el-dialog title="提示" :visible.sync="dialogVisible" width="30%">
            <span v-html="errorPrompt" />
            <span slot="footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="dialogVisible = false"
                    >确 定</el-button
                >
            </span>
        </el-dialog>
        <!-- 表格 -->
        <el-table
            v-loading="tableloading"
            :data="tableData"
            border
            :header-cell-style="{ background: '#fafafa', color: '#5b5d61' }"
        >
            <el-table-column
                type="index"
                label="序号"
                width="50px"
                align="center"
            />
            <el-table-column
                prop="brandName"
                label="品牌"
                width="100"
                show-overflow-tooltip
            />
            <el-table-column
                prop="schoolName"
                label="学校名称"
                width="250"
                show-overflow-tooltip
            />
            <el-table-column
                prop="activateCode"
                label="激活码"
                width="200"
                show-overflow-tooltip
            >
                <template slot-scope="scope">
                    <span @click="handleCopy(scope.$index, scope.row)">{{
                        scope.row.activateCode
                    }}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="totalActivateNumber"
                label="可激活总次数"
                width="120"
                show-overflow-tooltip
            />
            <el-table-column
                prop="surplusActivateNumber"
                label="剩余激活次数"
                width="120"
                show-overflow-tooltip
            />
            <el-table-column
                prop="createDate"
                label="生成日期"
                align="center"
                width="130"
                show-overflow-tooltip
            />

            <el-table-column
                prop="deviceMacAddress"
                label="设备MAC"
                show-overflow-tooltip
            />
            <el-table-column
                prop="activateDate"
                label="激活日期"
                show-overflow-tooltip
            />
            <el-table-column
                prop="activateState"
                label="激活状态"
                align="center"
                show-overflow-tooltip
            >
                <template slot-scope="scope">
                    <div
                        :style="
                            scope.row.activateState == '1'
                                ? 'color: #74bf74'
                                : 'color: red'
                        "
                    >
                        {{
                            scope.row.activateState == "1"
                                ? "已启用"
                                : scope.row.activateState == "2"
                                ? "禁用"
                                : ""
                        }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="remark" label="备注" show-overflow-tooltip />
            <el-table-column
                label="操作"
                align="right"
                width="180"
                fixed="right"
            >
                <template slot-scope="scope">
                    <el-button
                        type="text"
                        icon="el-icon-edit"
                        @click.stop="handleEdit(scope.row)"
                        >编辑</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <div class="paginationBlock">
            <el-pagination
                :current-page.sync="pagination.current"
                background
                :page-size="pagination.pagesize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="pagination.total"
                @current-change="handleCurrentChange"
            >
                <span>总共 {{ pagination.total }} 条记录</span>
            </el-pagination>
        </div>
        <!-- 编辑 -->
        <el-dialog title="编辑" :visible.sync="editForm" width="580px">
            <el-form
                ref="ruleForm"
                :model="edit"
                :rules="rules"
                label-width="150px"
                class="demo-ruleForm"
            >
                <el-form-item label="品牌：" :label-width="formLabelWidth">{{
                    this.edit.brandName
                }}</el-form-item>
                <el-form-item label="激活码：" :label-width="formLabelWidth">{{
                    this.edit.activateCode
                }}</el-form-item>
                <el-form-item
                    label="可激活次数："
                    :label-width="formLabelWidth"
                    >{{ this.edit.totalActivateNumber }}</el-form-item
                >
                <el-form-item
                    label="生成日期："
                    :label-width="formLabelWidth"
                    >{{ this.edit.createDate }}</el-form-item
                >
                <el-form-item
                    v-if="edit.deviceMacAddress"
                    label="设备MAC："
                    :label-width="formLabelWidth"
                    >{{ this.edit.deviceMacAddress }}</el-form-item
                >
                <el-form-item
                    label="学校名称："
                    :label-width="formLabelWidth"
                    >{{ this.edit.schoolName }}</el-form-item
                >
                <el-form-item label="备注：" :label-width="formLabelWidth">
                    <el-input
                        v-model="editRemark"
                        type="textarea"
                        rows="4"
                        :autosize="{ minRows: 2, maxRows: 4 }"
                    />
                </el-form-item>
                <el-form-item
                    v-if="edit.deviceMacAddress"
                    label="激活状态"
                    :label-width="formLabelWidth"
                    prop="activateState"
                >
                    <el-select v-model="edit.activateState" placeholder>
                        <el-option label="已启用" value="1" />
                        <el-option label="禁用" value="2" />
                    </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="editForm = false">取 消</el-button>
                <el-button type="primary" @click="editSubmitForm()"
                    >保存</el-button
                >
            </div>
        </el-dialog>
        <!-- 新增 -->
        <el-dialog
            title="新增"
            :visible.sync="addForm"
            :before-close="submitFormCancel"
            width="580px"
        >
            <el-form
                ref="ruleForm"
                :model="add"
                :rules="rules"
                label-width="120px"
                class="demo-ruleForm"
            >
                <el-form-item label="品牌:" prop="brand">
                    <el-select v-model="add.brand" placeholder="旷视">
                        <el-option label="旷视" value="旷视" />
                    </el-select>
                </el-form-item>

                <el-form-item label="激活码:" prop="activationCode">
                    <el-input v-model="add.activationCode" />
                </el-form-item>
                <el-form-item label="可激活总数：" prop="frequencyAll">
                    <el-input v-model="add.frequencyAll" />
                </el-form-item>
                <el-form-item label="生成日期：" prop="birthDate">
                    <el-date-picker
                        v-model="add.birthDate"
                        type="date"
                        value-format="yyyy-MM-dd"
                        value="yyyy-MM-dd"
                        placeholder="选择日期"
                    />
                </el-form-item>
                <el-form-item label="学校名称" prop="schoolName">
                    <el-input v-model="add.schoolName" />
                </el-form-item>
                <el-form-item label="备注：">
                    <el-input v-model="add.remark" type="textarea" rows="4" />
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button @click="submitFormCancel">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确 定</el-button
                >
            </div>
        </el-dialog>
    </el-card>
</template>
<script>
import {
    updateFaceInfo,
    updateEchoInfo,
    selectFaceAuthorizeList,
    addFaceInfo,
    exportInfo,
    importFaceAuthorize,
} from "@/api/faceSystem.js";
import { getToken } from "@/utils/auth";
export default {
    data() {
        return {
            tableloading: false,
            token: "",
            // 表单验证
            rules: {
                brand: [
                    {
                        required: true,
                        message: "请选择品牌",
                        trigger: "change",
                    },
                ],
                activateState: [
                    {
                        required: true,
                        message: "请选择激活状态",
                        trigger: "change",
                    },
                ],
                activationCode: [
                    {
                        required: true,
                        message: "请输入激活码",
                        trigger: "change",
                    },
                    {
                        pattern: /^\w+$/,
                        message: "请输入正确的激活码",
                        trigger: "blur",
                    },
                ],
                frequencyAll: [
                    {
                        required: true,
                        message: "请输入可激活总数",
                        trigger: "change",
                    },
                    {
                        pattern: /^(?:[1-9]\d*|0)$/,
                        message: "请输入正确的激活总数",
                        trigger: "blur",
                    },
                ],
                birthDate: [
                    {
                        required: true,
                        message: "请选择日期",
                        trigger: "change",
                    },
                ],
                schoolName: [
                    {
                        required: true,
                        message: "请输入学校名称",
                        trigger: "change",
                    },
                ],
            },
            copyData: null,
            // 查询表单
            searchform: {
                number: "",
                MAC: "",
                school: "",
                frequency: "",
                activateState: "",
                brand: "",
                region: "0",
            },
            // 表格数据
            tableData: [],
            // 分页
            pagination: {
                pagesize: 10,
                current: 1,
                total: 10,
            },
            // 编辑
            editForm: false,
            editRemark: "",
            edit: {
                id: "",
                brandName: "",
                activateCode: "",
                createDate: "",
                totalActivateNumber: "",
                deviceMacAddress: "",
                schoolName: "",
                activateState: "",
                faceAuthorizeBrandId: "",
            },
            // 添加
            addForm: false,
            add: {
                brand: "",
                activationCode: "",
                frequencyAll: "",
                birthDate: "",
                schoolName: "",
                remark: "",
            },
            formLabelWidth: "120px",
            formlabeposition: "left",
            dialogVisible: false,
            errorPrompt: "",
            importId: "",
            editId: "",
            editfaceAuthorizeBrandId: "",
            action:
                process.env.VUE_APP_BASE_API +
                "/backstage/cloud-backstage/face/authorize/excel/ImportActivationCode",
        };
    },
    created() {
        this.selectFaceAuthorizeList();
        this.token = "Bearer " + getToken();
    },
    methods: {
        searchResetForm(searchform) {
            this.$refs[searchform].resetFields();
            (this.searchform.number = ""),
                (this.searchform.MAC = ""),
                (this.searchform.school = ""),
                (this.searchform.frequency = ""),
                (this.searchform.activateState = ""),
                (this.searchform.brand = ""),
                (this.searchform.region = "0");
            this.selectFaceAuthorizeList();
        },
        importSuccess(res) {
            if (res.code == 200 && res.data.errorCount == 0) {
                this.importId = res.data.id;
                this.importFaceAuthorize();
            } else {
                this.importError(res);
            }
        },
        // 导入错误 提示
        importError(res) {
            this.dialogVisible = true;
            if (res.code == 200) {
                let result = "";
                res.data.errors.map((str) => {
                    result += `${str}<br/><br/>`;
                });
                this.errorPrompt = result;
            } else if (res.code == -1) {
                this.dialogVisible = true;
                this.errorPrompt = res.msg;
            }
        },
        // 导入进数据库
        importFaceAuthorize() {
            this.tableloading = true;
            importFaceAuthorize(this.importId)
                .then(({ code, msg }) => {
                    if (code == 200) {
                        this.$message.success(msg);
                        this.selectFaceAuthorizeList();
                    }
                })
                .finally(() => {
                    this.tableloading = false;
                });
        },

        // 导出
        exportData() {
            let obj = {
                number: this.searchform.number,
                MAC: this.searchform.MAC,
                school: this.searchform.school,
                frequency: this.searchform.frequency,
                activateState: this.searchform.activateState,
                brand: this.searchform.brand,
            };
            exportInfo(obj, "人脸激活码使用记录统计表");
        },

        // 序号
        indexMethod(index) {
            return index + 1;
        },

        // 搜素
        handleSearch() {
            this.pagination.current = 1;
            this.selectFaceAuthorizeList();
        },
        // 查询表格接口
        selectFaceAuthorizeList() {
            this.tableloading = true;
            let obj = {
                size: this.pagination.pagesize,
                current: this.pagination.current,
                brandName: this.searchform.brand,
                schoolName: this.searchform.school,
                deviceMacAddress: this.searchform.MAC,
                activateCode: this.searchform.number,
                surplusActivateStatus: this.searchform.frequency,
                activateState: this.searchform.activateState,
            };
            selectFaceAuthorizeList(obj)
                .then(({ data, code }) => {
                    if (code === 200) {
                        const { records, total, size, current } = data;
                        this.pagination.total = total;
                        this.pagination.pagesize = size;
                        this.pagination.current = current;
                        this.tableData = records;
                    }
                })
                .finally(() => {
                    this.tableloading = false;
                });
        },

        // 新增列表
        handleAdd() {
            this.addForm = true;
        },
        // 保存
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.loadAddFaceInfo();
                }
            });
        },
        //关闭新增弹框
        submitFormCancel() {
            this.addForm = false;
            this.add.remark = "";
            this.resetForm("ruleForm");
        },
        resetForm(formName) {
            this.$nextTick(() => {
                this.$refs[formName].resetFields();
            });
        },
        // 新增接口
        async loadAddFaceInfo() {
            let obj = {
                brandName: this.add.brand,
                activateCode: this.add.activationCode,
                totalActivateNumber: this.add.frequencyAll,
                createDate: this.add.birthDate,
                schoolName: this.add.schoolName,
                remark: this.add.remark,
            };
            let { code, msg } = await addFaceInfo(obj);
            if (code === 200) {
                this.selectFaceAuthorizeList();
                this.$message.success(msg);
                this.submitFormCancel();
            } else {
                this.addForm = true;
            }
        },

        // 回显编辑表格列表
        handleEdit(row) {
            // 显示弹框
            this.editForm = true;
            this.updateEchoInfo(row);
        },
        // 编辑回显接口
        updateEchoInfo(item) {
            // debugger;
            let obj = {
                id: item.id,
                faceAuthorizeBrandId: item.faceAuthorizeBrandId,
            };
            this.editId = item.id;
            this.editfaceAuthorizeBrandId = item.faceAuthorizeBrandId;
            updateEchoInfo(obj).then(({ code, data }) => {
                if (code === 200 && data) {
                    this.editRemark = data.remark;
                    this.edit = data;
                    if (this.edit.activateState == null) {
                        this.edit.activateState = "";
                    }
                }
            });
        },
        // 编辑提交表格
        editSubmitForm() {
            this.updateFaceInfo();
            this.editForm = false;
        },
        // 编辑接口
        updateFaceInfo() {
            let obj = {
                id: this.editId,
                faceAuthorizeBrandId: this.editfaceAuthorizeBrandId,
                remark: this.editRemark,
                activateState: this.edit.activateState,
            };
            updateFaceInfo(obj).then(({ code, msg }) => {
                if (code === 200) {
                    this.$message.success(msg);
                    this.selectFaceAuthorizeList();
                }
            });
        },

        // 分页
        handleCurrentChange(val) {
            this.pagination.current = val;
            this.selectFaceAuthorizeList();
        },

        // 复制激活码
        handleCopy(index, row) {
            this.copyData = row.activateCode;
            this.copy(this.copyData);
        },
        copy(data) {
            let url = data;
            let oInput = document.createElement("input");
            oInput.value = url;
            document.body.appendChild(oInput);
            oInput.select(); // 选择对象;
            console.log(oInput.value);
            document.execCommand("Copy"); // 执行浏览器复制命令
            this.$message({
                message: "复制成功",
                type: "success",
            });
            oInput.remove();
        },
    },
};
</script>
<style lang="scss" scoped>
.btn {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    padding-top: 16px;
    border-top: 1px solid #ebeef5;
    span {
        margin: 10px;
    }
    .multipartFile {
        display: flex;
    }
    .download {
        justify-content: center;
        display: flex;
        align-items: center;
        margin: 10px;
        a {
            color: #409eff;
        }
    }
}

.paginationBlock {
    margin-top: 16px;
    text-align: right;
}
.activateStateClass {
    margin-left: 60px;
}
</style>
