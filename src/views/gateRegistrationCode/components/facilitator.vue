<template>
    <el-card shadow="never" class="card_warp">
        <!-- 搜索表单 -->
        <el-form ref="queryForm" :inline="true" :model="queryForm">
            <el-form-item label="服务商：" prop="schoolId">
                <el-select
                    filterable
                    v-model="queryForm.schoolId"
                    clearable
                    placeholder="全部"
                >
                    <el-option
                        v-for="item in findPartnerOption"
                        :key="item.id"
                        :label="item.partnerName"
                        :value="item.id"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="注册码：" prop="registrationCode">
                <el-input
                    v-model="queryForm.registrationCode"
                    placeholder="请输入注册码"
                ></el-input>
            </el-form-item>
            <el-form-item label="设备序列号：" prop="no">
                <el-input
                    v-model="queryForm.no"
                    placeholder="请输入设备序列号"
                ></el-input>
            </el-form-item>
            <el-form-item label="设备类型：" prop="deviceType">
                <el-select
                    v-model="queryForm.deviceType"
                    placeholder="请选择设备类型"
                    prop="deviceType"
                >
                    <el-option label="全部" :value="null"></el-option>
                    <el-option
                        v-for="(item, index) in equipmentTypeList"
                        :key="item.id + index"
                        :label="item.name"
                        :value="item.id"
                    ></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="注册码状态：" prop="status">
                <el-select
                    v-model="queryForm.status"
                    placeholder="请选择设备类型"
                    prop="status"
                >
                    <el-option label="全部" :value="null"></el-option>
                    <el-option
                        v-for="(item, key) in statusList"
                        :key="key"
                        :label="item.label"
                        :value="item.value"
                    ></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button
                    type="primary"
                    @click="handleSearch"
                    icon="el-icon-search"
                    >查询</el-button
                >
                <el-button @click="reset">重置</el-button>
            </el-form-item>
        </el-form>
        <!-- 功能按钮 -->
        <div class="btn_warp">
            <span>注册码管理</span>
            <div>
                <el-button
                    type="primary"
                    icon="el-icon-plus"
                    @click="addEdit(false)"
                    v-auth="'manage.facilitator.gateRegistrationCode.add'"
                    >新增注册码</el-button
                >
                <el-button
                    icon="el-icon-bottom"
                    style="margin-left: 16px"
                    type="primary"
                    plain
                    :loading="exportLoading"
                    v-auth="'manage.facilitator.gateRegistrationCode.export'"
                    @click="exportData"
                    >导出</el-button
                >
            </div>
        </div>
        <!-- 列表数据 -->
        <div class="content_warp" v-loading="tableloading">
            <el-table
                :data="list"
                style="width: 100%"
                ref="table"
                border
                :header-cell-style="{ background: '#fafafa', color: '#5b5d61' }"
            >
                <el-table-column
                    type="index"
                    width="50"
                    label="序号"
                    align="center"
                />
                <el-table-column
                    prop="schoolName"
                    label="服务商"
                    align="center"
                />
                <el-table-column
                    prop="merchantName"
                    label="商户"
                    align="center"
                />
                <el-table-column
                    prop="registrationCode"
                    label="注册码"
                    align="center"
                >
                    <template #default="{ row }">
                        <span
                            style="cursor: pointer"
                            @click="copy(row.registrationCode)"
                        >
                            {{ row.registrationCode || "-" }}</span
                        >
                    </template>
                </el-table-column>
                <el-table-column prop="no" label="设备序列号" align="center">
                    <template #default="{ row }">
                        <span style="cursor: pointer" @click="copy(row.no)">{{
                            row.no || "-"
                        }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="deviceType"
                    label="设备类型"
                    align="center"
                >
                    <template #default="{ row }">
                        <span>
                            {{ typeText[row.deviceType] }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="status"
                    label="注册码状态"
                    show-overflow-tooltip
                    align="center"
                >
                    <template #default="{ row }">
                        <span>
                            {{ statusText[row.status] }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="remark"
                    align="center"
                    label="备注"
                ></el-table-column>
                <el-table-column
                    label="操作"
                    width="120"
                    align="right"
                    fixed="right"
                >
                    <template #default="{ row }">
                        <el-link
                            type="primary"
                            icon="el-icon-edit"
                            v-auth="
                                'manage.facilitator.gateRegistrationCode.edit'
                            "
                            @click="addEdit(true, row)"
                            >编辑</el-link
                        >
                        <el-link
                            v-auth="
                                'manage.facilitator.gateRegistrationCode.retrieve'
                            "
                            type="primary"
                            style="margin-left: 10px"
                            @click="retrieve(row)"
                            v-if="row.status == 0"
                        >
                            回收</el-link
                        >
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <div class="paginationBlock">
                <el-pagination
                    background
                    class="pagination"
                    :current-page="pagination.pageNo"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="pagination.pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pagination.total"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </div>
        </div>
        <!-- 新增编辑 -->
        <FacilitatorForm
            ref="addEditRef"
            @refurbish="getList"
        ></FacilitatorForm>
    </el-card>
</template>

<script>
import FacilitatorForm from "./facilitatorForm.vue";
import {
    getEquipmentType,
    selectRegistrationList,
    queryRegistrationDetail,
    retrieveCode,
    reportingExportInfo,
} from "@/api/gateRegistrationCode.js";
import { getFindPartner } from "@/api/gateRegistrationCode.js";
import { statusList, statusText, copy } from "../data";
export default {
    name: "Facilitator",
    components: {
        FacilitatorForm,
    },
    data() {
        return {
            findPartnerOption: [],
            tenantType: 2, // 租户类型 : 1.学校、2.商户
            statusList,
            statusText,
            copy,
            queryForm: {},
            equipmentTypeList: [], // 设备列表
            typeText: {},
            exportLoading: false,
            tableloading: false,
            list: [],
            pagination: {
                pageSize: 10,
                pageNo: 1,
                total: 0,
            },
        };
    },
    methods: {
        getList() {
            const obj = {
                ...this.queryForm,
                ...this.pagination,
                tenantType: this.tenantType,
            };
            selectRegistrationList(obj).then((res) => {
                this.list = res.data.list;
                this.pagination.pageNo = res.data.pageNo;
                this.pagination.pageSize = res.data.pageSize;
                this.pagination.total = res.data.total;
            });
        },
        // 编辑回显
        queryRegistrationDetail(data) {
            queryRegistrationDetail({
                id: data.id,
                tenantType: this.tenantType,
            }).then((res) => {
                const { data } = res;
                this.$refs.addEditRef.open(true, data, this.equipmentTypeList);
            });
        },
        getFindPartnerFn() {
            getFindPartner({}).then((res) => {
                this.findPartnerOption = res.data;
            });
        },
        addEdit(isEdit, form = {}) {
            this.isEdit = isEdit;
            if (isEdit) {
                this.queryRegistrationDetail(form);
            } else {
                this.$refs.addEditRef.open(
                    isEdit,
                    form,
                    this.equipmentTypeList
                );
            }
        },
        // 回收
        retrieve(data) {
            this.$confirm("回收后硬件无法使用，确定回收该注册码？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    retrieveCode({ id: data.id }).then((res) => {
                        this.$message.success(res.message);
                        this.getList();
                    });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消删除",
                    });
                });
        },
        // 导出
        exportData() {
            this.exportLoading = true;
            reportingExportInfo(
                {
                    ...this.queryForm,
                    tenantType: this.tenantType,
                },
                "注册码系统列表"
            ).finally(() => {
                this.exportLoading = false;
                this.$message.success("导出成功");
            });
        },
        // 搜素
        handleSearch() {
            this.pagination.pageNo = 1;
            this.getList();
        },
        // 重置
        reset() {
            this.queryForm = {};
            this.pagination.pageNo = 1;
            this.getList();
        },
        // 分页
        handleSizeChange(val) {
            this.pagination.pageNo = 1;
            this.pagination.pageSize = val;
            this.getList();
        },
        handleCurrentChange(val) {
            this.pagination.pageNo = val;
            this.getList();
        },
        // 设备管理
        async getEquipmentTypeFn() {
            await getEquipmentType({ tenantType: this.tenantType }).then(
                (res) => {
                    this.equipmentTypeList = res.data || [];
                    res.data.forEach((i) => {
                        this.typeText[i.id] = i.name;
                    });
                }
            );
        },
    },
    async created() {
        await this.getEquipmentTypeFn();
        this.getFindPartnerFn();
        this.getList();
    },
};
</script>

<style lang="scss" scoped>
.btn_warp {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0px;
    border-top: 1px solid #ebeef5;
}
.paginationBlock {
    margin-top: 16px;
    text-align: right;
}
</style>
