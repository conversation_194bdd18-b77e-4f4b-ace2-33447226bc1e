<template>
    <div>
        <!-- 新增 -->
        <el-dialog
            :title="isEdit ? '编辑注册码' : '新建注册码'"
            :visible.sync="show"
            :before-close="cancel"
            :wrapper-closable="false"
            width="900px"
        >
            <el-form
                ref="ruleForm"
                :model="form"
                :rules="addEditRule"
                label-width="120px"
            >
                <el-form-item label="服务商名称：" prop="schoolId">
                    <el-select
                        filterable
                        v-model="form.schoolId"
                        :disabled="isEdit"
                        style="width: 100%"
                        placeholder="请选择服务商名称"
                    >
                        <el-option
                            v-for="item in findPartnerOption"
                            :key="item.id"
                            :label="item.partnerName"
                            :value="item.id"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="设备类型：" prop="deviceType">
                    <el-select
                        style="width: 100%"
                        v-model="form.deviceType"
                        placeholder="请选择设备类型"
                        :disabled="isEdit"
                        filterable
                    >
                        <el-option
                            v-for="(item, index) in equipmentTypeList"
                            :key="item.id + index"
                            :label="item.name"
                            :value="item.id"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="注册码数量：" v-if="!isEdit">
                    <el-input-number
                        v-model="form.registrationCodeNumber"
                        :disabled="isEdit"
                        :min="1"
                        :max="999"
                    ></el-input-number>
                </el-form-item>
                <div v-if="isEdit">
                    <el-form-item label="注册码状态：">
                        <el-select
                            style="width: 100%"
                            v-model="form.status"
                            placeholder="请选择设备类型"
                            :disabled="isEdit"
                        >
                            <el-option
                                v-for="item in statusList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="商户名称：">
                        <el-input
                            :disabled="isEdit"
                            style="width: 100%"
                            v-model="form.merchantName"
                        ></el-input>
                    </el-form-item>
                </div>
                <el-form-item label="备注：" prop="remark">
                    <el-input
                        type="textarea"
                        style="width: 100%"
                        v-model="form.remark"
                        placeholder="请输入备注"
                        maxlength="200"
                        show-word-limit
                    ></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <div class="footer">
                    <el-button
                        type="primary"
                        :loading="addLoading"
                        @click="confirm"
                        >确定</el-button
                    >
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {
    addRegistration,
    updateRegistration,
    getFindPartner,
} from "@/api/gateRegistrationCode.js";
import { addEditRule, statusList } from "../data";
export default {
    name: "FacilitatorForm",
    data() {
        return {
            statusList,
            addEditRule,
            isEdit: false,
            show: false,
            form: {},
            addLoading: false,
            equipmentTypeList: [],
            findPartnerOption: [],
            tenantType: 2,
        };
    },
    methods: {
        getFindPartnerFn() {
            getFindPartner({}).then((res) => {
                this.findPartnerOption = res.data;
            });
        },
        open(status, data, equipment) {
            this.equipmentTypeList = equipment;
            this.getFindPartnerFn();
            this.isEdit = status;
            this.show = true;
            this.form = data;
            console.log(data);
        },

        // 新增接口
        addRegistrationFn() {
            this.addLoading = true;
            addRegistration({
                ...this.form,
                tenantType: this.tenantType,
            })
                .then((res) => {
                    this.$message.success(res.message);
                    this.cancel();
                })
                .finally(() => {
                    this.addLoading = false;
                });
        },
        // 编辑接口
        updateRegistrationFn() {
            this.addLoading = true;
            const obj = {
                ...this.form,
                tenantType: this.tenantType,
            };
            updateRegistration(obj)
                .then((res) => {
                    this.$message.success(res.message);
                    this.cancel();
                })
                .finally(() => {
                    this.addLoading = false;
                });
        },
        // 取消
        cancel() {
            this.$refs.ruleForm.resetFields();
            this.show = false;
            this.$emit("refurbish");
        },
        // 确定
        confirm() {
            this.$refs.ruleForm.validate((valid) => {
                if (valid) {
                    if (this.isEdit) {
                        this.updateRegistrationFn();
                    } else {
                        this.addRegistrationFn();
                    }
                } else {
                    return false;
                }
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.footer {
    display: flex;
    justify-content: flex-end;
}
:deep(.el-dialog__footer) {
    border-top: 1px solid #d9d9d9;
}
:deep(.el-dialog__header) {
    border-bottom: 1px solid #d9d9d9;
}
</style>
