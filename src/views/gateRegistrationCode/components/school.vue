<template>
    <el-card shadow="never" class="card_warp">
        <!-- 搜索表单 -->
        <el-form
            ref="gateCodeFrom"
            :inline="true"
            :model="gateCodeFrom"
            class="demo-form-inline"
        >
            <el-form-item label="学校：" prop="schoolName">
                <el-input
                    v-model="gateCodeFrom.schoolName"
                    placeholder="请输入学校名称"
                ></el-input>
            </el-form-item>
            <el-form-item label="注册码：" prop="registrationCode">
                <el-input
                    v-model="gateCodeFrom.registrationCode"
                    placeholder="请输入注册码"
                ></el-input>
            </el-form-item>
            <el-form-item label="设备序列号：" prop="no">
                <el-input
                    v-model="gateCodeFrom.no"
                    placeholder="请输入设备序列号"
                ></el-input>
            </el-form-item>
            <el-form-item label="设备类型：" prop="deviceType">
                <el-select
                    v-model="gateCodeFrom.deviceType"
                    placeholder="请选择设备类型"
                    prop="deviceType"
                >
                    <el-option label="全部" value=""></el-option>
                    <el-option
                        v-for="(item, index) in equipmentTypeList"
                        :key="item.id + index"
                        :label="item.name"
                        :value="item.id"
                    ></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="onSubmit">查询</el-button>
                <el-button @click="handleSearchReset">重 置</el-button>
            </el-form-item>
        </el-form>
        <!-- 功能按钮 -->
        <div class="btn_warp">
            <span>注册码管理</span>
            <div>
                <el-button
                    type="primary"
                    icon="el-icon-plus"
                    v-auth="'manage.school.gateRegistrationCode.add'"
                    @click="gateCodeFromAddEdit(false)"
                    >新增注册码</el-button
                >

                <el-button
                    icon="el-icon-bottom"
                    style="margin-left: 16px"
                    type="primary"
                    v-auth="'manage.school.gateRegistrationCode.export'"
                    plain
                    :loading="exportLoading"
                    @click="exportData"
                    >导出</el-button
                >
            </div>
        </div>
        <!-- 列表数据 -->
        <div class="content_warp" v-loading="tableloading">
            <el-table
                :data="list"
                style="width: 100%"
                ref="table"
                border
                :header-cell-style="{ background: '#fafafa', color: '#5b5d61' }"
            >
                <el-table-column
                    type="index"
                    width="50"
                    label="序号"
                    align="center"
                ></el-table-column>
                <el-table-column
                    prop="registrationCode"
                    label="注册码"
                    align="center"
                >
                    <template slot-scope="scope">
                        <span
                            style="cursor: pointer"
                            @click="registrationCopy(scope.$index, scope.row)"
                        >
                            {{ scope.row.registrationCode || "-" }}</span
                        >
                    </template>
                </el-table-column>
                <el-table-column
                    prop="schoolName"
                    label="学校"
                    align="center"
                ></el-table-column>
                <el-table-column prop="no" label="设备序列号" align="center">
                    <template slot-scope="scope">
                        <span
                            style="cursor: pointer"
                            @click="noCopy(scope.$index, scope.row)"
                            >{{ scope.row.no || "-" }}</span
                        >
                    </template>
                </el-table-column>
                <el-table-column
                    prop="deviceType"
                    label="设备类型"
                    align="center"
                >
                    <template slot-scope="scope">
                        <span>
                            {{ typeList[Number(scope.row.deviceType)] || "-" }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="status"
                    label="注册码状态"
                    show-overflow-tooltip
                    align="center"
                >
                    <template slot-scope="scope">
                        <span>
                            {{
                                ["未使用", "已使用"][
                                    Number(scope.row.status)
                                ] || "-"
                            }}
                        </span>
                    </template>
                </el-table-column>

                <el-table-column
                    prop="remark"
                    align="center"
                    label="备注"
                ></el-table-column>

                <!-- <el-table-column
                    prop="status"
                    label="作废时间"
                    show-overflow-tooltip
                    align="center"
                >
                    <template slot-scope="scope">
                        <span>
                            2024-12-12 12:12
                        </span>
                    </template>
                </el-table-column> -->
                <el-table-column
                    label="操作"
                    width="200"
                    align="right"
                    fixed="right"
                >
                    <template slot-scope="scope">
                        <el-link
                            v-auth="
                                'manage.school.gateRegistrationCode.transfer'
                            "
                            type="primary"
                            icon="el-icon-s-operation"
                            style="padding-right: 8px"
                            @click="gateCodeFromCodeTransfer(scope.row)"
                        >
                            转移
                        </el-link>
                        <el-link
                            v-auth="'manage.school.gateRegistrationCode.edit'"
                            type="primary"
                            icon="el-icon-edit"
                            style="padding-right: 8px"
                            @click="gateCodeFromAddEdit(true, scope.row)"
                        >
                            编辑
                        </el-link>
                        <el-link
                            v-auth="'manage.school.gateRegistrationCode.del'"
                            type="primary"
                            icon="el-icon-delete"
                            style="color: red"
                            @click="gateCodeFromDelete(scope.row)"
                        >
                            删除
                        </el-link>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!-- 分页 -->
        <div class="paginationBlock">
            <el-pagination
                style="margin-top: 10px"
                :current-page="pagination.current"
                :page-sizes="[10, 20, 30, 40]"
                :page-size="pagination.size"
                background
                layout="total, sizes, prev, pager, next, jumper"
                :total="pagination.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <!-- 新增编辑表单 -->
        <el-dialog
            :title="dialogTitle"
            width="30%"
            :visible.sync="dialogSchoolVisible"
            @close="cancelForm('dialogSchoolForm')"
        >
            <el-form
                :rules="rules"
                ref="dialogSchoolForm"
                label-position="right"
                label-width="100px"
                :model="dialogSchoolForm"
            >
                <div v-if="dialogTitle == '新增注册码'">
                    <el-form-item label="学校名称" prop="schoolId">
                        <el-select
                            style="width: 100%"
                            v-model="dialogSchoolForm.schoolId"
                            filterable
                            default-first-option
                            popper-class="selectClass"
                            placeholder="请选择"
                        >
                            <el-option
                                v-for="(item, index) in supplierSchoolList"
                                :key="index"
                                :label="item.name"
                                :value="item.id"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="设备类型" prop="deviceType">
                        <el-select
                            style="width: 100%"
                            v-model="dialogSchoolForm.deviceType"
                            placeholder="请选择设备类型"
                        >
                            <el-option
                                v-for="(item, index) in equipmentTypeList"
                                :key="item.id + index"
                                :label="item.name"
                                :value="item.id"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        label="注册码数量"
                        prop="registrationCodeNumber"
                        v-if="dialogTitle == '新增注册码'"
                    >
                        <el-input-number
                            v-model="dialogSchoolForm.registrationCodeNumber"
                            @change="handleChangeNum"
                            :min="1"
                            :max="999"
                        ></el-input-number>
                    </el-form-item>
                </div>
                <div v-if="dialogTitle == '编辑注册码'">
                    <el-form-item label="注册码：" prop="registrationCode">
                        {{ dialogSchoolForm.registrationCode || "" }}
                    </el-form-item>
                    <el-form-item label="设备类型：" prop="deviceType">
                        {{
                            typeList[Number(dialogSchoolForm.deviceType)] || "-"
                        }}
                    </el-form-item>
                    <el-form-item label="学校名称：" prop="schoolId">
                        {{ dialogSchoolForm.schoolName || "" }}
                    </el-form-item>
                </div>
                <el-form-item label="备注" prop="remark">
                    <el-input
                        type="textarea"
                        style="width: 100%"
                        v-model="dialogSchoolForm.remark"
                        placeholder="请输入备注"
                        maxlength="200"
                        show-word-limit
                    ></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog_footer">
                <el-button @click="cancelForm('dialogSchoolForm')"
                    >取 消</el-button
                >
                <el-button
                    type="primary"
                    :loading="addLoading"
                    @click="gateCodeFromOnSubmit('dialogSchoolForm')"
                    >确 定</el-button
                >
            </div>
        </el-dialog>
        <!-- 删除理由 -->
        <el-dialog
            title="删除理由"
            width="30%"
            :visible.sync="reasonDeletionOpen"
            @close="cancelReasonDeletionForm"
        >
            <el-form
                :rules="reasonRules"
                ref="reasonDeletionRef"
                label-position="right"
                :model="reasonDeletionForm"
            >
                <el-form-item label="" prop="remark">
                    <el-input
                        type="textarea"
                        style="width: 100%"
                        v-model="reasonDeletionForm.remark"
                        :autosize="{ minRows: 4, maxRows: 10 }"
                        placeholder="请输入删除理由"
                        maxlength="200"
                        show-word-limit
                    ></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog_footer">
                <el-button @click="cancelReasonDeletionForm">取 消</el-button>
                <el-button
                    type="primary"
                    :loading="addLoading"
                    @click="cancelReasonDeletionSubmit"
                >
                    确 定
                </el-button>
            </div>
        </el-dialog>

        <!-- 注册码转移 -->
        <el-dialog
            title="注册码转移"
            width="30%"
            :visible.sync="codeTransferOpen"
            @close="cancelReasonCodeTransferForm"
        >
            <el-form
                :rules="codeRules"
                ref="codeTransRef"
                label-position="right"
                :model="codeTransferForm"
            >
                <el-form-item label="" prop="schoolId">
                    <el-select
                        style="width: 100%"
                        v-model="codeTransferForm.schoolId"
                        filterable
                        default-first-option
                        popper-class="selectClass"
                        placeholder="请选择学校"
                    >
                        <el-option
                            v-for="(item, index) in supplierSchoolList"
                            :key="index"
                            :label="item.name"
                            :value="item.id"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="备 注" prop="remark">
                    <el-input
                        type="textarea"
                        style="width: 100%"
                        v-model="codeTransferForm.remark"
                        :autosize="{ minRows: 4, maxRows: 10 }"
                        placeholder="请输入备注"
                        maxlength="200"
                        show-word-limit
                    ></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog_footer">
                <el-button @click="cancelReasonCodeTransferForm"
                    >取 消</el-button
                >
                <el-button
                    type="primary"
                    :loading="addLoading"
                    @click="cancelReasonCodeTransferSubmit"
                >
                    确 定
                </el-button>
            </div>
        </el-dialog>
    </el-card>
</template>
<script>
import {
    getEquipmentType,
    selectRegistrationList,
    addRegistration,
    updateRegistration,
    queryRegistrationDetail,
    querySchoolInfoList,
    reportingExportInfo,
    getCodeTransfer,
    getCodeDelete,
} from "@/api/gateRegistrationCode.js";

export default {
    name: "School",
    data() {
        return {
            tenantType: 1, // 租户类型 : 1.学校、2.商户
            // talent
            typeList: {
                1: "人脸机",
                2: "班牌",
                3: "考勤机",
                4: "中性版班牌",
                5: "一体机",
                6: "借还机",
                7: "访客机",
                8: "门禁机",
                9: "会议机",
                10: "鸿蒙班牌",
                11: "画屏",
                20: "馆员工作站",
                22: "查询机",
                23: "盘点车",
                24: "安全门",
                25: "德育兑换机",
                26: "微型图书馆",
                27: "手持盘点仪",
                28: "摄像机",
                29: "德育积分机",
                35: "智慧点餐机",
            },
            equipmentTypeList: [], // 设备列表
            tableloading: false,
            addLoading: false,
            gateCodeFrom: {
                schoolName: "",
                registrationCode: "",
                no: "",
                deviceType: "",
            },
            list: [],
            pagination: {
                current: 1,
                size: 10,
                total: 0,
            },
            dialogSchoolVisible: false,
            dialogSchoolForm: {
                deviceType: "",
                remark: "",
                registrationCodeNumber: 1,
                schoolId: "",
                schoolName: "",
                registrationCode: "",
                reasonDeletionName: "",
                codeTransferSchool: "",
                reasonDeletionRemark: "",
            },
            supplierSchoolList: [],
            dialogTitle: "",
            data: "",
            rules: {
                deviceType: [
                    {
                        required: true,
                        message: "请选择设备类型",
                        trigger: "change",
                    },
                ],
                schoolId: [
                    {
                        required: true,
                        message: "请选择学校名称",
                        trigger: "change",
                    },
                ],
                registrationCode: [
                    {
                        required: true,
                        message: "请填写注册码",
                        trigger: "change",
                    },
                ],
            },
            exportLoading: false,
            copyData: "",
            reasonDeletionOpen: false,
            codeTransferOpen: false,
            reasonDeletionForm: {
                id: "",
                remark: "",
            },
            reasonRules: {
                remark: [
                    {
                        required: true,
                        message: "请输入备注",
                        trigger: "change",
                    },
                ],
            },
            codeTransferForm: {
                id: "",
                remark: "",
                schoolId: null,
            },
            codeRules: {
                remark: [
                    {
                        required: true,
                        message: "请输入备注",
                        trigger: "change",
                    },
                ],
                schoolId: [
                    {
                        required: true,
                        message: "请选择学校",
                        trigger: "change",
                    },
                ],
            },
        };
    },
    methods: {
        // 导出
        exportData() {
            this.exportLoading = true;
            let obj = {
                ...this.gateCodeFrom,
                tenantType: this.tenantType,
            };
            reportingExportInfo(obj, "注册码系统列表").finally(() => {
                this.exportLoading = false;
                this.$message.success("导出成功");
            });
        },
        // 复制激活码
        registrationCopy(index, row) {
            this.copyData = row.registrationCode;
            this.copy(this.copyData);
        },
        noCopy(index, row) {
            this.copyData = row.no;
            this.copy(this.copyData);
        },
        copy(data) {
            let url = data;
            let oInput = document.createElement("input");
            oInput.value = url;
            document.body.appendChild(oInput);
            oInput.select(); // 选择对象;
            console.log(oInput.value);
            document.execCommand("Copy"); // 执行浏览器复制命令
            this.$message({
                message: "复制成功",
                type: "success",
            });
            oInput.remove();
        },
        // 学校接口
        searchSchoolList() {
            this.tableloading = true;
            const obj = {
                current: 1,
                size: 999,
            };
            querySchoolInfoList(obj).then((res) => {
                this.tableloading = false;
                this.supplierSchoolList = res.data;
            });
        },
        selectRegistrationList() {
            const obj = {
                ...this.gateCodeFrom,
                pageNo: this.pagination.current,
                pageSize: this.pagination.size,
                tenantType: this.tenantType,
            };
            selectRegistrationList(obj).then((res) => {
                this.list = res.data.list;
                this.pagination.current = res.data.pageNo;
                this.pagination.size = res.data.pageSize;
                this.pagination.total = res.data.total;
            });
        },
        // 新增编辑按钮
        gateCodeFromAddEdit(status, data) {
            this.searchSchoolList();
            if (status == true) {
                this.data = data;
                this.dialogSchoolVisible = true;
                this.dialogTitle = "编辑注册码";
                this.queryRegistrationDetail(data);
            } else if (status == false) {
                this.dialogTitle = "新增注册码";
                this.dialogSchoolVisible = true;
                if (this.$refs.dialogSchoolForm) {
                    this.$refs.dialogSchoolForm.resetFields();
                }
            }
        },
        // 编辑回显
        queryRegistrationDetail() {
            queryRegistrationDetail({
                id: this.data.id,
                tenantType: this.tenantType,
            }).then((res) => {
                const { data } = res;
                this.dialogSchoolForm = {
                    deviceType: data.deviceType,
                    remark: data.remark,
                    registrationCodeNumber: data.registrationCodeNumber,
                    schoolId: data.schoolId,
                    schoolName: data.schoolName,
                    registrationCode: data.registrationCode,
                };
            });
        },
        // 弹框确定按钮
        gateCodeFromOnSubmit(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    if (this.dialogTitle == "新增注册码") {
                        this.addRegistration();
                    } else if (this.dialogTitle == "编辑注册码") {
                        this.updateRegistration();
                    }
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        // 编辑接口
        updateRegistration() {
            this.addLoading = true;
            const obj = {
                ...this.dialogSchoolForm,
                id: this.data.id,
                tenantType: this.tenantType,
            };
            updateRegistration(obj)
                .then((res) => {
                    this.selectRegistrationList();
                    this.$message.success(res.message);
                })
                .finally(() => {
                    this.dialogSchoolVisible = false;
                    this.addLoading = false;
                });
        },
        // 新增接口
        addRegistration() {
            this.addLoading = true;
            const obj = {
                ...this.dialogSchoolForm,
                tenantType: this.tenantType,
            };
            addRegistration(obj)
                .then((res) => {
                    this.$message.success(res.message);
                    this.selectRegistrationList();
                })
                .finally(() => {
                    this.dialogSchoolVisible = false;
                    this.addLoading = false;
                });
        },
        cancelForm(formName) {
            this.dialogSchoolVisible = false;
            this.$refs[formName].resetFields();
            this.selectRegistrationList();
        },
        // 分页
        handleSizeChange(val) {
            this.pagination.current = 1;
            this.pagination.size = val;
            this.selectRegistrationList();
        },
        handleCurrentChange(val) {
            this.pagination.current = val;
            this.selectRegistrationList();
        },
        onSubmit() {
            this.pagination.current = 1;
            this.selectRegistrationList();
        },
        handleSearchReset() {
            this.$refs.gateCodeFrom.resetFields();
            this.selectRegistrationList();
        },
        // 计数器
        handleChangeNum(value) {
            console.log(value);
        },
        // 设备管理
        getEquipmentTypeFn() {
            getEquipmentType({ tenantType: this.tenantType }).then((res) => {
                this.equipmentTypeList = res.data || [];
                const arr = res.data.map((item) => item.name);
                console.log(res.data, "res.data");
                this.typeList = [""].concat[arr] || {
                    1: "人脸机",
                    2: "班牌",
                    3: "考勤机",
                    4: "中性版班牌",
                    5: "一体机",
                    6: "借还机",
                    7: "访客机",
                    8: "门禁机",
                    9: "会议机",
                    10: "鸿蒙班牌",
                    11: "画屏",
                    20: "馆员工作站",
                    22: "查询机",
                    23: "盘点车",
                    24: "安全门",
                    25: "德育兑换机",
                    26: "微型图书馆",
                    27: "手持盘点仪",
                    28: "摄像机",
                    29: "德育积分机",
                    35: "智慧点餐机",
                };
            });
        },
        // 打开删除理由弹框
        gateCodeFromDelete(item) {
            this.reasonDeletionOpen = true;
            this.reasonDeletionForm.id = item.id;
        },
        // 关闭删除理由弹框
        cancelReasonDeletionForm() {
            this.reasonDeletionOpen = false;
            this.$refs.reasonDeletionRef.resetFields();
        },
        // 提交删除理由弹框
        cancelReasonDeletionSubmit() {
            const _this = this;
            this.$refs.reasonDeletionRef.validate((valid) => {
                if (valid) {
                    getCodeDelete(_this.reasonDeletionForm).then((res) => {
                        _this.$message.success(res.message);
                        this.cancelReasonDeletionForm();
                        this.selectRegistrationList();
                    });
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },

        //  打开注册码转移弹框
        gateCodeFromCodeTransfer(item) {
            this.codeTransferOpen = true;
            this.codeTransferForm.id = item.id;
            this.searchSchoolList();
        },
        // 关闭删除注册码转移弹框
        cancelReasonCodeTransferForm() {
            this.codeTransferOpen = false;
            this.$refs.codeTransRef.resetFields();
        },
        // 提交删除注册码转移弹框
        cancelReasonCodeTransferSubmit() {
            const _this = this;
            this.$refs.codeTransRef.validate((valid) => {
                if (valid) {
                    getCodeTransfer(_this.codeTransferForm).then((res) => {
                        _this.$message.success(res.message);
                        this.cancelReasonCodeTransferForm();
                        this.selectRegistrationList();
                    });
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
    },
    async created() {
        await this.getEquipmentTypeFn();
        await this.selectRegistrationList();
    },
};
</script>

<style lang="scss" scoped>
.btn_warp {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0px;
    border-top: 1px solid #ebeef5;
}

.paginationBlock {
    margin-top: 16px;
    text-align: right;
}
</style>
