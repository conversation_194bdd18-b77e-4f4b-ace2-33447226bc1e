export const statusList = [
    {
        value: 2,
        label: "已回收",
    },
    {
        value: 0,
        label: "未使用",
    },
    {
        value: 1,
        label: "已使用",
    },
];

export const statusText = {
    2: "已回收",
    0: "未使用",
    1: "已使用",
};

export function copy(data) {
    let url = data;
    let oInput = document.createElement("input");
    oInput.value = url;
    document.body.appendChild(oInput);
    oInput.select(); // 选择对象;
    console.log(oInput.value);
    document.execCommand("Copy"); // 执行浏览器复制命令
    this.$message({
        message: "复制成功",
        type: "success",
    });
    oInput.remove();
}

export const addEditRule = {
    deviceType: [
        {
            required: true,
            message: "请选择设备类型",
        },
    ],
    schoolId: [
        {
            required: true,
            message: "请输入服务商名称",
        },
    ],
};
