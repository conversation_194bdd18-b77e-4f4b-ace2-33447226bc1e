<template>
    <div class="registration_code">
        <el-tabs v-model="active" @tab-click="handleClick">
            <el-tab-pane label="学校" name="School" />
            <el-tab-pane label="服务商" name="Facilitator" />
        </el-tabs>
        <component :is="componentName"></component>
    </div>
</template>

<script>
import School from "./components/school.vue";
import Facilitator from "./components/facilitator.vue";
export default {
    components: { School, Facilitator },
    data() {
        return {
            active: "School",
            componentName: School,
        };
    },
    methods: {
        handleClick(active) {
            this.componentName = active.name;
        },
    },
};
</script>

<style lang="scss" scoped>
.registration_code {
    background-color: #fff;
    padding: 20px;
}
</style>
