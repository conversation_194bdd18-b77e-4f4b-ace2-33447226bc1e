<!--
 * @Description:  合作管理
 * @Version: 2.0
 * @Author: jing<PERSON>
 * @Date: 2021-11-03 10:09:20
 * @LastEditors: jingrou
 * @LastEditTime: 2022-07-28 17:51:04
-->
<template>
    <div class="cooperation">
        <!-- 表单 -->
        <div class="cooperation_form">
            <el-form
                ref="cooperationForm"
                :inline="true"
                :model="cooperationForm"
                label-width="87px"
            >
                <el-form-item label="联系人姓名:">
                    <el-input
                        placeholder="请输入联系人姓名"
                        v-model="cooperationForm.contactName"
                    ></el-input>
                </el-form-item>
                <el-form-item
                    label="联系人手机号:"
                    class="cooperation_contactName"
                >
                    <el-input
                        placeholder="请输入联系人手机号"
                        v-model="cooperationForm.contactPhone"
                    ></el-input>
                </el-form-item>
                <el-form-item class="cooperation_searchform">
                    <el-button type="primary" @click="searchCooperation"
                        >查询</el-button
                    >
                    <el-button @click="handleSearchReset">重置</el-button>
                </el-form-item>
            </el-form>
        </div>
        <!-- 按钮操作 -->
        <div class="cooperation_btn">
            <span>合作管理</span>
            <div>
                <!-- <el-button
					type="primary"
					@click="supplierAddForm"
					icon="el-icon-plus"
					>新增应用提供商
				</el-button>-->
            </div>
        </div>
        <!-- 表格 -->
        <div v-loading="loading">
            <el-table
                :data="cooperationTableData"
                border
                style="width: 100%"
                :header-cell-style="{
                    background: '#fafafa',
                    color: '#d2d2d2',
                }"
            >
                <el-table-column
                    prop
                    align="center"
                    label="序号"
                    type="index"
                    :index="indexMethod"
                    :resizable="false"
                    width="50"
                />
                <el-table-column
                    prop="solutionName"
                    label="解决方案名称"
                    align="left"
                    width="250"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="contactName"
                    label="联系人名称"
                    show-overflow-tooltip
                    align="left"
                    width="150"
                />
                <el-table-column
                    prop="contactPhone"
                    label="联系人手机号"
                    show-overflow-tooltip
                    align="left"
                    width="150"
                />
                <el-table-column
                    prop="companyName"
                    label="公司名称"
                    show-overflow-tooltip
                    align="left"
                />
                <el-table-column
                    prop="province"
                    label="省份"
                    show-overflow-tooltip
                    align="left"
                />
                <el-table-column
                    prop="city"
                    label="城市"
                    show-overflow-tooltip
                    align="left"
                />
                <el-table-column
                    prop="createTime"
                    label="创建时间"
                    align="center"
                    width="200"
                    show-overflow-tooltip
                />
                <el-table-column
                    label="操作"
                    align="right"
                    width="150"
                    fixed="right"
                >
                    <template slot-scope="scope">
                        <el-link
                            v-auth="'manage.cooperation.del'"
                            type="danger"
                            @click="handleRemoveHitn(scope.row)"
                        >
                            删除
                            <i class="el-icon-delete" />
                        </el-link>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 分页 -->
        <div class="paginationBlock">
            <el-pagination
                style="margin-top: 10px"
                :current-page="pagination.pageNo"
                :page-sizes="[10, 20, 30, 40]"
                :page-size="pagination.pageSize"
                background
                layout="total, sizes, prev, pager, next, jumper"
                :total="pagination.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
    </div>
</template>

<script>
import {
    getCooperationList,
    deleteCooperation,
} from "@/api/guanwangManagement.js";
export default {
    data() {
        return {
            cooperationForm: {
                contactName: "",
                contactPhone: "",
            },
            cooperationTableData: [],
            pagination: {
                pageSize: 10,
                pageNo: 1,
                total: 0,
            },
            loading: false,
        };
    },
    methods: {
        //重置
        handleSearchReset() {
            this.$refs.cooperationForm.resetFields();
            this.cooperationForm.contactName = "";
            this.cooperationForm.contactPhone = "";
            this.getCooperationList();
        },
        // 表格列表
        getCooperationList() {
            this.loading = true;
            const obj = {
                pageSize: this.pagination.pageSize,
                pageNo: this.pagination.pageNo,
                contactName: this.cooperationForm.contactName,
                contactPhone: this.cooperationForm.contactPhone,
            };
            getCooperationList(obj)
                .then((res) => {
                    this.pagination.total = res.data.total;
                    this.pagination.pageSize = res.data.pageSize;
                    this.pagination.pageNo = res.data.pageNo;
                    this.cooperationTableData = res.data.list;
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        // 删除
        handleRemoveHitn(row) {
            this.$confirm("是否确定删除此合作?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    deleteCooperation({ id: row.id }).then(({ message }) => {
                        this.$message.success(message);
                        this.getCooperationList();
                    });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消删除",
                    });
                });
        },
        // 查询按钮
        searchCooperation() {
            this.getCooperationList();
        },
        // 分页
        handleSizeChange(val) {
            this.pagination.pageNo = 1;
            this.pagination.pageSize = val;
            this.getCooperationList();
        },
        handleCurrentChange(val) {
            this.pagination.pageNo = val;
            this.getCooperationList();
        },
        // 序号
        indexMethod(index) {
            return index + 1;
        },
    },
    created() {
        this.getCooperationList();
    },
};
</script>

<style lang="scss" scoped>
.cooperation {
    padding: 20px;
    background-color: #fff;
    .cooperation_btn {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;
        padding-top: 16px;
        border-top: 1px solid #ebeef5;
        span {
            margin: 10px;
        }
        div {
            display: flex;
        }
    }
    .paginationBlock {
        margin-top: 16px;
        text-align: right;
    }
}
</style>
<style lang="scss">
.cooperation {
    .cooperation_searchform {
        .el-form-item__content {
            margin-left: 20px;
        }
    }
    .cooperation_contactName {
        .el-form-item__label {
            width: 120px !important;
        }
    }
}
</style>
