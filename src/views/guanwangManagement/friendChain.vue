<!--
 * @Description: 
 * @Version: 2.0
 * @Author: jingrou
 * @Date: 2021-11-03 10:09:20
 * @LastEditors: jingrou
 * @LastEditTime: 2022-07-25 17:11:08
-->
<template>
    <div class="friendChain">
        <!-- 表单 -->
        <div class="friendChain_form">
            <el-form
                ref="friendChainForm"
                :inline="true"
                :model="friendChainForm"
                label-width="100px"
            >
                <el-form-item label="友链名称：">
                    <el-input
                        placeholder="请输入友链名称"
                        v-model="friendChainForm.name"
                    ></el-input>
                </el-form-item>
                <el-form-item class="friendChain_searchform">
                    <el-button type="primary" @click="searchBanner"
                        >查询</el-button
                    >
                    <el-button @click="resetForm('friendChainForm')"
                        >重置</el-button
                    >
                </el-form-item>
            </el-form>
        </div>
        <!-- 按钮操作 -->
        <div class="friendChain_btn">
            <span>友链管理</span>
            <div>
                <el-button
                    v-auth="'manage.friendChain.add'"
                    type="primary"
                    @click="friendChainAddForm"
                    icon="el-icon-plus"
                    >新增友链
                </el-button>
            </div>
        </div>
        <!-- 表格 -->
        <div v-loading="loading">
            <el-table
                :data="friendChainTableList"
                border
                style="width: 100%"
                :header-cell-style="{
                    background: '#fafafa',
                    color: '#d2d2d2',
                }"
            >
                <!-- <el-table-column
                    prop
                    align="center"
                    label="序号"
                    type="index"
                    :index="indexMethod"
                    :resizable="false"
                    width="50"
                /> -->
                 <el-table-column
                    prop="sort"
                    label="排序"
                    align="left"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="name"
                    label="友链名称"
                    align="left"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="url"
                    label="友链地址"
                    align="left"
                    show-overflow-tooltip
                    width="450"
                />
                <el-table-column
                    prop="createTime"
                    label="创建时间"
                    show-overflow-tooltip
                    align="center"
                />
                <el-table-column
                    prop="status"
                    label="显示状态"
                    show-overflow-tooltip
                    align="center"
                >
                    <template slot-scope="scope">
                        <span>{{
                            scope.row.status == true ? "显示" : "隐藏"
                        }}</span>
                    </template>
                </el-table-column>

                <el-table-column
                    label="操作"
                    align="right"
                    width="150"
                    fixed="right"
                >
                    <template slot-scope="scope">
                        <el-link
                            type="primary"
                            @click="editBannerForm(scope.row)"
                            v-auth="'manage.friendChain.edit'"
                            ><i class="el-icon-edit" />
                            编辑
                        </el-link>
                        <el-link
                            style="margin-left: 10px"
                            type="danger"
                            @click="removeBannerForm(scope.row)"
                            v-auth="'manage.friendChain.del'"
                        >
                            删除
                            <i class="el-icon-delete" />
                        </el-link>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!-- 分页 -->
        <div class="paginationBlock">
            <el-pagination
                style="margin-top: 10px"
                :current-page="pagination.pageNo"
                :page-sizes="[10, 20, 30, 40]"
                :page-size="pagination.pageSize"
                background
                layout="total, sizes, prev, pager, next, jumper"
                :total="pagination.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <!-- 新增表单 -->
        <el-dialog
            :modal-append-to-body="false"
            :title="dialogTitle"
            width="30%"
            :visible.sync="dialogSchoolVisible"
            @close="cancelForm('ruleForm')"
        >
            <el-form
                :rules="rules"
                ref="ruleForm"
                label-position="right"
                label-width="100px"
                :model="dialogfriendChainForm"
            >
                <el-form-item label="友链名称" prop="name">
                    <el-input
                        :maxlength="20"
                        show-word-limit
                        style="width: 100%"
                        v-model="dialogfriendChainForm.name"
                        placeholder="请输入友链名称"
                    ></el-input>
                </el-form-item>
                <el-form-item label="排序" prop="sort">
                    <el-input-number
                        v-model="dialogfriendChainForm.sort"
                        :min="0"
                        label="请输入排序"
                    ></el-input-number>
                </el-form-item>
                <el-form-item label="链接" prop="url">
                    <el-input
                        style="width: 100%"
                        v-model="dialogfriendChainForm.url"
                        placeholder="请输入链接"
                    ></el-input>
                </el-form-item>
                <el-form-item label="显示/隐藏" prop="status">
                    <el-switch v-model="dialogfriendChainForm.status">
                    </el-switch>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog_footer">
                <el-button @click="cancelForm('ruleForm')">取 消</el-button>
                <el-button type="primary" @click="bannerFromOk('ruleForm')"
                    >确 定</el-button
                >
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {
    getFriendChainList,
    addFriendChain,
    echoFriendChain,
    updateFriendChain,
    deleteFriendChain,
} from "@/api/guanwangManagement.js";
export default {
    data() {
        return {
            friendChainForm: {
                name: "",
            },
            friendChainTableList: [],
            pagination: {
                pageSize: 10,
                pageNo: 1,
                total: 0,
            },
            loading: false,
            dialogfriendChainForm: {
                name: "",
                status: true,
                url: "",
                sort: 0,
            },
            dialogTitle: "新增友链",
            dialogSchoolVisible: false,
            rules: {
                name: [
                    {
                        required: true,
                        message: "请输入banner名称",
                        trigger: "blur",
                    },
                ],
                sort: [
                    {
                        required: true,
                        message: "请输入排序",
                        trigger: "blur",
                    },
                ],
                url: [
                    {
                        required: true,
                        message: "请输入链接",
                        trigger: "blur",
                    },
                    {
                        pattern:
                            /^(https?|ftp|file):\/\/[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]$/,
                        message: "请输入正确链接地址",
                        trigger: "blur",
                    },
                ],
                status: [
                    {
                        required: true,
                        message: "请选择状态",
                        trigger: "change",
                    },
                ],
            },
        };
    },
    methods: {
        // 查询按钮
        searchBanner() {
            this.pagination.pageNo = 1;
            this.getfriendChainPage();
        },
        friendChainAddForm() {
            this.getfriendChainPage()
            this.dialogfriendChainForm.sort = this.pagination.total
            this.dialogSchoolVisible = true;
            this.dialogTitle = "新增友链";
        },
        resetForm(formName){
            this.$refs[formName].resetFields();
            this.friendChainForm.name = ''
            this.getfriendChainPage()
        },
        //重置
        // handleSearchReset() {
        //     this.$refs.cooperationForm.resetFields();
        //     this.getCooperationList();
        // },
        // 表格列表
        getfriendChainPage() {
            this.loading = true;
            const obj = {
                name: this.friendChainForm.name,
                pageSize: this.pagination.pageSize,
                pageNo: this.pagination.pageNo,
            };
            getFriendChainList(obj)
                .then((res) => {
                    let { data } = res;
                    this.pagination.total = data.total;
                    this.pagination.pagesize = data.pagesize;
                    this.pagination.pageNo = data.pageNo;
                    this.friendChainTableList = data.list;
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        cancelForm(formName) {
            this.dialogSchoolVisible = false;
            this.$refs[formName].resetFields();
            this.getfriendChainPage();
        },
        // 弹框确定按钮
        bannerFromOk(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    if (this.dialogTitle == "新增友链") {
                        addFriendChain({ ...this.dialogfriendChainForm }).then(
                            (res) => {
                                this.$message.success(res.message);
                                this.cancelForm("ruleForm");
                            }
                        );
                    } else if (this.dialogTitle == "编辑友链") {
                        updateFriendChain({
                            ...this.dialogfriendChainForm,
                        }).then((res) => {
                            this.$message.success(res.message);
                            this.cancelForm("ruleForm");
                        });
                    }
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        editBannerForm(item) {
            this.dialogSchoolVisible = true;
            this.dialogTitle = "编辑友链";
            // 回显
            echoFriendChain({ id: item.id }).then((res) => {
                this.dialogfriendChainForm = res.data;
                this.dialogfriendChainForm.status == 1
                    ? (this.dialogfriendChainForm.status = true)
                    : (this.dialogfriendChainForm.status = false);
            });
        },
        // 删除
        removeBannerForm(row) {
            this.$confirm("是否确定删除此友链?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    deleteFriendChain({ id: row.id }).then((res) => {
                        this.$message.success(res.message);
                        this.getfriendChainPage();
                    });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消删除",
                    });
                });
        },
        // 分页
        handleSizeChange(val) {
            this.pagination.pageNo = 1;
            this.pagination.pageSize = val;
            this.getfriendChainPage();
        },
        handleCurrentChange(val) {
            this.pagination.pageNo = val;
            this.getfriendChainPage();
        },
        // 序号
        indexMethod(index) {
            return index + 1;
        },
    },
    created() {
        this.getfriendChainPage();
    },
};
</script>

<style lang="scss" scoped>
.video {
    position: relative;
    width: 300px;
    height: 180px;
    .del {
        width: 20px;
        height: 20px;
        line-height: 18px;
        text-align: center;
        background: #f5222d;
        color: #fff;
        position: absolute;
        top: -2px;
        right: -10px;
        cursor: pointer;
        border-radius: 50%;
    }
}
.friendChain {
    padding: 20px;
    background-color: #fff;
    .friendChain_btn {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;
        padding-top: 16px;
        border-top: 1px solid #ebeef5;
        span {
            margin: 10px;
        }
        div {
            display: flex;
        }
    }
    .paginationBlock {
        margin-top: 16px;
        text-align: right;
    }
}
</style>
<style lang="scss">
.friendChain {
    .friendChain_searchform {
        .el-form-item__content {
            margin-left: 20px;
        }
    }
}
</style>
