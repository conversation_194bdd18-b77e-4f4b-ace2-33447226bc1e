<!--
 * @Descripttion: 
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-11-06 09:39:13
 * @LastEditors: jingrou
 * @LastEditTime: 2022-10-14 09:39:35
-->
<template>
    <el-card shadow="never">
        <YdTable ref="table" :tableColumn="tableColumn" :data="loadData" describe="新闻列表">
            <!--  -->
            <template slot="search" slot-scope="scope">
                <el-form :inline="true" ref="queryParam" :model="queryParam" size="medium">
                    <el-form-item label="标题：" prop="title">
                        <el-input v-model="queryParam.title" placeholder="请输入标题"></el-input>
                    </el-form-item>
                    <el-form-item label="分类：" prop="newTypeId">
                        <el-select v-model="queryParam.newTypeId" placeholder="请选择">
                            <el-option v-for="item in newDictDateList" :key="item.id" :label="item.name"
                                :value="item.id"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="首页新闻状态：" prop="newIndex">
                        <el-select v-model="queryParam.newIndex" placeholder="请选择">
                            <el-option label="展示" :value="0"></el-option>
                            <el-option label="隐藏" :value="1"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" :loading="scope.loading" @click="scope.handleSearch"
                            icon="el-icon-search">查询</el-button>
                        <el-button size="medium" @click="searchReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </template>

            <!--  -->
            <template slot="btn">
                <el-button v-auth="'manage.newslist.add'" size="medium" icon="el-icon-plus" type="primary" @click="handleAddEdit(true)">新 建</el-button>
            </template>
            <!--  -->
            <span slot="action" slot-scope="scope">
                <el-link type="primary" icon="el-icon-edit" v-auth="'manage.newslist.edit'" @click="handleAddEdit(false, scope.row)">编辑</el-link>
                <el-link style="margin-left: 10px" type="danger" v-auth="'manage.newslist.del'" @click="handleRemove(scope.row)">
                    删除
                    <i class="el-icon-delete" />
                </el-link>
            </span>
            <!--  -->
        </YdTable>
        <div class="dialog_class">
            <el-dialog :modal="false" :title="dialogTitle" :visible.sync="dialogVisible" width="80%" top="50px" style="height: 1000px;
    overflow: hidden;" :before-close="handleClose">
                <NewForm :formTitle="dialogTitle" ref="NewForm" @dialogOpen="cancel" :addNewForm="addEditNewForm"
                    :newTypeList="newDictDateList" />
            </el-dialog>
        </div>

    </el-card>
</template>

<script>
import NewForm from "@/components/NewForm";
import YdTable from "@/components/YdTable";
import {
    getNewsList,
    removeNews,
    getPlatformList,
    getNewsInfo,
} from "@/api/guanwangManagement.js";
export default {
    components: { YdTable, NewForm },
    data() {
        return {
            dialogTitle: "新增新闻",
            dialogVisible: false,
            tableColumn: [
                {
                    label: "序号",
                    type: "index",
                    align: "center",
                    width: "60px",
                    isShow: true,
                    showtooltip: true,
                },
                {
                    label: "新闻标题",
                    index: "title",
                    showtooltip: true,

                    isShow: true,
                },
                {
                    label: "分类",
                    index: "newTypeId",
                    showtooltip: true,

                    isShow: true,
                    formatter: (row) =>
                        ["", "行业资讯", "合作案例", "公司动态"][row.newTypeId],
                    // scopedSlots:{
                    //     customRender:"aaaa"
                    // }
                },
                {
                    label: "关键字",
                    showtooltip: true,

                    index: "keyword",
                    isShow: true,
                },
                {
                    label: "描述",
                    index: "newDescribe",
                    showtooltip: true,
                    isShow: true,
                },
                {
                    label: "封面图",
                    index: "icon",
                    isShow: true,
                    align: "center",
                    showtooltip: false,
                    formatter: (row) => (
                        <el-image
                            style="width: 150px; height: auto"
                            src={row.icon}
                            preview-src-list={[row.icon]}
                        ></el-image>
                    ),
                },
                {
                    label: "首页新闻状态",
                    index: "newIndex",
                    showtooltip: true,
                    isShow: true,
                    align: "center",
                    formatter: (row) => ["显示", "隐藏"][row.newIndex],
                },
                {
                    label: "标为经典案例",
                    index: "isClassic",
                    showtooltip: true,
                    isShow: true,
                    align: "center",
                    formatter: (row) => ["显示", "隐藏"][row.isClassic],
                },
                {
                    label: "状态",
                    index: "status",
                    showtooltip: true,
                    isShow: true,
                    align: "center",
                    formatter: (row) => ["显示", "隐藏"][row.status],
                },
                {
                    label: "操作",
                    isShow: true,
                    align: "right",
                    showtooltip: true,
                    width: "220",
                    scopedSlots: { customRender: "action" },
                },
            ],
            newDictDateList: [],
            queryParam: {
                title: "",
                newTypeId: "",
                newIndex: "",
            },
            addEditNewForm: {},
            loadData: (parameter) => {
                return getNewsList(
                    Object.assign({ ...parameter }, this.queryParam)
                );
            },
        };
    },
    methods: {
        cancel(val) {
            this.dialogVisible = val;
            this.$refs.table.handleRefresh(true);
        },
        handleClose() {
            this.dialogVisible = false;
        },
        searchReset() {
            this.$refs.queryParam.resetFields();
            // this.queryParam.dictDateId
            this.$refs.table.handleRefresh();
        },
        handleRemove(row) {
            this.$confirm("是否确定删除此新闻吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    removeNews({ id: row.id }).then(({ message }) => {
                        this.$message.success(message);
                        // 如果参数为 true, 则刷新到第一页
                        this.$refs.table.handleRefresh(true);
                    });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消删除",
                    });
                });
        },
        handleAddEdit(Add, row) {
            this.dialogVisible = true;
            if (!Add) {
                this.getNewsInfo(row);
                this.dialogTitle = "编辑新闻";
            } else {
                this.dialogTitle = "新增新闻";
                this.addEditNewForm = { newTypeId: "" };
            }
        },
        // 回显表单数据
        getNewsInfo(item) {
            getNewsInfo({ id: item.id }).then((res) => {
                this.addEditNewForm = res.data;
            });
        },
        getNewsClass() {
            getPlatformList()
                .then((res) => {
                    this.newDictDateList = res.data;
                })
                .catch((err) => { });
        },
    },
    created() {
        this.getNewsClass();
    },
};
</script>

<style lang="scss">
.dialog_class {
    .el-dialog__wrapper {
        z-index: 1100 !important;
    }

    .el-dialog {
        position: relative;

        .el-dialog__body {
            height: 770px !important;
            overflow-y: auto !important;

            .el-card__body {
                padding-bottom: 60px !important;
            }
        }
    }

    .article_createOrEdit__footer {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        border-top: 1px solid #e8e8e8;

        .btn_warp_bottom {
            padding: 0px 24px;
            border-top: 1px solid #e8e8e8;
            justify-content: right;
        }

        z-index: 9;
        height: 60px;
        line-height: 60px;
        background: #fff;
        text-align: right;
    }
}
</style>
