<!--
 * @Description: 
 * @Version: 2.0
 * @Author: jingrou
 * @Date: 2021-11-03 10:09:20
 * @LastEditors: jingrou
 * @LastEditTime: 2022-08-05 10:37:56
-->
<template>
    <div class="cooperation">
        <!-- 表单 -->
        <div class="cooperation_form">
            <el-form
                ref="bannerForm"
                :inline="true"
                :model="searchBannerForm"
                label-width="100px"
            >
                <el-form-item label="banner名称：">
                    <el-input
                        placeholder="请输入banner名称"
                        v-model="searchBannerForm.name"
                    ></el-input>
                </el-form-item>
                <el-form-item class="cooperation_searchform">
                    <el-button type="primary" @click="searchBanner"
                        >查询</el-button
                    >
                    <el-button @click="resetForm('bannerForm')">重置</el-button>
                </el-form-item>
            </el-form>
        </div>
        <!-- 按钮操作 -->
        <div class="cooperation_btn">
            <span>banner管理</span>
            <div>
                <el-button
                    v-auth="'manage.rotationChart.add'"
                    type="primary"
                    @click="bannerAddForm"
                    icon="el-icon-plus"
                    >新增banner
                </el-button>
            </div>
        </div>
        <!-- 表格 -->
        <div v-loading="loading">
            <el-table
                border
                :data="bannerTableData"
                style="width: 100%"
                :header-cell-style="{
                    background: '#fafafa',
                    color: '#d2d2d2',
                }"
            >
                <!-- <el-table-column
                    prop
                    align="center"
                    label="序号"
                    type="index"
                    :index="indexMethod"
                    :resizable="false"
                    width="50"
                /> -->
                <el-table-column
                    prop="sort"
                    label="排序"
                    align="left"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="name"
                    label="banner名称"
                    align="left"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="fileUrl"
                    label="预览图/视频"
                    align="center"
                    width="330"
                >
                    <template slot-scope="scope">
                        <div style="width: 300px">
                            <div class="video" v-if="scope.row.isVideo">
                                <video
                                    :src="scope.row.fileUrl"
                                    controls="controls"
                                    class="video"
                                ></video>
                            </div>
                            <el-image
                                v-else
                                style="width: 150px; height: auto"
                                :src="scope.row.fileUrl"
                                :preview-src-list="[scope.row.fileUrl]"
                            >
                            </el-image>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="skipUrl" label="链接地址" align="left" />

                <!-- width="450" -->
                <el-table-column
                    prop="createTime"
                    label="创建时间"
                    show-overflow-tooltip
                    align="center"
                />
                <el-table-column
                    prop="status"
                    label="显示状态"
                    show-overflow-tooltip
                    align="center"
                >
                    <template slot-scope="scope">
                        <span>{{
                            scope.row.status == 0
                                ? "显示"
                                : scope.row.status == 1
                                ? "隐藏"
                                : "-"
                        }}</span>
                    </template>
                </el-table-column>

                <el-table-column
                    label="操作"
                    align="right"
                    width="150"
                    fixed="right"
                >
                    <template slot-scope="scope">
                        <el-link
                            type="primary"
                            @click="editBannerForm(scope.row)"
                            v-auth="'manage.rotationChart.edit'"
                            ><i class="el-icon-edit" />
                            编辑
                        </el-link>
                        <el-link
                            style="margin-left: 10px"
                            type="danger"
                            v-auth="'manage.rotationChart.del'"
                            @click="removeBannerForm(scope.row)"
                        >
                            删除
                            <i class="el-icon-delete" />
                        </el-link>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!-- 分页 -->
        <div class="paginationBlock">
            <el-pagination
                style="margin-top: 10px"
                :current-page="pagination.pageNo"
                :page-sizes="[10, 20, 30, 40]"
                :page-size="pagination.pageSize"
                background
                layout="total, sizes, prev, pager, next, jumper"
                :total="pagination.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <!-- 新增表单 -->
        <el-dialog
            :title="dialogTitle"
            width="30%"
            :visible.sync="dialogSchoolVisible"
            @close="cancelForm('ruleForm')"
            class="bannerDialog"
        >
            <el-form
                :rules="rules"
                ref="ruleForm"
                label-position="right"
                label-width="100px"
                :model="dialogBannerForm"
            >
                <el-form-item label="banner名称" prop="name">
                    <el-input
                        :maxlength="20"
                        style="width: 100%"
                        v-model="dialogBannerForm.name"
                        placeholder="请输入banner名称"
                    ></el-input>
                </el-form-item>
                <el-form-item
                    label="文件地址"
                    :rules="{
                        required: true,
                        message: '请上传文件地址',
                        trigger: 'blur',
                    }"
                    prop="fileUrl"
                >
                    <el-upload
                        action="/"
                        accept=".jpg, .jpeg, .png, .JPG, .JPEG, .mp4"
                        :on-change="uploadChange"
                        :before-remove="uploadRemove"
                        :file-list="fileList"
                        :multiple="false"
                        :auto-upload="false"
                        :show-file-list="false"
                        :on-preview="handlePictureCardPreview"
                        name="file"
                    >
                        <div v-if="dialogBannerForm.fileUrl">
                            <video
                                style="
                                    width: 100%;
                                    height: 149px;
                                    border: 1px solid #eee;
                                    min-width: 200px;
                                "
                                v-if="isVideo"
                                :src="dialogBannerForm.fileUrl"
                                controls
                            ></video>
                            <img
                                v-else
                                :src="dialogBannerForm.fileUrl"
                                class="avatar"
                                style="
                                    width: 100%;
                                    height: 149px;
                                    border: 1px solid #eee;
                                    min-width: 200px;
                                "
                            />
                        </div>
                        <i
                            style="
                                width: 200px;
                                height: 149px;
                                border: 1px solid #eee;
                                line-height: 149px;
                                font-size: 28px;
                            "
                            v-else
                            class="el-icon-plus avatar-uploader-icon"
                        ></i>
                    </el-upload>
                    <el-progress
                        v-if="this.percentage != 0 && this.percentage != 100"
                        :percentage="this.percentage"
                    ></el-progress>
                </el-form-item>
                <div
                    style="
                        display: flex;
                        flex-direction: column;
                        justify-content: space-around;
                        align-items: start;
                        width: 90%;
                        height: 80px;
                        margin: -15px 0px 20px 100px;
                        color: #d0d0d0;
                    "
                >
                    <span>提示：</span>
                    <span>上传jpg/png/jpge格式文件，文件不能超过1MB</span>
                    <span>上传MP4格式文件，文件不超过50MB</span>
                    <span>请上传1903*600尺寸，否则效果会有偏差</span>
                </div>
                <el-form-item label="排序" prop="sort">
                    <el-input-number
                        v-model="dialogBannerForm.sort"
                        :min="0"
                        label="请输入排序"
                    ></el-input-number>
                </el-form-item>
                <el-form-item label="链接" prop="skipUrl">
                    <el-input
                        style="width: 100%"
                        v-model="dialogBannerForm.skipUrl"
                        placeholder="请输入链接"
                    ></el-input>
                </el-form-item>
                <el-form-item label="显示/隐藏" prop="status">
                    <el-switch v-model="dialogBannerForm.status"> </el-switch>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog_footer">
                <el-button @click="cancelForm('ruleForm')">取 消</el-button>
                <el-button type="primary" @click="bannerFromOk('ruleForm')"
                    >确 定</el-button
                >
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {
    getBannerList,
    addBannerList,
    echoBannerInfo,
    updateBannerInfo,
    deleteBannerInfo,
} from "@/api/guanwangManagement.js";
import { getUploadFileOSSpath } from "@/utils/oss";
export default {
    data() {
        return {
            isVideo: false, // 是否是视频
            searchBannerForm: {
                name: "",
            },
            bannerTableData: [],
            pagination: {
                pageSize: 10,
                pageNo: 1,
                total: 0,
            },
            loading: false,
            dialogBannerForm: {
                name: "",
                fileUrl: "",
                status: true,
                skipUrl: "",
                sort: 0,
            },
            dialogTitle: "新增banner",
            dialogSchoolVisible: false,
            rules: {
                name: [
                    {
                        required: true,
                        message: "请输入banner名称",
                        trigger: "blur",
                    },
                ],
                sort: [
                    {
                        required: true,
                        message: "请输入排序",
                        trigger: "blur",
                    },
                ],
                skipUrl: [
                    {
                        required: true,
                        message: "请输入链接",
                        trigger: "blur",
                    },
                    {
                        pattern:
                            /^(https?|ftp|file):\/\/[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]$/,
                        message: "请输入正确链接地址",
                        trigger: "blur",
                    },
                ],
                status: [
                    {
                        required: true,
                        message: "请选择状态",
                        trigger: "change",
                    },
                ],
            },
            percentage: 0, // 上传文件进度
            fileList: [],
            accept: ".mpg,.mpeg,.avi,.rm,.rmvb,.mov,.wmv,.asf,.mp4,.mov,.mkv,.jpg, .jpeg, .png, .JPG, .JPEG,.mp4",
        };
    },
    methods: {
        bannerAddForm() {
            this.dialogSchoolVisible = true;
            this.fileList = [];
            this.dialogTitle = "新增banner";
        },
        //重置
        handleSearchReset() {
            this.$refs.cooperationForm.resetFields();
            this.cooperationForm.contactName = "";
            this.cooperationForm.contactPhone = "";
            this.getCooperationList();
        },
        handlePictureCardPreview(file) {
            this.dialogBannerForm.fileUrl = file.url;
            // this.dialogVisible = true;
        },
        uploadRemove(file) {
            file.url = "";
            this.dialogBannerForm.fileUrl = "";
        },
        // 上传图片
        uploadChange(file, fileList) {
            let uploadUrl = file.raw.type.substr(
                file.raw.type.lastIndexOf("/") + 1
            );
            let fileVideo =
                [
                    ".mpg",
                    ".mpeg",
                    ".avi",
                    ".rm",
                    ".rmvb",
                    ".mov",
                    ".wmv",
                    ".asf",
                    ".mp4",
                    ".mov",
                    ".mkv",
                ].indexOf(uploadUrl.toLowerCase()) !== -1;
            if (fileVideo) {
                this.isVideo = true;
                const isLt50M = file.size / 1024 / 1024 < 50;
                if (!isLt50M) {
                    this.$message.error("视频大于50M，请重新上传");
                    fileList = [];
                    this.fileList = [];
                    return false;
                } else {
                    this.uploadImgVideo(file, fileList);
                }
            }
            let fileImg =
                ["jpg", "JPG", "png", "PNG", "jpeg", "JPEG"].indexOf(
                    uploadUrl.toLowerCase()
                ) !== -1;
            if (fileImg) {
                this.isVideo = false;
                const isLt1M = file.size / 1024 / 1024 < 1;
                if (!isLt1M) {
                    this.$message.error("图片大于1M，请重新上传");
                    fileList = [];
                    this.fileList = [];
                    return false;
                } else {
                    this.uploadImgVideo(file, fileList);
                }
            }
        },
        async uploadImgVideo(file, fileList) {
            console.log(file);
            this.percentage = 0;
            const url = await getUploadFileOSSpath(
                file.raw,
                "officialWebsiteBanner",
                (num) => {
                    this.percentage = num;
                }
            );
            if (url) {
                if (fileList.length > 0) {
                    this.fileList = [fileList[fileList.length - 1]];
                }
                this.dialogBannerForm.fileUrl = url;
            } else {
                this.$message.error("上传失败");
                this.dialogBannerForm.fileUrl = "";
            }
        },
        // 表格列表
        getbannerPage() {
            this.loading = true;
            const obj = {
                name: this.searchBannerForm.name,
                pageSize: this.pagination.pageSize,
                pageNo: this.pagination.pageNo,
            };
            getBannerList(obj)
                .then((res) => {
                    let { data } = res;
                    this.pagination.total = data.total;
                    this.pagination.pagesize = data.pagesize;
                    this.pagination.pageNo = data.pageNo;
                    this.bannerTableData = data.list;
                    this.bannerTableData.map((item, index) => {
                        let url = item.fileUrl.substr(
                            item.fileUrl.lastIndexOf(".") + 1
                        );
                        let videoUrl =
                            [
                                "mpg",
                                "mpeg",
                                "avi",
                                "rm",
                                "rmvb",
                                "mov",
                                "wmv",
                                "asf",
                                "mp4",
                                "mov",
                                "mkv",
                            ].indexOf(url.toLowerCase()) !== -1;
                        if (videoUrl) {
                            item.isVideo = true;
                        }
                    });
                    console.log(this.bannerTableData);
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        cancelForm(formName) {
            this.dialogSchoolVisible = false;
            this.$refs[formName].resetFields();
            this.getbannerPage();
        },
        // 弹框确定按钮
        bannerFromOk(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    if (this.dialogTitle == "新增banner") {
                        let obj = {
                            ...this.dialogBannerForm,
                            status:
                                this.dialogBannerForm.status === true ? 0 : 1,
                        };
                        addBannerList(obj).then((res) => {
                            this.$message.success(res.message);
                            this.cancelForm("ruleForm");
                        });
                    } else if (this.dialogTitle == "编辑banner") {
                        let obj = {
                            ...this.dialogBannerForm,
                            status:
                                this.dialogBannerForm.status === true ? 0 : 1,
                        };
                        updateBannerInfo(obj).then((res) => {
                            this.$message.success(res.message);
                            this.cancelForm("ruleForm");
                        });
                        // this.updateAuthorizeCode();
                    }
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        editBannerForm(item) {
            this.dialogSchoolVisible = true;
            this.dialogTitle = "编辑banner";
            // 回显
            echoBannerInfo({ id: item.id }).then((res) => {
                this.dialogBannerForm = res.data;
                res.data.status == 1
                    ? (this.dialogBannerForm.status = false)
                    : res.data.status == 0
                    ? (this.dialogBannerForm.status = true)
                    : "";
                this.fileList = [];
                this.fileList.push({
                    url: res.data.fileUrl,
                    name: res.data.fileUrl,
                });
                let url = res.data.fileUrl.substr(
                    res.data.fileUrl.lastIndexOf(".") + 1
                );
                let videoUrl =
                    [
                        "mpg",
                        "mpeg",
                        "avi",
                        "rm",
                        "rmvb",
                        "mov",
                        "wmv",
                        "asf",
                        "mp4",
                        "mov",
                        "mkv",
                    ].indexOf(url.toLowerCase()) !== -1;
                if (videoUrl) {
                    this.isVideo = true;
                } else {
                    this.isVideo = false;
                }
            });
        },
        // 删除
        removeBannerForm(row) {
            this.$confirm("是否确定删除此banner图?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    deleteBannerInfo({ id: row.id }).then((res) => {
                        this.$message.success(res.message);
                        this.getbannerPage();
                    });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消删除",
                    });
                });
        },
        // 查询按钮
        searchBanner() {
            this.pagination.pageNo = 1;
            this.getbannerPage();
        },
        // 分页
        handleSizeChange(val) {
            this.pagination.pageNo = 1;
            this.pagination.pageSize = val;
            this.getbannerPage();
        },
        handleCurrentChange(val) {
            this.pagination.pageNo = val;
            this.getbannerPage();
        },
        // 序号
        indexMethod(index) {
            return index + 1;
        },
        // 重置
        resetForm(formName) {
            this.searchBannerForm.name = "";
            this.getbannerPage();
        },
    },
    created() {
        this.getbannerPage();
    },
};
</script>

<style lang="scss" scoped>
.video {
    position: relative;
    width: 300px;
    height: 180px;
    .del {
        width: 20px;
        height: 20px;
        line-height: 18px;
        text-align: center;
        background: #f5222d;
        color: #fff;
        position: absolute;
        top: -2px;
        right: -10px;
        cursor: pointer;
        border-radius: 50%;
    }
}
.cooperation {
    padding: 20px;
    background-color: #fff;
    .cooperation_btn {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;
        padding-top: 16px;
        border-top: 1px solid #ebeef5;
        span {
            margin: 10px;
        }
        div {
            display: flex;
        }
    }
    .paginationBlock {
        margin-top: 16px;
        text-align: right;
    }
}
</style>
<style lang="scss">
.cooperation {
    .cooperation_searchform {
        .el-form-item__content {
            margin-left: 20px;
        }
    }
    .cooperation_contactName {
        .el-form-item__label {
            width: 120px !important;
        }
    }
}
.bannerDialog {
    .el-dialog__body {
        height: 440px !important;
        overflow-y: auto !important;
        overflow-x: hidden;
    }
}
</style>
