<!--
 * @Descripttion: 
 * @version: 1.0.0
 * @Author: jingrou
 * @Date: 2022-08-04 10:18:56
 * @LastEditors: jingrou
 * @LastEditTime: 2023-03-01 17:12:35
-->
<template>
    <el-card shadow="never">
        <YdTable
            ref="table"
            :tableColumn="tableColumn"
            :data="loadData"
            describe="新闻列表"
        >
            <!--  -->
            <template slot="search" slot-scope="scope">
                <el-form
                    :inline="true"
                    ref="queryParam"
                    :model="queryParam"
                    size="medium"
                >
                    <el-form-item label="标题：" prop="name">
                        <el-input
                            v-model="queryParam.name"
                            placeholder="请输入标题"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="分类：" prop="type">
                        <el-select
                            v-model="queryParam.type"
                            placeholder="请选择"
                        >
                            <!-- <el-option label="入门指引" :value="0"></el-option>
                            <el-option label="进阶使用" :value="1"></el-option> -->
                            <el-option label="常见问题" :value="2"></el-option>
                            <el-option label="功能指南" :value="3"></el-option>
                            <el-option label="教学视频" :value="4"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="是否热门：" prop="hotInd">
                        <el-select
                            v-model="queryParam.hotInd"
                            placeholder="请选择"
                        >
                            <el-option label="是" :value="0"></el-option>
                            <el-option label="否" :value="1"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="版本标识：" prop="versionType">
                        <el-select
                            v-model="queryParam.versionType"
                            placeholder="请选择"
                        >
                            <el-option label="1.0" :value="1" />
                            <el-option label="2.0" :value="2" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="范围：" prop="hotInd">
                        <el-select
                            v-model="queryParam.idRanges"
                            placeholder="请选择"
                        >
                            <el-option
                                label="教师端（app） "
                                :value="0"
                            ></el-option>
                            <el-option
                                label="家长端（app）"
                                :value="1"
                            ></el-option>
                            <el-option
                                label="教师端（web）"
                                :value="2"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button
                            type="primary"
                            :loading="scope.loading"
                            @click="scope.handleSearch"
                            icon="el-icon-search"
                            >查询</el-button
                        >
                        <el-button size="medium" @click="searchReset"
                            >重置</el-button
                        >
                    </el-form-item>
                </el-form>
            </template>

            <!--  -->
            <template slot="btn">
                <el-button
                    size="medium"
                    icon="el-icon-plus"
                    type="primary"
                    v-auth="'manage.helpCore.add'"
                    @click="addHelp"
                    >新 建</el-button
                >
            </template>
            <span slot="action" slot-scope="scope">
                <el-link
                    type="primary"
                    icon="el-icon-edit"
                    v-auth="'manage.helpCore.edit'"
                    @click="eidtHelp(scope.row)"
                    >编辑</el-link
                >
                <el-link
                    style="margin-left: 10px"
                    type="danger"
                    v-auth="'manage.helpCore.del'"
                    @click="deleteHelp(scope.row)"
                >
                    删除
                    <i class="el-icon-delete" />
                </el-link>
            </span>
            <!--  -->
        </YdTable>
        <HelpForm
            :infoForm="helpInfoForm"
            @handleClose="close"
            :helpVisible="formStatus"
            :titleVisible="helpFormTitle"
        />
    </el-card>
</template>

<script>
import YdTable from "@/components/YdTable";
import HelpForm from "@/components/HelpForm";
import { getHelpList, getHelpInfo, deleteHelp } from "@/api/helpCore.js";
export default {
    components: { YdTable, HelpForm },
    data() {
        return {
            helpInfoForm: {},
            queryParam: {
                name: "",
                type: "",
                hotInd: "",
                idRanges: "",
                versionType: "",
            },
            tableColumn: [
                {
                    label: "序号",
                    type: "index",
                    align: "center",
                    width: "60px",
                    isShow: true,
                    showtooltip: true,
                },
                {
                    label: "编号",
                    index: "id",
                    showtooltip: true,
                    isShow: true,
                },
                {
                    label: "标题",
                    index: "name",
                    showtooltip: true,
                    isShow: true,
                },
                {
                    label: "分类",
                    index: "type",
                    showtooltip: true,
                    isShow: true,
                    formatter: (row) =>
                        [
                            "入门指引",
                            "进阶使用",
                            "常见问题",
                            "功能指南",
                            "教学视频",
                        ][row.type],
                },
                {
                    label: "范围",
                    index: "idRanges",
                    showtooltip: true,
                    isShow: true,
                    align: "left",
                    width: 350,
                    formatter: (row) => {
                        const idRangesText = Array.from(row.idRanges).map(
                            (type) => {
                                const textList = [
                                    "教师端（app）",
                                    "家长端（app）",
                                    "教师端（web）",
                                ];
                                return textList[type];
                            }
                        );
                        return idRangesText.join();
                    },
                },
                {
                    label: "是否热门",
                    showtooltip: true,
                    index: "hotInd",
                    isShow: true,
                    formatter: (row) => ["是", " 否"][row.hotInd],
                },
                {
                    label: "版本标识",
                    showtooltip: true,
                    index: "versionType",
                    isShow: true,
                    formatter: (row) => ["", "1.0", "2.0"][row.versionType],
                },
                {
                    label: "排序",
                    index: "sort",
                    showtooltip: true,
                    isShow: true,
                    align: "center",
                },

                // {
                //     label: "封面图",
                //     index: "icon",
                //     isShow: true,
                //     align: "center",
                //     showtooltip: false,
                //     formatter: (row) => (
                //         <el-image
                //             style="width: 150px; height: auto"
                //             src={row.icon}
                //             preview-src-list={[row.icon]}
                //         ></el-image>
                //     ),
                // },
                {
                    label: "操作",
                    isShow: true,
                    align: "right",
                    showtooltip: true,
                    width: "220",
                    scopedSlots: { customRender: "action" },
                },
            ],
            loadData: (parameter) => {
                return getHelpList(
                    Object.assign({ ...parameter }, this.queryParam)
                );
            },
            formStatus: false,
            helpFormTitle: false,
        };
    },
    methods: {
        deleteHelp(item) {
            this.$confirm("此操作将永久删除该帮助, 是否继续?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    deleteHelp({ id: item.id }).then((res) => {
                        this.$message.success(res.message);
                        this.searchReset();
                    });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消删除",
                    });
                });
        },
        searchReset() {
            this.queryParam = {};
            this.$refs.table.handleRefresh(true);
        },
        addHelp() {
            this.formStatus = true;
            this.helpFormTitle = false;
        },
        close(val) {
            this.formStatus = val;
            this.helpFormTitle = val;
            this.helpInfoForm = {
                status: 0,
                idRanges: [],
                name: "",
                type: "",
                hotInd: null,
                sort: 1,
                video: null,
                versionType: null,
            };
            this.$refs.table.handleRefresh(true);
        },
        eidtHelp(item) {
            this.formStatus = true;
            this.helpFormTitle = true;
            getHelpInfo({ id: item.id }).then((res) => {
                this.helpInfoForm = { ...res.data, status: 0 };
            });
        },
    },
};
</script>

<style lang="scss" scoped></style>
