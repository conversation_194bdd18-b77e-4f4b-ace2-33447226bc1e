<!--
 * @Description: 
 * @Version: 2.0
 * @Author: jingrou
 * @Date: 2021-11-03 10:09:20
 * @LastEditors: jingrou
 * @LastEditTime: 2022-11-23 10:48:51
-->
<template>
    <div class="login_log">
        <!-- 表单 -->
        <div class="login_log_form">
            <el-form ref="queryForm" :inline="true" :model="queryForm" label-width="100px">
                <el-form-item label="用户名：">
                    <el-input placeholder="请输入用户名" v-model="queryForm.userName"></el-input>
                </el-form-item>
                <el-form-item label="ip：">
                    <el-input placeholder="请输入ip" v-model="queryForm.ip"></el-input>
                </el-form-item>
                <el-form-item label="ip地址：">
                    <el-input placeholder="请输入ip地址" v-model="queryForm.location"></el-input>
                </el-form-item>
                <el-form-item label="登录状态：">
                    <el-select v-model="queryForm.status" placeholder="请选择登录状态">
                        <el-option :label="item.name" :value="item.id" v-for="item, index in stateList"
                            :key="item.id + 'state' + index"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="登录时间：" class="login_time_class">
                    <el-date-picker v-model="queryForm.loginTime" type="daterange" range-separator="至"
                        format="yyyy-MM-dd" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期">
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="queryList">查询</el-button>
                    <el-button @click="resetForm">重置</el-button>
                </el-form-item>
            </el-form>
        </div>
        <!-- 按钮操作 -->
        <div class="login_log_btn">
            <span>登录日志</span>
        </div>
        <!-- 表格 -->
        <div v-loading="tableLoading">
            <el-table border :data="loginLogTable" style="width: 100%" :header-cell-style="{
                background: '#fafafa',
                color: '#d2d2d2',
            }">

                <el-table-column prop="userName" label="用户名" show-overflow-tooltip align="center">
                    <template slot-scope="scope">
                        <span>
                            {{
                                    scope.row.userName && scope.row.userName != '' ? scope.row.userName : '-'
                            }}
                        </span>
                    </template>
                </el-table-column>
                <!-- <el-table-column prop="id" label="id" align="left" show-overflow-tooltip /> -->
                <el-table-column prop="ip" label="ip" align="center" show-overflow-tooltip />
                <el-table-column prop="location" label="ip地址" align="center" />
                <el-table-column prop="method" label="请求方式" show-overflow-tooltip align="center" />
                <el-table-column prop="title" label="标题" show-overflow-tooltip align="center" />
                <el-table-column prop="module" label="模块标识" show-overflow-tooltip align="center" />
                <el-table-column prop="moduleDesc" label="模块名称" show-overflow-tooltip align="center">
                    <template slot-scope="scope">
                        <span>
                            {{
                                    scope.row.moduleDesc && scope.row.moduleDesc != '' ? scope.row.moduleDesc : '-'
                            }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column prop="createTime" label="登录时间" show-overflow-tooltip align="center" />
                <el-table-column prop="status" label="登录状态" show-overflow-tooltip align="center">
                    <template slot-scope="scope">
                        <span>
                            {{
                                    scope.row.status == true
                                        ? "成功"
                                        : "失败"
                            }}
                        </span>
                    </template>
                </el-table-column>

            </el-table>
        </div>
        <!-- 分页 -->
        <div class="paginationBlock">
            <el-pagination style="margin-top: 10px" :current-page="pagination.pageNo" :page-sizes="[10, 20, 30, 40]"
                :page-size="pagination.pageSize" background layout="total, sizes, prev, pager, next, jumper"
                :total="pagination.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </div>
    </div>
</template>

<script>
import {
    getLoginPage
} from "@/api/loginLog.js";
export default {
    data() {
        return {
            stateList: [
                {
                    name: '失败',
                    id: 0,
                },
                {
                    name: '成功',
                    id: 1,
                }
            ],
            queryForm: {
                userName: '',
                location: '',
                status: null,
                ip: '',
                loginTime: []
            },
            pagination: {
                pageSize: 10,
                pageNo: 1,
                total: 0,
            },
            loginLogTable: [],
            tableLoading: false,
        };
    },
    methods: {
        queryList() {
            this.getLoginPageFn()
        },
        loginTimeFn(timeArr) {
            return {
                createStartTime: timeArr[0],
                createEndTime: timeArr[1],
            }
        },
        getLoginPageFn() {
            this.tableLoading = true
            const time = this.loginTimeFn(this.queryForm.loginTime)
            getLoginPage({ ...this.pagination, ...time, ...this.queryForm })
                .then(({ data }) => {
                    this.loginLogTable = data.list
                    this.pagination.pageSize = data.pageSize
                    this.pagination.pageNo = data.pageNo
                    this.pagination.total = data.total
                }).finally(() => {
                    this.tableLoading = false
                })
        },
        handleSizeChange(val) {
            this.pagination.pageNo = 1;
            this.pagination.pageSize = val;
            this.getLoginPageFn();
        },
        handleCurrentChange(val) {
            this.pagination.pageNo = val;
            this.getLoginPageFn();
        },
        resetForm(formName) {
            this.queryForm = {
                userName: '',
                location: '',
                status: null,
                loginTime: []
            }
            this.getLoginPageFn();
        }
    },
    created() {
        this.getLoginPageFn();
    },
};
</script>

<style lang="scss" scoped>
.login_log {
    padding: 20px;
    background-color: #fff;

    .login_log_btn {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;
        padding-top: 16px;
        border-top: 1px solid #ebeef5;

        span {
            margin: 10px;
        }

        div {
            display: flex;
        }
    }

    .paginationBlock {
        margin-top: 16px;
        text-align: right;
    }
}
</style>
<style lang="scss">
.login_time_class {
    .el-date-editor {
        border: 1px solid #DCDFE6 !important;
    }
}
</style>
