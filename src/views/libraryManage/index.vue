<template>
    <div class="card_warp">
        <el-form
            ref="versionForm"
            :model="versionForm"
            status-icon
            :inline="true"
            label-width="auto"
            class="demo-ruleForm"
        >
            <el-form-item label="学校：" prop="schoolName">
                <el-input
                    v-model="versionForm.schoolName"
                    placeholder="请输入学校名称"
                />
            </el-form-item>
            <el-form-item label="注册码：" prop="registrationCode">
                <el-input
                    v-model="versionForm.registrationCode"
                    placeholder="请输入注册码"
                />
            </el-form-item>
            <el-form-item label="设备序列号：" prop="no">
                <el-input v-model="versionForm.no" placeholder="请输入序列号" />
            </el-form-item>
            <el-form-item label="配置状态：" prop="configStatus">
                <el-select
                    v-model="versionForm.configStatus"
                    placeholder="请选择"
                >
                    <el-option label="未配置" value="1"> </el-option>
                    <el-option label="已配置" value="2"> </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="query" icon="el-icon-search"
                    >查询</el-button
                >
                <el-button @click="searchResetForm">重置</el-button>
            </el-form-item>
        </el-form>
        <!-- 按钮 -->
        <!-- <div class="btn_warp">
            <span>版本列表</span>
            <div>
                <el-button
                    type="primary"
                    @click="configuration(multipleSelection)"
                    >配置借还书机</el-button
                >
            </div>
        </div> -->
        <!-- 表格 -->
        <el-table
            class="el-tables"
            style="width: 100%; margin-top: 16px"
            :data="tableData || []"
            border
            @selection-change="handleSelectionChange"
            :header-cell-style="{ background: '#fafafa', color: '#5b5d61' }"
        >
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column
                type="index"
                width="50"
                label="序号"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="schoolName"
                show-overflow-tooltip
                label="学校"
                align="center"
            >
            </el-table-column>
            <el-table-column
                prop="no"
                show-overflow-tooltip
                label="设备序列号"
                align="center"
            >
            </el-table-column>
            <el-table-column
                prop="registrationCode"
                show-overflow-tooltip
                label="注册码"
                align="center"
            >
            </el-table-column>
            <el-table-column
                prop="status"
                show-overflow-tooltip
                label="注册码状态"
                align="center"
            >
                <template slot-scope="scope">
                    <span>{{
                        scope.row.status === 1 ? "已使用" : "未使用"
                    }}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="configStatus"
                show-overflow-tooltip
                label="配置状态"
                align="center"
            >
                <template slot-scope="scope">
                    <span>{{
                        scope.row.configStatus === 1 ? "未配置" : "已配置"
                    }}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="remark"
                show-overflow-tooltip
                label="备注"
                align="center"
            >
            </el-table-column>
            <!-- <el-table-column
                fixed="right"
                label="操作"
                width="180"
                align="center"
            >
                <template slot-scope="scope">
                    <div>
                        <el-button
                            style="margin-left: 10px"
                            type="text"
                            @click="configuration(scope.row)"
                            >配置借还书机</el-button
                        >
                    </div>
                </template>
            </el-table-column> -->
        </el-table>
        <!-- 分页 -->
        <el-pagination
            background
            style="margin-top: 16px; text-align: right"
            :current-page="pagination.pageNo"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="pagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
    </div>
</template>

<script>
import { getBookManageList } from "@/api/libraryManage.js";

export default {
    data() {
        return {
            pagination: {
                pageNo: 1,
                pageSize: 10,
                total: 0,
            },
            versionForm: {
                registrationCode: "",
                no: "",
                configStatus: "",
                schoolName: "",
            },
            tableData: [],
            rules: {},
            multipleSelection: [],
        };
    },
    created() {
        this.getBookManageList(); // 借还书机列表
    },
    methods: {
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        // 查询
        query() {
            this.pagination.pageNo = 1;
            this.getBookManageList();
        },

        // 借还书机列表
        getBookManageList() {
            const obj = {
                ...this.pagination,
                ...this.versionForm,
            };
            getBookManageList(obj)
                .then((res) => {
                    this.tableData = res.data.list;
                    this.pagination.total = res.data.total;
                })
                .catch();
        },
        // 重置
        searchResetForm() {
            this.$refs.versionForm.resetFields();
            this.getBookManageList();
        },
        // 分页
        handleSizeChange(val) {
            this.pagination.pageNo = 1;
            this.formPage.pageSize = val;
            this.getBookManageList();
        },
        handleCurrentChange(val) {
            this.pagination.pageNo = val;
            this.getBookManageList();
        },

        configuration(row) {
            console.log("row :>> ", row);
        },
    },
};
</script>

<style lang="scss" scoped>
.card_warp {
    background-color: #fff;
    padding: 20px;
    .btn_warp {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;
        padding-top: 16px;
        border-top: 1px solid #ebeef5;
        span {
            margin: 10px;
        }
        div {
            display: flex;
        }
    }
}
.inputClass {
    width: 100%;
    padding: 0px 50px;
    margin: auto;
    overflow-y: auto;
    height: 714px;
    i {
        margin-left: 5px;
        cursor: pointer;
    }
    li {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 20px;
        width: 80%;
        img {
            margin-right: 10px;
        }
    }
}
</style>
<style lang="scss">
.drawerClass {
    .el-drawer__body {
        padding-right: 30px;
    }
}
.administrationClass,
.demo-ruleForm {
    .el-drawer__body {
        position: relative;
    }
    .yd-form-footer {
        width: 100%;
        height: 70px;
        justify-content: center;
        display: flex;
        align-items: center;
        border-top: 1px #eee solid;
        background-color: #fff;
        position: absolute;
        bottom: 0px;
        left: 0px;
        .el-form-item__content {
            margin: 0px !important;
        }
    }
}
</style>
