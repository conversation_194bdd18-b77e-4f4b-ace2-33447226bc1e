<!--
 * @Descripttion: 消息模板添加，编辑
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-04-01 15:16:45
 * @LastEditors: jingrou
 * @LastEditTime: 2022-06-14 09:59:07
-->

<template>
    <el-drawer
        :title="`${isEdit ? '编辑模板' : '添加模板'}`"
        :visible.sync="visible"
        direction="rtl"
    >
        <el-form
            :model="form"
            :rules="rules"
            ref="form"
            label-width="90px"
            class="drawer_form"
        >
            <el-form-item label="标题：" prop="name">
                <el-input
                    v-model.trim="form.name"
                    placeholder="标题需在4-12字符之间，建议模版标题以“通知”或“提醒”作为结尾"
                ></el-input>
            </el-form-item>
            <el-form-item label="副标题：" prop="firstData">
                <el-input v-model.trim="form.firstData"></el-input>
            </el-form-item>
            <el-form-item
                label="正文："
                prop="messages"
                style="margin-bottom: 5px"
            >
                <el-tooltip class="item" effect="light" placement="left">
                    <div slot="content">
                        <examples />
                    </div>
                    <el-link>查看示例</el-link>
                </el-tooltip>
            </el-form-item>
            <div
                style="padding-right: 90px; position: relative"
                v-for="(item, index) in form.messages"
                :key="`msg_${index}`"
            >
                <el-form-item
                    :prop="'messages.' + index + '.name'"
                    :rules="{
                        required: true,
                        message: '关键字不能为空',
                        trigger: 'blur',
                    }"
                >
                    <el-input v-model="item.name" placeholder="关键字(选填)">
                        <template slot="append"
                            >：{{ `keyword${index + 1}.DATA` }}</template
                        >
                    </el-input>
                </el-form-item>
                <div style="position: absolute; right: 0px; top: 5px">
                    <i
                        v-if="form.messages.length < 10"
                        class="el-icon-plus form_icon"
                        title="添加关键词"
                        @click="addKeyword"
                    ></i>
                    <i
                        v-if="form.messages.length > 1"
                        class="el-icon-delete form_icon"
                        title="删除关键词"
                        @click="delKeyword(index)"
                    ></i>
                </div>
            </div>
            <el-form-item label="备注：" prop="remakeData">
                <el-input v-model.trim="form.remakeData"></el-input>
            </el-form-item>
            <el-form-item label="发送至：" prop="type">
                <!-- <el-checkbox-group v-model="form.type">
					<el-checkbox :label="0">消息通知</el-checkbox>
					<el-checkbox :label="1">待办</el-checkbox>
				</el-checkbox-group>-->
                <el-radio-group v-model="form.type">
                    <el-radio label="0">消息通知</el-radio>
                    <el-radio label="1">待办</el-radio>
                </el-radio-group>
            </el-form-item>
            <div class="drawer_form__footre">
                <el-button
                    type="primary"
                    :loading="loading"
                    @click="submitForm()"
                    >确定</el-button
                >
                <el-button @click="resetForm">取消</el-button>
            </div>
        </el-form>
    </el-drawer>
</template>
<script>
import {
    createMsgTemp,
    getMsgTempInfo,
    updateMsgTemp,
} from "@/api/messageTemplate.js";
import Examples from "./examples";
export default {
    props: {
        isEdit: {
            type: Boolean,
            default: false,
        },
    },
    components: { Examples },
    data() {
        return {
            visible: false,
            loading: false,
            form: {
                name: "",
                type: "0",
                firstData: null,
                messages: [{ name: "" }],
                remakeData: null,
            },
            rules: {
                name: [
                    { required: true, message: "请输入标题", trigger: "blur" },
                    {
                        min: 4,
                        max: 12,
                        message: "标题需在4-12字符之间",
                        trigger: "blur",
                    },
                ],
                type: [
                    {
                        required: true,
                        message: "请选择发送类型",
                        trigger: "change",
                    },
                ],
            },
        };
    },
    created() {},
    methods: {
        addKeyword() {
            this.form.messages.push({ name: "" });
        },
        delKeyword(index) {
            this.form.messages.splice(index, 1);
        },
        open() {
            this.visible = true;
            this.$nextTick(() => {
                this.$refs.form.resetFields();
            });
            return this;
        },
        submitForm() {
            this.$refs.form.validate((valid) => {
                if (valid) {
                    this.loading = true;
                    const collective = ({ code, message }) => {
                            if (code === 200) {
                                this.$message.success(message);
                                this.$emit("updata");
                                this.resetForm();
                            } else {
                                this.$message.error(message);
                            }
                        },
                        finallyF = () => {
                            this.loading = false;
                        };

                    if (this.isEdit) {
                        updateMsgTemp({ ...this.form, id: this.id })
                            .then(collective)
                            .finally(finallyF);
                    } else {
                        createMsgTemp(this.form)
                            .then(collective)
                            .finally(finallyF);
                    }
                } else {
                    return false;
                }
            });
        },
        getMsgTempInfo(id) {
            this.id = id;
            getMsgTempInfo({ id }).then(({ code, data, message }) => {
                if (code === 200) {
                    const { name, messages, type, firstData, remakeData } =
                        data;

                    this.form.name = name;
                    this.form.type = type;
                    this.form.firstData = firstData;
                    this.form.remakeData = remakeData;
                    this.form.messages = messages.map((item) => {
                        return {
                            id: item.id,
                            name: item.name,
                        };
                    });
                } else {
                    this.$message.error(message);
                }
            });
        },
        resetForm() {
            this.visible = false;
            this.$refs.form.resetFields();
        },
    },
};
</script>

<style lang="scss" scoped>
.drawer_form {
    height: 100%;
    overflow-y: auto;
    position: relative;
    padding: 0 40px 0 20px;
    &__footre {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        text-align: center;
        padding: 20px 0;
        border-top: 1px solid #dcdfe6;
    }
    .form_icon {
        font-size: 18px;
        padding: 5px;
        margin-left: 10px;
        cursor: pointer;
        color: #3a8ee6;
        &:last-child {
            color: #f56c6c;
        }
    }
}
</style>
