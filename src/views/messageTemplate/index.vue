<!--
 * @Descripttion: 消息模板
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-04-01 13:37:02
 * @LastEditors: jingrou
 * @LastEditTime: 2022-06-14 09:51:11
-->
<template>
    <div class="card_warp">
        <div class="search_warp">
            <el-form :inline="true" :model="searchform" ref="searchform">
                <el-form-item label="模板名称：">
                    <el-input
                        v-model="searchform.name"
                        placeholder="模板名称"
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleSearch"
                        >查 询</el-button
                    >
                    <el-button @click="handleSearchReset">重 置</el-button>
                </el-form-item>
            </el-form>
        </div>
        <div class="btn_warp">
            <span>消息模板</span>
            <div>
                <el-button
                    type="primary"
                    icon="el-icon-plus"
                    size="small"
                    @click="handleAddEdit(false)"
                    >添加模板</el-button
                >
                <!-- <el-button type="primary" icon="el-icon-position" :loading="sendloading" size="small" @click="send()">作业推送</el-button>
				<el-button type="primary" icon="el-icon-position" :loading="sendCClassloading" size="small" @click="sendClassReminder()">上课提醒</el-button>-->
            </div>
        </div>
        <!-- <div v-loading="loading"> -->
        <div class="content_warp">
            <el-table
                border
                :header-cell-style="{
                    background: '#fafafa',
                    color: '#5b5d61',
                    height: '48px',
                }"
                :data="list"
                style="width: 100%"
                :maxHeight="`${showHeight - 336}`"
            >
                <el-table-column prop="id" label="模板ID"></el-table-column>
                <el-table-column
                    prop="name"
                    label="标题"
                    show-overflow-tooltip
                ></el-table-column>
                <el-table-column label="发送至">
                    <template slot-scope="scope">{{
                        scope.row.type == "0" ? "消息通知" : "待办"
                    }}</template>
                </el-table-column>
                <el-table-column
                    prop="address"
                    label="操作"
                    width="200"
                    align="right"
                    fixed="right"
                >
                    <template slot-scope="scope">
                        <el-link
                            type="primary"
                            icon="el-icon-edit"
                            @click="handleAddEdit(true, scope.row)"
                            size="small"
                            >编辑</el-link
                        >
                        <el-link
                            type="danger"
                            style="margin-left: 10px"
                            size="small"
                            @click="handleRemoveHitn(scope.row)"
                        >
                            删除
                            <i class="el-icon-delete" />
                        </el-link>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="paginationBlock">
            <el-pagination
                background
                @current-change="handleCurrentChange"
                :current-page.sync="pagination.current"
                :page-size="pagination.size"
                layout="total, prev, pager, next"
                :total="pagination.total"
            >
                <span>总共 {{ pagination.total }} 条记录</span>
            </el-pagination>
        </div>
        <!-- </div> -->
        <!--  -->
        <el-dialog
            title="确定删除该消息模板？"
            :visible.sync="dialogVisible"
            width="400px"
        >
            <span>删除后将无法使用该模板发送消息</span>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="handleRemove"
                    >确 定</el-button
                >
            </span>
        </el-dialog>
        <!--  -->
        <addEdit ref="addEdit" :isEdit="isEdit" @updata="getMsgTempList" />
    </div>
</template>
<script>
// 作业推送 上课提醒 接口 sendTask, sendClassReminder
// import { getMsgTempList, deleteMsgTemp } from "@/api/messageTemplate.js";
import addEdit from "./addEdit";
export default {
    components: { addEdit },
    data() {
        return {
            list: [],
            pagination: {
                size: 10,
                current: 1,
                total: 0,
            },
            searchform: {
                naem: "",
            },
            dialogVisible: false,
            isEdit: false,
            loading: false,
            id: null,
            sendloading: false,
            sendCClassloading: false,
            showHeight: document.documentElement.clientHeight,
        };
    },
    created() {
        this.getMsgTempList();
    },
    methods: {
        // 作业推送
        // sendClassReminder() {
        // 	this.sendCClassloading = true;
        // 	sendClassReminder().then(({ code, msg }) => {
        // 		if (code == 200) {
        // 			this.$message.success(msg)
        // 		} else {
        // 			this.$message.error(msg)
        // 		}

        // 	}).finally(() => {
        // 		this.sendCClassloading = false;
        // 	})
        // },
        // 上课提醒
        // send() {
        // 	this.sendloading = true;
        // 	sendTask().then(({ code, msg }) => {
        // 		if (code == 200) {
        // 			this.$message.success(msg)
        // 		} else {
        // 			this.$message.error(msg)
        // 		}

        // 	}).finally(() => {
        // 		this.sendloading = false;
        // 	})
        // },
        getMsgTempList(isSearch) {
            this.loading = true;
            const params = {
                size: this.pagination.size,
                current: this.pagination.current,
            };

            if (isSearch === "search") {
                params["name"] = this.searchform.name;
            }
            // getMsgTempList(params)
            // 	.then(({ code, data, message }) => {
            // 		if (code === 200) {
            // 			const { records, total, size, current } = data;

            // 			this.pagination.total = total;
            // 			this.pagination.size = size;
            // 			this.pagination.current = current;
            // 			this.list = records;
            // 		} else {
            // 			this.$message.error(message);
            // 		}
            // 	})
            // 	.finally(() => {
            // 		this.loading = false;
            // 	});
        },
        handleRemoveHitn(row) {
            this.id = row.id;
            this.dialogVisible = true;
        },
        // 删除
        handleRemove() {
            // deleteMsgTemp(this.id).then(({ code, message }) => {
            // 	if (code === 200) {
            // 		this.$message.success(message);
            // 		this.dialogVisible = false;
            // 		this.getMsgTempList();
            // 	} else {
            // 		this.$message.error(message);
            // 	}
            // });
        },
        // 添加编辑
        handleAddEdit(isEdit, row) {
            this.isEdit = isEdit;
            this.$refs.addEdit.open();
            isEdit && this.$refs.addEdit.getMsgTempInfo(row.id);
        },
        // 搜索
        handleSearch() {
            this.pagination.current = 1;
            this.getMsgTempList("search");
        },
        // 重置
        handleSearchReset() {
            this.$refs.searchform.resetFields();
            this.searchform.name = null;
            this.pagination.current = 1;
            this.getMsgTempList();
        },
        handleCurrentChange(current) {
            this.pagination.current = current;
            this.getMsgTempList("search");
        },
    },
};
</script>

<style lang="scss" scoped>
.card_warp {
    background-color: #fff;
    padding: 20px;
    .content_warp {
        margin: 20px 0;
        border: 1px solid rgb(216, 220, 229);
        ::v-deep .has-gutter tr th {
            background-color: rgb(235, 238, 245);
            height: 48px;
        }
    }
    .btn_warp {
        display: flex;
        justify-content: space-between;
        margin: 16px 0;
        padding-top: 16px;
        border-top: 1px solid #ebeef5;
        span {
            margin: 10px;
        }
        div {
            display: flex;
        }
    }
}
.paginationBlock {
    margin-top: 16px;
    text-align: right;
}
</style>
