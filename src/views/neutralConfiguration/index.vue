<!--
 * @Descripttion: 
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-11-18 15:03:58
 * @LastEditors: jingrou
 * @LastEditTime: 2023-06-15 15:45:42
-->
<template>
    <div>
        <el-card shadow="never">
            <!-- 查询表单 -->
            <el-form
                ref="queryForm"
                :inline="true"
                :model="queryForm"
                class="demo-form-inline"
                label-width="140px"
            >
                <el-form-item label="网页网站名称：">
                    <el-input
                        v-model="queryForm.websiteName"
                        placeholder="请输入网页网站名称"
                    />
                </el-form-item>
                <el-form-item label="移动端网站名称：">
                    <el-input
                        v-model="queryForm.websiteMobileName"
                        placeholder="请输入移动端网站名称"
                    />
                </el-form-item>
                <el-form-item label="网站路径：">
                    <el-input
                        v-model="queryForm.url"
                        placeholder="请输入网站路径"
                    />
                </el-form-item>
                <el-form-item>
                    <el-button
                        type="primary"
                        style="width: 100px; margin-left: 16px"
                        icon="el-icon-search"
                        @click="queryFn"
                        >查询</el-button
                    >
                    <el-button @click="resetFn">重置</el-button>
                </el-form-item>
            </el-form>
            <!-- 按钮  -->
            <div class="btn">
                <span>中性版配置</span>
                <div class="multipartFile">
                    <el-button
                        size="medium"
                        icon="el-icon-plus"
                        type="primary"
                        v-auth="'manage.neutralConfiguration.add'"
                        @click="operationConfigFn('create')"
                        >新增</el-button
                    >
                </div>
            </div>
            <!-- 表格 -->
            <div v-loading="tableloading">
                <el-table
                    :data="tableData"
                    border
                    :header-cell-style="{
                        background: '#fafafa',
                        color: '#5b5d61',
                    }"
                >
                    <el-table-column
                        type="index"
                        label="序号"
                        width="50"
                        align="center"
                    />
                    <el-table-column
                        prop="cloudUri"
                        label="网页网址"
                        align="center"
                        show-overflow-tooltip
                    />

                    <el-table-column
                        prop="mobileUri"
                        label="移动端网址"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="websiteId"
                        label="网址ID"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="websiteLogo"
                        label="网站logo"
                        align="center"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="websiteMobileName"
                        label="移动端网站名称"
                        align="center"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="websiteName"
                        label="网页网站名称"
                        align="center"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="createBy"
                        label="创建者"
                        align="center"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="createTime"
                        label="创建时间"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="updateBy"
                        label="修改人"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="updateTime"
                        label="修改时间"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="filingDesc"
                        label="备案号"
                        align="center"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        label="操作"
                        align="right"
                        width="180"
                        fixed="right"
                    >
                        <template slot-scope="scope">
                            <el-button
                                v-auth="'manage.neutralConfiguration.edit'"
                                type="text"
                                icon="el-icon-edit"
                                @click="operationConfigFn('edit', scope.row)"
                                >编辑</el-button
                            >
                            <el-link
                                type="danger"
                                v-auth="'manage.neutralConfiguration.del'"
                                style="margin-left: 10px"
                                size="small"
                                @click="operationConfigFn('delete', scope.row)"
                            >
                                删除
                                <i class="el-icon-delete" />
                            </el-link>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <!-- 分页 -->
            <div class="paginationBlock">
                <el-pagination
                    :current-page.sync="pagination.current"
                    background
                    :page-size="pagination.pagesize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pagination.total"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                >
                    <span>总共 {{ pagination.total }} 条记录</span>
                </el-pagination>
            </div>
        </el-card>
        <!-- 新增、编辑 -->
        <el-dialog
            :title="isEdit ? '编辑配置' : '新增配置'"
            width="30%"
            :visible.sync="dialogVisible"
            @close="cancelFormFn"
        >
            <el-form
                :rules="rules"
                ref="websiteFormRef"
                label-position="right"
                label-width="140px"
                :model="websiteForm"
            >
                <el-form-item label="网页网站名称：" prop="websiteName">
                    <el-input
                        style="width: 100%"
                        v-model="websiteForm.websiteName"
                        placeholder="请输入网页网站名称"
                    ></el-input>
                </el-form-item>
                <el-form-item label="移动端网站名称：" prop="websiteMobileName">
                    <el-input
                        style="width: 100%"
                        v-model="websiteForm.websiteMobileName"
                        placeholder="请输入移动端网站名称"
                    ></el-input>
                </el-form-item>
                <el-form-item label="网站Logo：" prop="websiteLogo">
                    <el-upload
                        :headers="{
                            Authorization: token,
                            platform: 'system',
                        }"
                        :data="{
                            folderType: 'manage',
                            folders: 'website'
                        }"
                        :action="action"
                        :show-file-list="false"
                        :file-list="websiteForm.filelist"
                        :on-success="importList"
                        :on-error="importError"
                        name="file"
                        :multiple="false"
                        class="upload_logo"
                    >
                        <img
                            v-if="websiteForm.websiteLogo !== ''"
                            :src="websiteForm.websiteLogo"
                        />
                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                </el-form-item>
                <el-form-item label="网页地址：" prop="cloudUri">
                    <el-input
                        style="width: 100%"
                        v-model="websiteForm.cloudUri"
                        placeholder="请输入网页地址"
                    ></el-input>
                </el-form-item>
                <el-form-item label="移动端地址：" prop="mobileUri">
                    <el-input
                        style="width: 100%"
                        v-model="websiteForm.mobileUri"
                        placeholder="请输入移动端地址"
                    ></el-input>
                </el-form-item>
                <el-form-item label="备案号：" prop="filingDesc">
                    <el-input
                        style="width: 100%"
                        v-model="websiteForm.filingDesc"
                        placeholder="请输入备案号"
                    ></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog_footer">
                <el-button @click="cancelFormFn">取 消</el-button>
                <el-button
                    type="primary"
                    v-loading="btnLoading"
                    @click="formOkFn"
                    >确 定</el-button
                >
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { ACCESS_TOKEN } from "@/store/mutation-types";
import {
    getConfigPage,
    createConfig,
    getConfigInfo,
    updateConfig,
    deleteConfig,
    uploadConfigLogo
} from "@/api/neutralConfiguration.js";
export default {
    data() {
        return {
            action:
                process.env.VUE_APP_API_BASE_URL + "/file/common/upload",
            queryForm: {
                websiteName: "",
                websiteMobileName: '',
                url: "",
            },
            tableloading: false,
            tableData: [],
            pagination: {
                pageSize: 10,
                pageNo: 1,
                total: 0,
            },
            btnLoading: false,
            isEdit: false,
            dialogVisible: false,
            rules: {
                websiteMobileName: [
                    {
                        required: true,
                        message: "请输入",
                        trigger: "blue",
                    },
                ],
                websiteName: [
                    {
                        required: true,
                        message: "请输入",
                        trigger: "blue",
                    },
                ],
                websiteLogo: [
                    {
                        required: true,
                        message: "请上传网站logo",
                        trigger: "blue",
                    },
                ],
                cloudUri: [
                    {
                        required: true,
                        message: "请输入",
                        trigger: "blue",
                    },
                ],
                mobileUri: [
                    {
                        required: true,
                        message: "请输入",
                        trigger: "blue",
                    },
                ],
                filingDesc: [
                    {
                        required: true,
                        message: "请输入",
                        trigger: "blue",
                    },
                ],
            },
            websiteForm: {
                filelist: [],
                websiteName: "",
                websiteMobileName: '', 
                websiteLogo: "https://alicdn.1d1j.cn/manage/website/20230613/20d2082c2d034721a0d421be075081b0.jpg",
                cloudUri: "",
                mobileUri: "",
                filingDesc: "",
            },
            token: "",
        };
    },
    methods: {
        importList(res) {
            const { url } = res.data[0]
            if (res.code === 0) {
                this.websiteForm.websiteLogo = url;
                this.websiteForm.filelist = res.data;
            } else {
                this.$message.error(res.message);
            }
        },
        importError(res) {
            this.$message.error(res.message);
        },
        cancelFormFn() {
            this.dialogVisible = false;
            this.$refs.websiteFormRef.resetFields();
        },
        getConfigInfoFn(websiteId) {
            getConfigInfo({ websiteId }).then((res) => {
                this.websiteForm = res.data || {};
            });
        },
        getConfigPageFn() {
            this.tableloading = true;
            let obj = {
                ...this.queryForm,
                pageSize: this.pagination.pageSize,
                pageNo: this.pagination.pageNo,
            };
            getConfigPage(obj)
                .then((res) => {
                    const { list, total } = res.data;
                    this.tableData = list;
                    this.pagination.total = total;
                })
                .finally(() => {
                    this.tableloading = false;
                });
        },
        operationConfigFn(status, data) {
            this.isEdit = !!(status === "edit");
            if (status !== "delete") {
                this.dialogVisible = true;
                if (this.isEdit) {
                    this.getConfigInfoFn(data.websiteId);
                }
            } else {
                this.$confirm(
                    "如删除此配置后，则不可恢复，是否确认删除？",
                    "提示",
                    {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    }
                )
                    .then(() => {
                        deleteConfig({ websiteId: data.websiteId }).then(
                            (res) => {
                                this.$message.success(res.message);
                                this.getConfigPageFn();
                            }
                        );
                    })
                    .catch(() => {
                        this.$message({
                            type: "info",
                            message: "已取消删除",
                        });
                    });
            }
        },
        formOkFn() {
            this.btnLoading = true;
            if (!this.isEdit) {
                createConfig(this.websiteForm)
                    .then((res) => {
                        this.$message.success(res.message);
                        this.cancelFormFn();
                        this.getConfigPageFn();
                    })
                    .finally(() => {
                        this.btnLoading = false;
                    });
            } else {
                updateConfig(this.websiteForm)
                    .then((res) => {
                        this.$message.success(res.message);
                        this.cancelFormFn();
                        this.getConfigPageFn();
                    })
                    .finally(() => {
                        this.btnLoading = false;
                    });
            }
        },
        // 分页
        handleCurrentChange(val) {
            this.pagination.pageNo = val;
            this.getConfigPageFn();
        },
        handleSizeChange(val) {
            this.pagination.pageSize = val;
            this.getConfigPageFn();
        },
        queryFn() {
            this.handleCurrentChange(1);
        },
        resetFn() {
            this.handleCurrentChange(1);
        },
    },
    created() {
        this.token = "Bearer " + this.$ls.get(ACCESS_TOKEN);
        this.getConfigPageFn();
    },
};
</script>

<style lang="scss" scoped>
.upload_logo {
    :deep(.el-upload) {
        img {
            width: 150px;
            height: 149px;
            border: 1px solid #eee;
        }
        i {
            width: 150px;
            height: 149px;
            border: 1px solid #eee;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}
.btn {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    padding-top: 16px;
    border-top: 1px solid #ebeef5;
    span {
        margin: 10px;
    }
    .multipartFile {
        display: flex;
    }
}
.paginationBlock {
    margin-top: 16px;
    text-align: right;
}
.footerButton {
    width: 100%;
    text-align: center;
    border-top: 1px solid #dcdfe6;
    position: absolute;
    padding-top: 20px;
    bottom: 0;
    margin: 0;
    padding: 20px 0;
}
.download {
    justify-content: center;
    display: flex;
    align-items: center;
    margin: 10px;
    a {
        color: #409eff;
    }
}
</style>
<style lang="scss">
.dialogVisibleForm {
    .formBox {
        display: flex;
        justify-content: space-between;
        .el-input {
            width: 270px;
        }
        .el-textarea__inner {
            width: 270px;
        }
    }
}
.demo-form-inline {
    .queryForm {
        display: block;
        display: flex;
    }
    .el-input {
        width: 160px;
    }
    .el-textarea__inner {
        width: 240px;
    }
    .btnShow {
        display: block;
    }
    .itemShow,
    .itemShow2 {
        display: none;
    }
}
</style>
