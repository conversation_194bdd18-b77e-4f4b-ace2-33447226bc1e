<template>
    <div class="card_warp">
        <el-form ref="versionForm" :model="versionForm" status-icon :inline="true" label-width="auto"
            class="demo-ruleForm">
            <el-form-item label="应用标签：">
                <el-select v-model="customParamsArr" @change="customParamsChange" value-key="type" multiple
                    collapse-tags placeholder="请选择">
                    <el-option v-for="item in customParamsOpt" :key="item.type" :label="item.label" :value="item">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="版本名称：">
                <el-input v-model="versionForm.versionName" placeholder="请输入版本名称" />
            </el-form-item>
            <el-form-item label="应用类型：">
                <el-cascader v-model="versionForm.appTypeId" :options="appTypeList" :props="{
                    value: 'id',
                    label: 'name',
                    emitPath: false,
                    children: 'typeList',
                }">
                </el-cascader>
            </el-form-item>
            <el-form-item label="创建日期：">
                <el-date-picker v-model="versionForm.createTime" type="date" format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd" placeholder="选择日期" />
            </el-form-item>
            <el-form-item label="更新日期：">
                <el-date-picker v-model="versionForm.updateTime" type="date" format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd" placeholder="选择日期" />
            </el-form-item>
            <el-form-item :label="`${it.label}：`" v-for="(it, index) in customParamsArr" :key="it.type">
                <el-input v-model="versionForm[it.type]" placeholder="请输入" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="query" icon="el-icon-search">查询</el-button>
                <el-button @click="searchResetForm('versionForm')">重置</el-button>
            </el-form-item>
        </el-form>
        <!-- 按钮 -->
        <div class="btn_warp" style="
                display: flex;
                align-items: center;
                justify-content: space-between;
            ">
            <span>版本列表</span>
            <div>
                <el-button v-auth="'manage.newVersionManagement.add'" type="primary" icon="el-icon-plus"
                    @click="addVersionFn">新增</el-button>
                <el-button type="danger" @click="batchDelete"
                    v-auth="'manage.newVersionManagement.del'">批量删除</el-button>
                <!-- <el-button
                    type="primary"
                    @click="administrationFn"
                    >管理</el-button
                > -->
            </div>
        </div>
        <!-- 表格 -->
        <el-table class="el-tables" style="width: 100%; margin-top: 16px" :data="tableData || []" border
            @selection-change="handleSelectionChange" :header-cell-style="{ background: '#fafafa', color: '#5b5d61' }">
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column type="index" width="50" label="序号" align="center"></el-table-column>
            <el-table-column prop="versionName" show-overflow-tooltip label="版本名称" align="center">
                <template slot-scope="scope">
                    <span>{{ scope.row.versionName }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="versionCode" show-overflow-tooltip label="版本号" align="center">
                <template slot-scope="scope">
                    <span>{{ scope.row.versionCode }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="versionDesc" show-overflow-tooltip label="更新说明" align="center">
                <template slot-scope="scope">
                    <span>{{ scope.row.versionDesc }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="filePath" label="文件地址" show-overflow-tooltip align="center">
                <template slot-scope="scope">
                    <span style="cursor: pointer" @click="authorizeCopy(scope.row.filePath || '')">
                        {{ scope.row.filePath }}
                    </span>
                </template>
            </el-table-column>
            <el-table-column prop="createTime" show-overflow-tooltip label="创建时间" align="center">
                <template slot-scope="scope">
                    <span>{{ scope.row.createTime }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="updateTime" show-overflow-tooltip label="更新时间" align="center">
                <template slot-scope="scope">
                    <span>{{ scope.row.updateTime }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="schoolName" show-overflow-tooltip label="学校名称" align="center">
                <template slot-scope="scope">
                    <span>{{ scope.row.schoolName }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="appTypeName" show-overflow-tooltip label="应用类型" align="center">
                <template slot-scope="scope">
                    <span>{{ scope.row.appTypeName }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="systemName" show-overflow-tooltip label="系统类型" align="center">
                <template slot-scope="scope">
                    <span>{{ scope.row.systemName }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="versionClaim" show-overflow-tooltip label="安装要求" align="center">
                <template slot-scope="scope">
                    <span>{{ scope.row.versionClaim }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="versionType" show-overflow-tooltip label="版本类型" align="center">
                <template slot-scope="scope">
                    <span>{{
                        scope.row.versionType === 1 ? "定制版本" : "通用版本"
                        }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="isCompulsory" show-overflow-tooltip label="更新类型" align="center">
                <template slot-scope="scope">
                    <span>{{
                        scope.row.isCompulsory === 1 ? "强制" : "不强制"
                        }}</span>
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="180" align="right">
                <template slot-scope="scope">
                    <!-- <div v-if="scope.row.isEnable === 2"> -->
                    <el-link type="primary" icon="el-icon-edit" v-auth="'manage.newVersionManagement.edit'"
                        @click="editVersionFn(scope.row)">编辑</el-link>
                    <el-button v-if="scope.row.isEnable === 2" style="margin-left: 10px" type="text" v-auth="'manage.newVersionManagement.enable'"
                        @click="stateInfo(scope.row)">启用</el-button>
                    <!-- </div>
                    <div v-else> -->
                    <el-button style="margin-left: 10px" type="text" @click="lookOver(scope.row)">查看</el-button>
                    <!-- </div> -->
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <el-pagination background style="margin-top: 16px; text-align: right" :current-page="pagination.pageNo"
            :page-sizes="[10, 20, 30, 50]" :page-size="pagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper" :total="pagination.total" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        <!-- 新增、编辑弹窗 -->
        <el-drawer :title="drawerTitle" @close="closeDrawer" :visible.sync="visibleForm" :close-on-press-escape="false"
            :destroy-on-close="true" :wrapperClosable="false" class="drawerClass">
            <el-form v-loading="loading" element-loading-text="文件上传中..." ref="versionFormVisible"
                :model="versionFormVisible" label-width="100px" class="demo-ruleForm">
                <div class="yd-form-box">
                    <el-form-item label="文件地址" :rules="{
                        required: true,
                        message: '请上传文件',
                    }" prop="filePath">
                        <el-upload action="#" accept="application/vnd.android.package-archive,.apk,.exe"
                            :on-change="onChangeData" :http-request="handleRequest" :before-upload="beforeUpload"
                            :before-remove="uploadRemove" :file-list="fileList" :show-file-list="true" :limit="1">
                            <el-button size="small" type="primary" :disabled="forbid">
                                {{
                                    !this.fileList.length
                                        ? "点击上传"
                                        : "更换附件"
                                }}
                            </el-button>
                        </el-upload>
                    </el-form-item>
                    <el-form-item label="版本名称" prop="versionName" :rules="[
                        {
                            required: true,
                            message: '请输入版本名称',
                            trigger: 'blur',
                        },
                    ]">
                        <el-input placeholder="上传文件自动获取" :disabled="forbid"
                            v-model="versionFormVisible.versionName"></el-input>
                    </el-form-item>
                    <el-form-item label="版本号" prop="versionCode" :rules="[
                        {
                            required: true,
                            message: '请输入版本号',
                            trigger: 'blur',
                        },
                    ]">
                        <el-input placeholder="上传文件自动获取" :disabled="forbid" v-model="versionFormVisible.versionCode" />
                    </el-form-item>
                    <el-form-item label="应用类型" prop="appTypeId" :rules="[
                        {
                            required: true,
                            message: '请选择应用类型',
                            trigger: 'change',
                        },
                    ]">
                        <el-cascader placeholder="上传文件自动获取" style="width: 100%" :disabled="forbid"
                            v-model="versionFormVisible.appTypeId" :options="appTypeList" :show-all-levels="false"
                            :props="{
                                value: 'id',
                                label: 'name',
                                emitPath: false,
                                children: 'typeList',
                            }">
                        </el-cascader>
                    </el-form-item>
                    <el-form-item label="版本类型" prop="versionType" :rules="[
                        {
                            required: true,
                            message: '请选择版本类型',
                            trigger: 'change',
                        },
                    ]">
                        <el-select v-model="versionFormVisible.versionType" style="width: 100%" :disabled="forbid"
                            placeholder="请选择版本类型">
                            <el-option :value="1" label="定制版本" />
                            <el-option :value="2" label="通用版本" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="更新类型" prop="isCompulsory" :rules="[
                        {
                            required: true,
                            message: '请选择更新类型',
                            trigger: 'change',
                        },
                    ]">
                        <el-select v-model="versionFormVisible.isCompulsory" style="width: 100%" :disabled="forbid"
                            placeholder="请选择更新类型">
                            <el-option v-for="(item, index) in updatetypeList" :key="index" :value="item.id"
                                :label="item.name" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="学校名称" prop="schoolIds">
                        <el-select multiple filterable :disabled="forbid" style="width: 100%" default-first-option
                            v-model="versionFormVisible.schoolIds" placeholder="请选择学校名称">
                            <el-option v-for="(item, index) in schoolNameList" :key="index" :label="item.name"
                                :value="item.id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="更新说明" prop="versionDesc" :rules="[
                        {
                            required: true,
                            message: '请输入更新说明',
                            trigger: 'blur',
                        },
                    ]">
                        <el-input v-model="versionFormVisible.versionDesc" type="textarea" :maxlength="1000"
                            :disabled="forbid" :rows="5" show-word-limit />
                    </el-form-item>
                    <el-form-item label="安装要求" prop="versionClaim">
                        <el-input v-model="versionFormVisible.versionClaim" type="textarea" :maxlength="1000"
                            :disabled="forbid" :rows="5" show-word-limit />
                    </el-form-item>
                    <el-form-item label="设备频率" prop="rfid_frequency">
                        <el-input placeholder="上传文件自动获取" v-model="versionFormVisible.rfid_frequency" :disabled="true" />
                    </el-form-item>
                    <el-form-item label="读写板品牌" prop="rfid_vendor">
                        <el-input placeholder="上传文件自动获取" v-model="versionFormVisible.rfid_vendor" :disabled="true" />
                    </el-form-item>
                </div>
                <div style="height: 100px"></div>
                <el-form-item class="yd-form-footer" v-if="this.drawerTitle !== '查看'">
                    <el-button class="reset" @click="closeDrawer">取消</el-button>
                    <el-button class="submitForm" type="primary" :loading="newVersionLoading"
                        @click="submitForm()">保存</el-button>
                </el-form-item>
            </el-form>
        </el-drawer>
    </div>
</template>

<script>
import {
    appVersionList,
    addVersionInfoApi,
    deleteAppVersion,
    querySystemList,
    editVersionInfoApi,
    echoVersionInfo,
    updateAppEnable,
    versionUpload,
    getAppVersionLabelList,
} from "@/api/newVersionManagement.js";
import JSZip from "jszip";
import { getUploadFileOSSpath } from "@/utils/oss";
import { searchVersionSchool } from "@/api/application.js";

const BRAND_ALIASES = {
    "YD-TGUHF3": "安的",
};

export default {
    data() {
        return {
            customParamsArr: [],
            customParamsOpt: [],
            editId: "",
            loading: false,
            fileList: [],
            pagination: {
                pageNo: 1,
                pageSize: 10,
                total: 0,
            },
            appTypeList: [],

            versionForm: {
                versionName: "",
                appTypeId: "",
                createTime: "",
                updateTime: "",
                rfid_frequency: "",
                rfid_vendor: "",
                customParams: [],
            },
            tableData: [],
            drawerTitle: "新增",
            updatetypeList: [
                { id: 1, name: "强制" },
                { id: 0, name: "不强制" },
            ],
            visibleForm: false,
            versionFormVisible: {
                rfid_frequency: "",
                rfid_vendor: "",
                versionType: "",
                versionCode: "",
                versionName: "",
                versionDesc: "",
                isCompulsory: 0,
                appTypeId: "",
                filePath: "",
                schoolIds: [],
                labelList: [],
            },
            schoolNameList: [],
            multipleSelection: [],
            newVersionLoading: false,
        };
    },
    computed: {
        forbid() {
            return this.drawerTitle === "查看";
        },
    },
    created() {
        this.getVersionList(); // 获取版本列表
        this.getyAppTypeList(); // 应用类型
        this.searchSchoolList(); // 学校接口
        this.reqAppVersionLabelList();
    },
    watch: {
        visibleForm(v) {
            if (v) {
                this.$refs.versionFormVisible &&
                    this.$refs.versionFormVisible.clearValidate();
            }
        },
    },
    methods: {
        parseProperties(propertiesString) {
            var properties = {};
            var lines = propertiesString.split("\n");
            for (var i = 0; i < lines.length; i++) {
                var line = lines[i].trim();
                if (line) {
                    var parts = line.split("=");
                    var key = parts[0].trim();
                    var value = parts[1].trim();
                    properties[key] = value;
                }
            }
            return properties;
        },

        async convertAndReadApk(data) {
            const file = data;
            if (!file) return;
            // 确保文件后缀为.apk
            if (file.name.toLowerCase().endsWith(".apk")) {
                const zip = new JSZip();
                const zipContent = await zip.loadAsync(file);

                // 将APK文件转换为ZIP格式
                const blob = await zipContent.generateAsync({
                    type: "nodebuffer",
                });
                const zipFile = new Blob([blob], { type: "application/zip" });
                // 从ZIP文件中读取内容
                // 例如，读取ZIP内的一个名为"assets/upgrade.properties"的文件
                const properties = await zipContent
                    .file("assets/upgrade.properties")
                    .async("string");
                console.log(properties);
                const propertiesObj = this.parseProperties(properties);
                console.log("propertiesObj", propertiesObj);
                this.versionFormVisible.versionName = propertiesObj[`app.name`];
                this.versionFormVisible.appTypeId = Number(
                    propertiesObj[`app.type`]
                );
                this.versionFormVisible.versionCode =
                    propertiesObj[`app.versionName`];
                this.versionFormVisible.rfid_frequency =
                    propertiesObj[`labels.rfid_frequency`];
                this.versionFormVisible.rfid_vendor =
                    propertiesObj[`labels.rfid_vendor`];
                this.$refs.versionFormVisible.validate();
                this.versionFormVisible.labelList = [];
                if (this.versionFormVisible.rfid_frequency) {
                    this.versionFormVisible.labelList.push({
                        type: "rfid_frequency",
                        value: this.versionFormVisible.rfid_frequency,
                        label: "设备频率",
                    });
                }
                if (this.versionFormVisible.rfid_vendor) {
                    this.versionFormVisible.labelList.push({
                        type: "rfid_vendor",
                        value: this.versionFormVisible.rfid_vendor,
                        label: "读写板品牌",
                    });
                }
            } else if (file.name.toLowerCase().endsWith(".exe")) {
                // 从文件名获取数据
                const info = file.name.split("_");
                if (info[0] === "馆员工作站") {
                    // 根据报名生成数据
                    this.versionFormVisible.versionName = info[0];
                    this.versionFormVisible.appTypeId = Number(info[3]);
                    this.versionFormVisible.versionCode = info[4];
                    this.versionFormVisible.rfid_frequency = info[2];
                    this.versionFormVisible.rfid_vendor =
                        BRAND_ALIASES[info[1]];
                    this.$refs.versionFormVisible.validate();
                    this.versionFormVisible.labelList = [];
                    if (this.versionFormVisible.rfid_frequency) {
                        this.versionFormVisible.labelList.push({
                            type: "rfid_frequency",
                            value: this.versionFormVisible.rfid_frequency,
                            label: "设备频率",
                        });
                    }
                    if (this.versionFormVisible.rfid_vendor) {
                        this.versionFormVisible.labelList.push({
                            type: "rfid_vendor",
                            value: this.versionFormVisible.rfid_vendor,
                            label: "读写板品牌",
                        });
                    }
                } else {
                    this.$message.warning("请手动输入应用版本信息");
                }
            } else {
                this.$message.error("非APK文件请手动输入应用版本信息");
            }
        },

        customParamsChange(val) {
            this.versionForm.rfid_frequency = "";
            this.versionForm.rfid_vendor = "";
        },
        reqAppVersionLabelList() {
            getAppVersionLabelList().then((res) => {
                console.log("res", res);
                this.customParamsOpt = res.data;
            });
        },
        // 复制
        authorizeCopy(data) {
            let url = data;
            let oInput = document.createElement("input");
            oInput.value = url;
            document.body.appendChild(oInput);
            oInput.select(); // 选择对象;
            document.execCommand("Copy"); // 执行浏览器复制命令
            this.$message({
                message: "复制成功",
                type: "success",
            });
            oInput.remove();
        },
        beforeUpload(file) {
            // const fileType = file.type;
            // if (
            //     fileType !== "application/vnd.android.package-archive" &&
            //     fileType !== "application/x-msdownload"
            // ) {
            //     this.$message.error("只能上传APK文件和EXE文件");
            //     return false; // 阻止上传
            // }
            // return true; // 允许上传
        },
        onChangeData(file, fileList) {
            this.fileList = fileList;
        },
        async uploadFile(data) {
            try {
                const url = await getUploadFileOSSpath(
                    data.file,
                    "clientVersionPackage"
                );
                if (url) {
                    this.versionFormVisible.filePath = url;
                    this.$refs.versionFormVisible.validate();
                } else {
                    this.$message.error("上传失败");
                    this.fileList = [];
                    this.versionFormVisible.filePath = "";
                }
            } catch (e) {
                console.log("e", e);
                this.fileList = [];
                this.versionFormVisible.filePath = "";
            } finally {
                this.loading = false; // 文件上传完结束加载
            }
        },

        // 上传
        async handleRequest(data) {
            this.loading = true; // 开启加载
            const params = new FormData();
            params.append("file", data.file);
            this.uploadFile(data); // 去分片上传这个文件

            this.convertAndReadApk(data.file);

            // try {
            //     const res = await versionUpload(params); // 去解析文件信息
            //     const {
            //         appTypeId,
            //         versionCode,
            //         versionName,
            //         filePath,
            //         labelList,
            //     } = res.data;
            //     this.versionFormVisible.appTypeId = Number(appTypeId);
            //     this.versionFormVisible.versionCode = versionCode;
            //     this.versionFormVisible.versionName = versionName;
            //     // this.versionFormVisible.filePath = filePath;
            //     if (labelList && labelList.length) {
            //         this.versionFormVisible.labelList = labelList;
            //         this.versionFormVisible.rfid_frequency = labelList.find(
            //             (item) => item.type === "rfid_frequency"
            //         ).value; // 设备频率
            //         this.versionFormVisible.rfid_vendor = labelList.find(
            //             (item) => item.type === "rfid_vendor"
            //         ).value; // 读写板品牌
            //     } else {
            //         this.versionFormVisible.labelList = [];
            //     }
            // } catch (e) {
            //     console.log("e", e);
            //     // this.fileList = [];
            //     // this.versionFormVisible.filePath = "";
            // } finally {
            // }
        },

        uploadRemove(file) {
            this.versionFormVisible.filePath = "";
            this.fileList = [];
        },

        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        // 查询
        query() {
            this.versionForm.customParams = [];
            this.pagination.pageNo = 1;
            if (this.versionForm.rfid_frequency) {
                this.versionForm.customParams.push({
                    rfid_frequency: this.versionForm.rfid_frequency,
                });
            }
            if (this.versionForm.rfid_vendor) {
                this.versionForm.customParams.push({
                    rfid_vendor: this.versionForm.rfid_vendor,
                });
            }
            this.getVersionList();
        },
        // 获取终端
        getyAppTypeList() {
            querySystemList().then((res) => {
                this.appTypeList = res.data.map((i) => {
                    return {
                        ...i,
                        id: "x" + i.id,
                    };
                });
            });
        },
        // 学校接口
        searchSchoolList() {
            searchVersionSchool().then((res) => {
                this.schoolNameList = res.data;
            });
        },
        // 版本列表接口
        getVersionList() {
            const obj = {
                ...this.pagination,
                ...this.versionForm,
            };
            appVersionList(obj)
                .then((res) => {
                    this.tableData = res.data.list;
                    this.pagination.total = res.data.total;
                })
                .catch();
        },
        // 重置
        searchResetForm(versionForm) {
            this.versionForm.versionName = "";
            this.versionForm.rfid_frequency = "";
            this.versionForm.rfid_vendor = "";
            this.versionForm.appTypeId = "";
            this.versionForm.createTime = "";
            this.versionForm.updateTime = "";
            this.customParamsArr = [];
            this.versionForm.customParams = [];
            this.getVersionList();
        },
        // 分页
        handleSizeChange(val) {
            this.pagination.pageNo = 1;
            this.formPage.pageSize = val;
            this.getVersionList();
        },
        handleCurrentChange(val) {
            this.pagination.pageNo = val;
            this.getVersionList();
        },
        // 取消
        closeDrawer() {
            this.fileList = [];
            this.versionFormVisible.filePath = "";
            this.visibleForm = false;
        },
        // 点击新增
        addVersionFn() {
            this.drawerTitle = "新增";
            this.editId = "";
            this.visibleForm = true;
            this.fileList = [];
            this.versionFormVisible = {
                filePath: "",
                versionType: 2,
                versionCode: "",
                versionName: "",
                versionDesc: "",
                isCompulsory: 0,
                appTypeId: "",
                schoolIds: [],
                labelList: [],
            };
        },
        batchDelete() {
            const length = this.multipleSelection.length;
            if (length == 0) {
                this.$message.warning("请至少选择一项");
            } else {
                const ids = this.multipleSelection.map((i) => i.id);
                deleteAppVersion({ ids }).then((res) => {
                    if (res.code == 0) {
                        this.$message.success("已成功删除");
                        this.getVersionList();
                    } else {
                        this.$message.error(res.message);
                    }
                });
            }
        },
        // 保存的按钮
        submitForm() {
            this.$refs.versionFormVisible.validate(async (valid) => {
                if (valid) {
                    this.newVersionLoading = true;

                    let addObj = {
                        ...this.versionFormVisible,
                        id: this.editId || undefined,
                    };
                    const apiUrl = this.editId
                        ? editVersionInfoApi
                        : addVersionInfoApi;

                    apiUrl(addObj)
                        .then((res) => {
                            if (res.code === 0) {
                                this.visibleForm = false;
                                this.$message.success(res.message);
                                this.getVersionList();
                            } else {
                                this.$message.error(res.message);
                            }
                        })
                        .finally(() => {
                            this.newVersionLoading = false;
                        });
                } else {
                    return false;
                }
            });
        },
        // 点击启动
        stateInfo(res) {
            let obj = {
                id: res.id,
                isEnable: 1, // 1为启动 2为禁用
            };
            updateAppEnable(obj).then((res) => {
                if (res.code === 0) {
                    this.$message.success(res.message);
                    this.getVersionList();
                } else {
                    this.$message.error(res.message);
                }
            });
        },
        // 调用接口回显信息
        echoVersion(data) {
            echoVersionInfo({ id: data.id }).then((res) => {
                if (res.code === 0) {
                    this.visibleForm = true;
                    this.versionFormVisible = res.data;
                    const filePath = res.data.filePath;
                    const labelList = res.data.labelList;
                    this.fileList = [];
                    this.fileList.push({
                        url: filePath,
                        name: filePath,
                    });
                    this.versionFormVisible.filePath = filePath;
                    if (labelList && labelList.length) {
                        this.versionFormVisible.rfid_frequency = labelList.find(
                            (item) => item.type === "rfid_frequency"
                        ).value; // 读写板品牌
                        this.versionFormVisible.rfid_vendor = labelList.find(
                            (item) => item.type === "rfid_vendor"
                        ).value; // 设备频率
                    } else {
                        this.versionFormVisible.labelList = [];
                    }
                } else {
                    this.$message.error(res.message);
                }
            });
        },
        // 查看信息
        lookOver(data) {
            this.editId = data.id;
            this.drawerTitle = "查看";
            this.echoVersion(data);
        },
        // 点击编辑
        editVersionFn(data) {
            console.log("编辑data", data);
            this.editId = data.id;
            this.drawerTitle = "编辑";
            this.echoVersion(data);
        },
    },
};
</script>

<style lang="scss" scoped>
.card_warp {
    background-color: #fff;
    padding: 20px;

    .btn_warp {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;
        padding-top: 16px;
        border-top: 1px solid #ebeef5;

        span {
            margin: 10px;
        }

        div {
            display: flex;
        }
    }
}

.inputClass {
    width: 100%;
    padding: 0px 50px;
    margin: auto;
    overflow-y: auto;
    height: 714px;

    i {
        margin-left: 5px;
        cursor: pointer;
    }

    li {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 20px;
        width: 80%;

        img {
            margin-right: 10px;
        }
    }
}
</style>
<style lang="scss">
.drawerClass {
    .el-drawer__body {
        padding-right: 30px;
    }
}

.demo-ruleForm {
    .el-drawer__body {
        position: relative;
    }

    .yd-form-footer {
        width: 100%;
        height: 70px;
        justify-content: center;
        display: flex;
        align-items: center;
        border-top: 1px #eee solid;
        background-color: #fff;
        position: absolute;
        bottom: 0px;
        left: 0px;

        .el-form-item__content {
            margin: 0px !important;
        }
    }
}
</style>
