<template>
    <div>
        <div class="card_warp" v-if="mainPage">
            <div class="inquireBox">
                <el-form
                    ref="versionForm"
                    :model="parameterObj"
                    :inline="true"
                    label-width="auto"
                >
                    <el-form-item label="学校名称：">
                        <el-input
                            v-model="parameterObj.schoolName"
                            placeholder="请输入学校名称"
                        />
                    </el-form-item>
                    <el-form-item label="推送平台：">
                        <el-select
                            v-model="parameterObj.platform"
                            placeholder="请选择推送平台"
                        >
                            <el-option
                                v-for="item in pushOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item>
                        <el-button
                            type="primary"
                            icon="el-icon-search"
                            @click="searchSchool"
                            >查询</el-button
                        >
                        <el-button @click="resetSchool">重置</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <div class="tableBox">
                <YdTable
                    ref="table"
                    :tableColumn="tableColumn"
                    :action="['refresh', 'tableSize', 'tableSet']"
                    :data="loadData"
                >
                    <template slot="btn">
                        <el-button
                            type="primary"
                            v-auth="'manage.pushMessage.add'"
                            icon="el-icon-plus"
                            @click="messageTemplateManage"
                            >微信消息模板管理</el-button
                        >
                    </template>
                    <template slot="schoolName" slot-scope="scope">
                        <div>
                            <span>{{ scope.row.name }} </span>
                            <i
                                v-if="scope.row.name"
                                style="cursor: pointer"
                                @click="handleCopy(scope.row.name)"
                                class="el-icon-copy-document"
                            ></i>
                        </div>
                    </template>
                    <template slot="templateSchoolList" slot-scope="scope">
                        <div
                            v-if="
                                scope.row.templateSchoolList &&
                                scope.row.templateSchoolList.length
                            "
                        >
                            微信:
                            <span
                                v-for="(item, index) in scope.row
                                    .templateSchoolList"
                            >
                                {{ index + 1
                                }}{{ item.functionalModuleTypeName }}
                            </span>
                        </div>
                    </template>
                    <template slot="operation" slot-scope="scope">
                        <el-link
                            type="primary"
                            size="small"
                            v-auth="'manage.pushMessage.look'"
                            style="margin-left: 10px"
                            @click="goPushInfo(scope.row)"
                        >
                            查看
                        </el-link>
                    </template>
                </YdTable>
            </div>
        </div>
        <component
            v-else
            v-bind:is="this.currentTabComponent"
            @handleBack="handleBack"
        ></component>
    </div>
</template>

<script>
import YdTable from "@/components/YdTable";
import { pageOpenTemplate } from "@/api/pushMessage.js";
import TemplateManage from "./templateManage.vue";
import PushInfo from "./pushInfo.vue";
export default {
    name: "PushMessage",
    components: {
        YdTable,
        PushInfo,
        TemplateManage,
    },
    data() {
        return {
            currentTabComponent: TemplateManage, // 页面组件都放这里来切换
            mainPage: true, // 是否显示主页面
            data: "",
            pushOptions: [
                {
                    value: "wx",
                    label: "微信",
                },
            ],
            tableColumn: [
                {
                    label: "序号",
                    type: "index",
                    width: "50px",
                    align: "center",
                    isShow: true,
                },
                {
                    label: "学校名称",
                    showtooltip: true,
                    index: "name",
                    isShow: true,
                    width: "320",
                    scopedSlots: {
                        customRender: "schoolName",
                    },
                },
                {
                    label: "学校编号",
                    index: "id",
                    showtooltip: true,
                    isShow: true,
                    align: "center",
                    width: "180",
                },
                {
                    label: "超级账号",
                    index: "superAccount",
                    isShow: true,
                    isShow: true,
                    align: "center",
                    width: "150",
                },
                {
                    label: "推送平台",
                    index: "appTypeName",
                    isShow: true,
                    isShow: true,
                    align: "center",
                },
                {
                    label: "推送消息",
                    index: "templateSchoolList",
                    isShow: true,
                    isShow: true,
                    align: "center",
                    scopedSlots: {
                        customRender: "templateSchoolList",
                    },
                },
                {
                    label: "推送详情",
                    index: "operation",
                    isShow: true,
                    align: "left",
                    isShow: true,
                    width: 100,
                    fixed: "right",
                    scopedSlots: {
                        customRender: "operation",
                    },
                },
            ],
            loadData: (parameter) => {
                return pageOpenTemplate(
                    Object.assign(
                        { ...parameter },
                        {
                            ...this.parameterObj,
                        }
                    )
                );
            },
            parameterObj: {},
        };
    },
    provide() {
        return {
            getschoolInfo: () => this.schoolInfo,
        };
    },
    created() {},
    watch: {},
    methods: {
        // 复制学校名称
        handleCopy(data) {
            let url = data;
            let oInput = document.createElement("input");
            oInput.value = url;
            document.body.appendChild(oInput);
            oInput.select(); // 选择对象;
            console.log(oInput.value);
            document.execCommand("Copy"); // 执行浏览器复制命令
            this.$message({
                message: "复制成功",
                type: "success",
            });
            oInput.remove();
        },
        messageTemplateManage() {
            this.mainPage = false;
            this.currentTabComponent = TemplateManage;
        },
        handleBack(val) {
            this.mainPage = val;
        },
        goPushInfo(data) {
            this.mainPage = false;
            // 显示详情
            this.currentTabComponent = PushInfo;
            this.schoolInfo = data;
        },
        searchSchool() {
            this.$refs.table.handleRefresh(true);
        },
        resetSchool() {
            this.parameterObj = {};
            this.$refs.table.handleRefresh(true);
        },
    },
};
</script>

<style lang="scss" scoped>
.card_warp {
    background-color: #fff;
    padding: 20px;
}
</style>
