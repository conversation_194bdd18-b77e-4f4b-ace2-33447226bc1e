<template>
    <div class="pushInfo_warp">
        <div class="pushInfo_head">
            <div class="icon_box" @click="goBack">
                <i class="el-icon-back icon-back"></i>
            </div>
            <div
                :class="{ push_item: true, active: thisTAB === item.value }"
                v-for="(item, index) in tabArr"
                :key="item.value"
                @click="activePush(item)"
            >
                {{ item.lable }}
            </div>
        </div>
        <Pushset v-if="thisTAB === 'push_set'"></Pushset>
        <Pushrecord v-if="thisTAB === 'push_record'"></Pushrecord>
    </div>
</template>

<script>
import Pushset from "./pushset";
import Pushrecord from "./pushrecord";

export default {
    name: "PushInfo",
    components: {
        Pushset,
        Pushrecord,
    },
    data() {
        return {
            thisTAB: "push_set",
            tabArr: [
                {
                    lable: "推送设置",
                    value: "push_set",
                },
                {
                    lable: "推送记录",
                    value: "push_record",
                },
            ],
        };
    },
    created() {},
    watch: {},
    methods: {
        goBack() {
            this.$emit("handleBack", true);
        },

        activePush(data) {
            this.thisTAB = data.value;
        },
    },
};
</script>

<style lang="scss" scoped>
.pushInfo_warp {
    background-color: #fff;
    height: calc(100vh - 100px);
    overflow: auto;
}
.icon-back {
    cursor: pointer;
    font-weight: 600;
}
.pushInfo_head {
    padding-left: 20px;
    height: 56px;
    line-height: 56px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #ebeef5;
    .icon_box {
        font-size: 24px;
    }
    .push_item {
        font-size: 18px;
        font-weight: 600;
        padding-left: 20px;
        cursor: pointer;
    }
}
.active {
    color: #3963bc;
}
</style>
