<template>
    <div class="push_record_box">
        <div class="inquireBox">
            <el-form
                ref="recordForm"
                :model="parameterObj"
                :inline="true"
                label-width="auto"
            >
                <el-form-item label="模板名称：">
                    <el-input
                        v-model.trim="parameterObj.templateTitle"
                        placeholder="请输入"
                    />
                </el-form-item>
                <el-form-item label="模板ID：">
                    <el-input
                        v-model.trim="parameterObj.templateId"
                        placeholder="请输入"
                    />
                </el-form-item>
                <el-form-item label="推送时间：">
                    <el-date-picker
                        v-model="parameterObj.time"
                        type="daterange"
                        range-separator="至"
                        value-format="yyyy-MM-dd"
                        value="yyyy-MM-dd"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="推送状态：">
                    <el-select
                        v-model="parameterObj.status"
                        placeholder="请选择"
                    >
                        <el-option
                            v-for="item in options"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item>
                    <el-button
                        type="primary"
                        icon="el-icon-search"
                        @click="searchBtn"
                        >查询</el-button
                    >
                    <el-button @click="resetBtn">重置</el-button>
                </el-form-item>
            </el-form>
        </div>
        <div class="tableBox" v-loading="loading">
            <el-table
                :data="tableData"
                :border="true"
                :header-cell-style="{
                    background: '#f5f7fa',
                    color: '#5b5d61',
                }"
            >
                <el-table-column label="序号">
                    <template slot-scope="scope">
                        <span v-text="scope.$index + 1"></span>
                    </template>
                </el-table-column>
                <el-table-column prop="platform" label="推送平台">
                    <template slot-scope="scope">
                        <span>{{ "微信" }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="templateTitle" label="模板名称">
                </el-table-column>
                <el-table-column prop="templateId" label="模板ID">
                </el-table-column>
                <el-table-column
                    prop="content"
                    label="推送内容"
                    :show-overflow-tooltip="true"
                >
                </el-table-column>
                <el-table-column prop="userName" label="接收对象">
                </el-table-column>
                <el-table-column prop="createTime" label="推送时间">
                </el-table-column>
                <el-table-column prop="retryCount" label="重试次数">
                </el-table-column>
                <el-table-column
                    prop="resultParam"
                    label="最后返回值"
                    :show-overflow-tooltip="true"
                >
                </el-table-column>
                <el-table-column prop="status" label="推送状态">
                    <template slot-scope="scope">
                        <span>{{
                            scope.row.status === 1 ? "失败" : "成功"
                        }}</span>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <el-pagination
                background
                style="margin-top: 16px; text-align: right"
                :current-page="pagination.pageNo"
                :page-sizes="[10, 20, 30, 50]"
                :page-size="pagination.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="pagination.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
    </div>
</template>

<script>
import { pageOpenTemplateLog } from "@/api/pushMessage.js";

export default {
    name: "Pushrecord",
    components: {},
    inject: ["getschoolInfo"],
    computed: {
        comSchoolInfo() {
            return this.getschoolInfo();
        },
    },
    data() {
        return {
            pagination: {
                pageNo: 1,
                pageSize: 10,
                total: 0,
            },
            loading: false,
            options: [
                {
                    value: 1,
                    label: "失败",
                },
                {
                    value: 2,
                    label: "成功",
                },
            ],
            parameterObj: {
                time: [],
            },

            tableData: [],
        };
    },
    created() {
        this.reqtemplatePagePushOpenTemplate();
    },
    watch: {},
    methods: {
        reqtemplatePagePushOpenTemplate() {
            this.loading = true;
            const [startTime, endTime] = this.parameterObj.time;
            const params = {
                schoolId: this.comSchoolInfo.id,
                ...this.pagination,
                ...this.parameterObj,
                startTime: startTime ? `${startTime} 00:00:00` : null,
                endTime: endTime ? `${endTime} 23:59:59` : null,
            };

            pageOpenTemplateLog(params)
                .then((res) => {
                    this.tableData = res.data.list;
                    this.pagination.total = res.data.total;
                    console.log("res", res);
                })
                .catch((e) => {
                    console.log("e", e);
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        searchBtn() {
            this.pagination.pageNo = 1
            this.reqtemplatePagePushOpenTemplate();
        },
        resetBtn() {
            this.pagination.pageNo = 1
            this.pagination.pageSize = 10
            this.parameterObj = {
                time: [],
            };
            this.reqtemplatePagePushOpenTemplate();
        },
        // 分页
        handleSizeChange(val) {
            this.pagination.pageNo = 1;
            this.pagination.pageSize = val;
            this.reqtemplatePagePushOpenTemplate();
        },
        handleCurrentChange(val) {
            this.pagination.pageNo = val;
            this.reqtemplatePagePushOpenTemplate();
        },
    },
};
</script>

<style lang="scss" scoped>
.push_record_box {
    padding: 20px;
}
</style>
