<template>
    <div>
        <div class="school_info">
            <div class="school_box">
                <div class="schoolName">{{ comSchoolInfo.name }}</div>
                <div class="serialNumber">
                    <div class="school_item school_num">
                        学校编号 <span>{{ comSchoolInfo.id }}</span>
                    </div>
                    <div class="school_item">
                        超级账号 <span>{{ comSchoolInfo.superAccount }}</span>
                    </div>
                </div>
                <div>
                    <div class="pushplatform">推送平台:</div>
                    <el-switch
                        v-model="parameterObj.isEnable"
                        @change="isEnableChange"
                        active-text="微信公众号"
                    >
                    </el-switch>
                </div>
            </div>
        </div>
        <!-- 微信公众号配置 -->
        <div class="wx_set" v-if="parameterObj.isEnable">
            <div class="setHead">
                <div class="setHead_name">微信公众号配置</div>
                <div>
                    <el-button
                        v-if="isEdit"
                        v-auth="'manage.pushMessage.editConf'"
                        size="medium"
                        type="primary"
                        @click="saveSET"
                    >
                        保存
                    </el-button>
                    <el-button
                        v-auth="'manage.pushMessage.editConf'"
                        size="medium"
                        v-else
                        type="primary"
                        @click="editSET"
                    >
                        编辑
                    </el-button>
                </div>
            </div>
            <div class="basicsInfo">
                <div class="basicsInfo_title">基础信息</div>
                <div class="infoList">
                    <div class="infoList_item">
                        <span class="mustIcon">*</span>
                        <div class="infoList_name">公众号名称：</div>
                        <el-input
                            size="small"
                            v-if="isEdit"
                            style="width: 150px"
                            v-model.trim="parameterObj.remark"
                            placeholder="请输入"
                        ></el-input>
                        <div class="infoList_name" v-else>
                            {{ parameterObj.remark }}
                        </div>
                    </div>
                    <div class="infoList_item">
                        <span class="mustIcon">*</span>
                        <div class="infoList_name">开发者Id：</div>
                        <el-input
                            size="small"
                            v-if="isEdit"
                            style="width: 150px"
                            v-model.trim="parameterObj.appId"
                            placeholder="请输入"
                        ></el-input>
                        <div class="infoList_name" v-else>
                            {{ parameterObj.appId }}
                        </div>
                    </div>
                    <div class="infoList_item">
                        <span class="mustIcon">*</span>
                        <div class="infoList_name">开发者密码：</div>
                        <el-input
                            size="small"
                            v-if="isEdit"
                            style="width: 150px"
                            v-model.trim="parameterObj.appSecret"
                            placeholder="请输入"
                        ></el-input>
                        <div class="infoList_name" v-else>
                            {{ parameterObj.appSecret }}
                        </div>
                    </div>
                    <div class="infoList_item">
                        <div class="infoList_name">业务域名：</div>
                        <el-input
                            size="small"
                            v-if="isEdit"
                            style="width: 150px"
                            v-model.trim="parameterObj.businessDomainName"
                            placeholder="请输入"
                        ></el-input>
                        <div class="infoList_name" v-else>
                            {{ parameterObj.businessDomainName }}
                        </div>
                    </div>
                    <div class="infoList_item">
                        <div class="infoList_name">js接口安全域名：</div>

                        <el-input
                            size="small"
                            v-if="isEdit"
                            style="width: 150px"
                            v-model.trim="parameterObj.securityDomainName"
                            placeholder="请输入"
                        ></el-input>
                        <div class="infoList_name" v-else>
                            {{ parameterObj.securityDomainName }}
                        </div>
                    </div>
                    <div class="infoList_item">
                        <div class="infoList_name">网页授权域名：</div>

                        <el-input
                            size="small"
                            v-if="isEdit"
                            style="width: 150px"
                            v-model.trim="parameterObj.authorizeDomainName"
                            placeholder="请输入"
                        ></el-input>
                        <div class="infoList_name" v-else>
                            {{ parameterObj.authorizeDomainName }}
                        </div>
                    </div>
                </div>
            </div>
            <div class="boardList">
                <div class="boardList_title">模板消息列表</div>
                <div class="card_list_box">
                    <div
                        class="card_list"
                        v-for="(item, index) in parameterObj.templateSchoolList"
                        :key="item.id">
                        <div
                            class="card_item"
                            @click="editopenTemplate(item, index)"
                        >
                            <div class="card_item_title">模板配置</div>
                            <div class="config_item">
                                <span class="config_item_name">一德推送：</span>
                                <!-- <el-select
                                    v-if="isEdit"
                                    size="small"
                                    style="width: 100%"
                                    @change="
                                        (val) => functionChangeItem(val, item)
                                    "
                                    v-model="item.functionalModuleType"
                                    placeholder="请选择"
                                >
                                    <el-option
                                        v-for="item in functionalModuleTypeOpt"
                                        :key="item.id"
                                        :label="item.name"
                                        :value="item.id"
                                    >
                                    </el-option>
                                </el-select> -->

                                <span>
                                    {{ item.functionalModuleTypeName }}
                                </span>
                            </div>
                            <div class="config_item">
                                <span class="config_item_name">模板名称：</span>
                                <!-- <el-select
                                    size="small"
                                    v-if="isEdit"
                                    style="width: 100%"
                                    v-model="item.pushOpenTemplateId"
                                    @change="templateIdChange"
                                    placeholder="请选择"
                                >
                                    <el-option
                                        v-for="itemThis in item.templateIdListOpt"
                                        :key="itemThis.id"
                                        :label="itemThis.templateTitle"
                                        :value="itemThis.id"
                                    >
                                    </el-option>
                                </el-select> -->
                                <span>{{ item.templateTitle }}</span>
                            </div>
                            <div class="config_item">
                                <span class="config_item_name"
                                    >模板ID：{{ item.templateId }}</span
                                >
                                <!-- <el-input
                                    size="small"
                                    v-if="isEdit"
                                    style="width: 100%"
                                    v-model="item.templateId"
                                    placeholder="请输入"
                                ></el-input> -->
                                <!-- <span>{{ item.templateId }}</span> -->
                            </div>
                            <div class="config_item">
                                <span class="config_item_name">跳转链接：</span>
                                <!-- <el-input
                                    size="small"
                                    v-if="isEdit"
                                    style="width: 100%"
                                    v-model="item.jumpLink"
                                    placeholder="请输入"
                                ></el-input> -->
                                <span>{{ item.jumpLink }}</span>
                            </div>

                            <div class="lineBox"></div>

                            <div class="card_item_title">模板内容</div>
                            <div
                                class="temp_com"
                                v-for="(it, inx) in item.paramList"
                                :key="it.id"
                            >
                                <span>{{ it.paramName }}：</span>
                                <span>
                                    {{ it.paramField }}
                                </span>
                                <span> {{ it.selectParamName }}</span>
                            </div>
                        </div>

                        <div
                            v-if="isEdit"
                            @click="removeItem(index)"
                            class="removeIcon"
                        >
                            <i class="el-icon-remove"></i>
                        </div>
                    </div>
                    <div
                        class="card_list addCard_list"
                        v-if="isEdit"
                        @click="openTemplate"
                    >
                        <div class="addCard_list_addBtn">
                            <div><i class="el-icon-plus"></i></div>
                            <div>新增模板消息</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <el-dialog
            :title="comMessageTitle"
            :close-on-click-modal="false"
            :visible.sync="dialogVisible"
            width="500px"
            :before-close="cancelNewsItem"
        >
            <span class="contitle">模板配置</span>
            <el-form
                :model="setForm"
                ref="ruleForm"
                label-position="left"
                label-width="80px"
            >
                <el-form-item
                    label="一德推送"
                    prop="functionalModuleType"
                    :rules="{
                        required: true,
                        message: '请选择',
                        trigger: 'change',
                    }"
                >
                    <el-select
                        @change="functionChange"
                        style="width: 100%"
                        v-model="setForm.functionalModuleType"
                        placeholder="请选择"
                    >
                        <el-option
                            v-for="(item,index) in functionalModuleTypeOpt"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item
                    label="模板名称"
                    prop="pushOpenTemplateId"
                    :rules="{
                        required: true,
                        message: '请选择',
                        trigger: 'change',
                    }"
                >
                    <el-select
                        v-model="setForm.pushOpenTemplateId"
                        @change="templateIdChange"
                        style="width: 100%"
                        placeholder="请选择"
                    >
                        <el-option
                            v-for="item in templateIdListOpt"
                            :key="item.id"
                            :label="item.templateTitle"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item
                    label="模板ID"
                    prop="templateId"
                    :rules="{
                        required: true,
                        message: '请输入',
                        trigger: 'blur',
                    }"
                >
                    <el-input
                        placeholder="请输入"
                        v-model.trim="setForm.templateId"
                    ></el-input>
                </el-form-item>
                <el-form-item label="跳转链接" prop="jumpLink">
                    <el-input
                        placeholder="请输入"
                        v-model.trim="setForm.jumpLink"
                    ></el-input>
                </el-form-item>
            </el-form>

            <div class="contitle">模板内容</div>
            <div class="contitle_list" v-for="(it, inx) in setForm.paramList">
                <span>{{ it.paramName }}：</span>
                <span>
                    {{ it.paramField }}
                </span>
                <span v-if="!it.isCustom"> {{ it.selectParamName }}</span>
                <span v-if="it.isCustom">
                    <el-input
                        style="width: 200px"
                        maxlength="20"
                        v-model.trim="it.selectParamName"
                        placeholder="请输入"
                    ></el-input>
                </span>
            </div>

            <span slot="footer" class="dialog-footer">
                <el-button @click="cancelNewsItem">取 消</el-button>
                <el-button type="primary" @click="confirmNewsItem"
                    >确 定</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>

<script>
import {
    queryPushOpenConfigInfo,
    updatePushOpenConfig,
    updateEnableStatus,
    getFunctionalModuleType,
    selectTemplateList,
} from "@/api/pushMessage.js";

export default {
    name: "Pushset",
    components: {},
    inject: ["getschoolInfo"],
    computed: {
        comSchoolInfo() {
            return this.getschoolInfo();
        },
        comMessageTitle() {
            return this.messagetype === "add" ? "新增模板消息" : "编辑模板消息";
        },
    },
    data() {
        return {
            editIndex: null,
            messagetype: "add",
            isEdit: false,
            dialogVisible: false,
            functionalModuleTypeOpt: [],
            templateIdListOpt: [],
            setForm: {
                templateId: "",
                jumpLink: "",
                templateIdListOpt: [],
                paramList: [],
            },
            parameterObj: {
                isEnable: false,
                remark: "",
                appId: "",
                appSecret: "",
                businessDomainName: "",
                securityDomainName: "",
                authorizeDomainName: "",
                templateSchoolList: [],
            },
            value1: false,
        };
    },
    created() {
        this.getqueryPushOpenConfigInfo();
        this.reqFunctionalModuleTypeParams();
    },
    watch: {},
    methods: {
        goBack() {
            this.$emit("handleBack", true);
        },

        editSET() {
            this.isEdit = true;
        },
        saveSET() {
            const isOk =
                this.parameterObj.remark &&
                this.parameterObj.appId &&
                this.parameterObj.appSecret;
            if (isOk) {
                // 发送修改学校推送配置的请求
                updatePushOpenConfig({
                    ...this.parameterObj,
                    isEnable: this.parameterObj.isEnable,
                    schoolId: this.comSchoolInfo.id,
                    templateSchoolList: this.parameterObj.templateSchoolList,
                }).then((res) => {
                    console.log("res", res);
                    this.getqueryPushOpenConfigInfo();
                    this.isEdit = false;
                });
            } else {
                this.$message.error("请填写基础信息");
            }
        },
        // 移出一个item
        removeItem(index) {
            this.parameterObj.templateSchoolList.splice(index, 1);
        },
        // 打开新增的模板消息
        openTemplate() {
            this.messagetype = "add";
            this.dialogVisible = true;
            this.templateIdListOpt = [];
            this.setForm = {
                // functionalModuleType: "",
                pushOpenTemplateId: "",
                templateId: "",
                jumpLink: "",
                templateIdListOpt: [],
            };
        },
        // 编辑
        editopenTemplate(data, index) {
            this.messagetype = "edit";
            this.editIndex = index;
            console.log("data1111111111111", data);
            if (this.isEdit) {
                this.dialogVisible = true;
                this.setForm.jumpLink = data.jumpLink;
                this.setForm.templateId = data.templateId;
                this.setForm.functionalModuleType = data.functionalModuleType;
                this.getselectTemplateList();
                this.setForm.pushOpenTemplateId = data.pushOpenTemplateId;
                this.setForm.functionalModuleTypeName =
                    data.functionalModuleTypeName;
                this.setForm.templateTitle = data.templateTitle;
                this.setForm.paramList = data.paramList;
            }
        },
        getqueryPushOpenConfigInfo() {
            queryPushOpenConfigInfo({
                schoolId: this.comSchoolInfo.id,
            }).then((res) => {
                Object.assign(this.parameterObj, res.data);
                console.log("[ this.parameterObj ] >", this.parameterObj);
            });
        },
        reqFunctionalModuleTypeParams() {
            getFunctionalModuleType().then((res) => {
                this.functionalModuleTypeOpt = res.data;
            });
        },
        isEnableChange(val) {
            this.isEdit = false;
            updateEnableStatus({
                isEnable: val,
                schoolId: this.comSchoolInfo.id,
            }).then((res) => {
                console.log("res", res);
            });
        },
        getselectTemplateList() {
            selectTemplateList({
                functionalModuleType: this.setForm.functionalModuleType,
            }).then((res) => {
                this.templateIdListOpt = res.data;
                this.setForm.templateIdListOpt = res.data;
            });
        },
        functionChange(val) {
            this.setForm.pushOpenTemplateId = ""; // 清除模板名称
            const thisObj = this.functionalModuleTypeOpt.find(
                (item) => (item.id === val)
            );
            console.log("thisObj", thisObj);
            this.setForm.functionalModuleTypeName = thisObj.name; // 存下选择到的文字

            this.getselectTemplateList();
        },
        // 切换item里的一德推送
        functionChangeItem(val, data) {
            data.pushOpenTemplateId = "";
            selectTemplateList({
                functionalModuleType: val,
            }).then((res) => {
                data.templateIdListOpt = res.data;
            });
        },
        templateIdChange(val) {
            const thisTempObj = this.templateIdListOpt.find(
                (item) => item.id === val
            );
            this.setForm.templateTitle = thisTempObj.templateTitle; // 存下选择到的文字
            this.setForm.jumpLink = thisTempObj.jumpLink;
            this.setForm.paramList = thisTempObj.paramList;

            console.log("thisTempObj", thisTempObj);
        },
        cancelNewsItem() {
            this.dialogVisible = false;
        },
        confirmNewsItem() {
            this.$refs.ruleForm.validate((valid) => {
                if (valid) {
                    if (this.messagetype === "add") {
                        const copySetForm = JSON.parse(
                            JSON.stringify(this.setForm)
                        );
                        this.parameterObj.templateSchoolList.push(copySetForm);
                    } else {
                        console.log(this.setForm);

                        const copySetForm = JSON.parse(
                            JSON.stringify(this.setForm)
                        );
                        this.parameterObj.templateSchoolList.splice(
                            this.editIndex,
                            1
                        );
                        this.parameterObj.templateSchoolList.splice(
                            this.editIndex,
                            0,
                            copySetForm
                        );
                    }
                    this.dialogVisible = false;
                }
            });
        },

        handleClose() {},
    },
};
</script>

<style lang="scss" scoped>
.school_info {
    background-color: #f2f2f2;
    margin: 20px;
    padding: 12px 20px;
    .school_box {
        .schoolName {
            font-size: 18px;
            font-weight: 600;
            padding-bottom: 8px;
        }
        .serialNumber {
            display: flex;
            align-items: center;
            .school_item {
                padding-bottom: 8px;
            }
            .school_num {
                padding-right: 12px;
            }
        }
    }
}
.active {
    color: #3963bc;
}

.wx_set {
    background-color: #f2f2f2;
    margin: 20px;
    padding: 12px 20px;
    .setHead {
        display: flex;
        align-items: center;
        .setHead_name {
            padding-right: 8px;
            font-size: 18px;
            font-weight: 600;
        }
    }
}

.basicsInfo {
    .basicsInfo_title {
        font-size: 16px;
        font-weight: 600;
        padding-bottom: 8px;
    }
    .infoList {
        display: flex;
        align-items: center;
        padding-bottom: 12px;
        // justify-content: space-between;
        flex-wrap: wrap;

        .infoList_item {
            flex-shrink: 0;
            flex: 0 0 16.666%; /* 一行显示三个块，总共六个块 */
            box-sizing: border-box; /* 让 padding 和 border 不影响宽度 */
            margin: 0 20px 12px 0;
            display: flex;
            align-items: center;
            .infoList_name {
                white-space: nowrap;
            }
        }
    }
}

.addCard_list {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    .addCard_list_addBtn {
        text-align: center;
    }
}
.card_list {
    position: relative;
    padding: 20px;
    width: 300px;
    min-height: 300px;
    background-color: #d7d7d7;
    margin-right: 20px;
    margin-bottom: 20px;
    .removeIcon {
        color: rgb(255, 103, 112);
        cursor: pointer;
        font-size: 22px;
        position: absolute;
        top: 0;
        right: 0;
    }
    .card_item {
        .card_item_title {
            font-size: 14px;
            font-weight: 600;
            padding-bottom: 8px;
        }
        .config_item {
            word-break: break-all;
            padding-bottom: 8px;
        }
    }
}
.lineBox {
    margin: 20px 0;
    height: 1px;
    width: 100%;
    background-color: #797979;
}

.temp_com {
    padding-bottom: 8px;
}

.pushplatform {
    padding-bottom: 8px;
}

.boardList {
    .card_list_box {
        display: flex;
        cursor: pointer;
        flex-wrap: wrap;
    }
    .boardList_title {
        font-size: 16px;
        font-weight: 600;
        padding-bottom: 8px;
    }
}
.mustIcon {
    color: #f56c6c;
    margin-right: 4px;
}

.contitle {
    padding-bottom: 12px;
}
.contitle_list {
    padding-bottom: 12px;
}
</style>
