<template>
    <div class="news_warp">
        <div class="head_box">
            <div class="icon_box" @click="goBack">
                <i class="el-icon-back"></i>
            </div>
            <div class="head_title">微信模板消息管理</div>
        </div>
        <div class="searchBox">
            <el-form ref="templateForm" :model="queryObj" :inline="true">
                <el-form-item label="模板类型：">
                    <el-select
                        v-model="queryObj.openTemplateType"
                        placeholder="请选择"
                    >
                        <el-option
                            v-for="item in openTemplateTypeOpt"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="服务类目/行业：">
                    <el-cascader
                        :show-all-levels="false"
                        :props="{
                            value: 'id',
                            label: 'name',
                            emitPath: false,
                        }"
                        :clearable="true"
                        v-model="queryObj.categoryTypeId"
                        :options="categoryList"
                    ></el-cascader>
                </el-form-item>
                <el-form-item label="模板名称：">
                    <el-input
                        v-model.trim="queryObj.templateTitle"
                        placeholder="请输入"
                    />
                </el-form-item>
                <el-form-item label="一德推送：">
                    <el-select
                        v-model="queryObj.functionalModuleType"
                        placeholder="请选择"
                    >
                        <el-option
                            v-for="item in functionalModuleTypeOpt"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item>
                    <el-button
                        type="primary"
                        icon="el-icon-search"
                        @click="searchBtn"
                        >查询</el-button
                    >
                    <el-button @click="resetBtn">重置</el-button>
                </el-form-item>
            </el-form>
        </div>
        <div class="searchBtn_Box">
            <el-button
                type="primary"
                v-auth="'manage.pushMessage.addtemp'"
                @click="addTemplate"
                >+ 新增消息模板</el-button
            >
            <el-button type="primary" @click="pushList"
                >一德推送功能列表</el-button
            >
        </div>
        <div class="list_table" v-loading="loading">
            <el-table
                :border="true"
                :data="tableData"
                :header-cell-style="{ background: '#f5f7fa', color: '#5b5d61' }"
            >
                <el-table-column prop="serial" label="序号">
                    <template slot-scope="scope">
                        <span v-text="scope.$index + 1"></span>
                    </template>
                </el-table-column>
                <el-table-column prop="templateTitle" label="模板名称">
                </el-table-column>
                <el-table-column prop="useNum" label="使用学校">
                    <template slot-scope="scope">
                        <span
                            class="functionalModuleTypeNameBox"
                            @click="infoSchoolList(scope.row)"
                            >{{ scope.row.useNum }}</span
                        >
                    </template>
                </el-table-column>
                <el-table-column prop="openTemplateTypeName" label="模板类型">
                </el-table-column>
                <el-table-column prop="categoryTypeName" label="服务类目或行业">
                </el-table-column>
                <el-table-column
                    prop="functionalModuleTypeName"
                    label="关联一德推送"
                >
                    <template slot-scope="scope">
                        <span
                            class="functionalModuleTypeNameBox"
                            @click="infoModuleTypeName(scope.row)"
                            >{{ scope.row.functionalModuleTypeName }}</span
                        >
                    </template>
                </el-table-column>
                <el-table-column prop="createTime" label="创建时间">
                </el-table-column>
                <el-table-column label="操作">
                    <template slot-scope="scope">
                        <el-button
                            type="primary"
                            plain
                            v-auth="'manage.pushMessage.edittemp'"
                            size="mini"
                            @click="editThisTemp(scope.row)"
                            >编辑</el-button
                        >

                        <el-button
                            type="danger"
                            plain
                            v-auth="'manage.pushMessage.deltemp'"
                            size="mini"
                            @click="delThisTemp(scope.row)"
                            >删除</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <el-pagination
                background
                style="margin-top: 16px; text-align: right"
                :current-page="pagination.pageNo"
                :page-sizes="[10, 20, 30, 50]"
                :page-size="pagination.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="pagination.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <!-- 添加模板的弹窗 -->
        <div>
            <el-dialog
                :title="dialogTitle"
                :close-on-click-modal="false"
                :visible.sync="dialogVisible"
                width="1000px"
                :before-close="tempCancel"
            >
                <div class="tempInfo">
                    <div class="tempInfo_title">模板基本信息</div>
                    <el-form
                        label-position="left"
                        label-width="auto"
                        :model="templateForm"
                    >
                        <el-form-item label="模板类型：">
                            <el-select
                                style="width: 100%"
                                v-model="templateForm.openTemplateType"
                                placeholder="请选择"
                            >
                                <el-option
                                    v-for="item in openTemplateTypeOpt"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="服务类目：">
                            <el-input
                                maxlength="50"
                                v-model.trim="templateForm.categoryTypeName"
                                placeholder="格式为XX-XX，横杠前的为一级类目，横杠后的为二级类目"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="模板名称：">
                            <el-input
                                v-model.trim="templateForm.templateTitle"
                                maxlength="50"
                                placeholder="请输入"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="一德推送：">
                            <el-select
                                style="width: 100%"
                                v-model="templateForm.functionalModuleType"
                                placeholder="请选择"
                                @change="functionalModuleTypeChange"
                            >
                                <el-option
                                    v-for="item in functionalModuleTypeOpt"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>

                        <el-form-item>
                            <el-checkbox-group
                                style="width: 100%"
                                v-model="templateForm.triggerList"
                            >
                                <el-checkbox
                                    :key="item.value"
                                    :label="item.value"
                                    v-for="item in typeTriggerList"
                                    >{{ item.name }}</el-checkbox
                                >
                            </el-checkbox-group>
                        </el-form-item>
                    </el-form>

                    <div class="contentDeploy">默认模板内容配置</div>
                    <div class="contentDeployList">
                        <!-- 头 -->
                        <div class="boxhead">
                            <div class="boxhead_item">微信模板关键词</div>
                            <div class="boxhead_item">微信模板参数</div>
                            <div class="boxhead_item">一德推送匹配参数</div>
                            <div class="boxhead_item"></div>
                        </div>
                        <!-- 循环的内容区 -->
                        <div
                            class="inputArea"
                            v-for="(item, index) in paramList"
                            :key="index"
                        >
                            <div class="inputArea_item">
                                <i
                                    class="el-icon-remove icon_remove"
                                    @click="removekeywords(index)"
                                ></i>
                                <el-input
                                    style="width: 200px"
                                    v-model.trim="item.paramName"
                                    placeholder="请输入"
                                ></el-input>
                            </div>
                            <div class="inputArea_item">
                                <el-input
                                    style="width: 220px"
                                    v-model.trim="item.paramField"
                                    placeholder="请输入"
                                ></el-input>
                            </div>

                            <div class="inputArea_item customBox">
                                <el-select
                                    v-if="!item.isCustom"
                                    v-model="item.selectParam"
                                    placeholder="请选择"
                                    style="width: 200px"
                                >
                                    <el-option
                                        v-for="item in selectParamOpt"
                                        :key="item.value"
                                        :label="item.name"
                                        :value="item.value"
                                    >
                                    </el-option>
                                </el-select>
                                <el-input
                                    v-if="item.isCustom"
                                    style="width: 200px"
                                    maxlength="20"
                                    v-model.trim="item.selectParam"
                                    placeholder="请输入"
                                ></el-input>
                                <el-checkbox
                                    style="padding-left: 8px"
                                    @change="() => (item.selectParam = '')"
                                    v-model="item.isCustom"
                                    >学校自定义参数</el-checkbox
                                >
                            </div>
                        </div>
                        <el-button
                            class="addkeywords"
                            type="text"
                            @click="addkeywords"
                            >+新增关键词</el-button
                        >
                    </div>
                    <div class="skipUrl">
                        <div class="tempInfo_title">公共跳转链接</div>
                        <el-input
                            v-model.trim="templateForm.jumpLink"
                            placeholder="选填"
                        ></el-input>
                    </div>
                </div>

                <span slot="footer" class="dialog-footer">
                    <el-button @click="tempCancel">取 消</el-button>
                    <el-button type="primary" @click="tempSubmit"
                        >确 定</el-button
                    >
                </span>
            </el-dialog>
        </div>
        <el-dialog
            title="一德推送功能列表"
            :visible.sync="pushdialogVisible"
            width="30%"
        >
            <div
                class="pushName"
                v-for="(item, index) in functionalModuleTypeOpt"
            >
                <span class="pushName" @click="getparameter(item)"
                    >{{ index + 1 }} {{ item.name }}</span
                >
            </div>
        </el-dialog>
        <el-dialog
            title="一德推送功能参数库"
            :visible.sync="parameterdialogVisible"
            width="30%"
        >
            <div class="parameterList">
                <div
                    class="parameterList_item"
                    v-for="(item, index) in parameterOpt"
                >
                    <el-button> {{ item.name }}</el-button>
                </div>
            </div>
        </el-dialog>
        <el-dialog
            title="使用学校列表"
            :visible.sync="schoolVisible"
            width="20%"
        >
            <div>
                <div
                    class="schoolVisiblelist"
                    v-for="(item, index) in schoolList"
                >
                    <span class="schoolName_item">{{ item.name }}</span>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {
    getFunctionalModuleType,
    getFunctionalModuleTypeParams,
    createpushOpenTemplate,
    templatePagePushOpenTemplate,
    deletepushOpenTemplate,
    getTemplateInfo,
    updatepushOpenTemplate,
    getFunctionalModuleTypeTrigger,
    templateSchoolList,
    getcategoryList,
} from "@/api/pushMessage.js";

export default {
    name: "TemplateManage",
    components: {},
    data() {
        return {
            categoryList: [],
            schoolList: [],
            schoolVisible: false,
            pushdialogVisible: false,
            parameterdialogVisible: false,
            loading: false,
            isAdded: true, // 这是新增的 编辑就是false
            dialogVisible: false,
            input: "",
            queryObj: {},
            typeTriggerList: [],
            parameterOpt: [],
            templateForm: {
                categoryTypeName: "",
                triggerList: [],
                jumpLink: "",
                templateTitle: "",
            },
            openTemplateTypeOpt: [
                {
                    value: 1,
                    label: "类目模版",
                },
                {
                    value: 2,
                    label: "历史模版",
                },
            ],
            functionalModuleTypeOpt: [],
            selectParamOpt: [],
            paramList: [
                {
                    paramName: "",
                    paramField: "",
                    selectParam: "",
                    isCustom: false,
                },
            ],
            options: [
                {
                    value: "wx",
                    label: "微信",
                },
            ],
            tableData: [],
            pagination: {
                pageNo: 1,
                pageSize: 10,
                total: 0,
            },
        };
    },
    computed: {
        dialogTitle() {
            return this.isAdded ? "新增模板" : "编辑模板";
        },
    },
    watch: {},
    methods: {
        functionalModuleTypeChange(val) {
            this.templateForm.triggerList = []
            this.paramList.forEach((item) => {
                item.selectParam = "";
            });
            getFunctionalModuleTypeParams({ type: val }).then((res) => {
                this.selectParamOpt = res.data;
            });
            getFunctionalModuleTypeTrigger({ type: val }).then((res) => {
                this.typeTriggerList = res.data;
            });
        },
        reqFunctionalModuleTypeParams() {
            getFunctionalModuleType().then((res) => {
                this.functionalModuleTypeOpt = res.data;
            });
        },
        goBack() {
            this.$emit("handleBack", true);
        },
        // 添加模板 打开弹窗
        addTemplate() {
            this.isAdded = true;
            this.dialogVisible = true;
        },
        pushList() {
            this.pushdialogVisible = true;
            // getFunctionalModuleType().then((res) => {
            //     this.functionalModuleTypeOpt = res.data;
            // });
        },
        // 编辑这个模板
        editThisTemp(data) {
            this.templateId = data.id;
            this.isAdded = false;
            // 发送请求
            getTemplateInfo({
                id: data.id,
            }).then((res) => {
                const {
                    templateTitle,
                    categoryTypeName,
                    openTemplateType,
                    functionalModuleType,
                    jumpLink,
                    triggerList,
                } = res.data;
                this.templateForm.templateTitle = templateTitle;
                this.templateForm.categoryTypeName = categoryTypeName;
                this.templateForm.openTemplateType = openTemplateType;
                this.templateForm.functionalModuleType = functionalModuleType;
                this.templateForm.jumpLink = jumpLink;
                this.templateForm.triggerList = triggerList;
                this.paramList = res.data.paramList;
                this.dialogVisible = true;
                getFunctionalModuleTypeParams({
                    type: this.templateForm.functionalModuleType,
                }).then((res) => {
                    this.selectParamOpt = res.data;
                });
                getFunctionalModuleTypeTrigger({
                    type: this.templateForm.functionalModuleType,
                }).then((res) => {
                    this.typeTriggerList = res.data;
                });
            });
        },
        tempCancel() {
            this.templateForm = {
                categoryTypeName: "",
                triggerList: [],
                jumpLink: "",
                templateTitle: "",
            };
            this.paramList = [
                {
                    paramName: "",
                    paramField: "",
                    selectParam: "",
                    isCustom: false,
                },
            ];
            this.dialogVisible = false;
        },
        // 获取模板分页
        reqtemplatePagePushOpenTemplate() {
            this.loading = true;
            const params = {
                ...this.pagination,
                ...this.queryObj,
            };

            templatePagePushOpenTemplate(params)
                .then((res) => {
                    this.tableData = res.data.list;
                    this.pagination.total = res.data.total;
                })
                .catch((e) => {
                    console.log("e", e);
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        delThisTemp(data) {
            if (data.useNum) {
                this.$alert(
                    "该模板有学校正在使用，请先在学校取消该模板的使用再进行删除模板操作！",
                    "提示",
                    {
                        confirmButtonText: "确定",
                    }
                );
            } else {
                deletepushOpenTemplate({
                    id: data.id,
                }).then((res) => {
                    this.$message.success("操作成功");
                    this.reqtemplatePagePushOpenTemplate();
                });
            }
        },
        tempSubmit() {
            let apiUrl = this.isAdded
                ? createpushOpenTemplate
                : updatepushOpenTemplate;
            apiUrl({
                id: this.isAdded ? undefined : this.templateId,
                ...this.templateForm,
                paramList: this.paramList,
            }).then((res) => {
                this.$message.success("操作成功");
                this.tempCancel()
                this.reqtemplatePagePushOpenTemplate();
            });
        },
        // 分页
        handleSizeChange(val) {
            this.pagination.pageNo = 1;
            this.pagination.pageSize = val;
            this.reqtemplatePagePushOpenTemplate();
        },
        handleCurrentChange(val) {
            this.pagination.pageNo = val;
            this.reqtemplatePagePushOpenTemplate();
        },
        searchBtn() {
            this.reqtemplatePagePushOpenTemplate();
        },
        resetBtn() {
            this.queryObj = {};
            this.reqtemplatePagePushOpenTemplate();
        },
        addkeywords() {
            if (this.paramList.length >= 5) {
                this.$message.error("微信模板关键词最多只能添加5个");
            } else {
                this.paramList.push({
                    paramName: "",
                    paramField: "",
                    selectParam: "",
                    isCustom: false,
                });
            }
        },
        removekeywords(index) {
            this.paramList.splice(index, 1);
        },
        getparameter(data) {
            getFunctionalModuleTypeParams({ type: data.id }).then((res) => {
                this.parameterOpt = res.data;
                this.parameterdialogVisible = true;
            });
        },
        infoModuleTypeName(data) {
            this.getparameter({ id: data.functionalModuleType });
        },
        infoSchoolList(data) {
            templateSchoolList({
                id: data.id,
            }).then((res) => {
                this.schoolList = res.data;
                this.schoolVisible = true;
            });
        },
        removeEmptyChildren(arr) {
            return arr.filter((item) => {
                if (item.children.length === 0) {
                    delete item.children;
                } else {
                    this.removeEmptyChildren(item.children);
                }
                return true;
            });
        },
        reqgetcategoryList() {
            getcategoryList().then((res) => {
                this.categoryList = this.removeEmptyChildren(res.data);
            });
        },
    },

    created() {
        this.reqtemplatePagePushOpenTemplate();
        this.reqFunctionalModuleTypeParams();
        this.reqgetcategoryList();
    },
};
</script>

<style lang="scss" scoped>
.news_warp {
    background-color: #fff;
    .head_box {
        padding-left: 20px;
        height: 56px;
        line-height: 56px;
        display: flex;
        align-items: center;
        border-bottom: 2px solid #ebeef5;
        .icon_box {
            font-size: 24px;
            cursor: pointer;
            padding-right: 8px;
        }
        .head_title {
            font-size: 18px;
            font-weight: 600;
        }
    }
    .searchBox {
        padding: 20px;
        // display: flex;
        // align-items: center;
    }
    .list_table {
        padding: 20px;
    }
}

.searchBtn_Box {
    text-align: right;
    padding: 0 20px;
}

.contentDeploy {
    font-weight: 600;
    padding-bottom: 8px;
}
.contentDeployList {
    background-color: #f2f2f2;
    padding: 12px;
}

.boxhead {
    display: flex;
    .boxhead_item {
        font-weight: 600;
        padding-bottom: 8px;
        width: 230px;
    }
}
.inputArea {
    display: flex;
    padding-bottom: 8px;
    .inputArea_item {
        width: 230px;
        .icon_remove {
            font-size: 18px;
            cursor: pointer;
            color: rgb(255, 103, 112);
        }
    }
}

.skipUrl {
    padding-top: 16px;
}

.tempInfo {
    .tempInfo_title {
        font-weight: 600;
        padding-bottom: 8px;
    }
}

.addkeywords {
    padding-top: 12px;
    cursor: pointer;
}

.pushName {
    cursor: pointer;
    padding-top: 12px;
}
.parameterList {
    display: flex;
    flex-wrap: wrap;
    .parameterList_item {
        margin: 12px;
    }
}

.functionalModuleTypeNameBox {
    cursor: pointer;
}
.schoolVisiblelist {
    padding-top: 12px;
}

.customBox {
    display: flex;
    align-items: center;
    width: 350px !important;
}
</style>
