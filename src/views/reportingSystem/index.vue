<!--
 * @Descripttion: 
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-11-18 15:03:58
 * @LastEditors: jingrou
 * @LastEditTime: 2022-06-14 15:56:33
-->
<template>
    <div>
        <el-card shadow="never">
            <!-- 查询表单 -->
            <el-form
                ref="reportingQueryForm"
                :inline="true"
                :model="reportingQueryForm"
                class="demo-form-inline"
                label-width="100px"
            >
                <div class="queryForm">
                    <el-form-item label="项目名称" prop="entryName">
                        <el-select
                            v-model="reportingQueryForm.entryName"
                            filterable
                            allow-create
                            default-first-option
                            remote
                            :multiple-limit="3"
                            reserve-keyword
                            @change="handerChage"
                            placeholder="请输入关键词"
                            :remote-method="getEntryNameList"
                        >
                            <el-option
                                v-for="(item, idx) in administratorsList"
                                :key="idx + 'a'"
                                :label="item.entryName"
                                :value="item.entryName"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="项目学校" prop="school">
                        <el-select
                            v-model="reportingQueryForm.school"
                            filterable
                            allow-create
                            default-first-option
                            remote
                            :multiple-limit="3"
                            reserve-keyword
                            @change="handerChage"
                            placeholder="请输入关键词"
                            :remote-method="getSchoolList"
                        >
                            <el-option
                                v-for="(item, idx) in administratorsList"
                                :key="idx + 'a'"
                                :label="item.school"
                                :value="item.school"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="项目经理" prop="projectManager">
                        <el-input
                            show-word-limit
                            v-model="reportingQueryForm.projectManager"
                            placeholder="请输入项目经理"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="项目进度" prop="projectScheduleId">
                        <el-select
                            filterable
                            v-model="reportingQueryForm.projectScheduleId"
                            placeholder="请输入进度"
                        >
                            <el-option
                                v-for="(item, i) in dictList"
                                :key="i"
                                :label="item.dictLabel"
                                :value="item.dictCode"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item class="btnShow">
                        <el-button
                            type="primary"
                            @click="queryForm"
                            :loading="queryloading"
                            >查询</el-button
                        >
                        <el-button @click="searchResetForm">重置</el-button>
                        <el-button type="text" @click="showForm"
                            >展开</el-button
                        >
                    </el-form-item>
                </div>
                <div class="queryForm itemShow">
                    <el-form-item label="业务部门" prop="businessUnit">
                        <el-input
                            show-word-limit
                            v-model="reportingQueryForm.businessUnit"
                            placeholder="请输入业务部门"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="业务员" prop="salesman">
                        <el-input
                            show-word-limit
                            v-model="reportingQueryForm.salesman"
                            placeholder="请输入业务员"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="采购单位" prop="purchaseUnit">
                        <el-select
                            v-model="reportingQueryForm.purchaseUnit"
                            filterable
                            allow-create
                            default-first-option
                            remote
                            :multiple-limit="3"
                            reserve-keyword
                            @change="handerChage"
                            placeholder="请输入关键词"
                            :remote-method="getPurchaseUnitList"
                        >
                            <el-option
                                v-for="(item, idx) in administratorsList"
                                :key="idx + 'a'"
                                :label="item.purchaseUnit"
                                :value="item.purchaseUnit"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="采购内容" prop="purchaseContent">
                        <el-input
                            v-model="reportingQueryForm.purchaseContent"
                            placeholder="请输入采购内容"
                        ></el-input>
                    </el-form-item>
                </div>
                <div class="queryForm itemShow2">
                    <el-form-item label="项目分类" prop="classification">
                        <el-select
                            v-model="reportingQueryForm.classification"
                            filterable
                            allow-create
                            default-first-option
                            remote
                            :multiple-limit="3"
                            reserve-keyword
                            @change="handerChage"
                            placeholder="请输入关键词"
                            :remote-method="getClassificationList"
                        >
                            <el-option
                                v-for="(item, idx) in administratorsList"
                                :key="idx + 'a'"
                                :label="item.classification"
                                :value="item.classification"
                            ></el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="项目所在地区" prop="projectArea">
                        <el-cascader
                            filterable
                            v-model="reportingQueryForm.projectArea"
                            :props="{ checkStrictly: true, value: 'label' }"
                            style="width: 100%"
                            :options="options"
                        />
                    </el-form-item>
                    <el-form-item class="btnShow2">
                        <el-button type="primary" @click="queryForm"
                            >查询</el-button
                        >
                        <el-button @click="searchResetForm">重置</el-button>
                        <el-button type="text" @click="isShowForm"
                            >收起</el-button
                        >
                    </el-form-item>
                </div>
            </el-form>
            <!-- 按钮  -->
            <div class="btn">
                <span>报备系统</span>
                <div class="multipartFile">
                    <el-button
                        size="medium"
                        icon="el-icon-plus"
                        type="primary"
                        @click="addReporting(true)"
                        >新增</el-button
                    >
                    <el-button
                        size="medium"
                        @click="fieldManagement"
                        icon="el-icon-menu"
                        type="primary"
                        >项目进度管理</el-button
                    >
                    <!-- 上传模板 -->
                    <el-upload
                        class="upload-demo"
                        :headers="{ Authorization: token, platform: 'system' }"
                        :before-upload="importChang"
                        :action="action"
                        :show-file-list="false"
                        :on-success="importSuccess"
                        name="multipartFile"
                    >
                        <el-button
                            icon="el-icon-top"
                            :loading="importLoading"
                            size="medium"
                            style="margin-left: 16px"
                            type="primary"
                            plain
                            >导入</el-button
                        >
                    </el-upload>
                    <el-button
                        icon="el-icon-bottom"
                        :loading="exportLoading"
                        size="medium"
                        style="margin-left: 16px"
                        @click="exportData"
                        type="primary"
                        plain
                        >导出</el-button
                    >
                    <div class="download">
                        <a href="https://file.1d1j.cn/reporting_template.xlsx"
                            >下载模板</a
                        >
                    </div>
                </div>
            </div>
            <!-- 表格 -->
            <div v-loading="tableloading">
                <el-table
                    :data="tableData"
                    border
                    :header-cell-style="{
                        background: '#fafafa',
                        color: '#5b5d61',
                    }"
                >
                    <el-table-column
                        type="index"
                        label="序号"
                        width="50"
                        align="center"
                    />
                    <el-table-column
                        prop="businessUnit"
                        label="业务部门"
                        width="130"
                        align="center"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="salesman"
                        label="业务员"
                        align="center"
                        width="130"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="projectArea"
                        width="200"
                        label="项目所属区域"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="entryName"
                        label="项目名称"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="purchaseContent"
                        label="采购内容"
                        align="center"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="classification"
                        label="项目分类"
                        align="center"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="school"
                        label="项目学校"
                        align="center"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="purchaseUnit"
                        label="采购单位"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="moneyTotal"
                        label="项目预算金额"
                        width="130"
                        align="center"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="projectManager"
                        label="项目经理"
                        align="center"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="projectScheduleName"
                        label="项目进度"
                        align="center"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="createdDateTime"
                        width="200"
                        label="录入时间"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="remarks"
                        label="备注"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        label="操作"
                        align="right"
                        width="180"
                        fixed="right"
                    >
                        <template slot-scope="scope">
                            <el-button
                                type="text"
                                icon="el-icon-edit"
                                @click="editReporting(scope.row)"
                                >编辑</el-button
                            >
                            <el-link
                                type="danger"
                                style="margin-left: 10px"
                                size="small"
                                @click="removeReporting(scope.row)"
                            >
                                删除
                                <i class="el-icon-delete" />
                            </el-link>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <!-- 分页 -->
            <div class="paginationBlock">
                <el-pagination
                    background
                    style="margin-top: 20px"
                    :current-page="pages.current"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="pages.pagesize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pages.total"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </div>
            <!-- 新增编辑 -->
            <el-dialog
                :title="formTitle"
                :visible.sync="dialogVisibleForm"
                width="840px"
                top="10vh"
            >
                <el-form
                    :rules="rules"
                    ref="reportingForm"
                    :model="reportingForm"
                    label-width="100px"
                    label-position="right"
                    class="dialogVisibleForm"
                >
                    <div class="formBox">
                        <el-form-item label="项目名称" prop="entryName">
                            <el-select
                                v-model="reportingForm.entryName"
                                filterable
                                allow-create
                                default-first-option
                                remote
                                :multiple-limit="3"
                                reserve-keyword
                                @change="handerChage"
                                placeholder="请输入关键词"
                                :remote-method="getEntryNameList"
                            >
                                <el-option
                                    v-for="(item, idx) in administratorsList"
                                    :key="idx + 'a'"
                                    :label="item.entryName"
                                    :value="item.entryName"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="项目学校" prop="school">
                            <el-select
                                v-model="reportingForm.school"
                                filterable
                                allow-create
                                default-first-option
                                remote
                                :multiple-limit="3"
                                reserve-keyword
                                @change="handerChage"
                                placeholder="请输入关键词"
                                :remote-method="getSchoolList"
                            >
                                <el-option
                                    v-for="(item, idx) in administratorsList"
                                    :key="idx + 'a'"
                                    :label="item.school"
                                    :value="item.school"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </div>
                    <div class="formBox">
                        <el-form-item label="项目经理" prop="projectManager">
                            <el-input
                                maxlength="20"
                                show-word-limit
                                v-model="reportingForm.projectManager"
                                placeholder="请输入项目经理"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="项目预算金额" prop="moneyTotal">
                            <el-input
                                show-word-limit
                                v-model="reportingForm.moneyTotal"
                                placeholder="请输入项目预算金额"
                            ></el-input>
                        </el-form-item>
                    </div>
                    <div class="formBox">
                        <el-form-item label="项目所属区域" prop="projectArea">
                            <el-cascader
                                filterable
                                v-model="reportingForm.projectArea"
                                :props="{ checkStrictly: true, value: 'label' }"
                                style="width: 100%"
                                :options="options"
                            />
                        </el-form-item>
                        <el-form-item label="项目进度" prop="projectScheduleId">
                            <el-select
                                filterable
                                v-model="reportingForm.projectScheduleId"
                                placeholder="请输入进度"
                            >
                                <el-option
                                    v-for="(item, i) in dictList"
                                    :key="i"
                                    :label="item.dictLabel"
                                    :value="item.dictCode"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </div>
                    <div class="formBox">
                        <el-form-item label="业务部门" prop="businessUnit">
                            <el-input
                                maxlength="20"
                                show-word-limit
                                v-model="reportingForm.businessUnit"
                                placeholder="请输入业务部门"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="业务员" prop="salesman">
                            <el-input
                                maxlength="20"
                                show-word-limit
                                v-model="reportingForm.salesman"
                                placeholder="请输入业务员"
                            ></el-input>
                        </el-form-item>
                    </div>
                    <div class="formBox">
                        <el-form-item label="采购单位" prop="purchaseUnit">
                            <el-select
                                v-model="reportingForm.purchaseUnit"
                                filterable
                                allow-create
                                default-first-option
                                remote
                                :multiple-limit="3"
                                reserve-keyword
                                @change="handerChage"
                                placeholder="请输入关键词"
                                :remote-method="getPurchaseUnitList"
                            >
                                <el-option
                                    v-for="(item, idx) in administratorsList"
                                    :key="idx + 'a'"
                                    :label="item.purchaseUnit"
                                    :value="item.purchaseUnit"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="项目分类" prop="classification">
                            <el-select
                                v-model="reportingForm.classification"
                                filterable
                                allow-create
                                default-first-option
                                remote
                                :multiple-limit="3"
                                reserve-keyword
                                @change="handerChage"
                                placeholder="请输入关键词"
                                :remote-method="getClassificationList"
                            >
                                <el-option
                                    v-for="(item, idx) in administratorsList"
                                    :key="idx + 'a'"
                                    :label="item.classification"
                                    :value="item.classification"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </div>
                    <div class="formBox">
                        <el-form-item label="采购内容" prop="purchaseContent">
                            <el-input
                                show-word-limit
                                type="textarea"
                                maxlength="500"
                                v-model="reportingForm.purchaseContent"
                                placeholder="请输入采购内容"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="备注" prop="remarks">
                            <el-input
                                show-word-limit
                                type="textarea"
                                maxlength="500"
                                v-model="reportingForm.remarks"
                                placeholder="请输入备注"
                            ></el-input>
                        </el-form-item>
                    </div>

                    <div style="margin: 10px auto; width: 400px">
                        <el-form-item>
                            <el-button
                                type="primary"
                                :loading="addLoading"
                                @click="onSubmit('reportingForm')"
                                >确定</el-button
                            >
                            <el-button @click="cancel">取消</el-button>
                        </el-form-item>
                    </div>
                </el-form>
            </el-dialog>
            <!-- 上传错误提示 -->
            <el-dialog title="提示" :visible.sync="dialogVisible" width="30%">
                <span v-html="errorPrompt" />
                <span slot="footer">
                    <el-button @click="dialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="dialogVisible = false"
                        >确 定</el-button
                    >
                </span>
            </el-dialog>
            <!-------------------------------------------------------------------------------------- -->
            <!-- 字段管理 -->
            <el-dialog
                title="进度管理"
                width="60%"
                :visible.sync="seeDialogTableVisible"
            >
                <el-button
                    type="primary"
                    style="margin-left: 17px; margin-bottom: 30px"
                    icon="el-icon-plus"
                    @click="addDict"
                    >添加项目进度</el-button
                >
                <template>
                    <div style="width: 97%; margin: auto">
                        <el-table
                            :data="TypeData"
                            style="width: 100%"
                            border
                            max-height="600"
                            :header-cell-style="{
                                background: '#f5f7fa',
                                color: '#5b5d61',
                            }"
                        >
                            <el-table-column
                                prop="dictLabel"
                                label="项目进度名称"
                                align="center"
                            />
                            <!-- <el-table-column prop="dictSort" label="进度排序" align="center" /> -->
                            <el-table-column
                                prop="dictValue"
                                label="进度识别码"
                                align="center"
                                width="140px"
                            />
                            <!-- <el-table-column prop="isDefault" label="是否默认" align="center">
								<template slot-scope="scope">
									<div>
										{{
										scope.row.isDefault == "Y"
										? "是"
										: scope.row.isDefault == "N"
										? "否"
										: ""
										}}
									</div>
								</template>
							</el-table-column>-->
                            <!-- <el-table-column prop="status" label="状态" align="center">
								<template slot-scope="scope">
									<div>
										{{
										scope.row.status == "0"
										? "正常"
										: scope.row.status == "1"
										? "停用"
										: ""
										}}
									</div>
								</template>
							</el-table-column>-->
                            <el-table-column
                                prop="remark"
                                label="备注"
                                align="center"
                            />
                            <el-table-column
                                prop="status"
                                label="操作"
                                align="center"
                                width="140px"
                            >
                                <template slot-scope="scope">
                                    <el-link
                                        type="primary"
                                        icon="el-icon-edit"
                                        size="small"
                                        @click="editFrom(scope.row)"
                                        >修改</el-link
                                    >
                                    <el-link
                                        type="danger"
                                        size="small"
                                        style="margin-left: 10px"
                                        @click="delData1(scope.row)"
                                    >
                                        删除
                                        <i class="el-icon-delete" />
                                    </el-link>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </template>
            </el-dialog>
            <!-- 字段管理添加编辑 -->
            <el-drawer
                :title="dictFromDialogTitle"
                :visible.sync="dictFromDialog"
                direction="rtl"
            >
                <el-form
                    ref="ruleForm"
                    :model="dictFromData"
                    :rules="rules"
                    label-width="100px"
                    class="demo-ruleForm"
                >
                    <!-- <el-form-item label="进度排序:">
						<el-input-number v-model="dictFromData.dictSort" :min="1" label="描述文字"></el-input-number>
					</el-form-item>-->
                    <el-form-item label="进度名称:" prop="dictLabel">
                        <el-input
                            v-model="dictFromData.dictLabel"
                            placeholder="请输入进度名称"
                            show-word-limit
                            maxlength="10"
                        />
                    </el-form-item>
                    <el-form-item label="进度识别码:" prop="dictValue">
                        <el-input
                            v-model="dictFromData.dictValue"
                            placeholder="如：1,2,3 / a,b,c"
                        />
                    </el-form-item>
                    <!-- <el-form-item label="是否默认:" prop="isDefault">
						<el-select v-model="dictFromData.isDefault" placeholder="请选择">
							<el-option label="是" value="Y" />
							<el-option label="否" value="N" />
						</el-select>
					</el-form-item>-->
                    <!-- <el-form-item label="进度类型" prop="dictType">
						<el-select
							v-model="dictFromData.dictType"
							placeholder="请选择"
						>
							<el-option
								label="项目进度"
								value="sys_project_schedule"
							/>
						</el-select>
					</el-form-item>-->
                    <!-- <el-form-item label="状态:" prop="status">
						<el-select v-model="dictFromData.status" placeholder="请选择状态">
							<el-option label="正常" value="0" />
							<el-option label="停用" value="1" />
						</el-select>
					</el-form-item>-->

                    <el-form-item label="备注:">
                        <el-input
                            v-model="dictFromData.remark"
                            placeholder="请输入备注"
                            show-word-limit
                        />
                    </el-form-item>

                    <div class="footerButton">
                        <el-button
                            type="primary"
                            :loading="buttonLoading"
                            @click="submitForm('ruleForm')"
                            >确定</el-button
                        >
                        <el-button @click="dictFromDialog = false"
                            >取消</el-button
                        >
                    </div>
                </el-form>
            </el-drawer>
        </el-card>
    </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import {
    getReportingList,
    addReportingList,
    editReportingList,
    getEditReportingList,
    reportingExportInfo,
    removeReporting,
} from "@/api/reportingSystem.js";
import {
    selectDictList,
    addDictList,
    echoDictList,
    editDictList,
    removeDictList,
} from "@/api/systemAdministration.js";
import { regionData } from "element-china-area-data";
import { searchSchoolList } from "@/api/application.js";
export default {
    data() {
        return {
            importLoading: false,
            exportLoading: false,
            buttonLoading: false,
            addLoading: false,
            tableloading: false,
            queryloading: false,
            administratorsList: [],
            departmentTreeProps: {
                emitPath: false,
                checkStrictly: true,
                value: "deptId",
                label: "deptName",
                children: "children",
            },
            dialogVisible: false,
            importId: "",
            errorPrompt: "",
            action:
                process.env.VUE_APP_BASE_API +
                "/backstage/cloud-backstage/reporting/importReporting",
            token: "",
            province: "",
            options: regionData,
            departmentData: [],
            dialogVisibleForm: false,
            reportingQueryForm: {
                entryName: "",
                projectArea: [],
                businessUnit: "",
                salesman: "",
                school: "",
                projectManager: "",
                projectScheduleId: "",
                purchaseUnit: "",
                purchaseContent: "",
                classification: "",
            },
            tableData: [],
            pages: {
                pagesize: 10,
                current: 1,
                total: 10,
            },
            reportingForm: {
                entryName: "",
                projectArea: [],
                businessUnit: "",
                salesman: "",
                school: "",
                projectManager: "",
                projectScheduleId: "",
                purchaseUnit: "",
                purchaseContent: "",
                classification: "",
                moneyTotal: "",
                remarks: "",
            },
            formTitle: "",
            dictList: [],
            userDataList: [],
            editRowId: "",
            rules: {
                dictType: [
                    {
                        required: true,
                        message: "请选择",
                        trigger: "change",
                    },
                ],
                moneyTotal: [
                    {
                        pattern:
                            /^([0-9]{0,10}\.{1}[0-9]{1,2})$|^([0-9]{0,10})$/,
                        message: "请输入正确的项目预算金额",
                        trigger: "blur",
                    },
                ],
                dictSort: [
                    {
                        required: true,
                        message: "请输入字段名称",
                        trigger: "blur",
                    },
                ],

                dictValue: [
                    {
                        required: true,
                        message: "请输入字段值",
                        trigger: "blur",
                    },
                ],
                dictLabel: [
                    {
                        required: true,
                        message: "请输入字段名称",
                        trigger: "blur",
                    },
                ],
                isDefault: [
                    {
                        required: true,
                        message: "请选择状态",
                        trigger: "change",
                    },
                ],
                status: [
                    {
                        required: true,
                        message: "请选择状态",
                        trigger: "change",
                    },
                ],
            },
            // -----------------------
            dictFromDialogTitle: "",
            TypeFromData: [],
            seeDialogTableVisible: false,
            searchform: {
                dictType: "",
                dectLabel: "",
                status: "",
            },
            selectFieldData: [],
            dictFromDialog: false,
            dictFromData: {
                dictCode: "",
                dictSort: "",
                dictValue: "",
                dictType: "sys_project_schedule",
                status: "",
                cssClass: "",
                dictLabel: "",
                isDefault: "",
                remark: "",
            },
            dictFromDataButton: "",
            TypeData: [],
        };
    },
    methods: {
        importChang() {
            this.importLoading = true;
        },
        getEntryNameList(query) {
            let obj = {
                entryName: query,
                pageNum: 1,
                pageSize: 999,
            };
            getReportingList(obj).then(({ msg, code, rows }) => {
                if (code == 200) {
                    this.administratorsList = rows;
                }
            });
        },
        getClassificationList(query) {
            let obj = {
                classification: query,
                pageNum: 1,
                pageSize: 999,
            };
            getReportingList(obj).then(({ code, rows }) => {
                if (code == 200) {
                    this.administratorsList = rows;
                }
            });
        },
        getPurchaseUnitList(query) {
            let obj = {
                purchaseUnit: query,
                pageNum: 1,
                pageSize: 999,
            };
            getReportingList(obj).then(({ code, rows }) => {
                if (code == 200) {
                    this.administratorsList = rows;
                }
            });
        },
        getSchoolList(query) {
            let obj = {
                school: query,
                pageNum: 1,
                pageSize: 999,
            };
            getReportingList(obj).then(({ code, rows }) => {
                if (code == 200) {
                    this.administratorsList = rows;
                }
            });
        },
        handerChage() {},
        showForm() {
            let obj1 = document.querySelector(".itemShow");
            let obj2 = document.querySelector(".itemShow2");
            let btn = document.querySelector(".btnShow");
            obj1.style.display = "block";
            obj2.style.display = "block";
            btn.style.display = "none";
        },
        isShowForm() {
            let obj1 = document.querySelector(".itemShow");
            let obj2 = document.querySelector(".itemShow2");
            let btn = document.querySelector(".btnShow");
            obj1.style.display = "none";
            obj2.style.display = "none";
            btn.style.display = "block";
        },
        // 导入成功
        importSuccess(res) {
            if (res.code == 200 && res.data.errorCount == 0) {
                this.getReportingList();
                this.$message.success(res.msg);
            } else {
                this.importError(res);
            }
            this.importLoading = false;
        },
        // 导入错误 提示
        importError(res) {
            this.dialogVisible = true;
            if (res.code == 200) {
                let result = "";
                res.data.errors.map((str) => {
                    result += `${str}<br/><br/>`;
                });
                this.errorPrompt = result;
            } else if (res.code == -1) {
                this.dialogVisible = true;
                this.errorPrompt = res.msg;
            }
            this.importLoading = false;
        },
        // 删除
        removeReporting(row) {
            let obj = {
                ids: [row.id],
            };
            this.$confirm("删除不可恢复，是否确认删除？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    removeReporting(obj).then(({ msg, code }) => {
                        if (code == 200) {
                            this.$message.success(msg);
                            this.getReportingList();
                        } else {
                            this.$message.error(msg);
                        }
                    });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消删除",
                    });
                });
        },
        // 导出
        exportData() {
            this.exportLoading = true;
            let obj = {
                ...this.reportingQueryForm,
            };
            reportingExportInfo(obj, "报备系统列表").finally(() => {
                this.exportLoading = false;
            });
        },
        // 关闭弹框
        cancel() {
            this.dialogVisibleForm = false;
            this.$refs.reportingForm.resetFields();
        },
        // 查询表格按钮
        queryForm() {
            this.queryloading = true;
            if (this.reportingQueryForm.projectArea.length) {
                this.reportingQueryForm.projectArea =
                    this.reportingQueryForm.projectArea.join("/");
            }
            this.getReportingList();
            this.queryloading = false;
        },
        // 列表接口
        getReportingList() {
            this.tableloading = true;
            let obj = {
                pageNum: this.pages.current,
                pageSize: this.pages.pagesize,
                ...this.reportingQueryForm,
            };
            getReportingList(obj).then(
                ({ code, rows, pageNum, pageSize, total }) => {
                    if (code == 200) {
                        this.tableloading = false;
                        this.pages.current = pageNum;
                        this.pages.pagesize = pageSize;
                        this.pages.total = total;
                        this.tableData = rows;
                        this.reportingQueryForm.projectArea = [];
                    }
                }
            );
        },
        // 打开弹框
        editReporting(row) {
            this.selectDictList();
            this.editRowId = row.id;
            this.dialogVisibleForm = true;
            this.formTitle = "编辑";
            // this.searchSchoolList();
            this.getEditReportingList();
        },
        addReporting() {
            this.formTitle = "新增";
            // this.searchSchoolList();
            this.$nextTick(function () {
                this.$refs.reportingForm.resetFields();
            });
            this.selectDictList();
            this.dialogVisibleForm = true;
        },
        // 新增编辑确定按钮
        onSubmit(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    if (this.formTitle == "编辑") {
                        this.editReportingList();
                    } else if (this.formTitle == "新增") {
                        this.addReportingList();
                    }
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        // 编辑接口
        editReportingList() {
            this.addLoading = true;
            const projectArea = this.reportingForm.projectArea;
            if (this.reportingForm.projectArea) {
                this.reportingForm.projectArea =
                    this.reportingForm.projectArea.join("/");
            }
            let obj = {
                ...this.reportingForm,
                id: this.editRowId,
            };
            editReportingList(obj).then(({ msg, code }) => {
                if (code == 200) {
                    this.addLoading = false;
                    this.dialogVisibleForm = false;
                    this.getReportingList();
                    this.$message.success(msg);
                } else {
                    this.$message.error(msg);
                    this.reportingForm.projectArea = projectArea;
                }
            });
        },
        // 新增接口
        addReportingList() {
            this.addLoading = true;
            const projectArea = this.reportingForm.projectArea;
            if (this.reportingForm.projectArea) {
                this.reportingForm.projectArea =
                    this.reportingForm.projectArea.join("/");
            }
            let obj = {
                ...this.reportingForm,
            };
            addReportingList(obj).then(({ code, msg }) => {
                if (code == 200) {
                    this.addLoading = false;
                    this.dialogVisibleForm = false;
                    this.getReportingList();
                    this.$message.success(msg);
                } else {
                    this.$message.error(msg);
                    this.reportingForm.projectArea = projectArea;
                }
            });
        },
        // 回显接口
        async getEditReportingList() {
            const { code, data, msg } = await getEditReportingList({
                id: this.editRowId,
            });
            if (code == 200) {
                const {
                    businessUnit,
                    projectScheduleId,
                    salesman,
                    entryName,
                    school,
                    projectManager,
                    projectSchedule,
                    purchaseUnit,
                    purchaseContent,
                    moneyTotal,
                    projectArea,
                    remarks,
                    classification,
                } = data;
                this.reportingForm = {
                    projectScheduleId: projectScheduleId
                        ? Number.parseInt(projectScheduleId)
                        : "",
                    projectArea: projectArea ? projectArea.split("/") : "",
                    entryName,
                    businessUnit,
                    classification,
                    school,
                    projectManager,
                    projectSchedule,
                    purchaseUnit,
                    purchaseContent,
                    moneyTotal,
                    remarks,
                    salesman,
                };
            } else {
                this.$message.error(msg);
            }
        },
        // 项目进度
        selectDictList() {
            selectDictList({ dictType: "sys_project_schedule" }).then(
                ({ code, rows }) => {
                    if (code == 200) {
                        this.dictList = rows;
                    }
                }
            );
        },
        // 重置
        searchResetForm() {
            this.$refs.reportingQueryForm.resetFields();
            this.getReportingList();
        },
        // 分页
        handleSizeChange(val) {
            this.pages.pagesize = val;
            this.getReportingList();
        },
        handleCurrentChange(val) {
            this.pages.current = val;
            this.getReportingList();
        },
        // 查看
        fieldManagement() {
            this.seeDialogTableVisible = true;
            this.selectFieldList();
        },
        // -------------------------------------------------------------------------------
        // 字典类型接口
        selectFieldList() {
            let obj = {
                dictName: "",
                dictType: "sys_project_schedule",
            };
            selectDictList(obj).then((res) => {
                if (res.code == 200) {
                    this.TypeData = res.rows;
                }
            });
        },
        // 添加
        addDict() {
            this.dictFromDataButton = "1";
            this.dictFromDialogTitle = "添加字段";
            this.dictFromDialog = true;
            this.dictFromData.dictSort = "1";
            this.dictFromData.dictValue = "";
            this.dictFromData.dictLabel = "";
            this.dictFromData.remark = "";
            this.dictFromData.status = "0";
            this.dictFromData.isDefault = "Y";
            this.dictFromData.dictType = "sys_project_schedule";
            this.selectFieldList();
        },
        // 表单确认按钮
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    if (this.dictFromDataButton == 1) {
                        this.addDictList();
                        this.selectFieldList(this.row);
                    } else if (this.dictFromDataButton == 2) {
                        this.submitEditDictList();
                        this.selectFieldList(this.row);
                    }
                }
                this.selectFieldList(this.row);
            });
        },
        // 新增列表接口
        addDictList() {
            this.buttonLoading = true;
            let obj = {
                ...this.dictFromData,
            };
            addDictList(obj).then(({ code, msg }) => {
                if (code == 200) {
                    this.buttonLoading = false;
                    this.dictFromDialog = false;
                    this.$message.success(msg);
                    // this.selectDictList();
                    this.selectFieldList(this.row);
                } else {
                    this.$message.error(msg);
                }
                this.buttonLoading = false;
            });
        },
        // 修改按钮
        editFrom(row) {
            this.dictFromDataButton = "2";
            this.dictFromDialogTitle = "编辑字段";
            this.dictFromDialog = true;
            this.echoDictList(row);
        },
        // 回显字典内容表单
        echoDictList(item) {
            let obj = {
                dictCode: item.dictCode,
            };
            echoDictList(obj).then(({ code, msg, data }) => {
                if (code == 200) {
                    const {
                        dictCode,
                        dictSort,
                        dictValue,
                        dictType,
                        status,
                        dictLabel,
                        isDefault,
                        remark,
                    } = data;
                    this.dictFromData = {
                        dictCode,
                        dictSort,
                        dictValue,
                        dictType,
                        status,
                        dictLabel,
                        isDefault,
                        remark,
                    };
                }
            });
        },
        // 确认编辑
        submitEditDictList(row) {
            this.buttonLoading = true;
            let obj = {
                ...this.dictFromData,
            };
            // debugger
            editDictList(obj).then(({ code, msg, data }) => {
                if (code == 200) {
                    (this.buttonLoading = false), this.$message.success(msg);
                    this.dictFromDialog = false;
                    // this.selectDictList(row);
                    this.selectFieldList();
                } else {
                    this.$message.error(msg);
                }
                this.buttonLoading = false;
            });
        },
        // 删除
        delData1(row) {
            this.removeDictList(row);
        },
        removeDictList(row) {
            this.$confirm("删除后将无法使用该字段", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })

                .then(() => {
                    let dictCode = row.dictCode;
                    removeDictList([dictCode]).then(({ code, msg }) => {
                        if (code == 200) {
                            this.$message.success(msg);
                            this.selectFieldList(row);
                        } else {
                            this.$message.error(msg);
                        }
                    });
                })
                .catch(() => {});
        },
        // 重置
        resetForm(formName) {
            this.searchform = {};
        },
    },
    created() {
        this.getReportingList();
        this.selectDictList();
        this.token = "Bearer " + getToken();
    },
};
</script>

<style lang="scss" scoped>
.btn {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    padding-top: 16px;
    border-top: 1px solid #ebeef5;
    span {
        margin: 10px;
    }
    .multipartFile {
        display: flex;
    }
}
.paginationBlock {
    margin-top: 16px;
    text-align: right;
}
.footerButton {
    width: 100%;
    text-align: center;
    border-top: 1px solid #dcdfe6;
    position: absolute;
    padding-top: 20px;
    bottom: 0;
    margin: 0;
    padding: 20px 0;
}
.download {
    justify-content: center;
    display: flex;
    align-items: center;
    margin: 10px;
    a {
        color: #409eff;
    }
}
</style>
<style lang="scss">
.dialogVisibleForm {
    .formBox {
        display: flex;
        justify-content: space-between;
        .el-input {
            width: 270px;
        }
        .el-textarea__inner {
            width: 270px;
        }
    }
}
.demo-form-inline {
    .queryForm {
        display: block;
        display: flex;
    }
    .el-input {
        width: 160px;
    }
    .el-textarea__inner {
        width: 240px;
    }
    .btnShow {
        display: block;
    }
    .itemShow,
    .itemShow2 {
        display: none;
    }
}
</style>
