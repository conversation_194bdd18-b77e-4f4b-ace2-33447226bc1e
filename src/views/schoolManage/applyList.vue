<template>
    <div class="ApplyList">
        <div class="setAppList">
            <div class="headBox">
                <div>
                    <i class="el-icon-back" @click="goBack()"></i>配置应用
                </div>
            </div>
            <div class="setAppListCon">
                <div class="setAppListCon_left">
                    <div class="allSelect">应用/系统：</div>
                    <div class="basicsAppList">
                        <div
                            v-for="(item, index) in allSchoolAppList"
                            class="appBox_right"
                            :key="item.id"
                        >
                            <img class="appIcon" :src="item.logo" alt="应用" />
                            <div class="appName">{{ item.name }}</div>
                            <div class="deploy" v-if="item.isDist">已配置</div>
                            <img
                                v-else
                                class="delIcon"
                                @click="addApp(item, '1')"
                                src="@/assets/images/pitchIcon.png"
                                alt="添加"
                            />
                        </div>
                    </div>
                </div>
                <div class="setAppListCon_right">
                    <div class="operationBox">已配应用：</div>
                    <div class="basicsAppList">
                        <div
                            v-for="(item, index) in configAppList"
                            class="appBox_right"
                        >
                            <img class="appIcon" :src="item.logo" alt="应用" />
                            <div class="appName">{{ item.name }}</div>
                            <div
                                class="setApp"
                                @click="allocation(item)"
                                v-if="item.isMenu"
                            >
                                配置功能
                            </div>
                            <div
                                class="setApp"
                                @click="configMode(item)"
                                v-if="['smartLib'].includes(item.code)"
                            >
                                配置模式
                            </div>
                            <div
                                class="setApp"
                                @click="upperComputerConfigMode(item)"
                                v-if="['antiBullying'].includes(item.code)"
                            >
                                上位机配置
                            </div>
                            <img
                                class="delIcon"
                                @click="deleteApp(item, '2')"
                                src="@/assets/images/del.png"
                                alt="删除"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <el-dialog title="配置" :visible.sync="dialogVisible" width="700px">
            <el-checkbox v-model="checkAll" @change="handleCheckAllChange"
                >全选/全不选</el-checkbox
            >
            <div style="margin: 15px 0"></div>
            <el-checkbox-group v-model="checkedmenus">
                <el-checkbox
                    v-for="item in applications"
                    :label="item.id"
                    :key="item.id"
                    >{{ item.name }}</el-checkbox
                >
            </el-checkbox-group>

            <span slot="footer" class="dialog-footer">
                <span class="selected">已选： {{ checkedmenus.length }}</span>
                <span>
                    <el-button @click="dialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="menuConfirm()"
                        >确 定</el-button
                    >
                </span>
            </span>
        </el-dialog>

        <el-dialog
            title="配置模式"
            :visible.sync="dialogConfigModeVisible"
            width="700px"
        >
            <el-form
                :model="configModeForm"
                label-position="top"
                ref="ruleForm"
                label-width="100px"
                class="demo-ruleForm"
            >
                <el-form-item label="图书馆模式：" prop="libraryModel" required>
                    <el-radio-group v-model="configModeForm.libraryModel">
                        <el-radio
                            v-for="item in configModeList"
                            :key="item.value"
                            :label="item.value"
                            >{{ item.label }}</el-radio
                        >
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <span class="selected"></span>
                <span>
                    <el-button @click="dialogConfigModeVisible = false"
                        >取 消</el-button
                    >
                    <el-button type="primary" @click="configModeConfirm()">
                        确 定
                    </el-button>
                </span>
            </span>
        </el-dialog>

        <el-dialog
            title="上位机配置"
            :visible.sync="dialogUpperComputerVisible"
            width="700px"
            @close="closeUpperComputerDialog"
        >
            <el-form
                :model="upperComputerForm"
                label-position="top"
                ref="upperComputerRef"
                :rules="rulesUpperComputer"
                label-width="100px"
                class="demo-ruleForm"
            >
                <el-form-item label="Access Key ID" prop="accessKey">
                    <el-input
                        v-model="upperComputerForm.accessKey"
                        :maxlength="50"
                        show-word-limit
                        placeholder="请输入Access Key ID"
                    ></el-input>
                </el-form-item>
                <el-form-item label="Access Key Secret" prop="accessSecret">
                    <el-input
                        placeholder="请输入Access Key Secret"
                        :maxlength="50"
                        v-model="upperComputerForm.accessSecret"
                        show-password
                    ></el-input>
                </el-form-item>
            </el-form>
            <p class="tips">
                <i class="el-icon-question"></i
                >上位机管理平台—系统设置—运维系统—安全认证
            </p>
            <span slot="footer" class="dialog-footer">
                <span class="selected"></span>
                <span>
                    <el-button @click="dialogUpperComputerVisible = false"
                        >取 消</el-button
                    >
                    <el-button
                        type="primary"
                        :loading="upperComputerLoading"
                        @click="sbumitUpperComputer"
                    >
                        确 定
                    </el-button>
                </span>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import {
    getAppOwner,
    deleteAppSchool,
    getConfigOpenApp,
    getCreateAppSchool,
    getMenuList,
    getMenuListByBid,
    getUpdateMenuBid,
    getLibraryModel,
    updateLibraryModel,
    getAlarmConfig,
    updateAlarmConfig,
} from "@/api/apply.js";

export default {
    name: "ApplyList",
    components: {},
    props: {
        appSchoolId: {
            type: String,
            default: "",
        },
        schoolType: {
            type: String,
            default: "",
        },
    },
    data() {
        return {
            allSchoolAppList: [],
            platform: "",
            dialogVisible: false,
            checkAll: false,
            checkedmenus: [],
            applications: [],
            dialogConfigModeVisible: false,
            configModeForm: {
                libraryModel: 1,
            },
            configModeList: [
                { label: "条码模式", value: 1 },
                { label: "ID模式", value: 2 },
                { label: "TID模式", value: 3 },
            ],
            // 上位机配置
            dialogUpperComputerVisible: false,
            upperComputerLoading: false,
            upperComputerForm: {
                accessKey: "",
                accessSecret: "",
            },
            rulesUpperComputer: {
                accessKey: [
                    {
                        required: true,
                        message: "请输入Access Key ID",
                        trigger: "blur",
                    },
                ],
                accessSecret: [
                    {
                        required: true,
                        message: "请输入Access Key Secret",
                        trigger: "blur",
                    },
                ],
            },
        };
    },
    computed: {
        configAppList() {
            return this.allSchoolAppList.filter((item) => item.isDist);
        },
    },
    methods: {
        handleCheckAllChange(val) {
            console.log("val", val);
            this.checkedmenus = val
                ? this.applications.map((item) => item.id)
                : [];
        },
        goBack() {
            this.$emit("handleBack", false);
        },
        reqAppOwner() {
            getAppOwner({
                type: this.schoolType,
                schoolId: this.appSchoolId,
            }).then((res) => {
                this.allSchoolAppList = res.data;
            });
        },
        reqDeleteAppSchool(data, type) {
            return new Promise((resolve, reject) => {
                deleteAppSchool({
                    schoolId: this.appSchoolId,
                    appId: data.id,
                })
                    .then(() => {
                        this.$message({
                            type: "success",
                            message: "删除成功!",
                        });
                        resolve();
                    })
                    .catch(() => {
                        this.$message({
                            type: "info",
                            message: "删除失败!",
                        });
                        reject();
                    });
            });
        },
        reqCreateAppSchool(data, type) {
            return new Promise((resolve, reject) => {
                getCreateAppSchool({
                    schoolId: this.appSchoolId,
                    appId: data.id,
                })
                    .then(() => {
                        this.$message({
                            type: "success",
                            message: "分配成功!",
                        });
                        resolve();
                    })
                    .catch(() => {
                        this.$message({
                            type: "info",
                            message: "分配失败!",
                        });
                        reject();
                    });
            });
        },
        reqConfigOpenApp(data, type) {
            return new Promise((resolve, reject) => {
                getConfigOpenApp({
                    schoolId: this.appSchoolId,
                    appId: data.id,
                    saveType: type,
                })
                    .then(() => {
                        this.$message({
                            type: "success",
                            message: "开通成功!",
                        });
                        resolve();
                    })
                    .catch(() => {
                        this.$message({
                            type: "info",
                            message: "开通失败!",
                        });
                        reject();
                    });
            });
        },

        deleteApp(data, type) {
            this.$confirm("确认删除该系统/应用吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    const deletePromise1 = this.reqDeleteAppSchool(data, type); // 第一个接口的删除操作返回一个Promise对象
                    const deletePromise2 = this.reqConfigOpenApp(data, type); // 第二个接口的删除操作返回一个Promise对象
                    Promise.all([deletePromise1, deletePromise2])
                        .then(() => {
                            this.reqAppOwner();
                            // 两个接口都成功删除后的操作
                            console.log("删除成功!");
                        })
                        .catch((error) => {
                            // 至少一个接口删除失败的操作
                            console.error("删除失败!", error);
                        });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消",
                    });
                });
        },
        addApp(data, type) {
            // 如果data.code=== evalActivity 则不允许添加
            if (data.code === "evalActivity") {
                this.$message({
                    type: "info",
                    message: "不需要添加该应用",
                });
                return;
            }
            this.$confirm("确认添加该系统/应用吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    const addPromise1 = this.reqCreateAppSchool(data, type); // 第一个接口的删除操作返回一个Promise对象
                    const addPromise2 = this.reqConfigOpenApp(data, type); // 第二个接口的删除操作返回一个Promise对象
                    Promise.all([addPromise1, addPromise2])
                        .then(() => {
                            this.reqAppOwner();
                            // 两个接口都成功删除后的操作
                            console.log("添加成功!");
                        })
                        .catch((error) => {
                            // 至少一个接口删除失败的操作
                            console.error("添加失败!", error);
                        });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消",
                    });
                });
        },
        allocation(data) {
            this.checkAll = false;
            this.platform = data.code;
            this.checkedmenus = [];
            // 点击配置功能
            // 先查一下 应用的菜单 再查学校已经分配的
            getMenuListByBid({
                code: data.code,
            }).then((res) => {
                this.applications = res.data;
                getMenuListByBid({
                    code: data.code,
                    schoolId: this.appSchoolId,
                }).then((res) => {
                    this.checkedmenus = res.data.map((item) => item.id);
                    this.dialogVisible = true;
                });
            });
        },
        menuConfirm() {
            getUpdateMenuBid({
                platform: this.platform,
                schoolId: this.appSchoolId,
                menuIds: this.checkedmenus,
            }).then(() => {
                this.dialogVisible = false;
            });
        },
        // 配置模式
        configMode() {
            this.dialogConfigModeVisible = true;
            getLibraryModel({ schoolId: this.appSchoolId }).then(({ data }) => {
                this.configModeForm.libraryModel = data.libraryModel;
            });
        },
        configModeConfirm() {
            const { libraryModel } = this.configModeForm;
            updateLibraryModel({
                schoolId: this.appSchoolId,
                libraryModel,
            }).then((res) => {
                this.$message({
                    type: "success",
                    message: res.message,
                });
                this.dialogConfigModeVisible = false;
            });
        },
        closeUpperComputerDialog() {
            this.$refs.upperComputerRef.resetFields();
            this.upperComputerLoading = false;
        },
        upperComputerConfigMode() {
            this.dialogUpperComputerVisible = true;
            getAlarmConfig({
                schoolId: this.appSchoolId,
            }).then(({ data }) => {
                this.upperComputerForm.accessKey = data.accessKey || "";
                this.upperComputerForm.accessSecret = data.accessSecret || "";
            });
        },
        sbumitUpperComputer() {
            this.$refs.upperComputerRef.validate((valid) => {
                if (valid) {
                    this.upperComputerLoading = true;
                    updateAlarmConfig({
                        schoolId: this.appSchoolId,
                        ...this.upperComputerForm,
                    })
                        .then((res) => {
                            this.$message({
                                type: "success",
                                message: res.message,
                            });
                            this.dialogUpperComputerVisible = false;
                        })
                        .finally(() => {
                            this.closeUpperComputerDialog();
                        });
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
    },
    created() {
        this.reqAppOwner();
    },
};
</script>

<style lang="scss">
.ApplyList {
    height: 100%;
}
.headBox {
    height: 62px;
    border-bottom: 1px solid #d9d9d9;
    line-height: 62px;
    padding-left: 24px;
    font-size: 16px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    .el-icon-back {
        cursor: pointer;
        padding-right: 8px;
    }
}

.basicsAppList {
    display: flex;
    flex-wrap: wrap;
}

.addApp {
    cursor: pointer;
}
.appName {
    padding-top: 12px;
    font-size: 14px;
    font-weight: 500;
    color: #262626;
}

.dialog-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.selected {
    font-size: 14px;
    font-weight: 500;
    color: #2e8aff;
}

.ApplyList .el-dialog__footer {
    border-top: 1px solid #d9d9d9;
}
.ApplyList .el-dialog__header {
    border-bottom: 1px solid #d9d9d9;
}

.setAppList {
    height: 100%;
    background: #ffffff;
    border-radius: 4px;
}

.setAppListCon {
    display: flex;
    height: calc(100% - 62px);
}
.setAppListCon_left,
.setAppListCon_right {
    padding: 16px;
    flex: 50%;
}
.setAppListCon_left {
    border-right: 1px solid #d9d9d9;
}

.appBox_right {
    padding-top: 15px;
    width: 100px;
    height: 100px;
    margin-right: 20px;
    margin-bottom: 20px;
    border: 1px dashed #d9d9d9;
    text-align: center;
    position: relative;
    .appIcon {
        height: 46px;
        width: 46px;
    }
    .delIcon {
        position: absolute;
        cursor: pointer;
        top: 0px;
        right: 0px;
        height: 20px;
        width: 20px;
    }
}
.operationBox {
    padding-bottom: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.allSelect {
    padding-bottom: 16px;
    font-size: 14px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
}

.deploy {
    background: #2e8aff;
    font-weight: 400;
    color: #ffffff;
    font-size: 12px;
    padding: 2px;
    border-radius: 0px 0px 0px 5px;
    position: absolute;
    cursor: pointer;
    top: 0px;
    right: 0px;
}

.setApp {
    padding-top: 6px;
    font-size: 12px;
    font-weight: 400;
    cursor: pointer;
    color: #2e8aff;
}
</style>
