<template>
    <div>
        <!-- 新增 -->
        <el-dialog
            title="审核学校"
            :visible.sync="show"
            :before-close="cancel"
            :wrapper-closable="false"
            width="500px"
        >
            <el-form
                ref="ruleForm"
                :model="form"
                :rules="rule"
                label-width="100px"
            >
                <el-form-item
                    prop="status"
                    label="是否通过："
                    class="my_radio_class"
                >
                    <el-radio-group v-model="form.status">
                        <el-radio :label="2">审核通过</el-radio>
                        <el-radio :label="0">审核不通过</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="原因：" prop="approvalReason">
                    <el-input
                        type="textarea"
                        :autosize="{ minRows: 2, maxRows: 4 }"
                        placeholder="请输入内容"
                        v-model="form.approvalReason"
                    >
                    </el-input>
                </el-form-item>
            </el-form>

            <div slot="footer">
                <div class="footer">
                    <el-button type="primary" @click="confirm">确定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { updateStatus } from "@/api/administrator.js";
export default {
    name: "CheckDialog",
    data() {
        return {
            show: false,
            form: {},
            rule: {
                status: [
                    {
                        required: true,
                        message: "请选择",
                    },
                ],
                approvalReason: [
                    {
                        required: true,
                        message: "请输入原因",
                    },
                ],
            },
        };
    },
    methods: {
        open(data) {
            this.show = true;
            this.form = { id: data.id };
        },
        // 取消
        cancel() {
            this.$refs.ruleForm.resetFields();
            this.show = false;
            this.$emit("refurbish");
        },
        // 确定
        confirm() {
            this.$refs.ruleForm.validate((valid) => {
                if (valid) {
                    updateStatus(this.form).then((res) => {
                        this.$message.success(res.message);
                        this.cancel();
                    });
                } else {
                    return false;
                }
            });
        },
    },
};
</script>

<style lang="scss" scoped>
:deep(.el-radio-group) {
    display: flex !important;
}
.my_radio_class {
    :deep(.el-radio) {
        display: flex;
        height: 40px;
        align-items: center;
    }
}

.footer {
    display: flex;
    justify-content: flex-end;
}
:deep(.el-dialog__footer) {
    border-top: 1px solid #d9d9d9;
}
:deep(.el-dialog__header) {
    border-bottom: 1px solid #d9d9d9;
}
</style>
