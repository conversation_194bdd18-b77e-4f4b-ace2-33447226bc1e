<template>
    <div class="schoolDetailsBox">
        <div class="headBox">
            <div><i class="el-icon-back" @click="goBack()"></i>学校详情</div>
        </div>
        <div class="schoolDec">
            <div class="school_icon">
                <el-image
                    style="width: 120px; height: 120px"
                    :src="this.schoolObj.badgeUrl"
                    fit="cover"
                >
                    <div slot="error" class="image-slot">
                        <img
                            style="width: 120px; height: 120px"
                            src="@/assets/images/uploadImg.png"
                            alt=""
                        />
                    </div>
                </el-image>
            </div>
            <div>
                <div class="school_title">{{ this.schoolObj.name }}</div>
                <el-descriptions :column="3" :colon="false" class="shcooldesc">
                    <el-descriptions-item label="学校编号：">{{
                        this.schoolObj.id
                    }}</el-descriptions-item>
                    <el-descriptions-item label="学校类型：">{{
                        getSchoolTypeObj(this.schoolObj.type)
                    }}</el-descriptions-item>
                    <el-descriptions-item label="区域：">{{
                        this.schoolObj.area
                    }}</el-descriptions-item>
                    <el-descriptions-item label="联系人：">{{
                        this.schoolObj.contact
                    }}</el-descriptions-item>
                    <el-descriptions-item label="手机号码：">{{
                        this.schoolObj.contactPhone
                    }}</el-descriptions-item>
                    <el-descriptions-item label="销售负责人：">{{
                        this.schoolObj.headSales || "-"
                    }}</el-descriptions-item>
                    <el-descriptions-item label="客户负责人：">{{
                        this.schoolObj.headClient
                    }}</el-descriptions-item>
                    <el-descriptions-item label="业务类型：">{{
                        getserviceType(this.schoolObj.serviceType)
                    }}</el-descriptions-item>
                    <el-descriptions-item label="一加壹平台状态：">{{
                        getenable(this.schoolObj.isCloudEnable)
                    }}</el-descriptions-item>
                    <el-descriptions-item label="详细地址：">{{
                        this.schoolObj.address || "-"
                    }}</el-descriptions-item>
                    <el-descriptions-item label="审核状态：">
                        {{
                            statusText[this.schoolObj.status] || "-"
                        }}</el-descriptions-item
                    >
                    <el-descriptions-item label="原因：">
                        {{
                            this.schoolObj.approvalReason || "-"
                        }}</el-descriptions-item
                    >
                </el-descriptions>
            </div>
        </div>
        <div class="appBox">
            <div class="appTitle">
                已配应用（<span style="color: #318afb">{{
                    appList.length
                }}</span
                >个）
            </div>
            <div class="appItem">
                <div
                    class="appItemBox"
                    v-for="(item, index) in appList"
                    :key="index"
                >
                    <div class="nameBox">
                        <div>
                            <img
                                style="
                                    width: 48px;
                                    height: 48px;
                                    padding-right: 12px;
                                "
                                :src="item.logo"
                                alt="应用icon"
                            />
                        </div>
                        <div>
                            <div class="appItemName">{{ item.name }}</div>
                            <!-- 这个是应用下分配的名字, -->
                            <div class="sonNameBox">
                                <div
                                    class="sonName"
                                    v-for="(it, inx) in item.menuList"
                                    :key="inx"
                                >
                                    {{ it }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { getSchoolCheckInfo } from "@/api/administrator.js";
export default {
    name: "schoolDetails",
    components: {},
    props: {
        // 当前学校id
        appSchoolId: {
            type: String,
            default: "",
        },
        schoolType: {
            type: Number,
            default: null,
        },
    },
    data() {
        return {
            statusText: {
                0: "拒绝",
                1: "审批中",
                2: "已通过",
            },
            enableObj: {
                0: "禁用",
                1: "可用",
                default: "-", // 默认值存储在 default 属性中
            },
            schoolTypeObj: {
                1: "初中等教育",
                2: "高等教育",
                default: "-", // 默认值存储在 default 属性中
            },
            serviceTypeObj: {
                1: "演示版",
                2: "商业版",
                default: "-", // 默认值存储在 default 属性中
            },
            schoolObj: {},
            appList: [],
        };
    },
    computed: {},
    methods: {
        getenable(key) {
            return this.enableObj[key] || this.enableObj.default;
        },
        getSchoolTypeObj(key) {
            return this.schoolTypeObj[key] || this.schoolTypeObj.default;
        },
        getserviceType(key) {
            return this.serviceTypeObj[key] || this.serviceTypeObj.default;
        },
        // 返回
        goBack() {
            this.$emit("handleBack", false);
        },
        // 获取学校详情
        reqSchoolInfoMes() {
            getSchoolCheckInfo({
                id: this.appSchoolId,
            }).then((res) => {
                this.schoolObj = res.data;
                // 获取应用列表
                this.appList = res.data.appList;
            });
        },
    },
    created() {
        this.reqSchoolInfoMes();
    },
};
</script>

<style lang="scss" scoped>
.schoolDetailsBox {
    height: 100%;
    background: #ffffff;
}
.headBox {
    height: 62px;
    border-bottom: 1px solid #d9d9d9;
    line-height: 62px;
    padding-left: 24px;
    font-size: 16px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    .el-icon-back {
        cursor: pointer;
        padding-right: 8px;
    }
}

.schoolDec {
    background: #e5effb;
    border-radius: 4px;
    display: flex;
    padding: 20px;
    margin: 20px;
}
.school_title {
    font-size: 18px;
    font-weight: 600;
    padding-bottom: 20px;
    color: rgba(0, 0, 0, 0.85);
}

.appBox {
    padding: 20px;
    .appTitle {
        font-size: 18px;
        padding-bottom: 16px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.85);
    }
}
.appItemBox {
    width: 316px;
    padding: 20px;
    // height: 88px;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #d9d9d9;
    display: flex;

    align-items: center;
    margin-right: 20px;
    margin-bottom: 10px;
    justify-content: space-between;
}

.nameBox {
    display: flex;
    align-items: center;
}
.sonName {
    margin-right: 8px;
    font-size: 12px;
    font-weight: 400;
    height: 30px;
    color: rgba(0, 0, 0, 0.45);
    display: flex;
    align-items: center;
}

.sonNameBox {
    display: flex;
    align-items: center;
}

.appItem {
    margin-right: 20px;
    flex-wrap: wrap;

    display: flex;
}

.operateDot {
    background-color: #f5f5f5;
    height: 24px;
    border-radius: 4px;
    width: 24px;
    text-align: center;
    line-height: 24px;
    cursor: pointer;
}

.shcooldesc .el-descriptions-item__content {
    font-weight: 400;
    // color: rgba(0,0,0,0.85) !important;
}
</style>

<style lang="scss">
.shcooldesc {
    .el-descriptions__body {
        background-color: #e5effb;
        .el-descriptions-item__label {
            font-weight: 400;
            color: rgba(0, 0, 0, 0.45);
        }
        .el-descriptions-item__content {
            font-weight: 400;
            color: rgba(0, 0, 0, 0.85);
        }
    }
}
.school_icon {
    padding-right: 16px;
    height: 120px;
    width: 120px;
    img {
        border-radius: 50%;
        width: 100%;
        height: 100%;
    }
}
</style>
