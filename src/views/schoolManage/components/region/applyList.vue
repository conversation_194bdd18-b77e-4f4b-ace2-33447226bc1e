<template>
    <div class="applyList">
        <div class="setAppList">
            <div class="headBox">
                <div>
                    <i class="el-icon-back" @click="goBack()"></i>配置应用
                </div>
            </div>
            <div class="tabsBox">
                <el-tabs
                    type="card"
                    v-model="activeTabs"
                    @tab-click="handleTabs"
                >
                    <el-tab-pane
                        label="区域应用/系统"
                        name="region"
                    ></el-tab-pane>
                    <el-tab-pane
                        label="学校应用/系统"
                        name="school"
                    ></el-tab-pane>
                </el-tabs>
            </div>
            <div class="setAppListCon">
                <div class="setAppListCon_left">
                    <div class="allSelect">应用/系统：</div>
                    <div v-if="allSchoolAppList.length" class="basicsAppList">
                        <div
                            v-for="item in allSchoolAppList"
                            class="appBox_right"
                            :key="item.id"
                        >
                            <img class="appIcon" :src="item.logo" alt="应用" />
                            <div class="appName">{{ item.name }}</div>
                            <div class="deploy" v-if="item.isDist">已配置</div>
                            <img
                                v-else
                                class="delIcon"
                                @click="addApp(item)"
                                src="@/assets/images/pitchIcon.png"
                                alt="添加"
                            />
                        </div>
                    </div>
                    <div class="noData" v-else>
                        <el-empty description="暂无数据"></el-empty>
                    </div>
                </div>
                <div class="setAppListCon_right">
                    <div class="operationBox">已配应用：</div>
                    <div class="basicsAppList" v-if="configAppList.length">
                        <div
                            v-for="(item, index) in configAppList"
                            class="appBox_right"
                            :key="index"
                        >
                            <img class="appIcon" :src="item.logo" alt="应用" />
                            <div class="appName">{{ item.name }}</div>
                            <img
                                class="delIcon"
                                @click="deleteApp(item, '2')"
                                src="@/assets/images/del.png"
                                alt="删除"
                            />
                        </div>
                    </div>
                    <div class="noData" v-else>
                        <el-empty description="暂无数据"></el-empty>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {
    getAppOwner,
    createAppOwner,
    deleteAppOwner,
} from "@/api/regionSystem.js";

export default {
    name: "ApplyList",
    components: {},
    props: {
        comForm: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            activeTabs: "region",
            allSchoolAppList: [],
        };
    },
    computed: {
        configAppList() {
            return this.allSchoolAppList.filter((item) => item.isDist);
        },
    },
    methods: {
        handleTabs(tab) {
            if (tab._props.name == "school") {
                this.reqAppOwner();
            } else {
                this.allSchoolAppList = [];
            }
        },
        goBack() {
            this.$emit("handleBack", false);
        },
        reqAppOwner() {
            getAppOwner({ organizationId: this.comForm.id }).then((res) => {
                this.allSchoolAppList = res.data;
            });
        },

        deleteApp(data, type) {
            this.$confirm("确认删除该系统/应用吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    deleteAppOwner({
                        appId: data.id,
                        organizationId: this.comForm.id,
                    }).then((res) => {
                        this.$message.success(res.message || "操作成功");
                        this.reqAppOwner();
                    });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消",
                    });
                });
        },
        addApp(data) {
            this.$confirm("确认添加该系统/应用吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    createAppOwner({
                        appId: data.id,
                        organizationId: this.comForm.id,
                    }).then((res) => {
                        this.$message.success(res.message || "操作成功");
                        this.reqAppOwner();
                    });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消",
                    });
                });
        },
    },
    created() {},
};
</script>

<style lang="scss">
.applyList {
    .el-tabs__header {
        margin: 0 !important;
    }
}
.applyList {
    height: 100%;
}
.tabsBox {
    margin-top: 10px;
}
.headBox {
    height: 62px;
    border-bottom: 1px solid #d9d9d9;
    line-height: 62px;
    padding-left: 24px;
    font-size: 16px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    .el-icon-back {
        cursor: pointer;
        padding-right: 8px;
    }
}

.basicsAppList {
    display: flex;
    flex-wrap: wrap;
    min-height: 60vh;
    align-content: flex-start;
}
.noData {
    padding-top: 60px;
    min-height: calc(60vh - 60px);
}

.addApp {
    cursor: pointer;
}
.appName {
    padding-top: 12px;
    font-size: 14px;
    font-weight: 500;
    color: #262626;
}

.dialog-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.selected {
    font-size: 14px;
    font-weight: 500;
    color: #2e8aff;
}

.ApplyList .el-dialog__footer {
    border-top: 1px solid #d9d9d9;
}
.ApplyList .el-dialog__header {
    border-bottom: 1px solid #d9d9d9;
}

.setAppList {
    min-height: 80vh;
    background: #ffffff;
    border-radius: 4px;
}

.setAppListCon {
    display: flex;
    height: calc(100% - 62px);
}
.setAppListCon_left,
.setAppListCon_right {
    padding: 16px;
    min-height: 100%;
    flex: 50%;
}
.setAppListCon_left {
    border-right: 1px solid #d9d9d9;
}

.appBox_right {
    padding-top: 15px;
    width: 100px;
    height: 100px;
    margin-right: 20px;
    margin-bottom: 20px;
    border: 1px dashed #d9d9d9;
    text-align: center;
    position: relative;
    .appIcon {
        height: 46px;
        width: 46px;
    }
    .delIcon {
        position: absolute;
        cursor: pointer;
        top: 0px;
        right: 0px;
        height: 20px;
        width: 20px;
    }
}
.operationBox {
    padding-bottom: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.allSelect {
    padding-bottom: 16px;
    font-size: 14px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
}

.deploy {
    background: #2e8aff;
    font-weight: 400;
    color: #ffffff;
    font-size: 12px;
    padding: 2px;
    border-radius: 0px 0px 0px 5px;
    position: absolute;
    cursor: pointer;
    top: 0px;
    right: 0px;
}

.setApp {
    padding-top: 6px;
    font-size: 12px;
    font-weight: 400;
    cursor: pointer;
    color: #2e8aff;
}
</style>
