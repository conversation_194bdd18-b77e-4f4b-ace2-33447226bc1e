<template>
    <div v-loading="tableLoading" class="tableBox">
        <YdTable
            ref="subTable"
            :tableColumn="tableColumn"
            :data="loadData"
            describe="区域列表"
        >
            <template slot="btn">
                <el-button
                    v-auth="'manage.regionSystem.add'"
                    type="primary"
                    icon="el-icon-plus"
                    @click="openForm({})"
                    >新增区域</el-button
                >
            </template>
            <template slot="operation" slot-scope="scope">
                <el-link
                    type="primary"
                    v-auth="'manage.regionSystem.edit'"
                    size="small"
                    icon="el-icon-edit"
                    :disabled="scope.row.pid == 0"
                    @click="openForm(scope.row)"
                    >编辑</el-link
                >

                <el-link
                    type="danger"
                    :disabled="scope.row.pid == 0"
                    size="small"
                    v-auth="'manage.regionSystem.info'"
                    style="margin-left: 10px"
                    @click="deleteForm(scope.row)"
                    >删除</el-link
                >
            </template>
        </YdTable>
        <el-drawer
            :title="isEdit ? '编辑区域' : '新增区域'"
            :visible.sync="drawer"
            :before-close="cancelForm"
            :wrapper-closable="false"
        >
            <el-form ref="form" :model="form" :rules="formRules">
                <div class="formClass">
                    <el-form-item label="区域名称：" prop="name">
                        <el-input
                            v-model="form.name"
                            size="medium"
                            maxlength="20"
                            placeholder="请输入区域名称"
                        />
                    </el-form-item>
                    <el-form-item label="上级行政区域：">
                        <el-input
                            :disabled="true"
                            v-model="comForm.regionName"
                            size="medium"
                            placeholder="请输入行政区代码"
                        />
                    </el-form-item>
                    <el-form-item label="行政区域代码：" prop="regionCode">
                        <el-input
                            v-model="form.regionCode"
                            size="medium"
                            placeholder="请输入行政区代码"
                        />
                    </el-form-item>
                </div>
            </el-form>
            <div class="yd-form-footer">
                <div class="footerForm">
                    <el-button class="reset" @click="cancelForm"
                        >取消</el-button
                    >
                    <el-button
                        type="primary"
                        :loading="btnLoading"
                        @click="submitForm"
                        >确 定</el-button
                    >
                </div>
            </div>
        </el-drawer>
    </div>
</template>

<script>
import YdTable from "@/components/YdTable";
import {
    getSubRegionPage,
    updateSubRegion,
    createSubRegion,
    deleteSubRegion,
} from "@/api/regionSystem.js";
export default {
    name: "CheckRegion",
    components: {
        YdTable,
    },
    props: {
        comForm: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            info: {},
            tableLoading: false,
            isEdit: false,
            tableColumn: [
                {
                    label: "序号",
                    type: "index",
                    width: "50px",
                    align: "center",
                    isShow: true,
                },
                {
                    label: "区域名称",
                    showtooltip: true,
                    index: "name",
                    isShow: true,
                },
                {
                    label: "行政区划代码",
                    index: "regionCode",
                    showtooltip: true,
                    isShow: true,
                    align: "center",
                },
                {
                    label: "上级行政区域",
                    index: "supRegionName",
                    isShow: true,
                    width: "150",
                    align: "center",
                },
                {
                    label: "超级账号",
                    index: "superAccount",
                    isShow: true,
                    isShow: true,
                    align: "center",
                },
                {
                    label: "创建时间",
                    index: "createTime",
                    isShow: true,
                    align: "center",
                    isShow: true,
                },
                {
                    label: "操作",
                    index: "operation",
                    isShow: true,
                    align: "left",
                    isShow: true,
                    fixed: "right",
                    scopedSlots: {
                        customRender: "operation",
                    },
                },
            ],
            loadData: (parameter) => {
                return getSubRegionPage({
                    ...parameter,
                    id: this.comForm.id,
                });
            },
            drawer: false,
            form: {},
            btnLoading: false,
            formRules: {
                name: [
                    {
                        required: true,
                        message: "请输入区域名称",
                        trigger: "blur",
                    },
                ],
                regionName: [
                    {
                        required: true,
                        message: "请选择上级行政区域",
                        trigger: "change",
                    },
                ],
                regionCode: [
                    {
                        required: true,
                        message: "请输入行政区代码",
                        trigger: "change",
                    },
                ],
            },
        };
    },
    methods: {
        openForm(item) {
            this.drawer = true;
            this.form = item || {};
            this.isEdit = !!item.id;
        },
        deleteForm(item) {
            this.$confirm("是否确认删除区域？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    deleteSubRegion({ id: item.id }).then((res) => {
                        this.$message.success(res.message || "操作成功");
                        this.$refs.subTable.handleRefresh(true);
                    });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消",
                    });
                });
        },
        cancelForm() {
            this.drawer = false;
        },
        submitForm() {
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    if (this.isEdit) {
                        updateSubRegion(this.form).then((res) => {
                            this.$message.success(res.message || "操作成功");
                            this.cancelForm();
                            this.$refs.subTable.handleRefresh(true);
                        });
                    } else {
                        this.form.id = this.info.id;
                        createSubRegion(this.form).then((res) => {
                            this.$message.success(res.message || "操作成功");
                            this.cancelForm();
                            this.$refs.subTable.handleRefresh(true);
                        });
                    }
                }
            });
        },
    },
    watch: {
        comForm: {
            handler(val) {
                this.info = val;
            },
            deep: true,
            immediate: true,
        },
    },
};
</script>

<style lang="scss" scoped>
.formClass {
    outline: none;
    margin-bottom: 76px;
    overflow-y: auto;
    border-top: 1px solid #ebeef5;
    padding: 20px;
    :deep(.el-select) {
        width: 100% !important;
    }
}
.yd-form-footer {
    z-index: 2;
    text-align: center;
    height: 64px;
    position: absolute;
    bottom: 0px;
    background: #fff;
    width: 100%;

    .footerForm {
        display: flex;
        width: 100%;
        justify-content: center;
        align-items: center;
        height: 64px;
        border: 1px solid #eee;
    }
}
</style>
