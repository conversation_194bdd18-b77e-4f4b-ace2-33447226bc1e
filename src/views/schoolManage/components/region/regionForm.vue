<template>
    <div>
        <el-drawer
            :title="isEdit ? '编辑区域' : '新增区域'"
            :visible.sync="drawer"
            :before-close="handleClose"
            :wrapper-closable="false"
        >
            <el-form ref="form" :model="form" :rules="formRules">
                <div class="formClass">
                    <el-form-item label="区域名称：" prop="name">
                        <el-input
                            v-model="form.name"
                            size="medium"
                            maxlength="20"
                            placeholder="请输入"
                        />
                    </el-form-item>
                    <el-form-item label="区域类型：" prop="type">
                        <el-select
                            :disabled="isEdit"
                            v-model="form.type"
                            @change="changeType"
                            size="medium"
                            placeholder="请选择"
                        >
                            <el-option label="市级" :value="1" />
                            <el-option label="区县级" :value="2" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="行政区域：" prop="regionCode">
                        <el-cascader
                            :disabled="isEdit"
                            v-model="form.regionCodes"
                            :props="{
                                value: 'id',
                                label: 'name',
                                children: 'children',
                            }"
                            @change="changeRegionCode"
                            style="width: 100%"
                            placeholder="请选择行政区域"
                            :options="regionCodeList"
                        />
                    </el-form-item>
                    <el-form-item label="最大学校数" prop="maxSchool">
                        <el-input
                            v-model.number="form.maxSchool"
                            size="medium"
                            placeholder="请输入"
                        />
                    </el-form-item>
                    <el-form-item label="联系人：" prop="contact">
                        <el-input
                            v-model="form.contact"
                            size="medium"
                            placeholder="请输入联系人"
                        />
                    </el-form-item>
                    <el-form-item label="手机号码：" prop="contactPhone">
                        <el-input
                            v-model.number="form.contactPhone"
                            size="medium"
                            maxlength="11"
                            placeholder="请输入手机号码"
                        />
                    </el-form-item>
                    <el-form-item label="销售负责人：" prop="headSales">
                        <el-input
                            v-model="form.headSales"
                            size="medium"
                            placeholder="请输入销售负责人"
                        />
                    </el-form-item>
                    <el-form-item label="业务类型：" prop="serviceType">
                        <el-select
                            v-model="form.serviceType"
                            placeholder="请选择"
                        >
                            <el-option label="演示版" :value="1"></el-option>
                            <el-option label="商业版" :value="2"></el-option>
                        </el-select>
                    </el-form-item>
                </div>
            </el-form>
            <div class="yd-form-footer">
                <div class="footerForm">
                    <el-button class="reset" @click="handleClose">
                        取消
                    </el-button>
                    <el-button
                        type="primary"
                        :loading="btnLoading"
                        @click="submitForm"
                        >确 定</el-button
                    >
                </div>
            </div>
        </el-drawer>
    </div>
</template>

<script>
import {
    getRegionTree,
    createRegion,
    updateRegion,
} from "@/api/regionSystem.js";
export default {
    name: "RegionForm",
    props: {
        drawer: {
            type: Boolean,
            default: false,
        },
        comForm: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        const validateTelephone = (rule, value, callback) => {
            if (!/^1[3-9]\d{9}$/.test(value)) {
                return callback(new Error("请输入正确的手机号！"));
            }
            callback();
        };
        return {
            isEdit: false,
            form: {},
            regionCodeList: [],
            btnLoading: false,
            formRules: {
                name: [
                    {
                        required: true,
                        message: "请输入区域名称",
                        trigger: "blur",
                    },
                ],
                type: [
                    {
                        required: true,
                        message: "请选择区域类型",
                        trigger: "change",
                    },
                ],
                regionCode: [
                    {
                        required: true,
                        message: "请选择行政区域",
                        trigger: "change",
                    },
                ],
                maxSchool: [
                    {
                        required: true,
                        message: "请输入最大学校数",
                        trigger: "blur",
                    },
                ],
                contact: [
                    {
                        required: true,
                        message: "请输入联系人",
                        trigger: "blur",
                    },
                ],
                contactPhone: [
                    {
                        required: true,
                        validator: validateTelephone,
                        message: "请输入正确的手机号码",
                        trigger: "blur",
                    },
                ],
            },
        };
    },
    methods: {
        async removeEmptyChildren(data) {
            // 遍历树的每个节点
            data.forEach((node) => {
                // 如果有 children 字段
                if (node.children && Array.isArray(node.children)) {
                    // 如果 children 是空数组，则删除该字段
                    if (node.children.length === 0) {
                        delete node.children;
                    } else {
                        // 否则递归处理 children
                        this.removeEmptyChildren(node.children);
                    }
                }
            });
        },
        handleClose() {
            this.$emit("close");
        },
        submitForm() {
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    this.btnLoading = true;
                    if (this.isEdit) {
                        updateRegion(this.form)
                            .then((res) => {
                                this.$message.success(
                                    res.message || "操作成功"
                                );
                                this.handleClose();
                            })
                            .finally(() => {
                                this.btnLoading = false;
                            });
                    } else {
                        createRegion(this.form)
                            .then((res) => {
                                this.$message.success(
                                    res.message || "操作成功"
                                );
                                this.handleClose();
                            })
                            .finally(() => {
                                this.btnLoading = false;
                            });
                    }
                    console.log(this.isEdit, this.form);
                }
            });
        },
        changeType() {
            this.getRegionTreeFn();
        },
        getRegionTreeFn() {
            getRegionTree({ depth: this.form.type }).then(async (res) => {
                await this.removeEmptyChildren(res.data);
                this.regionCodeList = res.data;
            });
        },
        changeRegionCode() {
            this.form.regionCode =
                this.form.regionCodes[this.form.regionCodes.length - 1];
            console.log(this.form);
        },
    },
    watch: {
        drawer(val) {},
        comForm: {
            handler(val) {
                this.isEdit = !!val.id;
                this.form = val;
                if (val.id) {
                    this.getRegionTreeFn();
                }
            },
            deep: true,
        },
    },
};
</script>

<style lang="scss" scoped>
.formClass {
    outline: none;
    margin-bottom: 76px;
    overflow-y: auto;
    border-top: 1px solid #ebeef5;
    padding: 20px;
    :deep(.el-select) {
        width: 100% !important;
    }
}
.yd-form-footer {
    z-index: 2;
    text-align: center;
    height: 64px;
    position: absolute;
    bottom: 0px;
    background: #fff;
    width: 100%;

    .footerForm {
        display: flex;
        width: 100%;
        justify-content: center;
        align-items: center;
        height: 64px;
        border: 1px solid #eee;
    }
}
</style>
