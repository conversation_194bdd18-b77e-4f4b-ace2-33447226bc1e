<template>
    <div class="regionInfo">
        <div class="name">{{ info.name }}</div>
        <el-descriptions :column="3">
            <el-descriptions-item
                :label="item.label"
                v-for="(item, index) in infoLabel"
                :key="item.value || index"
            >
                <span v-if="item.value == 'type'">
                    {{ info[item.value] == 1 ? "市级" : "省市级" }}
                </span>
                <span v-else-if="item.value == 'serviceType'">
                    {{ info[item.value] == 1 ? "演示版" : "商业版" }}
                </span>
                <span v-else>
                    {{ info[item.value] }}
                </span>
            </el-descriptions-item>
        </el-descriptions>
        <div v-loading="tableLoading">
            <YdTable
                ref="table"
                :tableColumn="tableColumn"
                :data="loadData"
                describe="区域列表"
            >
            </YdTable>
        </div>
    </div>
</template>

<script>
import YdTable from "@/components/YdTable";
import { getSubRegionPage } from "@/api/regionSystem.js";
export default {
    name: "RegionInfo",
    components: {
        YdTable,
    },
    props: {
        comForm: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            info: {},
            infoLabel: [
                {
                    value: "name",
                    label: "区域名称",
                },
                {
                    value: "id",
                    label: "区域ID",
                },
                {
                    value: "type",
                    label: "区域类型",
                },
                {
                    value: "regionName",
                    label: "行政区域",
                },
                {
                    value: "regionCode",
                    label: "行政区划代码",
                },
                {
                    value: "maxSchool",
                    label: "最大学校数",
                },
                {
                    value: "schoolNum",
                    label: "当前学校数",
                },
                {
                    value: "contact",
                    label: "联系人",
                },
                {
                    value: "contactPhone",
                    label: "手机号码",
                },
                {
                    value: "headSales",
                    label: "销售负责人",
                },
                {
                    value: "serviceType",
                    label: "业务类型",
                },
            ],
            tableLoading: false,
            tableColumn: [
                {
                    label: "序号",
                    type: "index",
                    width: "50px",
                    align: "center",
                    isShow: true,
                },
                {
                    label: "区域名称",
                    showtooltip: true,
                    index: "name",
                    isShow: true,
                },
                {
                    label: "行政区划代码",
                    index: "regionCode",
                    showtooltip: true,
                    isShow: true,
                    align: "center",
                },
                {
                    label: "上级行政区域",
                    index: "supRegionName",
                    isShow: true,
                    width: "150",
                    align: "center",
                },
                {
                    label: "超级账号",
                    index: "superAccount",
                    isShow: true,
                    isShow: true,
                    align: "center",
                },
                {
                    label: "创建时间",
                    index: "createTime",
                    isShow: true,
                    align: "center",
                    isShow: true,
                },
            ],
            loadData: (parameter) => {
                return getSubRegionPage({
                    ...parameter,
                    id: this.comForm.id,
                });
            },
        };
    },
    methods: {},
    watch: {
        comForm: {
            handler(val) {
                this.info = val;
            },
            deep: true,
            immediate: true,
        },
    },
};
</script>

<style lang="scss" scoped>
.regionInfo {
    min-height: 100%;
    background: #ffffff;
    .name {
        font-size: 18px;
        font-weight: 600;
        padding-bottom: 20px;
        color: rgba(0, 0, 0, 0.85);
    }
}
</style>

<style lang="scss">
.regionInfo {
    .el-descriptions__body {
        background-color: #e5effb;
        padding: 12px 12px 0 12px;
        .el-descriptions-item__label {
            font-weight: 400;
            color: rgba(0, 0, 0, 0.45);
        }
        .el-descriptions-item__content {
            font-weight: 400;
            color: rgba(0, 0, 0, 0.85);
        }
    }
}
</style>
