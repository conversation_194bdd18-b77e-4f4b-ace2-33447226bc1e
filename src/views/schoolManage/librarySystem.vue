<template>
    <!-- 学校管理 -->
    <div style="height: 100%">
        <el-card shadow="never" v-if="!configApp">
            <el-form ref="searchLibrary" :model="searchLibrary" status-icon :inline="true" size="medium"
                label-width="auto" class="demo-ruleForm">
                <el-form-item label="图书馆名称：" prop="name">
                    <el-input v-model="searchLibrary.name" placeholder="请输入图书馆名称" />
                </el-form-item>
                <el-form-item label="业务类型：" prop="schoolType">
                    <el-select v-model="searchLibrary.serviceType" placeholder="请选择业务类型">
                        <el-option label="全部" value="" />
                        <el-option label="演示版" value="1" />
                        <el-option label="商业版" value="2" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" @click="searchLibrarySubmitForm">查询</el-button>
                    <el-button @click="searchLibraryResetForm">重置</el-button>
                </el-form-item>
            </el-form>

            <div class="tableBox">
                <YdTable ref="table" :tableColumn="tableColumns" :data="loadData" describe="图书馆列表" :action="[]">
                    <template slot="btn">
                        <el-button type="primary" icon="el-icon-plus" @click="editLibrary(null)">新增图书馆</el-button>
                    </template>
                    <template slot="serviceType" slot-scope="scope">
                        <div>
                            {{ scope.row.serviceType === 1
                                ? "演示版"
                                : "商业版" }}
                        </div>
                    </template>
                    <template slot="isCloudEnable" slot-scope="scope">
                        <div>
                            {{ scope.row.isCloudEnable == 1 ? '启用' : '禁用' }}
                        </div>
                    </template>


                    <template slot="operation" slot-scope="scope">
                        <el-link type="primary" size="small" icon="el-icon-edit"
                            @click="editLibrary(scope.row)">编辑</el-link>
                        <el-link type="primary" size="small" style="margin-left: 10px"
                            @click="handlerDetail(scope.row)">详情</el-link>
                        <el-link type="primary" size="small" style="margin-left: 10px"
                            @click="handlerEnable(scope.row)">
                            {{ scope.row.isCloudEnable == 1 ? "禁用" : "启用" }}
                        </el-link>
                        <el-link type="primary" size="small" style="margin-left: 10px"
                            @click="allotDev(scope.row)">分配设备</el-link>
                        <el-link type="primary" size="small" style="margin-left: 10px"
                            @click="handlerPassword(scope.row)">重置密码</el-link>

                        <el-link type="primary" size="small" style="margin-left: 10px"
                            @click="handlerUnlockSuper(scope.row)">账号解锁</el-link>
                    </template>
                </YdTable>
            </div>
        </el-card>
        <!-- 新增编辑 -->
        <el-drawer :title="libraryTitile" :visible.sync="libraryDrawer" :before-close="cancelForm"
            :estroy-on-close="true" :wrapperClosable="false">
            <el-form class="reset-form" ref="libraryFormRef" :model="libraryForm" status-icon :rules="rules"
                :inline="true" size="medium" label-width="140px">
                <el-form-item label="图书馆名称：" prop="name">
                    <el-input v-model="libraryForm.name" placeholder="请输入图书馆名称" maxlength="20" show-word-limit />
                </el-form-item>
                <el-form-item label="图书馆区域：" prop="area">
                    <el-cascader v-model="libraryForm.area" :props="{
                        checkStrictly: true,
                        value: 'name',
                        label: 'name',
                        children: 'area',
                    }" style="width: 100%" placeholder="请选择图书馆所在区域" :options="areaList" />
                </el-form-item>
                <el-form-item label="详细地址：" prop="address">
                    <el-input v-model="libraryForm.address" placeholder="请输入详细地址" type="textarea" maxlength="50"
                        show-word-limit />
                </el-form-item>
                <el-form-item label="销售负责人：" prop="headSales">
                    <el-input v-model="libraryForm.headSales" placeholder="请输入销售负责人" maxlength="20" show-word-limit />
                </el-form-item>
                <el-form-item label="客户负责人：" prop="headClient">
                    <el-input v-model="libraryForm.headClient" placeholder="请输入客户负责人" maxlength="20" show-word-limit />
                </el-form-item>
                <el-form-item label="客户联系人：" prop="contact">
                    <el-input v-model="libraryForm.contact" placeholder="请输入客户联系人" maxlength="20" show-word-limit />
                </el-form-item>
                <el-form-item label="手机号：" prop="contactPhone">
                    <el-input v-model="libraryForm.contactPhone" placeholder="请输入客户联系人手机号码" maxlength="11"
                        show-word-limit />
                </el-form-item>
                <el-form-item label="业务类型：" prop="serviceType">
                    <el-select v-model="libraryForm.serviceType" placeholder="请输入业务类型">
                        <el-option label="演示版" :value="1" />
                        <el-option label="商业版" :value="2" />
                    </el-select>
                </el-form-item>
                <el-form-item label="图书馆模式：" prop="libraryModel">
                    <el-select v-model="libraryForm.libraryModel" placeholder="请输入图书馆模式">
                        <el-option  v-for="(it) in configModeList" :key="it.value" :label="it.label" :value="it.value"/>
                    </el-select>
                </el-form-item>
            </el-form>
            <div class="yd-form-footer">
                <div class="footerForm">
                    <el-button class="reset" @click="cancelForm">取消</el-button>
                    <el-button type="primary" :loading="loading" @click="submitForm">确 定</el-button>
                </div>
            </div>
        </el-drawer>

        <!-- 详情 :before-close="libraryDrawerDetail = false" -->
        <el-drawer class="drawer_detail " title="图书馆详情" :visible.sync="libraryDrawerDetail">
            <div class="detailBox">
                <div class="detailItem" v-for="(item, idx) in tableColumnsDetail" :key="idx">
                    <span class="detailLabel"> {{ item.label }}：</span>
                    <span class="detailValue" v-if="item.index == 'serviceType'">
                        {{ libraryForm[item.index] === 1 ? "演示版" : "商业版" }}
                    </span>
                    <!-- <span class="detailValue" v-else-if="item.index == 'isCloudEnable'">
                        {{ libraryForm[item.index] === 1 ? "启用" : "禁用" }}
                    </span> -->
                    <span class="detailValue" v-else> {{ libraryForm[item.index] }}</span>
                </div>
            </div>
        </el-drawer>

        <!-- 这个是分配设备的弹窗 -->
        <el-dialog title="分配设备品牌" :visible.sync="allotDialogVisible" width="800px">
            <div class="allotDialogVisibleBox">
                <div class="fullSelected">
                    <el-checkbox @change="fullSelectedChange" v-model="fullSelected">全选</el-checkbox>
                </div>
                <div class="devicetypeBox" v-for="(item, index) in equipmentThree">
                    <!-- <div>{{ item.equipmentName }}：</div> -->
                    <el-checkbox v-model="item.allocated">{{
                        item.equipmentName
                        }}</el-checkbox>：
                    <div style="
                                display: flex;
                                align-items: center;
                                flex-wrap: wrap;
                            ">
                        <div v-for="(it, ix) in item.deviceBrandDTOList" style="margin-right: 15px">
                            <el-checkbox v-model="it.allocated" :disabled="!item.allocated">{{ it.brandName }}({{
                                it.deviceManufacturer
                                }})</el-checkbox>
                        </div>
                    </div>
                </div>
            </div>
            <span slot="footer">
                <el-button @click="allotDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="distribution">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import YdTable from "@/components/YdTable";
import {
    getAreaList,
    createSchool,
    getSchoolInfo,
    updateSchoolInfo,
    getSchoolAppList,
    unlockSuperAdmin,
    brandListBySchool,
    updateSchoolStatus,
    updateAssignedDevice,
    resetAdminPassword
} from "@/api/librarySystem.js";
import { verifyPhone } from "@/utils/toolsValidate.js";

const tableColumnsDetail = [
    {
        label: "图书馆名称",
        index: "name",
    },
    {
        label: "图书馆编号",
        index: "schoolId",
    },
    {
        label: "超级管理员",
        index: "superAccount",
    },
    {
        label: "图书馆区域",
        index: "area",
    },
    {
        label: "详细地址",
        index: "address",
    },
    {
        label: "销售负责人",
        index: "headSales",
    },
    {
        label: "客户负责人",
        index: "headClient",
    },
    {
        label: "客户联系人",
        index: "contact",
    },
    {
        label: "手机号",
        index: "contactPhone",
    },
    {
        label: "业务类型",
        index: "serviceType",
    },
    {
        label: "图书馆模式",
        index: "libraryModel",
    },

]
const tableColumns = [
    {
        label: "序号",
        type: "index",
        index: "index",
        width: "50px",
        align: "center",
        isShow: true,
    },
    {
        label: "图书馆名称",
        showtooltip: true,
        index: "name",
        isShow: true,
    },
    {
        label: "图书馆编号",
        showtooltip: true,
        index: "schoolId",
        isShow: true,
    },
    {
        label: "超级账号",
        showtooltip: true,
        index: "superAccount",
        isShow: true,
    },
    {
        label: "区域",
        showtooltip: true,
        index: "area",
        isShow: true,
        width: "180",
    },
    {
        label: "销售负责人",
        showtooltip: true,
        index: "headSales",
        isShow: true,
    },
    {
        label: "客户负责人",
        showtooltip: true,
        index: "headClient",
        isShow: true,
    },
    {
        label: "业务类型",
        showtooltip: true,
        index: "serviceType",
        isShow: true,
        scopedSlots: {
            customRender: "serviceType",
        },
    },
    {
        label: "状态",
        showtooltip: true,
        index: "isCloudEnable",
        isShow: true,
        scopedSlots: {
            customRender: "isCloudEnable",
        },
    },
    {
        label: "添加时间",
        showtooltip: true,
        index: "createTime",
        isShow: true,
        width: "160",
    },
    {
        label: "操作",
        index: "operation",
        isShow: true,
        align: "left",
        isShow: true,
        fixed: "right",
        width: "350",
        scopedSlots: {
            customRender: "operation",
        },
    },
]
function modifyAllocated(treeArray, allocated) {
    // 遍历树形数组的每个元素
    treeArray.forEach(function (node) {
        // 修改当前节点的allocated值
        node.allocated = allocated;
        // 如果当前节点有子节点，则递归调用modifyAllocated函数
        if (node.deviceBrandDTOList && node.deviceBrandDTOList.length > 0) {
            modifyAllocated(node.deviceBrandDTOList, allocated);
        }
    });
}

export default {
    name: "librarySystem",
    components: { YdTable, },
    data() {
        const validateCurrentPhone = async (_rule, value) => {
            if (value && verifyPhone(value)) {
                return Promise.resolve();
            }
            return Promise.reject("请输入正确的手机号码");
        };

        return {
            tableColumnsDetail,
            tableColumns,
            libraryDrawerDetail: false,
            libraryDrawer: false,
            // direction: 'rtl',
            libraryTitile: "新增图书馆",
            loading: false,
            configApp: false,
            allotDialogVisible: false,
            fullSelected: false,
            facilitySchoolId: "",
            equipmentThree: [],
            libraryForm: {
                id: '',
                schoolId: '',
                name: "",
                area: [],
                address: "",
                headSales: "",
                headClient: "",
                contact: "",
                contactPhone: "",
                serviceType: "",
                libraryModel:null
            },
            // 查询
            searchLibrary: {
                name: "",
                serviceType: "",
            },
            pagination: {
                pageNo: 1,
                pageSize: 10,
                total: 0
            },
            areaList: [],
            loadData: (parameter) => {
                const params = {
                    ...parameter,
                    ...this.searchLibrary,
                }
                return getSchoolAppList(params);
            },
            rules: {
                name: [
                    { required: true, message: '请输入图书馆名称', trigger: 'blur' },
                    { min: 0, max: 20, message: '长度在20个字符', trigger: 'blur' }
                ],
                area: [
                    { required: true, message: '请选择图书馆所在区域', trigger: 'change' },
                ],
                address: [
                    { required: true, message: '请输入详细地址', trigger: 'blur' },
                    { min: 0, max: 50, message: '长度在20个字符', trigger: 'blur' }
                ],
                headSales: [
                    { required: false, message: '请输入销售负责人', trigger: 'blur' },
                    { min: 0, max: 20, message: '长度在20个字符', trigger: 'blur' }
                ],
                headClient: [
                    { required: false, message: '请输入客户负责人', trigger: 'blur' },
                    { min: 0, max: 20, message: '长度在20个字符', trigger: 'blur' }
                ],
                contact: [
                    { required: true, message: '请输入客户联系人', trigger: 'blur' },
                    { min: 0, max: 20, message: '长度在20个字符', trigger: 'blur' }
                ],
                contactPhone: [
                    { required: true, validator: validateCurrentPhone, trigger: 'blur' },
                    // { min: 0, max: 11, message: '长度在11个字符', validator: validateCurrentPhone, trigger: 'blur' }
                ],
                serviceType: [
                    { required: true, message: '请选择业务类型', trigger: 'change' },
                ],
                libraryModel: [
                    { required: true, message: '请输入图书馆模式', trigger: 'change' },
                ],
            },
             configModeList:    [
                {label:'条码模式',value:1 },
                {label:'ID模式',value:2 },
                {label:'TID模式',value:3 },
            ]
        };
    },
    created() {
        this.getAreaListFn()
    },
    methods: {

        // 查询
        searchLibrarySubmitForm() {
            // this.pagination.pageNo = 1;
            // this.pagination.pageSize = 10;
            this.$refs.table.handleRefresh(true);
        },
        // 重置 
        searchLibraryResetForm() {
            this.searchLibrary = {
                name: "",
                serviceType: "",
            }
            this.searchLibrarySubmitForm()
        },
        // 移除空的子节点
        removeEmptyChildren(data) {
            data.forEach((item) => {
                if (item.area && item.area.length === 0) {
                    delete item.area;
                } else if (item.area) {
                    this.removeEmptyChildren(item.area);
                }
            });
            return data;
        },
        // 获取区域列表
        getAreaListFn() {
            getAreaList().then((res) => {
                this.areaList = this.removeEmptyChildren(res.data);
            });
        },

        // 新增
        submitForm() {
            this.$refs.libraryFormRef.validate((valid) => {
                if (valid) {
                    let address = ""
                    if (Array.isArray(this.libraryForm.area)) {
                        address = this.libraryForm.area.join("/");
                    }
                    const params = {
                        ...this.libraryForm,
                        area: address
                    }
                    let Api = createSchool
                    if (params.schoolId) {
                        Api = updateSchoolInfo
                    }
                    this.loading = true
                    Api(params).then(() => {
                        this.cancelForm();
                        this.searchLibrarySubmitForm()
                    }).finally(() => {
                        this.loading = false
                    })
                }
            })
        },
        // 编辑
        editLibrary(row) {
            this.libraryDrawer = true;
            this.libraryTitile = row ? '编辑图书馆' : "新增图书馆";
            if (row) {
                this.getSchoolInfoFn(row.schoolId, 'edit')
            }
        },

        // 获取学校详情
        getSchoolInfoFn(schoolId, type) {
            getSchoolInfo({ schoolId }).then(({data}) => {
                this.libraryForm = data
                if(type == 'edit') {
                    this.libraryForm.area =  data.area.split("/")
                }else{
                    this.configModeList.forEach(it=>{
                        if(it.value == data.libraryModel) {
                            this.libraryForm.libraryModel = it.label
                        }
                    }) 
                }
            })
        },
        // 详情
        handlerDetail(row) {
            this.libraryDrawerDetail = true
            this.getSchoolInfoFn(row.schoolId)
        },
        // 启用禁用
        handlerEnable(row) {
            const { schoolId, isCloudEnable } = row
            const params = {
                schoolId, isCloudEnable: isCloudEnable == 1 ? 0 : 1
            }
            this.loading = true
            updateSchoolStatus(params).then(() => {
                this.searchLibrarySubmitForm()
                this.$message.success("操作成功")
            }).finally(() => {
                this.loading = false
            })
        },
        // 分配设备
        allotDev(row) {
            brandListBySchool({
                schoolId: row.schoolId,
            }).then((res) => {
                this.allotDialogVisible = true;
                this.equipmentThree = res.data.schoolEquipmentTypeList.filter(
                    (item) =>
                        item.deviceBrandDTOList &&
                        item.deviceBrandDTOList.length > 0
                );
                this.facilitySchoolId = res.data.schoolId;
            });
        },

        // 分配设备品牌的确认按钮
        distribution() {
            // 使用filter方法筛选出allocated为true的数组对象，并使用map方法返回所需的数据结构
            const filterequipmentThree = this.equipmentThree.filter(
                (item) => item.allocated
            );
            const allocatedTrueObjects = filterequipmentThree.map((obj) => {
                return {
                    deviceBrandDTOList: obj.deviceBrandDTOList.filter(
                        (brand) => brand.allocated === true
                    ),
                    equipmentName: obj.equipmentName,
                    equipmentType: obj.equipmentType,
                };
            });

            updateAssignedDevice({
                schoolId: this.facilitySchoolId,
                schoolEquipmentTypeList: allocatedTrueObjects,
            }).then((res) => {
                this.allotDialogVisible = false;
                this.$message.success("分配成功");
            });

            console.log(allocatedTrueObjects);
        },
        // 全选逻辑在这里
        fullSelectedChange(val) {
            if (val) {
                modifyAllocated(this.equipmentThree, true);
            } else {
                modifyAllocated(this.equipmentThree, false);
            }
        },
        // 重置密码
        handlerPassword(row) {
            this.$confirm("是否确认重置密码？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    const params = {
                        id: row.schoolId
                    }
                    this.loading = true
                    resetAdminPassword(params).then(() => {
                        this.searchLibrarySubmitForm()
                        this.$message.success("操作成功")
                    }).finally(() => {
                        this.loading = false
                    })
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消",
                    });
                });

        },
        // 账号解锁
        handlerUnlockSuper(row) {
            this.$confirm("是否确认解锁账号？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    const params = {
                        schoolId: row.schoolId
                    }
                    this.loading = true
                    unlockSuperAdmin(params).then(() => {
                        this.searchLibrarySubmitForm()
                        this.$message.success("操作成功")
                    }).finally(() => {
                        this.loading = false
                    })
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消",
                    });
                });




        },
        // 取消
        cancelForm() {
            this.$refs.libraryFormRef.resetFields();
            this.libraryForm = {
                id: '',
                schoolId: '',
                name: "",
                area: [],
                address: "",
                headSales: "",
                headClient: "",
                contact: "",
                contactPhone: "",
                serviceType: "",
                libraryModel:null
            }
            this.libraryDrawer = false;
        },
    }

};
</script>

<style lang="scss" scoped>
:deep(.el-drawer__body) {
    border-top: 1px solid #ebeef5 !important;
    padding: 14px 14px 60px;
    position: relative;

    .reset-form {
        width: 100%;

        .el-form-item {
            display: flex;

            .el-select,
            .el-form-item__content {
                width: 100%;
            }
        }
    }

    .yd-form-footer {
        border-top: 1px solid #ebeef5 !important;
        position: absolute;
        bottom: 0;
        text-align: center;
        left: 0;
        right: 0;
        padding: 10px 0;
    }
}

.drawer_detail {
    :deep(.el-drawer__header) {
        span {
            font-weight: 700;
        }
    }

    .detailItem {
        padding: 10px;
        display: flex;

        .detailLabel {
            width: 90px;
            font-weight: bold;
            display: inline-block;
        }

        .detailValue {
            flex: 1;
        }
    }

}
</style>
