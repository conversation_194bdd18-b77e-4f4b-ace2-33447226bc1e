<template>
    <!-- 区域管理 -->
    <div class="regionSystem">
        <el-card shadow="never" v-if="type == 'table'">
            <el-form
                ref="queryFormRef"
                :model="queryForm"
                status-icon
                :inline="true"
                size="medium"
                label-width="auto"
                class="demo-ruleForm"
            >
                <el-form-item label="区域名称：" prop="name">
                    <el-input
                        v-model="queryForm.name"
                        placeholder="请输入联系电话"
                    />
                </el-form-item>
                <el-form-item label="区域类型：" prop="type">
                    <el-select
                        v-model="queryForm.type"
                        placeholder="请选择区域类型"
                    >
                        <el-option label="全部" value="" />
                        <el-option label="市区县" value="1" />
                        <el-option label="区县" value="2" />
                    </el-select>
                </el-form-item>
                <el-form-item label="联系电话：" prop="contactPhone">
                    <el-input
                        v-model="queryForm.contactPhone"
                        placeholder="请输入联系电话"
                    />
                </el-form-item>
                <el-form-item label="销售负责人：" prop="headSales">
                    <el-input
                        v-model="queryForm.headSales"
                        placeholder="请输入销售负责人"
                    />
                </el-form-item>
                <el-form-item label="业务类型：" prop="serviceType">
                    <el-select
                        v-model="queryForm.serviceType"
                        placeholder="请选择业务类型"
                    >
                        <el-option label="演示版" :value="1" />
                        <el-option label="商业版" :value="2" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button
                        type="primary"
                        icon="el-icon-search"
                        @click="queryTalbe"
                    >
                        查询
                    </el-button>
                    <el-button @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>
            <div v-loading="tableLoading" class="tableBox">
                <YdTable
                    ref="table"
                    :tableColumn="tableColumn"
                    :action="[]"
                    :data="loadData"
                    describe="区域列表"
                >
                    <template slot="type" slot-scope="scope">
                        {{ scope.row.type == 1 ? "市级" : "区县级" }}
                    </template>
                    <template slot="serviceType" slot-scope="scope">
                        <div>
                            {{
                                scope.row.serviceType == 1
                                    ? "演示版"
                                    : scope.row.serviceType == 2
                                    ? "商业版"
                                    : "-"
                            }}
                        </div>
                    </template>
                    <template slot="btn">
                        <el-button
                            v-auth="'manage.regionSystem.add'"
                            type="primary"
                            icon="el-icon-plus"
                            @click="openForm({})"
                            >新增区域</el-button
                        >
                        <el-button
                            v-auth="'manage.regionSystem.export'"
                            icon="el-icon-bottom"
                            style="margin-left: 16px"
                            type="primary"
                            plain
                            @click="exportData"
                            >导出</el-button
                        >
                    </template>
                    <template slot="operation" slot-scope="scope">
                        <el-link
                            type="primary"
                            v-auth="'manage.regionSystem.edit'"
                            size="small"
                            icon="el-icon-edit"
                            @click="openForm(scope.row)"
                            >编辑</el-link
                        >

                        <el-link
                            type="primary"
                            size="small"
                            v-auth="'manage.regionSystem.info'"
                            style="margin-left: 10px"
                            @click="goPage('info', scope.row)"
                            >详情</el-link
                        >
                        <el-link
                            type="primary"
                            size="small"
                            v-auth="'manage.regionSystem.checkRegion'"
                            style="margin-left: 10px"
                            @click="goPage('check', scope.row)"
                            >查看区域</el-link
                        >
                        <el-link
                            type="primary"
                            size="small"
                            v-auth="'manage.regionSystem.apply'"
                            style="margin-left: 10px"
                            @click="configApply(scope.row)"
                            >配置应用</el-link
                        >
                        <el-link
                            type="primary"
                            size="small"
                            v-auth="'manage.regionSystem.reset'"
                            style="margin-left: 10px"
                            @click="resetPassword(scope.row)"
                        >
                            重置密码
                        </el-link>
                        <el-link
                            type="primary"
                            size="small"
                            style="margin-left: 10px"
                            v-auth="'manage.regionSystem.unlock'"
                            @click="unlocking(scope.row)"
                        >
                            账号解锁
                        </el-link>
                    </template>
                </YdTable>
            </div>
        </el-card>
        <el-card v-if="type == 'info'" class="info_card">
            <div slot="header" class="headBox">
                <div>
                    <i class="el-icon-back" @click="pageBack"></i>区域详情
                </div>
            </div>
            <RegionInfo :comForm="comForm" />
        </el-card>
        <el-card v-if="type == 'check'" class="info_card">
            <div slot="header" class="headBox">
                <div>
                    <i class="el-icon-back" @click="pageBack"></i>查看区域
                </div>
            </div>
            <checkRegion :comForm="comForm" />
        </el-card>
        <ApplyList
            v-if="type == 'applyList'"
            @handleBack="pageBack"
            :comForm="comForm"
        />
        <!-- 表单 -->
        <RegionForm :drawer="drawer" :comForm="comForm" @close="closeForm" />
    </div>
</template>

<script>
import YdTable from "@/components/YdTable";
import RegionForm from "../components/region/regionForm.vue";
import RegionInfo from "../components/region/regionInfo.vue";
import CheckRegion from "../components/region/checkRegion.vue";
import ApplyList from "../components/region/applyList.vue";

import {
    getRegionPage,
    resetAdminPassword,
    unlockSuperAdmin,
    exportInfo,
} from "@/api/regionSystem.js";
export default {
    name: "regionSystem",
    components: {
        YdTable,
        RegionForm,
        RegionInfo,
        CheckRegion,
        ApplyList,
    },
    data() {
        return {
            type: "table",
            drawer: false,
            tableLoading: false,
            queryForm: {},
            comForm: {},
            tableColumn: [
                {
                    label: "序号",
                    type: "index",
                    width: "50px",
                    align: "center",
                    isShow: true,
                },
                {
                    label: "区域名称",
                    showtooltip: true,
                    index: "name",
                    width: "220",
                    isShow: true,
                },
                {
                    label: "区域ID",
                    index: "regionCode",
                    showtooltip: true,
                    isShow: true,
                    width: "180",
                    align: "center",
                },
                {
                    label: "手机号",
                    index: "contactPhone",
                    isShow: true,
                    width: "150",
                    align: "center",
                },
                {
                    label: "区域类型",
                    index: "type",
                    isShow: true,
                    isShow: true,
                    width: "150",
                    align: "center",
                    scopedSlots: {
                        customRender: "type",
                    },
                },
                {
                    label: "超管账号",
                    index: "superAccount",
                    isShow: true,
                    isShow: true,
                    width: "150",
                    align: "center",
                },
                {
                    label: "最大学校数量",
                    index: "maxSchool",
                    isShow: true,
                    isShow: true,
                    width: "150",
                    align: "center",
                },
                {
                    label: "当前学校数量",
                    index: "schoolNum",
                    isShow: true,
                    isShow: true,
                    width: "150",
                    align: "center",
                },
                {
                    label: "销售负责人",
                    index: "headSales",
                    width: "100",
                    isShow: true,
                    isShow: true,
                    align: "center",
                },
                {
                    label: "业务类型",
                    index: "serviceType",
                    isShow: true,
                    width: "100",
                    isShow: true,
                    align: "center",
                    width: "100",
                    scopedSlots: {
                        customRender: "serviceType",
                    },
                },
                {
                    label: "创建时间",
                    index: "createTime",
                    width: "200",
                    isShow: true,
                    align: "center",
                    isShow: true,
                },
                {
                    label: "操作",
                    index: "operation",
                    isShow: true,
                    align: "left",
                    isShow: true,
                    width: "400",
                    fixed: "right",
                    scopedSlots: {
                        customRender: "operation",
                    },
                },
            ],
            loadData: (parameter) => {
                return getRegionPage(
                    Object.assign({ ...parameter }, this.queryForm)
                );
            },
            regionId: null,
        };
    },
    methods: {
        openForm(item) {
            this.drawer = true;
            this.comForm = item || {};
        },
        closeForm() {
            this.drawer = false;
            this.$refs.table.handleRefresh(true);
        },
        pageBack() {
            this.type = "table";
        },
        goPage(newType, item) {
            this.type = newType;
            this.comForm = item || {};
        },
        configApply(item) {
            this.comForm = item || {};
            this.type = "applyList";
        },
        // 重置密码
        resetPassword(item) {
            this.$confirm("是否确认重置密码？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    resetAdminPassword({ id: item.id }).then((res) => {
                        this.$message.success("操作成功");
                        this.$refs.table.handleRefresh(true);
                    });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消",
                    });
                });
        },
        queryTalbe() {
            this.$refs.table.handleRefresh(true);
        },
        resetQuery() {
            this.queryForm = {};
            this.queryTalbe();
        },
        // 解锁账号
        unlocking(item) {
            this.$confirm("是否确认解锁账号？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    unlockSuperAdmin({ id: item.id }).then((res) => {
                        this.$message.success("操作成功");
                        this.$refs.table.handleRefresh(true);
                    });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消",
                    });
                });
        },
        exportData() {
            exportInfo({}, "区域列表");
        },
    },
};
</script>

<style scoped lang="scss">
.regionSystem {
    min-height: 100%;
    .tableBox ::-webkit-scrollbar {
        width: 10px !important;
        height: 10px !important;
        background-color: #f5f5f5;
    }

    .headBox {
        height: 62px;
        border-bottom: 1px solid #d9d9d9;
        line-height: 62px;
        padding-left: 24px;
        font-size: 16px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.85);
        .el-icon-back {
            cursor: pointer;
            padding-right: 8px;
        }
    }
}
</style>
<style lang="scss">
.info_card {
    .el-card__header {
        padding: 0 !important;
    }
}
</style>
