<template>
    <div style="height: 100%; background: #ffffff">
        <div class="school_check" v-if="!configApp">
            <!-- 表单 -->
            <el-form ref="form" :inline="true" :model="form">
                <el-form-item label="学校名称：">
                    <el-input v-model="form.name" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="学校类型：">
                    <el-select v-model="form.type" clearable placeholder="全部">
                        <el-option label="初中等教育" value="1" />
                        <el-option label="大学" value="2" />
                    </el-select>
                </el-form-item>
                <el-form-item label="区域：">
                    <el-cascader
                        @change="changeArea"
                        v-model="form.areaList"
                        :props="{
                            checkStrictly: true,
                            value: 'name',
                            label: 'name',
                            children: 'area',
                        }"
                        :options="areaList"
                        clearable
                    />
                </el-form-item>

                <el-form-item label="联系电话：">
                    <el-input
                        v-model="form.contactPhone"
                        placeholder="请输入"
                    />
                </el-form-item>
                <el-form-item>
                    <el-button
                        type="primary"
                        @click="handleSearch"
                        icon="el-icon-search"
                        >查询</el-button
                    >
                    <el-button @click="reset">重置</el-button>
                </el-form-item>
            </el-form>
            <!-- 新增 -->
            <div class="btn">
                <span>学校审核</span>
            </div>
            <el-table
                :header-cell-style="{
                    background: '#fafafa',
                    color: '#5b5d61',
                }"
                :data="tableData"
                row-key="id"
                border
                :tree-props="{
                    children: 'children',
                    hasChildren: 'hasChildren',
                }"
                style="min-height: 500px"
            >
                <el-table-column prop="name" label="学校名称">
                </el-table-column>
                <el-table-column prop="contactPhone" label="手机号">
                </el-table-column>
                <el-table-column prop="type" label="学校类型">
                    <template #default="{ row }">
                        {{ typeText[row.type] }}
                    </template>
                </el-table-column>
                <el-table-column prop="area" label="区域"> </el-table-column>
                <el-table-column prop="status" label="审核状态">
                    <template #default="{ row }">
                        {{ statusText[row.status] }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="operation"
                    label="操作"
                    :width="160"
                    align="right"
                    fixed="right"
                >
                    <template #default="{ row }">
                        <el-link
                            type="primary"
                            size="small"
                            style="margin-left: 10px"
                            v-auth="'manage.schoolCheck.schoolDetails'"
                            @click="schoolDetails(row)"
                            >学校详情</el-link
                        >
                        <el-link
                            v-if="row.status != 2 && row.status != 0"
                            type="primary"
                            size="small"
                            style="margin-left: 10px"
                            v-auth="'manage.schoolCheck.immediateReview'"
                            @click="immediateReview(row)"
                            >立即审核</el-link
                        >
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <el-pagination
                background
                class="pagination"
                :current-page="pagination.pageNo"
                :page-sizes="[10, 20, 30, 50]"
                :page-size="pagination.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="pagination.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <SchoolDetails
            v-else
            @handleBack="handleBack"
            :appSchoolId="this.appSchoolId"
            :schoolType="this.schoolType"
        />
        <CheckDialog ref="checkDialogRef" @refurbish="getList" />
    </div>
</template>

<script>
import CheckDialog from "./components/checkDialog.vue";
import SchoolDetails from "./components/checkSchoolDetails.vue";
import { getSchoolCheckList, getAreaList } from "@/api/administrator.js";
export default {
    components: {
        SchoolDetails,
        CheckDialog,
    },
    data() {
        return {
            statusText: {
                0: "拒绝",
                1: "审批中",
                2: "已通过",
            },
            typeText: {
                1: "初中等教育",
                2: "高等教育",
            },
            appSchoolId: "",
            configApp: false,
            tableData: [],
            schoolType: "",
            form: {},
            // 分页
            pagination: {
                pageSize: 10,
                pageNo: 1,
                total: 0,
            },
            areaList: [],
        };
    },
    methods: {
        getList() {
            getSchoolCheckList({ ...this.form, ...this.pagination }).then(
                (res) => {
                    const { total, pageNo, pageSize, list } = res.data;
                    this.tableData = list;
                    this.pagination.total = total;
                    this.pagination.pageNo = pageNo;
                    this.pagination.pageSize = pageSize;
                }
            );
        },
        // 查看学校详情的按钮
        schoolDetails(row) {
            this.appSchoolId = row.id;
            this.schoolType = row.type;
            this.configApp = true;
        },
        changeArea(arr) {
            this.form.area = Array.isArray(arr) ? arr.join("/") : [];
        },
        // 搜素
        handleSearch() {
            this.pagination.pageNo = 1;
            this.getList();
        },
        // 重置
        reset() {
            this.pagination.pageNo = 1;
            this.form = {};
            this.getList();
        },
        immediateReview(data) {
            this.$refs.checkDialogRef.open(data);
        },
        handleBack(val) {
            this.configApp = val;
        },
        // 分页
        handleSizeChange(val) {
            this.pagination.pageNo = 1;
            this.pagination.pageSize = val;
            this.getList();
        },
        handleCurrentChange(val) {
            this.pagination.pageNo = val;
            this.getList();
        },

        removeEmptyChildren(data) {
            data.forEach((item) => {
                if (item.area && item.area.length === 0) {
                    delete item.area;
                } else if (item.area) {
                    this.removeEmptyChildren(item.area);
                }
            });
            return data;
        },

        getAreaListFn() {
            getAreaList().then((res) => {
                this.areaList = this.removeEmptyChildren(res.data);
            });
        },
    },

    created() {
        this.getAreaListFn();
        this.getList();
    },
};
</script>

<style lang="scss" scoped>
.school_check {
    height: 100%;
    padding: 20px;
}
.btn {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    padding-top: 16px;
    border-top: 1px solid #ebeef5;
    span {
        margin: 10px;
    }
    .multipartFile {
        display: flex;
    }
    .download {
        justify-content: center;
        display: flex;
        align-items: center;
        margin: 10px;
        a {
            color: #409eff;
        }
    }
}
.pagination {
    margin-top: 16px;
    text-align: right;
    display: flex;
    justify-content: flex-end;
}
</style>
