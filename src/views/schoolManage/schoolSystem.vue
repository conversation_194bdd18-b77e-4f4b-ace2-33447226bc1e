<template>
    <!-- 学校管理 -->
    <div style="height: 100%">
        <el-card shadow="never" v-if="!configApp">
            <el-form
                ref="searchSchool"
                :model="searchSchool"
                status-icon
                :inline="true"
                size="medium"
                label-width="auto"
                class="demo-ruleForm"
            >
                <el-form-item label="学校名称：" prop="name">
                    <el-input
                        v-model="searchSchool.name"
                        placeholder="请输入学校名称："
                    />
                </el-form-item>
                <el-form-item label="学校类型：" prop="schoolType">
                    <el-select
                        v-model="searchSchool.schoolType"
                        placeholder="请选择学校类型:"
                    >
                        <el-option label="全部" value="" />
                        <el-option label="初中等教育" value="1" />
                        <el-option label="大学" value="2" />
                    </el-select>
                </el-form-item>
                <el-form-item label="省市区：" prop="area">
                    <el-cascader
                        :props="{
                            checkStrictly: true,
                            value: 'label',
                            expandTrigger: 'hover',
                        }"
                        ref="searchCascader"
                        popper-class="cascaderClass"
                        clearable
                        v-model="searchSchool.area"
                        filterable
                        :options="options"
                        @change="handleChange"
                    >
                        <!-- <template slot-scope="{ data }">
                        <span>{{ data.label }}</span>
                    </template> -->
                    </el-cascader>
                </el-form-item>
                <el-form-item label="联系电话：" prop="contactPhone">
                    <el-input
                        v-model="searchSchool.contactPhone"
                        placeholder="请输入联系电话"
                    />
                </el-form-item>
                <el-form-item label="销售负责人：" prop="headSales">
                    <el-input
                        v-model="searchSchool.headSales"
                        placeholder="请输入销售负责人"
                    />
                </el-form-item>
                <el-form-item label="客户负责人：" prop="headClient">
                    <el-input
                        v-model="searchSchool.headClient"
                        placeholder="请输入客户负责人"
                    />
                </el-form-item>
                <el-form-item label="业务类型：" prop="serviceType">
                    <el-select
                        v-model="searchSchool.serviceType"
                        placeholder="请选择业务类型"
                    >
                        <el-option label="演示版" :value="1" />
                        <el-option label="商业版" :value="2" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button
                        type="primary"
                        icon="el-icon-search"
                        @click="searchSchoolSubmitForm('searchSchool')"
                        >查询</el-button
                    >
                    <el-button @click="searchSchoolResetForm('searchSchool')"
                        >重置</el-button
                    >
                </el-form-item>
            </el-form>
            <div v-loading="loading" class="tableBox">
                <YdTable
                    ref="table"
                    :tableColumn="tableColumn"
                    :action="['refresh', 'tableSize', 'tableSet']"
                    :data="loadData"
                    describe="学校列表"
                >
                    <template slot="btn">
                        <el-button
                            v-auth="'manage.schoolSystem.add'"
                            type="primary"
                            icon="el-icon-plus"
                            @click="addSchool"
                            >新增学校</el-button
                        >
                        <el-button
                            v-auth="'manage.schoolSystem.export'"
                            icon="el-icon-bottom"
                            style="margin-left: 16px"
                            @click="exportData"
                            type="primary"
                            plain
                            >导出</el-button
                        >
                    </template>
                    <template slot="schoolName" slot-scope="scope">
                        <div>
                            <span>{{ scope.row.name }} </span>
                            <i
                                v-if="scope.row.name"
                                style="cursor: pointer"
                                @click="handleCopy(scope.row.name)"
                                class="el-icon-copy-document"
                            ></i>
                        </div>
                    </template>
                    <template slot="schoolType" slot-scope="scope">
                        <div>
                            <span>
                                {{
                                    scope.row.schoolType === "1"
                                        ? "K12"
                                        : "高等教育"
                                }}
                            </span>
                        </div>
                    </template>
                    <template slot="area" slot-scope="scope">
                        <div>
                            <span>{{ scope.row.area }} </span>
                            <i
                                v-if="scope.row.area"
                                style="cursor: pointer"
                                @click="handleCopy(scope.row.area)"
                                class="el-icon-copy-document"
                            ></i>
                        </div>
                    </template>
                    <template slot="superAccount" slot-scope="scope">
                        <div>
                            <span>{{ scope.row.superAccount }} </span>
                            <i
                                v-if="scope.row.superAccount"
                                style="cursor: pointer"
                                @click="handleCopy(scope.row.superAccount)"
                                class="el-icon-copy-document"
                            ></i>
                        </div>
                    </template>

                    <template slot="serviceType" slot-scope="scope">
                        <div>
                            <span>{{
                                scope.row.serviceType === 1
                                    ? "演示版"
                                    : "商业版"
                            }}</span>
                        </div>
                    </template>
                    <template slot="isCloudEnable" slot-scope="scope">
                        <div>
                            <span>{{
                                scope.row.isCloudEnable === 1
                                    ? "已启用"
                                    : "已禁用"
                            }}</span>
                        </div>
                    </template>
                    <template slot="isOpenEnable" slot-scope="scope">
                        <div>
                            <span>{{
                                scope.row.isOpenEnable === 1
                                    ? "已启用"
                                    : "已禁用"
                            }}</span>
                        </div>
                    </template>
                    <template slot="openDownTimeStr" slot-scope="scope">
                        <div>
                            <span>{{ scope.row.openDownTimeStr }}</span>
                            <span>无限制</span>
                        </div>
                    </template>
                    <template slot="operation" slot-scope="scope">
                        <el-link
                            type="primary"
                            v-auth="'manage.schoolSystem.edit'"
                            size="small"
                            icon="el-icon-edit"
                            @click="editSchool(scope.row)"
                            >编辑</el-link
                        >
                        <el-link
                            type="primary"
                            size="small"
                            v-auth="'manage.schoolSystem.condition'"
                            style="margin-left: 10px"
                            @click="stateManage(scope.row)"
                            >状态管理</el-link
                        >
                        <el-link
                            type="primary"
                            size="small"
                            style="margin-left: 10px"
                            @click="schoolDetails(scope.row)"
                            >学校详情</el-link
                        >
                        <el-link
                            type="primary"
                            size="small"
                            v-auth="'manage.schoolSystem.apply'"
                            style="margin-left: 10px"
                            @click="configApply(scope.row)"
                            >配置应用</el-link
                        >
                        <el-link
                            type="primary"
                            size="small"
                            v-auth="'manage.schoolSystem.allot'"
                            style="margin-left: 10px"
                            @click="allotDev(scope.row)"
                            >分配设备</el-link
                        >
                        <el-link
                            type="primary"
                            size="small"
                            v-auth="'manage.schoolSystem.reset'"
                            style="margin-left: 10px"
                            @click="resetPassword(scope.row)"
                        >
                            重置密码
                        </el-link>
                        <el-link
                            type="primary"
                            size="small"
                            style="margin-left: 10px"
                            v-auth="'manage.schoolSystem.unlock'"
                            @click="unlocking(scope.row)"
                        >
                            账号解锁
                        </el-link>
                    </template>
                </YdTable>
            </div>
            <!-- <div class="paginationBlock">
            <el-pagination
                style="margin-top: 10px"
                :current-page="listQuery.pageNo"
                :page-sizes="[10, 20, 30, 40]"
                :page-size="listQuery.pageSize"
                background
                layout="total, sizes, prev, pager, next, jumper"
                :total="listQuery.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div> -->
            <NewModifySchool
                :drawer="drawer"
                :drawerTitle="drawerTitle"
                @handleClose="close"
                :schoolRuleForm="schoolInfo"
            />
            <el-dialog
                :title="`${name} - 配置应用`"
                :visible.sync="applyDrawer"
                width="800px"
            >
                <div style="height: 480px; overflow-y: auto" class="applymodel">
                    <el-transfer
                        v-model="selectApplyList"
                        :data="allApplyList"
                        filterable
                        filter-placeholder="请输入应用名称"
                        :props="{
                            key: 'id',
                            label: 'name',
                        }"
                        :titles="['应用列表', '已配置应用']"
                    />
                </div>

                <span slot="footer">
                    <el-button @click="applyDrawer = false">取 消</el-button>
                    <el-button
                        :loading="applyloading"
                        type="primary"
                        :disabled="selectApplyList.length ? false : true"
                        @click="submitApplyConfig"
                        >确 定</el-button
                    >
                </span>
            </el-dialog>
            <el-dialog
                title="业务状态管理"
                center
                :visible.sync="businessStatusDialog"
                width="500px"
                @close="cancel()"
            >
                <el-form
                    ref="statusForm"
                    :model="businessStatusForm"
                    label-width="100px"
                >
                    <el-form-item
                        :rules="[{ required: true, message: '请选择' }]"
                        label="云平台："
                        class="status_from"
                        prop="isCloudEnable"
                    >
                        <el-select
                            v-model="businessStatusForm.isCloudEnable"
                            placeholder="请选择"
                        >
                            <el-option label="启用" :value="1"></el-option>
                            <el-option label="禁用" :value="0"></el-option>
                            <!-- <el-option label="停止于某天" value="2"></el-option> -->
                        </el-select>
                    </el-form-item>
                    <!-- <el-form-item
                        v-if="businessStatusForm.isCloudEnable == 2"
                        prop="classcardData"
                    >
                        <el-date-picker
                            v-model="businessStatusForm.classcardData"
                            type="date"
                            placeholder="选择日期"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                        ></el-date-picker>
                    </el-form-item> -->
                    <!-- <el-form-item
                    :rules="[
                        { required: true, message: '请选择' },
                        ]"
                        label="开放平台："
                        class="status_from"
                        prop="isOpenEnable"
                    >
                        <el-select
                            v-model="businessStatusForm.isOpenEnable"
                            placeholder="请选择"
                        >
                            <el-option label="启用" :value="1"></el-option>
                            <el-option label="禁用" :value="0"></el-option>
                            <el-option label="停止于某天" value="2"></el-option>
                        </el-select>
                    </el-form-item> -->
                    <!-- <el-form-item
                        v-if="businessStatusForm.isOpenEnable == 2"
                        prop="openbackData"
                    >
                        <el-date-picker
                            v-model="businessStatusForm.openbackData"
                            type="date"
                            placeholder="选择日期"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                        ></el-date-picker>
                    </el-form-item> -->
                    <el-form-item class="dialog-footer">
                        <el-button type="primary" @click="submitStatusForm()"
                            >确定</el-button
                        >
                        <el-button @click="cancel()">取消</el-button>
                    </el-form-item>
                </el-form>
            </el-dialog>

            <!-- 这个是分配设备的弹窗 -->
            <el-dialog
                title="分配设备品牌"
                :visible.sync="allotDialogVisible"
                width="800px"
            >
                <div class="allotDialogVisibleBox">
                    <div class="fullSelected">
                        <el-checkbox
                            @change="fullSelectedChange"
                            v-model="fullSelected"
                            >全选</el-checkbox
                        >
                    </div>
                    <div
                        class="devicetypeBox"
                        v-for="(item, index) in equipmentThree"
                    >
                        <!-- <div>{{ item.equipmentName }}：</div> -->
                        <el-checkbox v-model="item.allocated">{{
                            item.equipmentName
                        }}</el-checkbox
                        >：
                        <div
                            style="
                                display: flex;
                                align-items: center;
                                flex-wrap: wrap;
                            "
                        >
                            <div
                                v-for="(it, ix) in item.deviceBrandDTOList"
                                style="margin-right: 15px"
                            >
                                <el-checkbox
                                    v-model="it.allocated"
                                    :disabled="!item.allocated"
                                    >{{ it.brandName }}({{
                                        it.deviceManufacturer
                                    }})</el-checkbox
                                >
                            </div>
                        </div>
                    </div>
                </div>
                <span slot="footer">
                    <el-button @click="allotDialogVisible = false"
                        >取 消</el-button
                    >
                    <el-button type="primary" @click="distribution"
                        >确 定</el-button
                    >
                </span>
            </el-dialog>
        </el-card>
        <component
            v-else
            v-bind:is="this.currentTabComponent"
            @handleBack="handleBack"
            :appSchoolId="this.appSchoolId"
            :schoolType="this.schoolType"
        ></component>
    </div>
</template>

<script>
import { mapGetters } from "vuex";
import NewModifySchool from "@/components/NewModifySchool";
import YdTable from "@/components/YdTable";
import SchoolDetails from "./schoolDetails.vue";
import ApplyList from "./applyList.vue";
import { regionData } from "element-china-area-data";

import {
    applySchoolSave,
    getApplyList,
    openPlatform,
    selectState,
    exportInfo,
    resetAdminPassword,
    brandListBySchool,
    updateAssignedDevice,
    unlockSuperAdmin,
} from "@/api/apply.js";

import {
    getSchoolAppList,
    echoSchoolAppList,
    updateSchoolStatus,
} from "@/api/administrator.js";
// 省市二级联动（不带“全部”选项: provinceAndCityData
// 省市二级联动（带“全部”选项）: provinceAndCityDataPlus
// 省市区三级联动（不带“全部”选项）: regionData
// 省市区三级联动（带“全部”选项): regionDataPlus

function modifyAllocated(treeArray, allocated) {
    // 遍历树形数组的每个元素
    treeArray.forEach(function (node) {
        // 修改当前节点的allocated值
        node.allocated = allocated;
        // 如果当前节点有子节点，则递归调用modifyAllocated函数
        if (node.deviceBrandDTOList && node.deviceBrandDTOList.length > 0) {
            modifyAllocated(node.deviceBrandDTOList, allocated);
        }
    });
}

export default {
    name: "SchoolManagement",
    components: {
        NewModifySchool,
        YdTable,
        SchoolDetails,
        ApplyList,
    },
    // props: {
    //     appSchoolId: {
    //         type: String,
    //         default: "",
    //     },
    //     schoolType: {
    //         type: String,
    //         default: "",
    //     },
    // },
    data() {
        return {
            currentTabComponent: ApplyList,
            loadData: (parameter) => {
                return getSchoolAppList(
                    Object.assign(
                        { ...parameter },
                        {
                            // ...this.listQuery,
                            ...this.searchSchool,
                            area: this.address,
                        }
                    )
                );
            },
            defaultProps: {
                children: "deviceBrandDTOList",
                label: "equipmentName",
            },
            facilitySchoolId: "",
            allotDialogVisible: false,
            equipmentThree: [],
            fullSelected: false,
            configApp: false,
            tableColumn: [
                {
                    label: "序号",
                    type: "index",
                    width: "50px",
                    align: "center",
                    isShow: true,
                },
                {
                    label: "学校名称",
                    showtooltip: true,
                    index: "name",
                    isShow: true,
                    width: "320",
                    scopedSlots: {
                        customRender: "schoolName",
                    },
                },
                {
                    label: "学校编号",
                    index: "schoolId",
                    showtooltip: true,
                    isShow: true,
                    align: "center",
                    width: "180",
                },
                {
                    label: "手机号码",
                    index: "contactPhone",
                    isShow: true,
                    align: "center",
                    width: "150",
                },
                {
                    label: "超级账号",
                    index: "superAccount",
                    isShow: true,
                    isShow: true,
                    align: "center",
                    width: "150",
                    scopedSlots: {
                        customRender: "superAccount",
                    },
                },
                {
                    label: "学校类型",
                    index: "schoolType",
                    isShow: true,
                    isShow: true,
                    align: "center",
                    width: "150",
                    scopedSlots: {
                        customRender: "schoolType",
                    },
                },
                {
                    label: "区域",
                    index: "area",
                    isShow: true,
                    isShow: true,
                    align: "left",
                    width: "180",
                    scopedSlots: {
                        customRender: "area",
                    },
                },
                {
                    label: "校区数量",
                    index: "count",
                    isShow: true,
                    isShow: true,
                    align: "center",
                    width: "80",
                },
                {
                    label: "销售负责人",
                    index: "headSales",
                    isShow: true,
                    isShow: true,
                    align: "center",
                    width: "100",
                },
                {
                    label: "客户负责人",
                    index: "headClient",
                    isShow: true,
                    isShow: true,
                    align: "center",
                    width: "100",
                },
                {
                    label: "业务类型",
                    index: "serviceType",
                    isShow: true,
                    align: "center",
                    isShow: true,
                    width: "100",
                    scopedSlots: {
                        customRender: "serviceType",
                    },
                },
                {
                    label: "一加壹平台状态",
                    index: "isCloudEnable",
                    isShow: false,
                    align: "center",
                    isShow: true,
                    width: "100",
                    scopedSlots: {
                        customRender: "isCloudEnable",
                    },
                },
                {
                    label: "一加壹平台到期日",
                    index: "cloudDeadlineTime",
                    isShow: false,
                    isShow: true,
                    align: "center",
                    width: "100",
                },
                {
                    label: "开放平台状态",
                    index: "isOpenEnable",
                    isShow: false,
                    align: "center",
                    isShow: true,
                    width: "100",
                    scopedSlots: {
                        customRender: "isOpenEnable",
                    },
                },
                {
                    label: "开放平台到期日",
                    index: "openDownTimeStr",
                    isShow: false,
                    align: "center",
                    isShow: true,
                    width: "100",
                    scopedSlots: {
                        customRender: "openDownTimeStr",
                    },
                },
                {
                    label: "操作",
                    index: "operation",
                    isShow: true,
                    align: "left",
                    isShow: true,
                    fixed: "right",
                    width: "450",
                    scopedSlots: {
                        customRender: "operation",
                    },
                },
            ],
            schoolInfo: {},
            businessSchoolId: "",
            businessStatusForm: {
                isCloudEnable: null,
                // isOpenEnable: null,
                // classcardData: "",
                // openbackData: "",
            },
            businessStatusDialog: false,
            loading: false,
            OpenDialogVisible: false,
            applyDrawer: false,
            applyloading: false,
            name: null,
            appSchoolId: "",
            schoolType: "",
            schoolId: null,
            sign: "1",
            selectionApplyList: [],
            allApplyList: [],
            copeAllApplyList: [],
            selectApplyList: [],
            // 省级联动
            options: regionData,
            // 省级联动
            drawerTitle: false,
            drawer: false,
            ruleFormChild: {
                schoolId: "",
                name: "",
                type: "",
                address: "",
                area: "",
                contactPerson: "",
                contactPhone: null,
                logoPic: "",
                headSales: "",
                headClient: "",
                sections: [],
                // status: "Y",
            },
            // 查询
            searchSchool: {
                name: "",
                area: [],
                type: "",
                contactPhone: "",
                headSales: "",
                headClient: "",
                schoolType: "",
                serviceType: "",
            },
            province: "",
            schoolList: [],
            total: 0,
            listQuery: {
                pageNo: 1,
                pageSize: 10,
                total: 0,
            },
            listLoading: false,
            list: [],
            // rules: {
            //     classcardData: [
            //         {
            //             required: true,
            //             message: "请选择日期",
            //             trigger: "change",
            //         },
            //     ],
            //     openbackData: [
            //         {
            //             required: true,
            //             message: "请选择日期",
            //             trigger: "change",
            //         },
            //     ],
            // },
            address: "",
        };
    },

    computed: {
        ...mapGetters(["showWidth", "navBars"]),
    },
    // created() {
    //     this.schoolAppData();
    // },

    methods: {
        // 全选逻辑在这里
        fullSelectedChange(val) {
            console.log("val", val);
            if (val) {
                modifyAllocated(this.equipmentThree, true);
            } else {
                modifyAllocated(this.equipmentThree, false);
            }
        },
        // 分配设备品牌的确认按钮
        distribution() {
            // 使用filter方法筛选出allocated为true的数组对象，并使用map方法返回所需的数据结构
            const filterequipmentThree = this.equipmentThree.filter(
                (item) => item.allocated
            );
            const allocatedTrueObjects = filterequipmentThree.map((obj) => {
                return {
                    deviceBrandDTOList: obj.deviceBrandDTOList.filter(
                        (brand) => brand.allocated === true
                    ),
                    equipmentName: obj.equipmentName,
                    equipmentType: obj.equipmentType,
                };
            });
            // .filter((obj) => {
            //     return obj.deviceBrandDTOList.length > 0;
            // });

            updateAssignedDevice({
                schoolId: this.facilitySchoolId,
                schoolEquipmentTypeList: allocatedTrueObjects,
            }).then((res) => {
                this.allotDialogVisible = false;
                this.$message.success("分配成功");
            });

            console.log(allocatedTrueObjects);
        },
        // 查看学校详情的按钮
        schoolDetails(row) {
            this.currentTabComponent = SchoolDetails;
            console.log("查看学校详情的按钮", row);
            this.appSchoolId = row.schoolId;
            this.schoolType = row.schoolType;
            this.configApp = true;
            // console.log('data', data)
        },
        // 重置密码
        resetPassword(row) {
            this.$confirm("是否确认重置密码？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    resetAdminPassword({ id: row.schoolId }).then((res) => {
                        this.$message.success(res.message);
                    });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消",
                    });
                });
        },
        unlocking(row) {
            this.$confirm("是否确认解锁账号？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    unlockSuperAdmin({ schoolId: row.schoolId }).then((res) => {
                        this.$message.success(res.message);
                    });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消",
                    });
                });
        },
        // 复制
        handleCopy(data) {
            let url = data;
            let oInput = document.createElement("input");
            oInput.value = url;
            document.body.appendChild(oInput);
            oInput.select(); // 选择对象;
            console.log(oInput.value);
            document.execCommand("Copy"); // 执行浏览器复制命令
            this.$message({
                message: "复制成功",
                type: "success",
            });
            oInput.remove();
        },
        // // 分页
        // handleSizeChange(val) {
        //     this.listQuery.pageNo = 1;
        //     this.listQuery.pageSize = val;
        //     this.schoolAppData();
        // },
        // handleCurrentChange(val) {
        //     this.listQuery.pageNo = val;
        //     this.schoolAppData();
        // },
        addSchool() {
            this.drawer = true;
            this.drawerTitle = false;
        },
        editSchool(item) {
            this.drawer = true;
            this.drawerTitle = true;
            echoSchoolAppList({ schoolId: item.schoolId })
                .then((res) => {
                    if (res.code === 0) {
                        this.schoolInfo = res.data;
                    } else {
                        this.$message.error(res.message);
                    }
                })
                .catch((error) => {
                    this.$message.error(error);
                });
        },
        // schoolAppData() {
        //     let obj = {
        //         ...this.listQuery,
        //         ...this.searchSchool,
        //         area: this.address,
        //     };
        //     getSchoolAppList(obj).then((res) => {
        //         const { list, pageNo, pageSize, total } = res.data;
        //         this.schoolList = list;
        //         this.listQuery.pageNo = pageNo;
        //         this.listQuery.pageSize = pageSize;
        //         this.listQuery.total = total;
        //     });
        // },
        exportData() {
            this.area();
            let obj = {
                ...this.searchSchool,
                area: this.address,
            };
            exportInfo(obj, "学校管理列表");
        },
        // 修改平台状态
        editSchoolstateInfo() {
            const obj = {
                schoolId: this.businessSchoolId,
                isCloudEnable: this.businessStatusForm.isCloudEnable,
                // isOpenEnable: this.businessStatusForm.isOpenEnable,
            };
            updateSchoolStatus(obj).then((res) => {
                if (res.code == 0) {
                    this.$message.success(res.message);
                    this.$store.dispatch(
                        "administrator/schoolList",
                        this.listQuery
                    );
                } else {
                    this.$message.error(res.message);
                }
            });
        },
        open() {
            // 如果是启用直接让他启用就行了
            // 如果是禁用的话 就把再次确认弹出来
            if (this.businessStatusForm.isCloudEnable === 1) {
                this.editSchoolstateInfo();
                this.$refs.statusForm.resetFields();
                this.businessStatusDialog = false;
                this.searchSchoolResetForm("searchSchool");
            } else {
                this.$confirm("确定禁用该学校一加壹平台使用权吗", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(() => {
                        this.editSchoolstateInfo();
                        this.$refs.statusForm.resetFields();
                        this.businessStatusDialog = false;
                        this.searchSchoolResetForm("searchSchool");
                    })
                    .catch(() => {
                        console.log("取消");
                    });
            }
        },
        submitStatusForm() {
            this.$refs.statusForm.validate((valid) => {
                if (valid) {
                    this.open();
                } else {
                    return false;
                }
            });
        },
        cancel() {
            this.businessStatusDialog = false;
            this.$refs.statusForm.resetFields();
        },
        getApplyList() {
            this.loading = true;
            const params = {
                pageNo: 1,
                pageSize: 1000,
                schoolId: this.schoolId,
                sign: this.sign,
            };
            getApplyList(params).then(({ code, data, message }) => {
                if (code === 200) {
                    const { records, total, pageSize, pageNo } = data;
                    this.copeAllApplyList = JSON.parse(JSON.stringify(records));
                    this.allApplyList = records;
                    this.loading = false;
                    const selectApplyList = records.filter((i) => {
                        return i.isChecked == true;
                    });

                    this.selectApplyList = selectApplyList.map((i) => i.id);
                } else {
                    this.$message.error(message);
                }
            });
        },

        submitApplyConfig() {
            this.applyloading = true;
            const list = [];
            this.copeAllApplyList.forEach((item) => {
                if (this.selectApplyList.find((j) => item.id === j)) {
                    list.push(item);
                }
            });
            const arr = list.map((i) => {
                return {
                    schoolId: this.schoolId,
                    groupId: i.groupId,
                    applicationId: i.id,
                    name: i.name,
                    groupName: i.groupName,
                    sort: i.sort,
                    type: i.type,
                    img: i.img,
                    num: i.num,
                    path: i.path,
                    groupSort: i.groupSort,
                    introduction: i.introduction,
                    backstagePath: i.backstagePath,
                    showType: i.showType,
                };
            });

            applySchoolSave(arr)
                .then(({ code, msg }) => {
                    if (code === 200) {
                        this.$message.success(msg);
                        this.applyDrawer = false;
                        this.$store.dispatch(
                            "administrator/schoolList",
                            this.listQuery
                        );
                    } else {
                        this.$message.error(msg);
                    }
                })
                .finally(() => {
                    this.applyloading = false;
                });
        },
        selectionChange(list) {
            this.selectionApplyList = list;
        },
        // 配置应用
        configApply(row) {
            console.log("row", row);
            this.currentTabComponent = ApplyList;
            this.appSchoolId = row.schoolId;
            this.schoolType = row.schoolType;
            this.configApp = true;
        },
        // 状态管理
        stateManage(row) {
            this.businessSchoolId = row.schoolId;
            this.businessStatusDialog = true;
        },
        allotDev(row) {
            brandListBySchool({
                schoolId: row.schoolId,
            }).then((res) => {
                this.allotDialogVisible = true;
                this.equipmentThree = res.data.schoolEquipmentTypeList.filter(
                    (item) =>
                        item.deviceBrandDTOList &&
                        item.deviceBrandDTOList.length > 0
                );
                this.facilitySchoolId = res.data.schoolId;
            });
            console.log("分配设备");
        },

        handleChange(value) {
            this.$refs.searchCascader._data.dropDownVisible = false;
        },
        close(state) {
            this.drawer = state;
            this.$refs.table.handleRefresh(true);
            this.drawerTitle = state;
        },
        area() {
            if (Array.isArray(this.searchSchool.area)) {
                this.address = this.searchSchool.area.join("/");
            }
        },
        searchSchoolSubmitForm(ruleForm) {
            this.area();
            this.listQuery.pageNo = 1;
            this.listQuery.pageSize = 10;
            this.$refs.table.handleRefresh(true);
        },
        searchSchoolResetForm(ruleForm) {
            this.$refs[ruleForm].resetFields();
            this.searchSchool.area = [];
            this.address = "";
            this.listQuery.pageNo = 1;
            this.$refs.table.handleRefresh(true);
        },
        deleteRow(index, rows) {
            rows.splice(index, 1);
        },
        handleBack(val) {
            this.configApp = val;
        },
    },
};
</script>

<style lang="scss">
.cascaderClass .el-radio__inner {
    top: -18px;
    left: -19px;
    border-radius: 0;
    border: none !important;
    width: 170px;
    height: 34px;
    background-color: transparent;
    cursor: pointer;
    box-sizing: border-box;
    position: absolute;

    .el-cascader-node__label {
        padding: 0 !important;
    }
}

.cascaderClass .el-cascader-node {
    padding-left: 0 !important;
}

.cascaderClass .el-radio__input.is-checked .el-radio__inner {
    background: transparent;
}

.applymodel {
    .el-transfer {
        height: 100%;
        display: flex;
        align-items: center;
    }

    .el-transfer-panel {
        height: 100% !important;
        flex: 1;

        .el-transfer-panel__body {
            height: 100%;

            .el-checkbox-group {
                height: 370px;
            }
        }
    }
}

.status_from {
    .el-input {
        width: 220px;
    }
}

.text {
    margin: 50px;
    font-size: 16px;
}

.paginationBlock {
    margin-top: 16px;
    text-align: right;
    display: flex;
    justify-content: flex-end;
}

.devicetypeBox {
    display: flex;
    align-items: center;
    padding-bottom: 15px;
}

.fullSelected {
    padding-bottom: 20px;
}

.tableBox ::-webkit-scrollbar {
    width: 10px !important;
    height: 10px !important;
    background-color: #f5f5f5;
}

// .allotDialogVisibleBox {
//     max-height: 600px;
//     overflow-y: scroll;
// }
</style>
