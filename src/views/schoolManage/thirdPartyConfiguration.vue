<template>
    <el-card shadow="never">
        <el-form
            ref="thirdPartyForm"
            :model="queryForm"
            status-icon
            :inline="true"
            size="mini"
            label-width="auto"
            class="demo-ruleForm"
        >
            <el-form-item label="学校名称：" prop="name">
                <el-input
                    v-model="queryForm.name"
                    size="medium"
                    placeholder="请输入学校名称："
                />
            </el-form-item>
            <el-form-item label="接入平台：" prop="appCodeList">
                <el-select
                    multiple
                    v-model="queryForm.appCodeList"
                    size="medium"
                    placeholder="请选择接入平台"
                >
                    <el-option label="企业微信" value="wechat_work" />
                    <el-option label="钉钉" value="dingtalk" />
                    <el-option
                        label="企业微信通讯录"
                        value="wechat_work_address_book"
                    />
                    <el-option label="晓羊" value="xiaoyang" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button
                    size="medium"
                    type="primary"
                    icon="el-icon-search"
                    @click="getTableFn"
                    >查询</el-button
                >
                <el-button size="medium" @click="resetForm('thirdPartyForm')"
                    >重置</el-button
                >
            </el-form-item>
        </el-form>
        <div v-loading="loading">
            <el-table
                :data="tableData"
                border
                :header-cell-style="{
                    background: '#fafafa',
                    color: '#5b5d61',
                }"
            >
                <el-table-column
                    type="index"
                    label="序号"
                    width="50"
                    align="center"
                />
                <el-table-column
                    prop="name"
                    label="学校名称"
                    align="center"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="schoolId"
                    label="学校编号"
                    align="center"
                    show-overflow-tooltip
                >
                </el-table-column>

                <el-table-column
                    prop="superAccount"
                    label="超级账号"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="appCodeNames"
                    label="接入平台"
                    show-overflow-tooltip
                />

                <el-table-column
                    label="操作"
                    align="right"
                    width="180"
                    fixed="right"
                >
                    <template slot-scope="scope">
                        <el-button
                            v-auth="'manage.schoolPortal.edit'"
                            type="text"
                            icon="el-icon-edit"
                            @click="editFormDialog('look', scope.row)"
                            >查看</el-button
                        >
                        <el-button
                            v-auth="'manage.schoolPortal.edit'"
                            type="text"
                            icon="el-icon-edit"
                            @click="editFormDialog('edit', scope.row)"
                            >修改</el-button
                        >
                    </template>
                </el-table-column>
                <el-table-column
                    label="同步通讯录"
                    align="right"
                    width="180"
                    fixed="right"
                >
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            @click="synchronization('department', scope.row)"
                            >部门同步</el-button
                        >
                        <el-button
                            type="text"
                            @click="synchronization('personnel', scope.row)"
                            >人员同步</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
            <div class="paginationBlock">
                <el-pagination
                    :current-page.sync="pagination.current"
                    background
                    :page-size="pagination.pagesize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pagination.total"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                >
                    <span>总共 {{ pagination.total }} 条</span>
                </el-pagination>
            </div>
        </div>
        <!-- 新增、编辑 -->
        <div class="dialog_class">
            <el-dialog
                :title="dialogType === 'look' ? '查看配置' : ' 修改配置'"
                width="600px"
                center
                :visible.sync="dialogVisible"
                @close="cancelFormFn"
            >
                <div class="title_school">{{ schoolName }}</div>

                <div v-for="(item, index) in editList" :key="index">
                    <el-form
                        :rules="rules"
                        ref="formRef"
                        label-position="right"
                        label-width="100px"
                        :model="item"
                    >
                        <el-form-item
                            label="企业微信接入："
                            prop="isWx"
                            label-width="120px"
                        >
                            <el-switch
                                :disabled="dialogType === 'look'"
                                name="wechat_work"
                                active-value="wechat_work"
                                v-model="item.appPlatform"
                            >
                            </el-switch>
                        </el-form-item>
                        <div v-if="item.appPlatform === 'wechat_work'">
                            <el-form-item label="机构标识：" prop="corpId">
                                <el-input
                                    :disabled="dialogType === 'look'"
                                    :maxlength="200"
                                    v-model="item.corpId"
                                    placeholder="请输入机构标识"
                                />
                            </el-form-item>
                            <el-form-item label="身份标识：" prop="appType">
                                <el-select
                                    :disabled="dialogType === 'look'"
                                    filterable
                                    clearable
                                    v-model="item.appType"
                                    placeholder="请选择"
                                >
                                    <el-option
                                        v-for="item in appTypeOptions"
                                        :key="item.id"
                                        :label="item.name"
                                        :value="item.id"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item
                                label="应用列表："
                                prop="appConfigInfos"
                            >
                                <el-checkbox-group
                                    v-model="item.appConfigInfos"
                                    :disabled="dialogType === 'look'"
                                >
                                    <el-checkbox
                                        :label="
                                            item.appConfigInfos[0] || list[0]
                                        "
                                    >
                                        1.云平台
                                    </el-checkbox>
                                    <el-checkbox
                                        :label="
                                            item.appConfigInfos[1] || list[1]
                                        "
                                    >
                                        2.通讯录同步
                                    </el-checkbox>
                                </el-checkbox-group>
                            </el-form-item>
                            <div
                                v-for="(
                                    appItem, appIndex
                                ) in item.appConfigInfos"
                                :key="appIndex"
                                style="
                                    background: #eee;
                                    margin: 10px 0px;
                                    padding: 20px 20px 10px 20px;
                                "
                            >
                                <div class="title_type">
                                    {{
                                        appItem.appCode ===
                                        "wechat_work_address_book"
                                            ? "通讯录同步："
                                            : "云平台："
                                    }}
                                </div>
                                <el-form-item
                                    label="Secret："
                                    prop="clientSecret"
                                >
                                    <el-input
                                        :disabled="dialogType === 'look'"
                                        :maxlength="200"
                                        v-model="appItem.clientSecret"
                                        placeholder="请输入Secret"
                                    />
                                </el-form-item>
                                <el-form-item
                                    label="数据源同步："
                                    prop="syncSource"
                                    v-if="appItem.appCode === 'wechat_work'"
                                >
                                    <el-radio-group
                                        v-model="appItem.syncSource"
                                        :disabled="dialogType === 'look'"
                                    >
                                        <el-radio :label="1">是</el-radio>
                                        <el-radio :label="0">否</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                                <el-form-item
                                    label="AgentId："
                                    prop="client_id"
                                    v-if="appItem.appCode === 'wechat_work'"
                                >
                                    <el-input
                                        :disabled="dialogType === 'look'"
                                        :maxlength="200"
                                        v-model="appItem.clientId"
                                        placeholder="请输入AgentId"
                                    />
                                </el-form-item>
                                <el-form-item
                                    v-if="
                                        appItem.appCode ===
                                        'wechat_work_address_book'
                                    "
                                    label="Token："
                                    prop="token"
                                >
                                    <el-input
                                        :disabled="dialogType === 'look'"
                                        :maxlength="200"
                                        v-model="appItem.configDataJson.token"
                                        placeholder="请输入token"
                                    />
                                </el-form-item>
                                <el-form-item
                                    label-width="150px"
                                    v-if="
                                        appItem.appCode ===
                                        'wechat_work_address_book'
                                    "
                                    label="EncodingAESKey："
                                    prop="encodingAESKey"
                                >
                                    <el-input
                                        :disabled="dialogType === 'look'"
                                        :maxlength="200"
                                        v-model="
                                            appItem.configDataJson
                                                .encodingAESKey
                                        "
                                        placeholder="请输入encodingAESKey"
                                    />
                                </el-form-item>
                            </div>
                        </div>
                    </el-form>
                </div>
                <div shadow="never" class="article_createOrEdit__footer">
                    <div class="btn_warp_bottom">
                        <el-button @click="cancelFormFn">取 消</el-button>
                        <el-button
                            type="primary"
                            v-loading="btnLoading"
                            @click="confirm"
                            >确 定</el-button
                        >
                    </div>
                </div>
            </el-dialog>
        </div>
    </el-card>
</template>

<script>
import {
    configList,
    configInfo,
    editConfig,
    pullPersonnel,
    pullDepartment,
} from "@/api/thirdParty.js";
export default {
    data() {
        return {
            list: [
                {
                    syncSource: 1,
                    appCode: "wechat_work",
                    clientSecret: "",
                    clientId: "",
                },
                {
                    appCode: "wechat_work_address_book",
                    clientSecret: "",
                    configDataJson: {
                        encodingAESKey: "",
                        token: "",
                    },
                },
            ],
            editList: [],
            queryForm: {
                name: "",
                appCodeList: [],
            },
            rules: {
                schoolId: [
                    {
                        required: true,
                        message: "请选择",
                        trigger: "change",
                    },
                ],
            },
            appTypeOptions: [
                {
                    name: "全平台",
                    id: "4",
                },
                {
                    name: "学生端",
                    id: "0",
                },
                {
                    name: "教职工端",
                    id: "1",
                },
                {
                    name: "家长端",
                    id: "2",
                },
            ],
            loading: false,
            pagination: {
                pageSize: 10,
                pageNo: 1,
                total: 0,
            },
            btnLoading: false,
            tableData: [],
            dialogVisible: false,
            schoolName: "",
            dialogType: "edit",
            schoolId: "",
        };
    },
    methods: {
        synchronization(type, { syncAppInfo }) {
            if (type === "personnel") {
                pullPersonnel(syncAppInfo.id, syncAppInfo.appCode).then(
                    (res) => {
                        this.$message.success(res.message || "服务异常");
                    }
                );
            } else if (type === "department") {
                pullDepartment(syncAppInfo.id, syncAppInfo.appCode).then(
                    (res) => {
                        this.$message.success(res.message || "服务异常");
                    }
                );
            }
        },
        getTableFn() {
            this.loading = true;
            let obj = {
                ...this.queryForm,
                ...this.pagination,
            };
            configList(obj)
                .then((res) => {
                    const { list, total } = res.data;
                    this.tableData = list;
                    this.pagination.total = total;
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        resetForm(thirdPartyForm) {
            this.$refs[thirdPartyForm].resetFields();
        },
        confirm() {
            const params = this.editList.map((i) => {
                return {
                    ...i,
                    schoolId: this.schoolId,
                };
            });
            console.log(params);
            this.btnLoading = true;
            editConfig(params)
                .then((res) => {
                    this.dialogVisible = false;
                    this.$message.success(res.message || "服务异常");
                    this.getTableFn();
                })
                .finally(() => {
                    this.btnLoading = false;
                });
        },
        // 分页
        handleCurrentChange(val) {
            this.pagination.pageNo = val;
            this.getTableFn();
        },
        handleSizeChange(val) {
            this.pagination.pageSize = val;
            this.getTableFn();
            this.$message.warning("已取消操作！");
        },
        cancelFormFn() {
            this.dialogVisible = false;
            this.editList = [];
        },
        getConfigInfo(schoolId) {
            configInfo({ schoolId }).then((res) => {
                if (res.data && res.data.length === 0) {
                    this.editList = [
                        {
                            appConfigInfos: [
                                {
                                    syncSource: 1,
                                    appCode: "wechat_work",
                                    clientSecret: "",
                                    clientId: "",
                                },
                                {
                                    appCode: "wechat_work_address_book",
                                    clientSecret: "",
                                    configDataJson: {
                                        encodingAESKey: "",
                                        token: "",
                                    },
                                },
                            ],
                            appPlatform: "",
                            appType: "",
                            corpId: "",
                            schoolId: "",
                        },
                    ];
                } else {
                    this.editList = res.data;
                }
            });
        },
        editFormDialog(type, { schoolId, name }) {
            this.dialogType = type;
            this.schoolName = name;
            this.schoolId = schoolId;
            this.getConfigInfo(schoolId);
            this.dialogVisible = true;
        },
    },
    created() {
        this.getTableFn();
    },
};
</script>

<style lang="scss" scoped>
.paginationBlock {
    margin-top: 16px;
    text-align: right;
}

.title_school {
    font-weight: 600;
    font-size: 18px;
    margin-bottom: 12px;
}
.title_type {
    font-weight: 700;
    font-size: 16px;
    margin-bottom: 12px;
}
</style>

<style lang="scss">
.dialog_class {
    .el-dialog__wrapper {
        z-index: 1100 !important;
    }
    .el-dialog {
        position: relative;
        .el-dialog__body {
            max-height: 570px !important;
            overflow-y: auto !important;
            padding: 25px 25px 80px 25px !important;
        }
    }
    .article_createOrEdit__footer {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        // box-shadow: 0 -1px 2px rgb(0 0 0 / 3%);
        border-top: 1px solid #e8e8e8;
        .btn_warp_bottom {
            padding: 0px 24px;
            border-top: 1px solid #e8e8e8;
            justify-content: right;
        }
        z-index: 9;
        height: 60px;
        line-height: 60px;
        background: #fff;
        text-align: right;
    }
    .el-checkbox-group {
        font-size: 1px !important;
    }
    .el-radio-group {
        font-size: 1px !important;
    }
}
</style>
