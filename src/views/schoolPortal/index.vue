<!--
 * @Descripttion: 
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-11-18 15:03:58
 * @LastEditors: jingrou
 * @LastEditTime: 2023-05-25 09:18:44
-->
<template>
    <div>
        <el-card shadow="never">
            <!-- 查询表单 -->
            <el-form
                ref="queryForm"
                :inline="true"
                :model="queryForm"
                class="demo-form-inline"
                label-width="100px"
            >
                <el-form-item label="学校名称：">
                    <el-select
                        filterable
                        clearable
                        v-model="queryForm.schoolId"
                        placeholder="请选择"
                    >
                        <el-option
                            v-for="item in schoolOptions"
                            :key="item.schoolId"
                            :label="item.name"
                            :value="item.schoolId"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="网站类型：">
                    <el-select
                        filterable
                        clearable
                        v-model="queryForm.type"
                        placeholder="请选择"
                    >
                        <el-option
                            v-for="item in typeOptions"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="网站地址：">
                    <el-input
                        v-model="queryForm.domain"
                        placeholder="请输入网站地址"
                    />
                </el-form-item>
                <el-form-item>
                    <el-button
                        type="primary"
                        style="width: 100px; margin-left: 16px"
                        icon="el-icon-search"
                        @click="queryFn"
                        >查询</el-button
                    >
                    <el-button @click="resetFn">重置</el-button>
                </el-form-item>
            </el-form>
            <!-- 按钮  -->
            <div class="btn">
                <span>校园门户配置</span>
                <div class="multipartFile">
                    <el-button
                        size="medium"
                        icon="el-icon-plus"
                        type="primary"
                        v-auth="'manage.schoolPortal.edit'"
                        @click="operationConfigFn('create')"
                        >新增</el-button
                    >
                </div>
            </div>
            <!-- 表格 -->
            <div v-loading="tableloading">
                <el-table
                    :data="tableData"
                    border
                    :header-cell-style="{
                        background: '#fafafa',
                        color: '#5b5d61',
                    }"
                >
                    <el-table-column
                        type="index"
                        label="序号"
                        width="50"
                        align="center"
                    />
                    <el-table-column
                        prop="schoolName"
                        label="学校名称"
                        align="center"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="type"
                        label="网站类型"
                        align="center"
                        show-overflow-tooltip
                    >
                        <template slot-scope="scope">
                            {{ typeList[scope.row.type] }}
                        </template>
                    </el-table-column>

                    <el-table-column
                        prop="domain"
                        label="网站地址"
                        show-overflow-tooltip
                    />
                    <!-- <el-table-column
                        prop="createBy"
                        label="创建者"
                        align="center"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="createTime"
                        label="创建时间"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="updateBy"
                        label="修改人"
                        show-overflow-tooltip
                    />
                    <el-table-column
                        prop="updateTime"
                        label="修改时间"
                        show-overflow-tooltip
                    /> -->
                    <el-table-column
                        label="操作"
                        align="right"
                        width="180"
                        fixed="right"
                    >
                        <template slot-scope="scope">
                            <el-button
                                v-auth="'manage.schoolPortal.edit'"
                                type="text"
                                icon="el-icon-edit"
                                @click="operationConfigFn('edit', scope.row)"
                                >编辑</el-button
                            >
                            <el-link
                                type="danger"
                                v-auth="'manage.schoolPortal.del'"
                                style="margin-left: 10px"
                                size="small"
                                @click="operationConfigFn('delete', scope.row)"
                            >
                                删除
                                <i class="el-icon-delete" />
                            </el-link>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <!-- 分页 -->
            <div class="paginationBlock">
                <el-pagination
                    :current-page.sync="pagination.current"
                    background
                    :page-size="pagination.pagesize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pagination.total"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                >
                    <span>总共 {{ pagination.total }} 条</span>
                </el-pagination>
            </div>
        </el-card>
        <!-- 新增、编辑 -->
        <el-dialog
            :title="isEdit ? '编辑配置' : '新增配置'"
            width="30%"
            :visible.sync="dialogVisible"
            @close="cancelFormFn"
        >
            <el-form
                :rules="rules"
                ref="formRef"
                label-position="right"
                label-width="110px"
                :model="websiteForm"
            >
                <el-form-item label="学校名称：" prop="schoolId">
                    <el-select
                        filterable
                        clearable
                        v-model="websiteForm.schoolId"
                        placeholder="请选择"
                    >
                        <el-option
                            v-for="item in schoolOptions"
                            :key="item.schoolId"
                            :label="item.name"
                            :value="item.schoolId"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="网站类型：" prop="type">
                    <el-select
                        filterable
                        clearable
                        v-model="websiteForm.type"
                        placeholder="请选择"
                    >
                        <el-option
                            v-for="item in typeOptions"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="网站地址：" prop="domain">
                    <el-input
                        :maxlength="100"
                        show-word-limit
                        v-model="websiteForm.domain"
                        placeholder="请输入网站地址"
                    />
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog_footer">
                <el-button @click="cancelFormFn">取 消</el-button>
                <el-button
                    type="primary"
                    v-loading="btnLoading"
                    @click="formOkFn"
                    >确 定</el-button
                >
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {
    getSchoolAppList,
    getWebsiteList,
    createWebsite,
    getWebsiteInfo,
    updateWebsite,
    deleteWebsite,
    uploadConfigLogo,
} from "@/api/schoolPortal.js";
export default {
    data() {
        var domainValidatePass = (rule, value, callback) => {
            console.log(value, "value");
            var reg = /(http|https):\/\/\S*/;
            if (!reg.test(value)) {
                callback();
            } else {
                callback(new Error("请输入正确的格式"));
            }
        };
        return {
            typeList: ["", "网页端", "移动端"],
            typeOptions: [
                {
                    id: 1,
                    name: "网页端",
                },
                {
                    id: 2,
                    name: "移动端",
                },
            ],
            schoolOptions: [],
            queryForm: {
                schoolId: null,
                type: null,
                domain: "",
            },
            tableloading: false,
            tableData: [],
            pagination: {
                pageSize: 10,
                pageNo: 1,
                total: 0,
            },
            btnLoading: false,
            isEdit: false,
            dialogVisible: false,
            rules: {
                schoolId: [
                    {
                        required: true,
                        message: "请选择",
                        trigger: "change",
                    },
                ],
                type: [
                    {
                        required: true,
                        message: "请选择",
                        trigger: "change",
                    },
                ],
                domain: [
                    {
                        required: true,
                        message: "请输入",
                        trigger: "blue",
                    },
                    { validator: domainValidatePass, trigger: "blur" },
                    // {
                    //     pattern:
                    //         /[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+\.?/,
                    //     message: "请輸入正确的格式",
                    //     trigger: "blur",
                    // },
                ],
            },
            websiteForm: {
                schoolId: null,
                type: null,
                domain: "",
            },
            token: "",
        };
    },
    methods: {
        cancelFormFn() {
            this.$refs.formRef.resetFields();
            this.dialogVisible = false;
        },
        getConfigInfoFn(id) {
            getWebsiteInfo({ id }).then((res) => {
                this.websiteForm = res.data || {};
            });
        },
        getWebsitePageFn() {
            this.tableloading = true;
            let obj = {
                ...this.queryForm,
                pageSize: this.pagination.pageSize,
                pageNo: this.pagination.pageNo,
            };
            getWebsiteList(obj)
                .then((res) => {
                    const { list, total } = res.data;
                    this.tableData = list;
                    this.pagination.total = total;
                })
                .finally(() => {
                    this.tableloading = false;
                });
        },
        operationConfigFn(status, data) {
            this.isEdit = !!(status === "edit");
            if (status !== "delete") {
                this.dialogVisible = true;
                if (this.isEdit) {
                    this.getConfigInfoFn(data.id);
                }
            } else {
                this.$confirm(
                    "如删除此配置后，则不可恢复，是否确认删除？",
                    "提示",
                    {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    }
                )
                    .then(() => {
                        deleteWebsite([data.id]).then((res) => {
                            this.$message.success(res.message);
                            this.getWebsitePageFn();
                        });
                    })
                    .catch(() => {
                        this.$message({
                            type: "info",
                            message: "已取消删除",
                        });
                    });
            }
        },
        formOkFn() {
            this.$refs.formRef.validate((valid) => {
                if (valid) {
                    this.btnLoading = true;
                    if (!this.isEdit) {
                        createWebsite(this.websiteForm)
                            .then((res) => {
                                this.$message.success(res.message);
                                this.cancelFormFn();
                                this.getWebsitePageFn();
                            })
                            .finally(() => {
                                this.btnLoading = false;
                            });
                    } else {
                        updateWebsite(this.websiteForm)
                            .then((res) => {
                                this.$message.success(res.message);
                                this.cancelFormFn();
                                this.getWebsitePageFn();
                            })
                            .finally(() => {
                                this.btnLoading = false;
                            });
                    }
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        // 分页
        handleCurrentChange(val) {
            this.pagination.pageNo = val;
            this.getWebsitePageFn();
        },
        handleSizeChange(val) {
            this.pagination.pageSize = val;
            this.getWebsitePageFn();
        },
        queryFn() {
            this.handleCurrentChange(1);
        },
        resetFn() {
            this.queryForm = {};
            this.handleCurrentChange(1);
        },
        getSchoolOptionsFn() {
            getSchoolAppList({ pageNo: 1, pageSize: 999 }).then((res) => {
                const { list } = res.data;
                this.schoolOptions = list;
            });
        },
    },
    created() {
        this.getSchoolOptionsFn();
        this.getWebsitePageFn();
    },
};
</script>

<style lang="scss" scoped>
.upload_logo {
    :deep(.el-upload) {
        img {
            width: 150px;
            height: 149px;
            border: 1px solid #eee;
        }
        i {
            width: 150px;
            height: 149px;
            border: 1px solid #eee;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}
.btn {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    padding-top: 16px;
    border-top: 1px solid #ebeef5;
    span {
        margin: 10px;
    }
    .multipartFile {
        display: flex;
    }
}
.paginationBlock {
    margin-top: 16px;
    text-align: right;
}
.footerButton {
    width: 100%;
    text-align: center;
    border-top: 1px solid #dcdfe6;
    position: absolute;
    padding-top: 20px;
    bottom: 0;
    margin: 0;
    padding: 20px 0;
}
.download {
    justify-content: center;
    display: flex;
    align-items: center;
    margin: 10px;
    a {
        color: #409eff;
    }
}
</style>
<style lang="scss">
.dialogVisibleForm {
    .formBox {
        display: flex;
        justify-content: space-between;
        .el-input {
            width: 270px;
        }
        .el-textarea__inner {
            width: 270px;
        }
    }
}
.demo-form-inline {
    .queryForm {
        display: block;
        display: flex;
    }
    .el-input {
        width: 160px;
    }
    .el-textarea__inner {
        width: 240px;
    }
    .btnShow {
        display: block;
    }
    .itemShow,
    .itemShow2 {
        display: none;
    }
}
</style>
