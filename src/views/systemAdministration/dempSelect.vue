<!--
 * @Descripttion: 单选多选部门
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-03-17 12:59:11
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2021-11-16 13:32:15
-->

<template>
	<el-dialog
		:title="title"
		:visible.sync="visible"
		width="830px"
		class="select_source"
		append-to-body
	>
		<div slot="title">{{ title }}</div>
		<div class="select_source-body">
			<div class="select_source-body-left">
				<div class="select_source-search">
					<el-input
						class="select_source-search-input"
						placeholder="请输入按回车搜索"
						size="small"
						v-model.trim="filterText"
						@keyup.enter.native="searchTreeNode"
					>
						<!-- <i slot="suffix" class="el-icon-search" @click="searchTreeNode"></i> -->
					</el-input>
					<span
						v-if="searchStatus"
						@click="cancelSearch"
						class="select_source-search—cancel"
						title="取消搜索"
						>取消</span
					>
				</div>
				<!-- 按组织架构 -->
				<div class="select_source-bd">
					<div
						class="select_source-bd-left"
						v-loading="depaLoading"
						element-loading-background="rgba(255, 255, 255, 0.8)"
					>
						<el-scrollbar>
							<el-tree
								ref="departmentTree"
								:data="departmentTree"
								:filter-node-method="filterNode"
								:props="departmentTreeProps"
								@node-click="handleNodeClick"
							>
								<span
									class="custom-tree-node"
									slot-scope="{ data }"
								>
									<span>
										<i
											style="color:#409eff;"
											class="el-icon-folder-opened"
											title="搜索"
										></i>
										{{ data.deptName }}
									</span>
								</span>
							</el-tree>
						</el-scrollbar>
					</div>
					<div class="select_source-bd-right">
						<el-scrollbar>
							<ul>
								<li v-if="multiple">
									<label class="select_item">
										<span class="select_item_text"
											>全选</span
										>
										<el-checkbox
											:indeterminate="
												selectAllDepartment.indeterminate
											"
											v-model="selectAllDepartment.check"
											@change="
												handleCheckselectAllDepartment
											"
											:disabled="
												selectAllDepartment.disabled
											"
										></el-checkbox>
									</label>
								</li>
								<li
									v-for="(item,
									index) in currentDepartmentChildList"
									:key="`curchild_${index}`"
								>
									<label class="select_item">
										<i
											class="el-icon-folder-opened select_item_icon show_icon"
										></i>
										<span class="select_item_text">
											{{ item.deptName }}
											<span
												style="font-size: 12px;color: #eee;float: right;padding-right: 10px;"
												v-if="item.status == '1'"
												>已停用</span
											>
										</span>

										<el-checkbox
											v-if="multiple"
											v-model="item._check"
											:disabled="item._disabled"
										></el-checkbox>
										<el-radio
											v-else
											v-model="singleItem"
											:label="item.id"
											:disabled="item._disabled"
											@change="
												v => changeSingleItem(v, item)
											"
										>
											<span />
										</el-radio>
									</label>
								</li>
							</ul>
							<div
								class="nodata"
								v-if="!currentDepartmentChildList.length"
							>
								<span class="description">暂无数据</span>
							</div>
						</el-scrollbar>
					</div>
				</div>
			</div>
			<div class="select_source-body-right">
				<div class="emptybox">
					<span class="text">已选{{ selestList.length }}个部门</span>
					<span
						class="hint_text"
						@click="handleEmptySelestList"
						v-show="selestList.length"
						>清空</span
					>
				</div>
				<div class="select_source_list">
					<el-scrollbar>
						<ul>
							<li
								v-for="(item, index) in selestList"
								:key="`select_${index}`"
							>
								<label class="select_item">
									<i
										class="el-icon-folder-opened select_item_icon show_icon"
									></i>
									<span class="select_item_text">{{
										item._label
									}}</span>
									<i
										class="el-icon-close select_item_icon del_icon"
										title="删除"
										@click="delSelectItem(item, index)"
									></i>
								</label>
							</li>
						</ul>
					</el-scrollbar>
				</div>
			</div>
		</div>
		<span slot="footer">
			<el-button @click="cancel">取 消</el-button>
			<el-button
				@click="ok"
				type="primary"
				:disabled="selestList.length ? false : true"
				>确 定</el-button
			>
		</span>
	</el-dialog>
</template>

<script>
import { getDepartmentList } from "@/api/systemManagement.js";
export default {
	props: {
		title: {
			type: String,
			default: "请选择部门"
		},
		multiple: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			visible: false,
			selestList: [],
			searchStatus: false,
			filterText: "",
			singleItem: "",

			//
			depaLoading: false,
			departmentTree: [
				{
					showName: "唐学校55",
					id: "1432218110536548354",
					list: [
						{
							showName: "物理组",
							id: "1432219881837264897",
							list: []
						},
						{
							showName: "美术组",
							id: "1432219881740795905",
							list: [
								{
									showName: "油画",
									id: "1432219881837222397",
									list: []
								}
							]
						}
					]
				}
			],
			departmentTreeProps: {
				children: "children",
				label: "deptName"
			},
			// 当前部门全选
			selectAllDepartment: {
				indeterminate: false,
				check: false,
				disabled: false
			},
			// 当前部门child列表
			currentDepartmentChildList: [],
			//
			// 当前部门child列表
			currentClassGradeChildList: []
		};
	},
	created() {},
	watch: {
		// 选择组织架构
		currentDepartmentChildList: {
			handler(v) {
				if (this.multiple) {
					// 多选
					this.selestMultipleList(v);
				} else {
					// 单选
					console.log("单选:", v, this.singleItem);
				}
			},
			deep: true
		}
	},
	methods: {
		getDepartmentList(params) {
			this.depaLoading = true;
			getDepartmentList(params)
				.then(res => {
					if (res.code == 200) {
						this.departmentTree = res.data;
					}
				})
				.finally(() => {
					this.depaLoading = false;
				});
		},
		// 清空
		handleEmptySelestList() {
			this.selestList = [];
			this.singleItem = "";
			this.currentDepartmentChildList.forEach(item => {
				item._check = false;
			});
		},
		//取消
		cancel() {
			this.$emit("cancel");
			this.visible = false;
		},
		// 确定
		ok() {
			console.log(this.selestList);
			this.$emit("ok", this.selestList);
		},
		// 部门单选
		changeSingleItem(v, item) {
			console.log(v, item);
			this.singleItem = v;
			this.selestList = [
				{
					...item,
					_id: item.deptId,
					_label: item.deptName
				}
			];
		},
		// 部门多选
		selestMultipleList(v) {
			v.forEach(i => {
				const index = this.selestList.findIndex(c => c._id == i.deptId);
				// 如果选中

				if (i._check == true) {
					// 如果已选择没有当前的，则添加
					if (index == -1) {
						this.selestList.push({
							...i,
							_id: i.deptId,
							_label: i.deptName
						});
					}
				} else {
					// 如果已选择的存在当前的，则删除
					if (index != -1) {
						this.selestList.splice(index, 1);
					}
				}
			});
			// 处理全选
			if (this.selestList.length) {
				const curCheck = v.filter(i => i._check == true);

				if (curCheck.length) {
					this.selectAllDepartment.indeterminate =
						curCheck.length === v.length ? false : true;
					this.selectAllDepartment.check =
						curCheck.length === v.length ? true : false;
				} else {
					this.resetSelectAll();
				}
			} else {
				this.resetSelectAll();
			}
			this.selectAllDepartment.disabled = v.length ? false : true;
		},
		// 全选部门
		handleCheckselectAllDepartment(val) {
			if (this.currentDepartmentChildList.length) {
				this.currentDepartmentChildList.forEach(i => {
					i._check = val;
				});
			}
		},
		//重置全选状态
		resetSelectAll() {
			this.selectAllDepartment.indeterminate = false;
			this.selectAllDepartment.check = false;
			this.selectAllDepartment.disabled = false;
		},
		//删除选中的
		delSelectItem(item, index) {
			const existIndex = this.currentDepartmentChildList.findIndex(
				c => c.id == item._id
			);

			if (existIndex != -1) {
				this.currentDepartmentChildList[existIndex]._check = false;
			}
			this.selestList.splice(index, 1);
			// 如果是单选
			if (!this.multiple) {
				this.singleItem = "";
			}
		},

		cancelSearch() {
			this.filterText = "";
			this.searchTreeNode();
		},
		// 搜索节点
		searchTreeNode() {
			this.searchStatus = this.filterText ? true : false;
			this.$refs["departmentTree"].filter(this.filterText);
		},
		filterNode(value, data) {
			if (!value) return true;
			return data.deptName.indexOf(value) !== -1;
		},
		handleNodeClick(data) {
			if (data.children && !data.children.length) {
				const index = this.selestList.findIndex(
						c => c._id == data.deptId
					),
					arr = [
						{
							...data,
							_check: index > -1 ? true : false,
							_disabled: data.status == "1" ? true : false,
							_source: "department",
							id: data.deptId
						}
					];

				this.currentDepartmentChildList = arr;
				console.log(arr);
				return;
			}

			if (data.children && data.children.length) {
				let arr = [];

				arr = data.children.map(item => {
					const index = this.selestList.findIndex(
						c => c._id == item.deptId
					);

					return {
						...item,
						_check: index > -1 ? true : false,
						_disabled: item.status == "1" ? true : false,
						_source: "department",
						id: item.deptId
					};
				});
				this.currentDepartmentChildList = arr;
			} else {
				this.currentDepartmentChildList = [];
			}

			console.log(this.currentDepartmentChildList);
		}
	}
};
</script>
<style lang="scss">
.select_source {
	.el-dialog__header {
		border-bottom: 1px solid #e9eaec;
		// padding: 14px 16px 0 16px;
		// padding: 14px 16px;
		line-height: 1;
	}
	.el-dialog__body {
		padding: 16px;
		font-size: 12px;
		line-height: 1.5;
	}
	.el-dialog__footer {
		border-top: 1px solid #e9eaec;
		padding: 12px 18px;
		text-align: right;
	}
	.el-input__icon {
		cursor: pointer;
	}
	.el-scrollbar {
		height: 100%;
	}
	.el-scrollbar__wrap {
		overflow-x: hidden;
		position: relative;
	}

	.is-current > .el-tree-node__content {
		background-color: #f0faff;
		color: #409eff;
	}
	.el-tree-node:focus > .el-tree-node__content {
		background-color: #f0faff;
	}
	.el-tree-node__content:hover {
		background-color: #f0faff;
		color: #409eff;
	}
	.el-tree-node__content {
		height: 30px;
		font-size: 14px;
	}
	.el-tabs__header {
		margin: 0 0 0;
	}
	.el-tabs--card > .el-tabs__header {
		border-bottom: none;
	}
}
</style>
<style lang="scss" scoped>
.select_source {
	font-size: 16px;
	//
	.select_source-body {
		display: flex;
	}
	.select_source-body-left {
		flex: 1;
		border: 1px solid #dddee1;
	}
	.select_source-body-right {
		width: 250px;
		height: 600px;
		border: 1px solid #dddee1;
		margin-left: 20px;
		.emptybox {
			padding: 20px;
			font-size: 14px;
			.hint_text {
				float: right;
				cursor: pointer;
				color: #409eff;
			}
		}
	}
	.select_item {
		display: flex;
		align-items: center;
		padding: 5px 20px;
		transition: all 0.25s;
		font-size: 14px;
		&:hover {
			background-color: #f0faff;
		}
		.show_icon {
			color: #409eff;
		}
		.select_item_icon {
			font-size: 16px;
		}
		.del_icon:hover {
			color: #ff4d4f;
		}
		.select_item_text {
			width: 180px;
			flex: 1;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
	}
}
</style>
<style lang="scss" scoped>
.select_source {
	font-size: 16px;
	//
	.select_source-body {
		display: flex;
	}
	.del_icon {
		cursor: pointer;
	}
}
.select_source-search {
	padding: 20px;
	border-bottom: 1px solid #dddee1;
	display: flex;
	align-items: center;
	.select_source-search—input {
		flex: 1;
	}
	.select_source-search—cancel {
		width: 60px;
		margin-left: 10px;
		padding: 4px 4px;
		cursor: pointer;
		letter-spacing: 4px;
		transition: all 0.25s;
		&:hover {
			color: #409eff;
		}
	}
}
.select_source-bd {
	display: flex;
	box-sizing: border-box;
	.select_source-bd-left {
		box-sizing: border-box;
		flex: 1;
		height: 526px;
	}
	.select_source-bd-right {
		flex: 1;
		height: 526px;
		box-sizing: border-box;
		border-left: 1px solid #dcdee2;
		.select_item {
			cursor: pointer;
		}
	}
	.nodata {
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate3d(-25%, -25%, 0);
	}
}
.select_source_list {
	height: 543px;
}
</style>
