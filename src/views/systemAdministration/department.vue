<!--
 * @Descripttion: 部门管理
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-09-30 11:34:27
 * @LastEditors: jingrou
 * @LastEditTime: 2021-12-02 15:37:35
-->
<template>
	<div>
		<Platform
			v-model="currentPlatform"
			:data="platformList"
			@change="platformChang"
		/>
		<el-card shadow="never">
			<!--  -->
			<div class="department">
				<div class="department_tree">
					<el-input
						class="department_tree_search"
						v-model.trim="filterText"
						@keyup.enter.native="searchTreeNode"
						suffix-icon="el-icon-search"
						placeholder="请输入部门名称"
					></el-input>
					<el-tree
						ref="tree"
						:filter-node-method="filterNode"
						:data="platformDempList"
						node-key="deptId"
						default-expand-all
						:props="departmentTreeProps"
						@node-click="dempChange"
						:expand-on-click-node="false"
					>
						<span
							class="custom-tree-node"
							slot-scope="{ node, data }"
						>
							<span>{{ data.deptName }}</span>
							<el-dropdown
								@command="
									(command) =>
										handleTreeCommand(command, node, data)
								"
							>
								<span
									style="
										padding: 10px;
										color: #409eff;
										z-index: 99;
									"
								>
									<i class="el-icon-more"></i>
								</span>
								<el-dropdown-menu slot="dropdown">
									<el-dropdown-item
										command="add"
										icon="el-icon-plus"
										:disabled="
											data.status == '1' ? true : false
										"
										>添加</el-dropdown-item
									>
									<el-dropdown-item
										command="edit"
										icon="el-icon-edit"
										>编辑</el-dropdown-item
									>
									<el-dropdown-item
										command="del"
										icon="el-icon-delete"
										>删除</el-dropdown-item
									>
								</el-dropdown-menu>
							</el-dropdown>
						</span>
					</el-tree>
					<div
						v-if="!platformDempList.length"
						style="text-align: center"
					>
						<el-link
							icon="el-icon-plus"
							type="primary"
							@click="handleAddEditOpen(true)"
							>添加</el-link
						>
					</div>
				</div>
				<!--  -->
				<div style="flex: 1; margin-left: 16px">
					<YdTable
						ref="table"
						:tableColumn="tableColumn"
						:data="loadData"
						describe="部门成员"
					>
						<!-- search -->
						<template slot="search" slot-scope="scope">
							<el-form
								:inline="true"
								:model="queryParam"
								size="medium"
							>
								<el-form-item label="成员名称">
									<el-input
										v-model="queryParam.userName"
										placeholder="请输入成员名称"
									></el-input>
								</el-form-item>
								<el-form-item label="手机号">
									<el-input
										v-model="queryParam.phonenumber"
										placeholder="请输入手机号"
									></el-input>
								</el-form-item>
								<el-form-item>
									<el-button
										type="primary"
										:loading="scope.loading"
										@click="scope.handleSearch"
										icon="el-icon-search"
										>查询</el-button
									>
									<el-button
										size="medium"
										@click="searchResetForm"
										>重置</el-button
									>
								</el-form-item>
							</el-form>
						</template>
						<!-- Action -->
						<template slot="btn">
							<!-- <el-button size="medium" icon="el-icon-plus" type="primary">添加成员</el-button>
							<el-button class="btn_plain_danger" size="medium" plain type="danger" disabled @click="handleRemove()">
								删 除
								<i class="el-icon-delete" />
							</el-button>-->
						</template>
						<!-- table  -->
						<div
							slot="expand"
							slot-scope="scope"
							style="padding: 16px 16px"
						>
							{{ scope.row }}
						</div>
						<!-- <span slot="action" slot-scope="scope"> -->
						<!-- <el-popover placement="top" width="160" v-model="removeVisible">
								<p style="padding: 3px 5px 10px;">确定删除吗？</p>
								<div style="text-align: right; margin: 0">
									<el-button size="mini" type="text" @click="removeVisible = false">取消</el-button>
									<el-button type="primary" size="mini" @click="handleRemove(scope.row)">确定</el-button>
								</div>
								<el-link slot="reference" style="margin-left:10px" type="danger">
									删除
									<i class="el-icon-delete" />
								</el-link>
						</el-popover>-->
						<!-- </span> -->
					</YdTable>
					<!-- add，edit -->
					<el-dialog
						:title="dialog.status ? '新增部门' : '编辑部门'"
						:visible.sync="dialog.visible"
						:width="dialog.width"
					>
						<div>
							<el-form
								:model="form"
								:rules="rules"
								ref="form"
								label-width="120px"
							>
								<el-form-item label="上级部门：">
									<el-select
										placeholder="请选择"
										v-model="form.parentId"
									>
										<el-option
											label="无"
											:value="0"
										></el-option>
										<el-option
											v-for="item in departmentList"
											:key="item.deptId"
											:label="item.deptName"
											:value="item.deptId"
										></el-option>
									</el-select>
								</el-form-item>
								<el-form-item
									label="部门名称："
									prop="deptName"
								>
									<el-input
										v-model="form.deptName"
										placeholder="请输入部门名称"
										show-word-limit
										maxlength="10"
									></el-input>
								</el-form-item>
								<el-form-item label="部门负责人：">
									<el-input
										v-model="form.leader"
										placeholder="请选择负责人"
										show-word-limit
										maxlength="10"
									></el-input>
								</el-form-item>
								<el-form-item label="手机号:" prop="phone">
									<el-input
										v-model="form.phone"
										placeholder="请输入手机号"
									></el-input>
								</el-form-item>
								<el-form-item label="邮箱:" prop="email">
									<el-input
										v-model="form.email"
										placeholder="请输入邮箱"
										show-word-limit
									></el-input>
								</el-form-item>
								<el-form-item label="排序值" prop="orderNum">
									<el-input-number
										v-model="form.orderNum"
										controls-position="right"
										:min="1"
									></el-input-number>
								</el-form-item>
								<el-form-item label="状态:" prop="status">
									<el-select
										v-model="form.status"
										placeholder="请选择状态"
									>
										<el-option
											label="正常"
											value="0"
										></el-option>
										<el-option
											label="停用"
											value="1"
										></el-option>
									</el-select>
								</el-form-item>
							</el-form>
						</div>
						<span slot="footer">
							<el-button @click="handleAddEdlitCancel"
								>取 消</el-button
							>
							<el-button
								:loading="dialog.btnSubmitLoading"
								type="primary"
								@click="handleAddEdlitSubmit"
								>确 定</el-button
							>
						</span>
					</el-dialog>
				</div>
			</div>
		</el-card>
	</div>
</template>

<script>
import YdTable from "@/components/YdTable";
import Platform from "./platform";
import {
	getDepartmentList,
	getUserList,
	createDepartment,
	getDepartmentInfo,
	updateDepartment,
	removeDepartment,
} from "@/api/systemManagement.js";
import mixins from "./mixins";
export default {
	mixins: [mixins],
	components: {
		YdTable,
		Platform,
	},
	data() {
		const _this = this;

		return {
			filterText: "",
			//
			tableColumn: [
				// {
				// 	label: "#",
				// 	width: 55,
				// 	type: "selection",
				// 	align: "center",
				// 	isShow: true,
				// },
				{
					label: "序号",
					type: "index",
					align: "center",
					width: "60px",
					isShow: true,
				},
				{
					label: "用户名称",
					index: "userName",
					isShow: true,
				},
				{
					label: "姓名",
					index: "nickName",
					isShow: true,
				},
				{
					label: "手机号",
					index: "phonenumber",
					isShow: true,
				},
				{
					label: "邮箱",
					index: "email",
					isShow: false,
				},
				{
					label: "创建时间",
					isShow: true,
					index: "createTime",
				},
				{
					label: "角色",
					isShow: true,
					formatter: (row) => {
						const roles = row.roles.map((i) => i.roleName);

						return roles.join(",");
					},
				},
				// {
				// 	label: "操作",
				// 	isShow: true,
				// 	align: "right",
				// 	scopedSlots: { customRender: 'action' },
				// }
			],
			removeVisible: false,
			queryParam: {
				userName: "",
				phonenumber: "",
			},
			loadData: (parameter) => {
				return getUserList(
					Object.assign(
						{
							...parameter,
							platform: this.currentPlatform,
							deptId: this.deptId,
						},
						this.queryParam
					)
				);
			},
			departmentTreeProps: {
				children: "children",
				label: "deptName",
			},
			//
			dialog: {
				status: true,
				visible: false,
				width: "580px",
				loading: false,
				btnSubmitLoading: false,
			},
			//
			form: {
				parentId: 0,
				deptName: "",
				leader: "",
				phone: "",
				email: "",
				orderNum: "",
				status: "0",
			},
			rules: {
				deptName: [
					{ required: true, message: "请输入", trigger: "blur" },
				],
				nickName: [
					{ required: true, message: "请输入昵称", trigger: "blur" },
				],
				phone: [
					{
						pattern: /^1[3-9]\d{9}$/,
						message: "请输入正确的手机号",
						trigger: "blur",
					},
				],
				password: [
					{ required: true, message: "请输入密码", trigger: "blur" },
				],
				email: [
					{
						pattern:
							/^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/,
						message: "请输入正确的邮箱",
						trigger: "blur",
					},
				],
			},
		};
	},
	created() {},
	methods: {
		searchResetForm() {
			(this.queryParam.userName = ""), (this.queryParam.phonenumber = "");
			this.$refs.table.handleRefresh();
		},
		searchTreeNode() {
			this.$refs.tree.filter(this.filterText);
		},
		filterNode(value, data) {
			if (!value) return true;
			return data.deptName.indexOf(value) !== -1;
		},
		dempChange(data) {
			this.deptId = data.deptId;
			this.$refs.table.handleRefresh();
		},
		// tree
		handleTreeCommand(command, node, data, e) {
			console.log(command, node, data);
			switch (command) {
				case "add":
					this.handleAddEditOpen(true, data);
					break;
				case "edit":
					this.handleAddEditOpen(false, data);
					break;
				case "del":
					this.$confirm("确定删除该部门吗, 是否继续?", "提示", {
						confirmButtonText: "确定",
						cancelButtonText: "取消",
						type: "warning",
					})
						.then(() => {
							removeDepartment(data.deptId).then((res) => {
								this.$message({
									type: "success",
									message: "删除成功!",
								});
								this.getPlatformDempList();
							});
						})
						.catch(() => {});
					break;
				default:
					break;
			}
		},

		// 查询
		handleSearch() {
			//
		},
		// 取消
		handleAddEdlitCancel() {
			this.dialog.visible = false;
			this.$refs.form.resetFields();
			this.form.deptName = "";
			this.form.leader = "";
			this.form.phone = "";
			this.form.email = "";
			this.form.orderNum = "";
		},
		// 确定
		handleAddEdlitSubmit() {
			this.$refs.form.validate((valid) => {
				if (valid) {
					this.dialog.btnSubmitLoading = true;

					const {
							form,
							currentPlatform,
							dialog: { status },
							deptId,
						} = this,
						params = { ...form, platform: currentPlatform };

					if (status) {
						// 新竲
						createDepartment(params)
							.then(({ msg }) => {
								this.$message.success(msg);
								this.handleAddEdlitCancel();
								this.getPlatformDempList();
								this.$refs.table.handleRefresh();
							})
							.finally(() => {
								this.dialog.btnSubmitLoading = false;
							});
					} else {
						// 编辑
						updateDepartment({
							...params,
							deptId,
						})
							.then(({ msg }) => {
								this.$message.success(msg);
								this.handleAddEdlitCancel();
								this.getPlatformDempList();
								this.$refs.table.handleRefresh();
							})
							.finally(() => {
								this.dialog.btnSubmitLoading = false;
							});
					}
				} else {
					return false;
				}
			});
		},
		// 添加,编辑
		handleAddEditOpen(isEdit, row) {
			this.dialog.visible = true;
			this.dialog.status = isEdit;
			this.$nextTick(() => {
				this.$refs.form.resetFields();
				this.form.deptName = "";
				this.form.leader = "";
				this.form.phone = "";
				this.form.email = "";
				this.form.orderNum = "";
			});
			getDepartmentInfo({ deptId: row.deptId }).then(({ data }) => {
				this.form.parentId = data.deptId;
			});

			if (!isEdit) {
				this.deptId = row.deptId;
				getDepartmentInfo({ deptId: row.deptId }).then(({ data }) => {
					const {
						parentId,
						deptName,
						leader,
						phone,
						email,
						orderNum,
						status,
					} = data;

					this.form = {
						parentId,
						deptName,
						leader,
						phone,
						email,
						orderNum,
						status,
					};
				});
			}
		},
		// 删除
		handleRemove() {},
		// 平台切换
		platformChang() {
			this.departmentList = [];
			this.getPlatformDempList(() => {
				this.$refs.table.handleRefresh();
			});
			this.form.parentId = 0;
		},
	},
};
</script>

<style lang="scss" scoped>
.department {
	margin-top: 16px;
	display: flex;
	.department_tree {
		padding-right: 16px;
		border-right: 1px solid #ebeef5;
		min-width: 256px;
		.department_tree_search {
			margin-bottom: 22px;
		}
		::v-deep .el-tree-node__content {
			padding: 20px 0;
		}
		::v-deep .is-current .el-tree-node__content {
			background-color: #f5f7fa;
		}
	}
}
</style>
