<!--
 * @Descripttion: 字典类型中的查看列表
 * @version: 1.0.0
 * @Author:jingrou
 * @Date: 2021-09-30 11:34:27
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2021-10-25 09:46:16
-->
<template>
	<el-card shadow="never">
		<!-- 查询 -->
		<el-form ref="ruleForm" :model="searchform" :inline="true" label-width="100px">
			<div style="display: flex; margin-top: 40px; margin-left: 20px">
				<el-form-item label="字典类型:">
					<el-select v-model="searchform.dictType" placeholder="请选择字典类型">
						<el-option v-for="item in TypeData" :key="item.dictType" :label="item.dictName" :value="item.dictType" />
					</el-select>
				</el-form-item>
				<el-form-item label="字典名称:">
					<el-input v-model="searchform.dectLabel" placeholder="请输入字典名称" />
				</el-form-item>
				<el-form-item label="字典状态:">
					<el-select v-model="searchform.status" placeholder="请选择字典状态">
						<el-option label="正常" value="0" />
						<el-option label="停用" value="1" />
					</el-select>
				</el-form-item>
				<el-form-item style="margin-left: 30px">
					<el-button type="primary" @click="searchDictList">查询</el-button>
					<el-button @click="resetForm('ruleForm')">重置</el-button>
				</el-form-item>
			</div>
		</el-form>
		<!-- 添加按钮 -->
		<el-button type="primary" style="margin-left: 42px; margin-bottom: 30px" icon="el-icon-plus" @click="addDict">添加字典</el-button>
		<!-- 表格列表 -->
		<template>
			<div style="width: 97%; margin: auto">
				<el-table :data="tableData" style="width: 100%" border max-height="600" :header-cell-style="{
						background: '#f5f7fa',
						color: '#5b5d61',
					}">
					<el-table-column prop="createBy" label="创建人" align="center" />
					<el-table-column prop="dictLabel" label="字典标签" align="center" />
					<el-table-column prop="dictSort" label="字典排序" align="center" />
					<el-table-column prop="dictType" label="字典类型" align="center"></el-table-column>
					<el-table-column prop="dictValue" label="字典键值" align="center" width="140px" />
					<el-table-column prop="isDefault" label="是否默认" align="center">
						<template slot-scope="scope">
							<div>
								{{
								scope.row.isDefault == "Y"
								? "是"
								: scope.row.isDefault == "N"
								? "否"
								: ""
								}}
							</div>
						</template>
					</el-table-column>
					<el-table-column prop="status" label="状态" align="center">
						<template slot-scope="scope">
							<div>
								{{
								scope.row.status == "0"
								? "正常"
								: scope.row.status == "1"
								? "停用"
								: ""
								}}
							</div>
						</template>
					</el-table-column>
					<el-table-column prop="remark" label="备注" align="center" />
					<el-table-column prop="status" label="操作" align="center" width="140px">
						<template slot-scope="scope">
							<el-button type="text" size="small" @click="editFrom(scope.row)">修改</el-button>
							<el-button type="text" size="small" style="margin-left: 10px" @click="delData(scope.row)">删除</el-button>
							<el-button type="text" style="margin-left: 10px" @click="addDict">添加字典</el-button>
						</template>
					</el-table-column>
				</el-table>
			</div>
		</template>
		<!-- 新增编辑 -->
		<el-drawer title="用户管理" :visible.sync="dictFromDialog" direction="rtl">
			<el-form ref="ruleForm" :model="dictFromData" :rules="rules" label-width="100px" class="demo-ruleForm">
				<el-form-item label="字典排序:" prop="dictSort">
					<el-input v-model="dictFromData.dictSort" placeholder="请输入字典排序" show-word-limit maxlength="10" />
				</el-form-item>
				<el-form-item label="字典标签:" prop="dictLabel">
					<el-input v-model="dictFromData.dictLabel" placeholder="请输入字典标签" show-word-limit maxlength="10" />
				</el-form-item>
				<el-form-item label="字典键值:" prop="dictValue">
					<el-input v-model="dictFromData.dictValue" placeholder="请输入字典键值" />
				</el-form-item>
				<el-form-item label="是否默认:" prop="isDefault">
					<el-select v-model="dictFromData.isDefault" placeholder="请选择">
						<el-option label="是" value="Y" />
						<el-option label="否" value="N" />
					</el-select>
				</el-form-item>
				<el-form-item label="字典类型" prop="dictType">
					<el-select v-model="dictFromData.dictType" placeholder="请选择">
						<el-option v-for="item in TypeData" :key="item.dictType" :label="item.dictName" :value="item.dictType" />
					</el-select>
				</el-form-item>
				<el-form-item label="状态:" prop="status">
					<el-select v-model="dictFromData.status" placeholder="请选择状态">
						<el-option label="正常" value="0" />
						<el-option label="停用" value="1" />
					</el-select>
				</el-form-item>

				<el-form-item label="备注:">
					<el-input v-model="dictFromData.remark" placeholder="请输入备注" show-word-limit />
				</el-form-item>
				<div class="footerButton">
					<el-button type="primary" @click="submitForm('ruleForm')">确定</el-button>
					<el-button @click="dictFromDialog = false">取消</el-button>
				</div>
			</el-form>
		</el-drawer>
	</el-card>
</template>

<script>
import {
	selectDictList,
	addDictList,
	getTypeList,
	echoDictList,
	editDictList,
	removeDictList,
} from "@/api/systemAdministration.js";
export default {
	data() {
		return {
			searchform: {
				dictType: "",
				dectLabel: "",
				status: "",
			},
			tableData: [],
			dictFromDialog: false,
			dictFromData: {
				dictCode: "",
				dictSort: "",
				dictValue: "",
				dictType: "",
				status: "",
				cssClass: "",
				dictLabel: "",
				isDefault: "",
			},
			dictFromDataButton: "",
			rules: {
				dictSort: [
					{
						required: true,
						message: "请输入部门名称",
						trigger: "blur",
					},
				],
				dictLabel: [
					{
						required: true,
						message: "请输入部门名称",
						trigger: "blur",
					},
				],
				dictValue: [
					{
						required: true,
						message: "请输入部门名称",
						trigger: "blur",
					},
				],
				dictLabel: [
					{
						required: true,
						message: "请输入部门名称",
						trigger: "blur",
					},
				],
				isDefault: [
					{
						required: true,
						message: "请选择状态",
						trigger: "change",
					},
				],
				status: [
					{
						required: true,
						message: "请选择状态",
						trigger: "change",
					},
				],
			},
			TypeData: [],
		};
	},
	methods: {
		// 字典类型接口
		selectTypeList() {
			let obj = {
				dictName: "",
				dictType: "",
			};
			getTypeList(obj).then((res) => {
				if (res.code == 200) {
					this.TypeData = res.rows;
				}
			});
		},
		// 点击查询按钮
		searchDictList() {
			this.selectDictList();
		},
		// 字典列表
		selectDictList() {
			let obj = {
				dictType: this.searchform.dictType,
				dectLabel: this.searchform.dectLabel,
				status: this.searchform.status,
			};
			selectDictList(obj).then(({ code, rows }) => {
				if (code == 200) {
					this.tableData = rows;
				}
			});
		},
		// 添加
		addDict() {
			this.dictFromDataButton = "1";
			this.dictFromDialog = true;
			this.dictFromData = {};
			this.selectTypeList();
		},
		// 表单确认按钮
		submitForm(formName) {
			this.$refs[formName].validate((valid) => {
				if (valid) {
					if (this.dictFromDataButton == 1) {
						this.addDictList();
						this.selectDictList();
					} else if (this.dictFromDataButton == 2) {
						this.editDictList();
						this.selectDictList();
					}
				}
			});
		},
		// 新增列表接口
		addDictList() {
			console.log(this.checkList);
			let obj = {
				...this.dictFromData,
			};
			addDictList(obj).then(({ code, msg }) => {
				if (code == 200) {
					this.dictFromDialog = false;
					this.$message.success(msg);
					this.selectDictList();
				}
			});
		},
		// 修改按钮
		editFrom(row) {
			this.dictFromDataButton = "2";
			this.dictFromDialog = true;
			this.echoDictList(row);
		},
		// 回显字典内容表单
		echoDictList(item) {
			let obj = {
				dictCode: item.dictCode,
			};
			echoDictList(obj).then(({ code, msg, data }) => {
				if (code == 200) {
					const {
						dictCode,
						dictSort,
						dictValue,
						dictType,
						status,
						dictLabel,
						isDefault,
						remark,
					} = data;
					this.dictFromData.dictCode = dictCode;
					this.dictFromData.dictSort = dictSort;
					this.dictFromData.dictValue = dictValue;
					this.dictFromData.dictType = dictType;
					this.dictFromData.status = status;
					this.dictFromData.dictLabel = dictLabel;
					this.dictFromData.isDefault = isDefault;
					this.dictFromData.remark = remark;
				}
			});
		},
		// 确认编辑
		editDictList() {
			let obj = {
				...this.dictFromData,
			};
			editDictList(obj).then(({ code, msg, data }) => {
				if (code == 200) {
					this.dictFromDialog = false;
					this.selectDictList();
					this.$message.success(msg);
				}
			});
		},
		// 删除
		delData(row) {
			this.removeDictList(row);
		},
		removeDictList(row) {
			this.dialogVisible = true;
			this.$confirm("删除后将无法使用该字典", "提示", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				type: "warning",
			})

				.then(() => {
					removeDictList([row.dictCode]).then(({ code, msg }) => {
						if (code == 200) {
							this.$message.success(msg);
							this.dialogVisible = false;
							this.selectDictList();
						}
					});
				})
				.catch(() => { });
		},
		// 重置
		resetForm(formName) {
			this.searchform = {};
		},
	},
	created() {
		this.selectDictList();
		this.selectTypeList();
	},
};
</script>

<style lang="scss" scoped>
	.footerButton {
		width: 100%;
		text-align: center;
		border-top: 1px solid #dcdfe6;
		position: absolute;
		padding-top: 20px;
		bottom: 0;
		margin: 0;
		padding: 20px 0;
	}
</style>