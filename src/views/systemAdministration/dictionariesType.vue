<!--
 * @Descripttion: 字典类型
 * @version: 1.0.0
 * @Author:jingrou
 * @Date: 2021-09-30 11:34:27
 * @LastEditors: jingrou
 * @LastEditTime: 2021-12-08 15:17:08
-->
<template>
	<el-card shadow="never">
		<!-- 查询 -->
		<el-form
			ref="form"
			:model="searchform"
			:inline="true"
			style="border-bottom: 1px solid #ebeef5"
		>
			<div style="display: flex; margin-top: 16px">
				<el-form-item label="字典名称:">
					<el-input
						v-model="searchform.dictName"
						placeholder="请输入字典名称"
					/>
				</el-form-item>
				<el-form-item label="状态">
					<el-select
						v-model="searchform.status"
						placeholder="请选择状态"
					>
						<el-option label="正常" value="0" />
						<el-option label="停用" value="1" />
					</el-select>
				</el-form-item>
				<el-form-item label="字典类型:">
					<el-select
						v-model="searchform.dictType"
						placeholder="请选择字典类型"
					>
						<el-option
							v-for="(item, i) in TypeFromData"
							:key="i"
							:label="item.dictType"
							:value="item.dictType"
						></el-option>
					</el-select>
				</el-form-item>
				<el-form-item style="margin-left: 30px">
					<el-button
						type="primary"
						@click="searchTypeList"
						icon="el-icon-search"
						>查询</el-button
					>
					<el-button @click="clear">重置</el-button>
				</el-form-item>
			</div>
		</el-form>
		<div
			style="
				margin: 16px 0;
				display: flex;
				justify-content: space-between;
				align-items: center;
			"
		>
			<span>字典列表</span>
			<div>
				<el-button
					type="primary"
					icon="el-icon-plus"
					@click="newAdd"
					>添加字典</el-button
				>
			</div>
		</div>
		<!-- 表格 -->
		<el-table
			v-loading="loading"
			:data="TypeData"
			row-key="menuId"
			border
			:header-cell-style="{ background: '#f5f7fa', color: '#5b5d61' }"
		>
			<el-table-column
				type="index"
				label="序号"
				width="60px"
				align="center"
			></el-table-column>
			<el-table-column prop="dictName" label="字典名称"></el-table-column>
			<el-table-column prop="dictType" label="字典类型"></el-table-column>
			<el-table-column prop="status" label="状态">
				<template slot-scope="scope">
					{{ scope.row.status == "0" ? "正常" : "停用" }}
				</template>
			</el-table-column>
			<el-table-column prop="remark" label="备注"></el-table-column>
			<el-table-column
				label="操作"
				align="right"
				width="180"
				fixed="right"
			>
				<template slot-scope="scope">
					<el-link
						type="primary"
						icon="el-icon-edit"
						size="small"
						@click="editFromOne(scope.row)"
						>编辑</el-link
					>
					<el-link
						style="margin-left: 10px"
						type="primary"
						icon="el-icon-view"
						el-icon-view
						size="small"
						@click="see(scope.row)"
						>查看</el-link
					>
					<el-link
						style="margin-left: 10px"
						type="danger"
						size="small"
						@click="delData(scope.row)"
					>
						删除
						<i class="el-icon-delete" />
					</el-link>
				</template>
			</el-table-column>
		</el-table>
		<!-- 分页 -->
		<div class="paginationBlock">
			<el-pagination
				style="margin-top: 10px"
				:current-page="pagination.current"
				:page-sizes="[10, 20, 30, 40]"
				:page-size="pagination.size"
				background
				layout="total, sizes, prev, pager, next, jumper"
				:total="pagination.total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
		</div>

		<!-- 新增、编辑 弹窗 -->
		<el-dialog
			:title="title"
			:visible.sync="dialogFormVisible"
			@close="close"
			width="580px"
		>
			<el-form :model="addForm" ref="ruleForm" :rules="rules">
				<el-form-item
					label="字典名称"
					:label-width="formLabelWidth"
					prop="dictName"
				>
					<el-input
						v-model="addForm.dictName"
						autocomplete="off"
						maxlength="10"
						show-word-limit
						placeholder="请输入字典名称"
					></el-input>
				</el-form-item>
				<el-form-item
					label="字典类型"
					:label-width="formLabelWidth"
					prop="dictType"
				>
					<el-input
						v-model="addForm.dictType"
						autocomplete="off"
						maxlength="20"
						show-word-limit
						placeholder="请输入字典类型"
					></el-input>
				</el-form-item>
				<el-form-item
					label="状态"
					:label-width="formLabelWidth"
					prop="status"
				>
					<el-select
						v-model="addForm.status"
						placeholder="请选择状态"
					>
						<el-option label="正常" value="0" />
						<el-option label="停用" value="1" />
					</el-select>
				</el-form-item>
				<el-form-item
					label="备注"
					:label-width="formLabelWidth"
					prop="remark"
				>
					<el-input
						v-model="addForm.remark"
						autocomplete="off"
						maxlength="20"
						show-word-limit
						placeholder="请输入备注"
					></el-input>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="dialogFormVisible = false">取 消</el-button>
				<el-button
					:loading="buttonLoadingDic"
					type="primary"
					@click="submitFormOne"
					>确 定</el-button
				>
			</div>
		</el-dialog>

		<!-- 查看弹框 -->
		<el-dialog
			title="查看"
			width="60%"
			:visible.sync="seeDialogTableVisible"
		>
			<el-button
				type="primary"
				style="margin-left: 42px; margin-bottom: 30px"
				icon="el-icon-plus"
				@click="addDict"
				>添加字典</el-button
			>
			<template>
				<div style="width: 97%; margin: auto">
					<el-table
						:data="tableData"
						style="width: 100%"
						border
						max-height="600"
						:header-cell-style="{
							background: '#f5f7fa',
							color: '#5b5d61'
						}"
					>
						<el-table-column
							prop="dictLabel"
							label="字典标签"
							align="center"
						/>
						<el-table-column
							prop="dictSort"
							label="字典排序"
							align="center"
						/>
						<el-table-column
							prop="dictValue"
							label="字典键值"
							align="center"
							width="140px"
						/>
						<el-table-column
							prop="isDefault"
							label="是否默认"
							align="center"
						>
							<template slot-scope="scope">
								<div>
									{{
										scope.row.isDefault == "Y"
											? "是"
											: scope.row.isDefault == "N"
											? "否"
											: ""
									}}
								</div>
							</template>
						</el-table-column>
						<el-table-column
							prop="status"
							label="状态"
							align="center"
						>
							<template slot-scope="scope">
								<div>
									{{
										scope.row.status == "0"
											? "正常"
											: scope.row.status == "1"
											? "停用"
											: ""
									}}
								</div>
							</template>
						</el-table-column>
						<el-table-column
							prop="remark"
							label="备注"
							align="center"
						/>
						<el-table-column
							prop="status"
							label="操作"
							align="center"
							width="140px"
						>
							<template slot-scope="scope">
								<el-link
									type="primary"
									icon="el-icon-edit"
									size="small"
									@click="editFrom(scope.row)"
									>修改</el-link
								>
								<el-link
									type="danger"
									size="small"
									style="margin-left: 10px"
									@click="delData1(scope.row)"
								>
									删除
									<i class="el-icon-delete" />
								</el-link>
							</template>
						</el-table-column>
					</el-table>
				</div>
			</template>
		</el-dialog>

		<el-drawer
			:title="dictFromDialogTitle"
			:visible.sync="dictFromDialog"
			direction="rtl"
		>
			<el-form
				ref="ruleForm"
				:model="dictFromData"
				:rules="rules"
				label-width="100px"
				class="demo-ruleForm"
			>
				<el-form-item label="字典排序:">
					<el-input-number
						v-model="dictFromData.dictSort"
						:min="1"
						label="描述文字"
					></el-input-number>
				</el-form-item>
				<el-form-item label="字典标签:" prop="dictLabel">
					<el-input
						v-model="dictFromData.dictLabel"
						placeholder="请输入字典标签"
						show-word-limit
						maxlength="10"
					/>
				</el-form-item>
				<el-form-item label="字典键值:" prop="dictValue">
					<el-input
						v-model="dictFromData.dictValue"
						placeholder="请输入字典键值"
					/>
				</el-form-item>
				<el-form-item label="是否默认:" prop="isDefault">
					<el-select
						v-model="dictFromData.isDefault"
						placeholder="请选择"
					>
						<el-option label="是" value="Y" />
						<el-option label="否" value="N" />
					</el-select>
				</el-form-item>
				<!-- <el-form-item label="字典类型" prop="dictType">
					<el-select
						v-model="dictFromData.dictType"
						placeholder="请选择"
					>
						<el-option
							v-for="item in TypeFromData"
							:key="item.dictType"
							:label="item.dictName"
							:value="item.dictType"
						/>
					</el-select>
				</el-form-item>-->
				<el-form-item label="状态:" prop="status">
					<el-select
						v-model="dictFromData.status"
						placeholder="请选择状态"
					>
						<el-option label="正常" value="0" />
						<el-option label="停用" value="1" />
					</el-select>
				</el-form-item>

				<el-form-item label="备注:">
					<el-input
						v-model="dictFromData.remark"
						placeholder="请输入备注"
						show-word-limit
					/>
				</el-form-item>

				<div class="footerButton">
					<el-button
						type="primary"
						:loading="buttonLoading"
						@click="submitForm('ruleForm')"
						>确定</el-button
					>
					<el-button @click="dictFromDialog = false">取消</el-button>
				</div>
			</el-form>
		</el-drawer>
	</el-card>
</template>

<script>
import {
	getTypeList,
	addTypeList,
	editTypeList,
	echoTypeList,
	removeTypeList
} from "@/api/systemAdministration.js";
import {
	selectDictList,
	addDictList,
	echoDictList,
	editDictList,
	removeDictList
} from "@/api/systemAdministration.js";
export default {
	data() {
		return {
			loading: false,
			pagination: {
				current: 1,
				size: 10,
				total: 0
			},
			buttonLoadingDic: false,
			buttonLoading: false,
			addDicDdictType: "",
			dictFromDialogTitle: "",
			formLabelWidth: "100px",
			TypeData: [],
			searchform: {
				dictName: "",
				status: "",
				dictType: ""
			},
			title: "",
			dialogFormVisible: false,
			addForm: {
				dictName: "",
				dictType: "",
				status: "",
				remark: "",
				dictId: ""
			},
			seeDialogTableVisible: false,
			tableData: [],
			dictFromDialog: false,
			dictFromData: {
				dictCode: "",
				dictSort: "1",
				dictValue: "",
				dictType: "",
				status: "0",
				cssClass: "",
				dictLabel: "",
				isDefault: "Y",
				remark: ""
			},
			dictFromDataButton: "",
			rules: {
				dictName: [
					{ required: true, message: "请输入", trigger: "change" }
				],
				dictType: [
					{ required: true, message: "请选择", trigger: "change" }
				],
				status: [
					{ required: true, message: "请选择", trigger: "change" }
				],
				remark: [
					{ required: true, message: "请输入", trigger: "change" }
				],
				dictId: [
					{ required: true, message: "请输入", trigger: "change" }
				],
				// ------------------------------
				dictSort: [
					{
						required: true,
						message: "请输入",
						trigger: "blur"
					}
				],
				dictLabel: [
					{
						required: true,
						message: "请输入",
						trigger: "blur"
					}
				],
				dictValue: [
					{
						required: true,
						message: "请输入",
						trigger: "blur"
					}
				],
				dictLabel: [
					{
						required: true,
						message: "请输入",
						trigger: "blur"
					}
				],
				isDefault: [
					{
						required: true,
						message: "请选择状态",
						trigger: "change"
					}
				],
				status: [
					{
						required: true,
						message: "请选择状态",
						trigger: "change"
					}
				]
			},
			TypeFromData: [],
			row: {
				dictType: ""
			}
		};
	},
	created() {
		this.selectTypeList();
		this.selectTypeFromList();
	},
	methods: {
		// 分页
		handleSizeChange(val) {
			this.pagination.current = 1;
			this.pagination.size = val;
			this.selectTypeList();
		},
		handleCurrentChange(val) {
			this.pagination.current = val;
			this.selectTypeList();
		},
		// 字典类型接口
		selectTypeFromList() {
			this.loading = true;
			let obj = {
				dictName: "",
				dictType: "",
				status: ""
			};
			getTypeList(obj).then(res => {
				if (res.code == 200) {
					console.log(res.rows);
					this.TypeFromData = res.rows;
					this.loading = false;
				}
			});
		},
		// 字典列表
		selectDictList(item) {
			console.log(item);
			let obj = {
				dictType: item.dictType,
				dectLabel: "",
				status: ""
			};
			selectDictList(obj).then(({ code, rows }) => {
				if (code == 200) {
					this.tableData = rows;
				}
			});
		},
		// 添加
		addDict() {
			this.dictFromDataButton = "1";
			this.dictFromDialogTitle = "添加字典";
			this.dictFromDialog = true;
			this.dictFromData.dictSort = "1";
			this.dictFromData.dictValue = "";
			this.dictFromData.dictLabel = "";
			this.dictFromData.remark = "";
			this.dictFromData.status = "0";
			this.dictFromData.isDefault = "Y";
			this.dictFromData.dictType = this.addDicDdictType;
			this.selectTypeFromList();
		},
		// 表单确认按钮
		submitForm(formName) {
			this.$refs[formName].validate(valid => {
				if (valid) {
					if (this.dictFromDataButton == 1) {
						this.addDictList();
						this.selectDictList(this.row);
					} else if (this.dictFromDataButton == 2) {
						this.submitEditDictList();
						this.selectDictList(this.row);
					}
				}
				this.selectDictList(this.row);
			});
		},
		// 新增列表接口
		addDictList() {
			this.buttonLoading = true;
			let obj = {
				...this.dictFromData
			};
			addDictList(obj).then(({ code, msg }) => {
				if (code == 200) {
					this.buttonLoading = false;
					this.dictFromDialog = false;
					this.$message.success(msg);
					// this.selectDictList();
					this.selectDictList(this.row);
				}
				this.buttonLoading = false;
			});
		},
		// 修改按钮
		editFrom(row) {
			this.dictFromDataButton = "2";
			this.dictFromDialogTitle = "编辑字典";
			this.dictFromDialog = true;
			this.echoDictList(row);
		},
		// 回显字典内容表单
		echoDictList(item) {
			let obj = {
				dictCode: item.dictCode
			};
			echoDictList(obj).then(({ code, msg, data }) => {
				if (code == 200) {
					const {
						dictCode,
						dictSort,
						dictValue,
						dictType,
						status,
						dictLabel,
						isDefault,
						remark
					} = data;
					this.dictFromData.dictCode = dictCode;
					this.dictFromData.dictSort = dictSort;
					this.dictFromData.dictValue = dictValue;
					this.dictFromData.dictType = dictType;
					this.dictFromData.status = status;
					this.dictFromData.dictLabel = dictLabel;
					this.dictFromData.isDefault = isDefault;
					this.dictFromData.remark = remark;
				}
			});
		},
		// 确认编辑
		submitEditDictList(row) {
			this.buttonLoading = true;
			let obj = {
				...this.dictFromData
			};
			// debugger
			editDictList(obj).then(({ code, msg, data }) => {
				if (code == 200) {
					(this.buttonLoading = false), this.$message.success(msg);
					this.dictFromDialog = false;
					// this.selectDictList(row);
					this.selectTypeFromList();
				}
				this.buttonLoading = false;
			});
		},
		// 删除
		delData1(row) {
			this.removeDictList(row);
		},
		removeDictList(row) {
			this.dialogVisible = true;
			this.$confirm("删除后将无法使用该字典", "提示", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				type: "warning"
			})

				.then(() => {
					let dictCode = row.dictCode;
					removeDictList([dictCode]).then(({ code, msg }) => {
						if (code == 200) {
							this.$message.success(msg);
							this.selectDictList(row);
							this.dialogVisible = false;
						}
					});
				})
				.catch(() => {});
		},
		// 重置
		resetForm(formName) {
			this.searchform = {};
		},
		// ------------------------------------------------------
		// 字典列表接口
		selectTypeList() {
			this.loading = true;
			let obj = {
				dictName: this.searchform.dictName,
				dictType: this.searchform.dictType,
				status: this.searchform.status,
				pageSize: this.pagination.size,
				pageNum: this.pagination.current
			};
			getTypeList(obj).then(res => {
				if (res.code == 200) {
					this.pagination.total = res.total;
					this.pagination.size = res.pageSize;
					this.pagination.current = res.pageNum;
					this.TypeData = res.rows;
					this.loading = false;
				}
			});
		},
		// 查询
		searchTypeList() {
			this.pagination.current = 1;
			this.selectTypeList();
		},
		// 重置
		clear() {
			this.pagination.current = 1;
			this.searchform = {};
			this.selectTypeList();
		},
		// 添加接口
		TypeAddList() {
			this.buttonLoadingDic = true;
			let obj = {
				dictName: this.addForm.dictName,
				dictType: this.addForm.dictType,
				status: this.addForm.status,
				remark: this.addForm.remark
			};
			addTypeList(obj).then(({ code, msg, data }) => {
				if (code === 200) {
					this.buttonLoadingDic = false;
					this.$message.success(msg);
					this.selectTypeList();
					this.dialogFormVisible = false;
				}
				this.buttonLoadingDic = false;
			});
		},
		// 编辑接口
		TypeEditList() {
			this.buttonLoadingDic = true;
			editTypeList(this.addForm).then(res => {
				if (res.code === 200) {
					this.buttonLoadingDic = false;
					this.$message.success(res.msg);
					this.selectTypeList();
					this.dialogFormVisible = false;
				}
				this.buttonLoadingDic = false;
			});
		},
		// 新增
		newAdd() {
			this.dialogFormVisible = true;
			this.title = "新增";
		},
		// 编辑
		editFromOne(row) {
			this.dialogFormVisible = true;
			this.title = "编辑";
			this.echoType(row);
		},
		// 确定添加、编辑
		submitFormOne() {
			this.$refs.ruleForm.validate(valid => {
				if (valid) {
					if (this.title == "新增") {
						this.TypeAddList();
					} else {
						console.log(11);
						this.TypeEditList();
					}
				}
			});
		},
		// 关闭弹窗的回掉
		close() {
			this.$refs.ruleForm.resetFields();
			this.dialogFormVisible = false;
		},
		// 回显
		echoType(row) {
			let obj = {
				dictId: row.dictId
			};
			echoTypeList(obj).then(({ code, msg, data }) => {
				if (code == 200) {
					this.addForm = data;
				}
			});
		},
		// 删除
		delData(row) {
			this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				type: "warning"
			})
				.then(() => {
					// debugger;
					removeTypeList([row.dictId]).then(res => {
						if (res.code == 200) {
							this.$message({
								type: "success",
								message: "删除成功!"
							});
							this.selectTypeList();
						}
					});
				})
				.catch(() => {
					this.$message({
						type: "info",
						message: "已取消删除"
					});
				});
		},
		// 查看
		see(row) {
			console.log(row, "1111111111111111");
			this.row.dictType = row.dictType;
			this.seeDialogTableVisible = true;
			this.addDicDdictType = row.dictType;
			this.selectDictList(row);
		}
	}
};
</script>

<style lang="scss" scoped>
.footerButton {
	width: 100%;
	text-align: center;
	border-top: 1px solid #dcdfe6;
	position: absolute;
	padding-top: 20px;
	bottom: 0;
	margin: 0;
	padding: 20px 0;
}
.paginationBlock {
	margin-top: 16px;
	text-align: right;
}
</style>
