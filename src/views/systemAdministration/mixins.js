/*
 * @Descripttion:mixins
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-09-28 17:43:37
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2021-11-10 19:51:18
 */
/* eslint-disable */
import {
	getPlatformList,
	getRoleList,
	getDepartmentList
} from "@/api/systemManagement.js";
export default {
	data() {
		return {
			loading: false,
			pagination: {
				size: 10,
				current: 1,
				total: 0
			},
			platformList: [],
			platformRoleList: [],
			platformDempList: [],

			currentPlatform: "",
			departmentList: [],
			deptId: ""
		};
	},

	created() {
		this.getPlatformList();
		// this.getPlatformDempList();
	},
	methods: {
		// 获取平台列表
		getPlatformList(
			params = {
				dictType: "sys_platform",
				status: "0"
			}
		) {
			getPlatformList(params)
				.then(res => {
					this.platformList = res.data;
					this.currentPlatform = res.data[0] && res.data[0].dictValue;
				})
				.catch(err => {
					this.$message.error("获取平台列表失败");
				});
		},

		// 获取平台角色
		getPlatformRoleList() {
			// if (this.platformRoleList.length) return;
			const params = {
				platform: this.currentPlatform
			};
			getRoleList(params).then(res => {
				res.rows.forEach(item => {
					item.disabled = item.status == 1 ? true : false;
				});
				this.platformRoleList = res.rows;
			});
		},

		// 获取平台部门
		getPlatformDempList(cb) {
			const params = {
				platform: this.currentPlatform
			};
			getDepartmentList(params).then(res => {
				this.platformDempList = res.data || res.rows || [];
				if (res.data && res.data.length) {
					this.deptId = res.data && res.data[0].deptId;
					this.departmentList = this.flatTree(res.data);
				}
				cb && cb();
			});
		},
		// tree 扁平
		flatTree(data, treeMap = [], depth = 0) {
			if (!(data && data.length)) return;
			depth++;
			return data.reduce((acc, cur) => {
				cur.depth = depth;
				acc.push(cur);
				if (cur.children && cur.children.length) {
					this.flatTree(cur.children, treeMap, depth);
				}
				return acc;
			}, treeMap);
		}
	}
};
