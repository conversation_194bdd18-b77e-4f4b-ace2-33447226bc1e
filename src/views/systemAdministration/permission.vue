<!--
 * @Descripttion: 权限管理
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-09-30 11:34:27
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2021-11-10 20:14:20
-->
<template>
	<div>
		<Platform v-model="currentPlatform" :data="platformList" @change="platformChang" />
		<el-card shadow="never">
			<div class="department">
				<YdTable ref="table" :tree-props="{children: 'btnIds'}" rowKey="menuId" :tableColumn="tableColumn" :data="loadData" @selectionChange="selectionChange">
					<!-- search -->
					<template slot="search" slot-scope="scope">
						<el-form :inline="true" :model="queryParam" size="medium">
							<el-form-item label="识别码">
								<el-input v-model="queryParam.path" placeholder="请输入菜单识别码"></el-input>
							</el-form-item>
							<el-form-item label="菜单名称">
								<el-input v-model="queryParam.menuName" placeholder="请输入菜单名称"></el-input>
							</el-form-item>
							<el-form-item>
								<el-button type="primary" :loading="scope.loading" @click="scope.handleSearch" icon="el-icon-search">查询</el-button>
								<el-button size="medium" @click="searchResetForm">重置</el-button>
							</el-form-item>
						</el-form>
					</template>
					<!--  -->
					<!-- 资源切换 -->
					<template slot="describe">
						<!-- <span>权限列表</span> -->
						<el-radio-group class="describe" v-model="currentResource">
							<el-radio :label="0" disabled>API 资源</el-radio>
							<el-radio :label="1" disabled>数据资源</el-radio>
							<el-radio :label="2">UI 资源</el-radio>
						</el-radio-group>
					</template>
					<!-- Action -->
					<template slot="btn">
						<el-button size="medium" icon="el-icon-plus" type="primary" @click.stop="handleAddEditOpen(true)">新 建</el-button>
						<!-- <el-button class="btn_plain_danger" size="medium" plain type="danger" :disabled="selectionTableList.length?false:true" @click="handleRemove('batch')">
							删 除
							<i class="el-icon-delete" />
						</el-button>-->
					</template>
					<!-- table  -->
					<a slot="date" slot-scope="scope">{{scope.row.date}}</a>
					<span slot="action" slot-scope="scope">
						<el-link type="primary" icon="el-icon-edit" @click.stop="handleAddEditOpen(false,scope.row)">编辑</el-link>
						<el-popover placement="top" width="160" v-model="scope.row.removeVisible">
							<p style="padding: 3px 5px 10px;">确定删除吗？</p>
							<div style="text-align: right; margin: 0">
								<el-button size="mini" type="text" @click.stop="scope.row.removeVisible = false">取消</el-button>
								<el-button type="primary" size="mini" @click.stop="handleRemove('single',scope.row)">确定</el-button>
							</div>
							<el-link slot="reference" style="margin-left:10px" type="danger">
								删除
								<i class="el-icon-delete" />
							</el-link>
						</el-popover>
					</span>
				</YdTable>
			</div>
		</el-card>
		<!-- add，edit -->
		<el-dialog :title="dialog.status?'新增':'编辑'" :visible.sync="dialog.visible" :width="dialog.width">
			<div>
				<el-form :model="form" :rules="rules" ref="form" label-width="120px" class="form">
					<el-form-item label="资源类型：" prop="menuType">
						<el-radio-group v-model="form.menuType" @change="menuTypeChange">
							<el-radio label="C">菜单</el-radio>
							<el-radio label="F">按钮</el-radio>
						</el-radio-group>
					</el-form-item>
					<el-form-item :label="`${form.menuType == 'C' ? '父级' : '所属'}菜单`" prop="parentId">
						<el-select v-model="form.parentId" filterable>
							<el-option label="顶级菜单" :value="0" v-if="form.menuType == 'C'" />
							<el-option v-for="(item, i) in menuList" :key="i" :label="item.menuName" :value="item.menuId" />
						</el-select>
					</el-form-item>
					<template v-if="form.menuType == 'C'">
						<el-form-item label="菜单名称：" prop="menuName">
							<el-input v-model="form.menuName" placeholder="对应前端路由(页面)名称，如：首页" show-word-limit maxlength="10"></el-input>
						</el-form-item>
						<el-form-item label="唯一识别码：" prop="path">
							<el-input v-model="form.path" placeholder="前端路由name 如：home" show-word-limit maxlength="30" />
						</el-form-item>
						<el-form-item label="图标Icon：">
							<IconSelect v-model="form.icon" />
						</el-form-item>
						<div style="display:flex;">
							<el-form-item label="菜单显示：" style="flex:1;">
								<el-switch v-model="form.visible" active-value="0" inactive-value="1"></el-switch>
							</el-form-item>
							<el-form-item label="缓存页面：" style="flex:1;">
								<el-switch v-model="form.isCache" active-value="0" inactive-value="1"></el-switch>
							</el-form-item>
						</div>
						<!-- <el-form-item label="可操作：">
									<el-checkbox-group>
										<el-checkbox v-for="(item,index) in actionList" :label="item.value" :key="index">{{item.label}}</el-checkbox>
									</el-checkbox-group>
						</el-form-item>-->
						<el-form-item label="排序值：">
							<el-input-number v-model="form.orderNum" controls-position="right" :min="1" :max="1000" />
						</el-form-item>
					</template>
					<template v-else>
						<el-form-item label="按钮名称" prop="menuName">
							<el-input v-model="form.menuName" placeholder="按钮名称" show-word-limit maxlength="10" />
						</el-form-item>
						<el-form-item label="唯一识别码" prop="path">
							<el-input v-model="form.path" placeholder="按钮唯一识别码" show-word-limit maxlength="20" />
						</el-form-item>
					</template>
					<el-form-item label="API资源：" prop="urlPerm">
						<el-input type="textarea" v-model="form.urlPerm" rows="4" placeholder="多个api资源请按回车隔开"></el-input>
					</el-form-item>
					<el-form-item label="状态：">
						<el-select v-model="form.status">
							<el-option label="启用" value="0" />
							<el-option label="禁用" value="1" />
						</el-select>
					</el-form-item>
				</el-form>
			</div>
			<span slot="footer">
				<el-button @click="handleAddEdlitCancel">取 消</el-button>
				<el-button :loading="dialog.btnSubmitLoading" type="primary" @click="handleAddEdlitSubmit">确 定</el-button>
			</span>
		</el-dialog>
		<!--  -->
	</div>
</template>

<script>
import YdTable from "@/components/YdTable";
import Platform from "./platform";
import IconSelect from "@/components/IconSelect";
import {
	getPermissionList, createPermission, updatePermission, getPermissionInfo, removePermission
} from "@/api/systemManagement.js";
import mixins from "./mixins";
export default {
	mixins: [mixins],
	components: {
		YdTable,
		Platform,
		IconSelect
	},
	data() {
		const _this = this;
		return {
			//
			tableColumn: [
				// {
				// 	label: "#",
				// 	width: 55,
				// 	type: "selection",
				// 	align: "center",
				// 	isShow: true,
				// },
				{
					label: "序号",
					type: "index",
					align: "center",
					width: "60px",
					isShow: true
				},
				{
					label: "菜单名称",
					index: "menuName",
					formatter: (row) => {
						return <span>
							{row.menuType == "C" ? <i class={row.icon} /> : ""}
							<span style="padding-left:10px">{row.menuName}</span>
						</span>;
					},
					isShow: true
				},
				{
					label: "识别码",
					index: "path",
					isShow: true
				},
				{
					label: "父级菜单",
					index: "parentName",
					isShow: true
				},
				{
					label: "菜单展示",
					align: "center",
					isShow: true,
					formatter: (row) => ["显示", "隐藏"][row.visible]
				},
				{
					label: "状态",
					index: "status",
					align: "center",
					isShow: true,
					formatter: (row) => ["启用", "禁用"][row.status]
				},
				{
					label: "操作",
					isShow: true,
					align: "right",
					scopedSlots: { customRender: "action" }
				}
			],
			removeVisible: false,
			queryParam: {
				menuType: "C",
				path: "",
				menuName: ""
			},
			loadData: (parameter) => {
				return getPermissionList(Object.assign({ ...parameter, platform: this.currentPlatform }, this.queryParam));
			},
			//
			currentResource: 2,

			//
			dialog: {
				status: true,
				visible: false,
				width: "580px",
				loading: false,
				btnSubmitLoading: false
			},
			//
			form: {
				parentId: 0,
				menuName: "",
				menuType: "C",
				path: "",
				visible: "0",
				isCache: "1",
				status: "0",
				orderNum: null,
				urlPerm: "",
				icon: "el-icon-tickets"
			},
			rules: {
				parentId: [
					{ required: true, message: "请选择", trigger: "change" }
				],
				menuName: [
					{ required: true, message: "请输入名称", trigger: "blur" }
				],
				path: [
					{ required: true, message: "请输入识别码", trigger: "blur" }
				]
			},
			actionList: [
				{ label: "新增", value: "add" },
				{ label: "编辑", value: "edit" },
				{ label: "删除", value: "del" },
				{ label: "导入", value: "import" },
				{ label: "导出", value: "export" }
			],
			menuList: [],
			selectionTableList: []
		};
	},
	created() {
		this.getPermissionList();
	},
	methods: {
		searchResetForm() {
			this.queryParam.path = "";
			this.queryParam.menuName = "";
			this.$refs.table.handleRefresh();
		},
		getPermissionList() {
			getPermissionList({
				menuType: "C",
				platform: this.currentPlatform
			}).then(res => {
				this.menuList = res.rows;
			});
		},
		selectionChange(v) {
			this.selectionTableList = v.map(i => i.userId);
		},
		// 查询
		handleSearch() {
			//
		},
		// 取消
		handleAddEdlitCancel() {
			this.dialog.visible = false;
			this.$refs.form.resetFields();
		},
		// 确定
		handleAddEdlitSubmit() {
			this.$refs.form.validate((valid) => {
				if (valid) {
					this.dialog.btnSubmitLoading = true;
					const { form, currentPlatform, dialog: { status }, menuId } = this;
					let urlPerm = form.urlPerm.replace(/\n/g, ",");
					urlPerm = urlPerm.split(",");
					urlPerm = urlPerm.map(i => {
						i = i.trim();
						return i;
					}).filter(i => {
						return i != "";
					});

					const params = { ...form, urlPerm, platform: currentPlatform };
					if (status) {
						// 新竲
						createPermission(params).then(({ msg }) => {
							this.$message.success(msg);
							this.handleAddEdlitCancel();
							this.$refs.table.handleRefresh();
						}).finally(() => {
							this.dialog.btnSubmitLoading = false;

						});
					} else {
						// 编辑
						updatePermission({
							...params,
							menuId
						}).then(({ msg }) => {
							this.$message.success(msg);
							this.handleAddEdlitCancel();
							this.$refs.table.handleRefresh();
						}).finally(() => {
							this.dialog.btnSubmitLoading = false;
						});
					}
				} else {
					return false;
				}
			});

		},
		// 添加,编辑
		handleAddEditOpen(isEdit, row) {
			console.log(123);
			this.dialog.visible = true;
			this.dialog.status = isEdit;
			this.$nextTick(() => {
				this.$refs.form.resetFields();
				this.form.urlPerm = "";
			});
			if (!isEdit) {
				this.menuId = row.menuId;
				getPermissionInfo({ menuId: row.menuId }).then(({ data }) => {
					const {
						parentId,
						menuName,
						menuType,
						path,
						visible,
						isCache,
						status,
						orderNum,
						icon,
						urlPerm
					} = data;
					this.form = {
						parentId,
						menuName,
						menuType,
						path,
						visible,
						isCache,
						status,
						orderNum: Number.parseInt(orderNum),
						icon,
						urlPerm: urlPerm.join(",").replace(/,/g, "\n")
					};
				});
			}
		},
		// 删除
		handleRemove(action, row) {
			const params = {
				batch: this.selectionTableList,
				single: (row && row.menuId) ? [row.menuId] : []
			};
			removePermission({ menuId: row.menuId }).then(({ msg }) => {
				this.$message.success(msg);
				// 如果参数为 true, 则刷新到第一页
				this.$refs.table.handleRefresh();
			});
		},
		// 平台切换
		platformChang(v) {
			this.getPermissionList();
			this.$refs.table.handleRefresh();
		},

		//
		menuTypeChange(v) {
			if (v == "F") {
				this.form.parentId = null;
				this.$nextTick(() => {
					this.$refs.form.clearValidate(["parentId"]);
				});
			}
		}
	}

};
</script>

<style lang="scss" scoped>
	.department {
		margin-top: 16px;
	}
</style>
<style lang="scss">
	.department {
		.describe .el-radio__input {
			display: none;
		}
	}
</style>