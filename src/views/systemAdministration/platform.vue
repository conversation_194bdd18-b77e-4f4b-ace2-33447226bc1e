<!--
 * @Author: your name
 * @Date: 2021-10-01 14:39:49
 * @LastEditTime: 2021-11-15 10:35:17
 * @LastEditors: <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: \laihq-web-servef:\备份\梦想编制者\manage-laihq\src\views\systemAdministration\platform.vue
-->

<template>
	<div class="platform" v-if="data.length">
		<el-tabs :value="value" @tab-click="handlePlatform">
			<el-tab-pane
				:label="item.dictLabel"
				:name="item.dictValue"
				v-for="(item, index) in data"
				:key="index"
			/>
		</el-tabs>
	</div>
</template>

<script>
export default {
	props: {
		value: {
			type: String
		},
		data: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			myValue: this.value
		};
	},
	watch: {
		value(v) {
			this.myValue = v;
		},
		myValue(v) {
			this.$emit("input", v);
			this.$emit("change", v);
		}
	},
	methods: {
		handlePlatform(tab, event) {
			this.myValue = tab.name;
		}
	}
};
</script>

<style lang="scss" scoped>
.platform {
	::v-deep .el-tabs__header {
		margin: 0 0 0px;
	}

	::v-deep .el-tabs__nav-wrap::after {
		content: "";
		height: 0;
	}
}
</style>
