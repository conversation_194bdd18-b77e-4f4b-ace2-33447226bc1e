<!--
 * @Descripttion: 角色管理
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-09-30 11:34:27
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2021-11-16 09:35:35
-->
<template>
	<div>
		<Platform
			v-model="currentPlatform"
			:data="platformList"
			@change="platformChang"
		/>
		<el-card shadow="never">
			<div class="role">
				<YdTable
					ref="table"
					:tableColumn="tableColumn"
					:data="loadData"
					describe="角色列表"
				>
					<!-- search -->
					<template slot="search" slot-scope="scope">
						<el-form
							:inline="true"
							:model="queryParam"
							size="medium"
						>
							<el-form-item label="角色名称">
								<el-input
									v-model="queryParam.roleName"
									placeholder="请输入角色名称"
								></el-input>
							</el-form-item>
							<el-form-item label="角色识别码">
								<el-input
									v-model="queryParam.roleKey"
									placeholder="识别码，例如：admin"
								></el-input>
							</el-form-item>
							<el-form-item>
								<el-button
									type="primary"
									:loading="scope.loading"
									@click="scope.handleSearch"
									icon="el-icon-search"
									>查询</el-button
								>
								<el-button
									size="medium"
									@click="searchResetForm"
									>重置</el-button
								>
							</el-form-item>
						</el-form>
					</template>
					<!-- Action -->
					<template slot="btn">
						<el-button
							size="medium"
							icon="el-icon-plus"
							type="primary"
							@click="handleAddEditOpen(true)"
							>新 建</el-button
						>
						<!-- <el-button class="btn_plain_danger" size="medium" plain type="danger" disabled @click="handleRemove('batch')">
							删 除
							<i class="el-icon-delete" />
						</el-button>-->
					</template>
					<!-- table  -->
					<div
						slot="expand"
						slot-scope="scope"
						style="padding: 16px 16px"
					>
						{{ scope.row }}
					</div>
					<span slot="action" slot-scope="scope">
						<template v-if="!scope.row.admin">
							<el-link
								type="primary"
								icon="el-icon-edit"
								@click="handleAddEditOpen(false, scope.row)"
								>编辑</el-link
							>
							<el-link
								type="primary"
								style="margin-left: 10px"
								icon="el-icon-set-up"
								@click="handleAssignPermissions(scope.row)"
								>分配权限</el-link
							>
							<el-popover
								placement="top"
								width="160"
								v-model="scope.row.removeVisible"
							>
								<p style="padding: 3px 5px 10px">
									确定删除吗？
								</p>
								<div style="text-align: right; margin: 0">
									<el-button
										size="mini"
										type="text"
										@click="scope.row.removeVisible = false"
										>取消</el-button
									>
									<el-button
										type="primary"
										size="mini"
										@click="
											handleRemove('single', scope.row)
										"
										>确定</el-button
									>
								</div>
								<el-link
									slot="reference"
									style="margin-left: 10px"
									type="danger"
								>
									删除
									<i class="el-icon-delete" />
								</el-link>
							</el-popover>
						</template>
					</span>
				</YdTable>
				<!-- add，edit -->
				<el-dialog
					:title="dialog.status ? '新增' : '编辑'"
					:visible.sync="dialog.visible"
					:width="dialog.width"
				>
					<div>
						<el-form
							:model="form"
							:rules="rules"
							ref="form"
							label-width="120px"
						>
							<el-form-item label="角色名称：" prop="roleName">
								<el-input
									v-model="form.roleName"
									placeholder="角色名称，例如：超级管理员"
									show-word-limit
									maxlength="10"
								></el-input>
							</el-form-item>
							<el-form-item label="唯一识别码：" prop="roleKey">
								<el-input
									v-model="form.roleKey"
									placeholder="角色识别码，例如：admin"
									show-word-limit
									maxlength="20"
								/>
							</el-form-item>
							<el-form-item label="备注：">
								<el-input
									type="textarea"
									rows="4"
									v-model="form.remark"
									show-word-limit
									maxlength="30"
								></el-input>
							</el-form-item>
							<el-form-item label="排序值：" prop="roleSort">
								<el-input-number
									v-model="form.roleSort"
									controls-position="right"
									:min="1"
									:max="1000"
								/>
							</el-form-item>
							<el-form-item label="状态：">
								<el-select v-model="form.status">
									<el-option label="启用" value="0" />
									<el-option label="禁用" value="1" />
								</el-select>
							</el-form-item>
						</el-form>
					</div>
					<span slot="footer">
						<el-button @click="handleAddEdlitCancel"
							>取 消</el-button
						>
						<el-button
							:loading="dialog.btnSubmitLoading"
							type="primary"
							@click="handleAddEdlitSubmit"
							>确 定</el-button
						>
					</span>
				</el-dialog>
				<!-- 分配权限 -->
				<el-dialog
					title="分配权限"
					:visible.sync="assignAuth.visible"
					width="980px"
					top="10vh"
				>
					<el-form
						:model="authForm"
						:rules="authRules"
						ref="authform"
						class="authForm"
					>
						<el-row>
							<el-col :span="12" style="overflow-y: auto">
								<el-form-item prop="menu">
									<el-tree
										ref="menuTree"
										:default-checked-keys="[2018]"
										:data="menuTree"
										show-checkbox
										node-key="id"
										props="defaultProps"
										default-expand-all
										:check-on-click-node="true"
										@check-change="handleMenuTreeNodeCheck"
										@check="currentMenuTreeChecked"
									></el-tree>
								</el-form-item>
							</el-col>
							<el-col :span="12" style="overflow-y: auto">
								<el-form-item prop="menu">
									<!-- {{actionList}} -->
									<div
										v-for="(item, index) in actionList"
										:key="index"
									>
										<h3
											style="
												background: #eee;
												padding-left: 10px;
											"
										>
											{{ item.menu && item.menu.label }}
										</h3>
										<div
											style="padding: 10px 10px 10px 20px"
										>
											<el-checkbox-group
												v-model="item.menuIds"
											>
												<el-checkbox
													v-for="(btn,
													cindex) in item.menu &&
														item.menu.btnList"
													:label="btn.id"
													:key="cindex"
												>
													{{ btn.label }}
												</el-checkbox>
											</el-checkbox-group>
										</div>
									</div>
								</el-form-item>
							</el-col>
						</el-row>
					</el-form>
					<span slot="footer">
						<el-button @click="assignAuth.visible = false"
							>取 消</el-button
						>
						<el-button
							:loading="assignAuth.btnSubmitLoading"
							type="primary"
							@click="handleAssignAuthSubmit"
							>确 定</el-button
						>
					</span>
				</el-dialog>
			</div>
		</el-card>
	</div>
</template>

<script>
import YdTable from "@/components/YdTable";
import Platform from "./platform";
// import IconSelect from "@/components/IconSelect";
import mixins from "./mixins";
import {
	getRoleList,
	createRole,
	getRoleInfo,
	updateRole,
	removeRole,
	getRolePermission,
	updateRolePermission
} from "@/api/systemManagement.js";
export default {
	mixins: [mixins],
	components: {
		YdTable,
		Platform
		// IconSelect,
	},
	data() {
		return {
			//
			tableColumn: [
				// {
				// 	label: "#",
				// 	width: 80,
				// 	type: "expand",
				// 	isShow: true,
				// 	scopedSlots: { customRender: 'expand' },
				// },
				{
					label: "序号",
					type: "index",
					align: "center",
					width: "60px",
					isShow: true
				},
				{
					label: "角色名称",
					index: "roleName",
					isShow: true
				},
				{
					label: "唯一识别码",
					index: "roleKey",
					isShow: true
				},
				{
					label: "角色名称",
					index: "perms",
					isShow: false
				},
				{
					label: "状态",
					align: "center",
					isShow: true,
					formatter: row => ["正常", "停用"][row.status]
				},
				{
					label: "创建时间",
					isShow: true,
					index: "createTime"
				},
				{
					label: "操作",
					isShow: true,
					align: "right",
					width: "220px",
					scopedSlots: { customRender: "action" }
				}
			],
			queryParam: {
				roleName: "",
				roleKey: ""
			},
			loadData: parameter => {
				return getRoleList(
					Object.assign(
						{ ...parameter, platform: this.currentPlatform },
						this.queryParam
					)
				);
			},

			//
			dialog: {
				status: true,
				visible: false,
				width: "580px",
				loading: false,
				btnSubmitLoading: false
			},
			assignAuth: {
				visible: false,
				btnSubmitLoading: false
			},
			//
			form: {
				roleName: "",
				roleKey: "",
				status: "0",
				remark: "",
				roleSort: 1
			},
			rules: {
				roleName: [
					{ required: true, message: "请选择", trigger: "blur" }
				],
				roleKey: [
					{ required: true, message: "请输入名称", trigger: "blur" }
				],
				roleSort: [
					{
						required: true,
						message: "请输入排序值",
						trigger: "blur"
					}
				]
			},
			authForm: {
				btnList: []
			},
			authRules: {},
			actionList: [
				// { label: "新增", value: "add" },
				// { label: "编辑", value: "edit" },
				// { label: "删除", value: "del" },
				// { label: "导入", value: "import" },
				// { label: "导出", value: "export" },
			],
			selectMenuIds: [],
			menuTree: [],
			defaultProps: {
				children: "children",
				label: "label"
			}
		};
	},
	async created() {
		this.menuTree = await this.getRolePermission();
	},
	methods: {
		searchResetForm() {
			this.queryParam.roleName = "";
			this.queryParam.roleKey = "";
			this.$refs.table.handleRefresh();
		},
		//获取权限
		async getRolePermission(params = {}) {
			const { code, menus } = await getRolePermission(params);

			if (code == 200) {
				menus.forEach(item => {
					if (item.label == "首页") {
						item.disabled = true;
					}
				});
				return menus;
			}
			return [];

			return [];
		},
		currentMenuTreeChecked(nodeObj, selectedObj) {
			this.selectMenuIds = [
				...selectedObj.halfCheckedKeys,
				...selectedObj.checkedKeys
			];
			// console.log("___________", selectedObj, this.selectMenuIds)
		},
		// 分配权限节点选择
		handleMenuTreeNodeCheck(data, curCheck) {
			// console.log(data, curCheck, childCheck)

			if (data.btnList) {
				const index = this.actionList.findIndex(i => i.id == data.id);

				if (curCheck && index == -1) {
					this.actionList.push({
						id: data.id,
						menuIds: [],
						menu: data
					});
				}
				if (!curCheck && index != -1) {
					this.actionList.splice(index, 1);
				}
			}
		},
		//

		// 分配权限 open
		async handleAssignPermissions(row) {
			this.roleId = row.roleId;
			this.roleKey = row.roleKey;
			this.assignAuth.visible = true;
			// 获取角色权限
			const permission = await this.getRolePermission({
				roleId: row.roleId
			});

			this.$nextTick(() => {
				if (!permission.length) {
					this.$refs.menuTree.setCheckedKeys([]);
					// 2018 首页ID
					this.selectMenuIds = [2018];
					this.actionList = [];
					return;
				}
				// 所有菜单id
				const menuBtnIds = this.flatTree(permission),
					// 菜单子级
					noprant = menuBtnIds.filter(i => i.children == undefined),
					// 需要设置的tree ID
					teeeCheckKeys = noprant.map(i => i.id);

				// 传递给后台选中的菜单id集合
				this.selectMenuIds = menuBtnIds.map(i => i.id);

				this.$refs.menuTree.setCheckedKeys(teeeCheckKeys);

				this.$nextTick(() => {
					this.actionList.forEach(item => {
						menuBtnIds.forEach(jitem => {
							if (item.id == jitem.id) {
								item.menuIds = jitem.btnList
									? jitem.btnList.map(b => b.id)
									: [];
							}
						});
					});
				});
			});
		},
		// 分配权限 submit
		handleAssignAuthSubmit() {
			this.assignAuth.btnSubmitLoading = true;
			const btnIds = this.actionList.map(i => i.menuIds).flat(Infinity),
				menuIds = [].concat(this.selectMenuIds, btnIds);

			updateRolePermission({
				roleId: this.roleId,
				menuIds,
				platform: this.currentPlatform,
				roleKey: this.roleKey
			})
				.then(res => {
					this.$message.success(res.msg);
					this.assignAuth.visible = false;
					this.$refs.table.handleRefresh();
				})
				.finally(() => {
					this.assignAuth.btnSubmitLoading = false;
				});
			console.log(menuIds);
		},
		// 取消
		handleAddEdlitCancel() {
			this.dialog.visible = false;
			this.$refs.form.resetFields();
		},
		// 确定
		handleAddEdlitSubmit() {
			this.$refs.form.validate(valid => {
				if (valid) {
					this.dialog.btnSubmitLoading = true;

					const {
							form,
							currentPlatform,
							dialog: { status },
							roleId
						} = this,
						params = { ...form, platform: currentPlatform };

					if (status) {
						// 新竲
						createRole(params)
							.then(({ msg }) => {
								this.$message.success(msg);
								this.handleAddEdlitCancel();
								this.$refs.table.handleRefresh();
							})
							.finally(() => {
								this.dialog.btnSubmitLoading = false;
							});
					} else {
						// 编辑
						updateRole({
							...params,
							roleId
						})
							.then(({ msg }) => {
								this.$message.success(msg);
								this.handleAddEdlitCancel();
								this.$refs.table.handleRefresh();
							})
							.finally(() => {
								this.dialog.btnSubmitLoading = false;
							});
					}
				} else {
					return false;
				}
			});
		},
		// 添加,编辑
		handleAddEditOpen(isEdit, row) {
			this.form.remark = "";
			this.dialog.visible = true;
			this.dialog.status = isEdit;
			this.$nextTick(() => {
				this.$refs.form.resetFields();
			});
			if (!isEdit) {
				this.roleId = row.roleId;
				getRoleInfo({ roleId: row.roleId }).then(({ data }) => {
					const {
						roleName,
						roleKey,
						status,
						remark,
						roleSort
					} = data;

					this.form = {
						roleName,
						roleKey,
						status,
						remark,
						roleSort
					};
				});
			}
		},
		// 删除
		handleRemove(action, row) {
			const params = {
				batch: this.selectionTableList,
				single: row && row.roleId ? [row.roleId] : []
			};

			removeRole(params[action]).then(({ msg }) => {
				this.$message.success(msg || "删除成功");
				// 如果参数为 true, 则刷新到第一页
				this.$refs.table.handleRefresh();
			});
		},

		// 平台切换
		platformChang(v) {
			this.$refs.table.handleRefresh();
		}
	}
};
</script>

<style lang="scss" scoped>
.role {
	margin-top: 16px;
	.authForm {
		height: 620px;
		overflow-y: auto;
		.el-row {
			height: 100%;
			border: 1px solid #ebeef5;
		}
	}
}
</style>
