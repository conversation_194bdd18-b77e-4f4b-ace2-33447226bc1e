<!--
 * @Descripttion: 用户管理
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2021-09-30 11:34:27
 * @LastEditors: jingrou
 * @LastEditTime: 2022-07-25 15:18:09
-->
<template>
    <div>
        <Platform
            v-model="currentPlatform"
            :data="platformList"
            @change="platformChang"
        />
        <el-card shadow="never">
            <div class="user">
                <YdTable
                    ref="table"
                    :tableColumn="tableColumn"
                    :data="loadData"
                    describe="用户列表"
                    @selectionChange="selectionChange"
                >
                    <!-- search -->
                    <template slot="search" slot-scope="scope">
                        <el-form
                            :inline="true"
                            :model="queryParam"
                            size="medium"
                        >
                            <el-form-item label="用户名称">
                                <el-input
                                    v-model="queryParam.userName"
                                    placeholder="请输入用户名称"
                                ></el-input>
                            </el-form-item>
                            <el-form-item label="手机号">
                                <el-input
                                    v-model="queryParam.phonenumber"
                                    placeholder="请输入手机号"
                                ></el-input>
                            </el-form-item>
                            <el-form-item>
                                <el-button
                                    type="primary"
                                    :loading="scope.loading"
                                    @click="scope.handleSearch"
                                    icon="el-icon-search"
                                    >查询</el-button
                                >
                                <el-button
                                    size="medium"
                                    @click="searchResetForm"
                                    >重置</el-button
                                >
                            </el-form-item>
                        </el-form>
                    </template>
                    <!-- Action -->
                    <template slot="btn">
                        <el-button
                            size="medium"
                            icon="el-icon-plus"
                            type="primary"
                            @click="handleAddEditOpen(true)"
                            >新 建</el-button
                        >
                        <!-- <el-button class="btn_plain_danger" size="medium" plain :disabled="selectionTableList.length?false:true" type="danger" @click="handleRemove('batch')">
							删 除
							<i class="el-icon-delete" />
						</el-button>-->
                    </template>
                    <!-- table  -->
                    <div
                        slot="expand"
                        slot-scope="scope"
                        style="padding: 16px 16px"
                    >
                        {{ scope.row }}
                    </div>
                    <span slot="action" slot-scope="scope">
                        <template v-if="!scope.row.admin">
                            <el-link
                                type="primary"
                                icon="el-icon-edit"
                                @click="handleAddEditOpen(false, scope.row)"
                                >编辑</el-link
                            >

                            <!-- <el-popover placement="top" width="160" v-model="scope.row.resetVisible">
								<p style="padding: 3px 5px 10px;">确定重置该用户密码吗？</p>
								<div style="text-align: right; margin: 0">
									<el-button size="mini" type="text" @click="scope.row.resetVisible = false">取消</el-button>
									<el-button type="primary" size="mini" @click="resetPassword(scope.row)">确定</el-button>
								</div>
								<el-link slot="reference" type="primary" style="margin-left:10px" icon="el-icon-unlock"  >重置密码</el-link>
							</el-popover>-->

                            <el-link
                                type="primary"
                                style="margin-left: 10px"
                                icon="el-icon-unlock"
                                @click="resetPassword(scope.row)"
                                >重置密码</el-link
                            >

                            <el-popover
                                placement="top"
                                width="160"
                                v-model="scope.row.removeVisible"
                            >
                                <p style="padding: 3px 5px 10px">
                                    确定删除吗？
                                </p>
                                <div style="text-align: right; margin: 0">
                                    <el-button
                                        size="mini"
                                        type="text"
                                        @click="scope.row.removeVisible = false"
                                        >取消</el-button
                                    >
                                    <el-button
                                        type="primary"
                                        size="mini"
                                        @click="
                                            handleRemove('single', scope.row)
                                        "
                                        >确定</el-button
                                    >
                                </div>
                                <el-link
                                    slot="reference"
                                    style="margin-left: 10px"
                                    type="danger"
                                >
                                    删除
                                    <i class="el-icon-delete" />
                                </el-link>
                            </el-popover>
                        </template>
                    </span>
                </YdTable>
                <!-- add，edit -->
                <el-dialog
                    :title="dialog.status ? '新增' : '编辑'"
                    :visible.sync="dialog.visible"
                    :width="dialog.width"
                >
                    <div>
                        <el-form
                            :model="form"
                            :rules="rules"
                            ref="form"
                            label-width="120px"
                        >
                            <el-form-item label="用户名:" prop="userName">
                                <el-input
                                    v-model.trim="form.userName"
                                    placeholder="请输入用户名"
                                    show-word-limit
                                    maxlength="20"
                                />
                            </el-form-item>
                            <el-form-item label="手机号:" prop="phonenumber">
                                <el-input
                                    v-model.trim="form.phonenumber"
                                    placeholder="请输入手机号"
                                />
                            </el-form-item>
                            <el-form-item
                                v-if="dialog.status"
                                label="密码:"
                                prop="password"
                            >
                                <el-input
                                    v-model.trim="form.password"
                                    placeholder="请输入密码"
                                    show-word-limit
                                    maxlength="20"
                                />
                            </el-form-item>
                            <div style="display: flex">
                                <el-form-item
                                    label="姓名:"
                                    style="flex: 1"
                                    prop="nickName"
                                >
                                    <el-input
                                        v-model="form.nickName"
                                        placeholder="请输入姓名"
                                        show-word-limit
                                        maxlength="20"
                                    />
                                </el-form-item>
                                <el-form-item
                                    label="邮箱:"
                                    style="flex: 1"
                                    prop="email"
                                >
                                    <el-input
                                        v-model="form.email"
                                        placeholder="请输入邮箱"
                                        show-word-limit
                                    />
                                </el-form-item>
                            </div>
                            <div style="display: flex; position: relative">
                                <el-form-item label="性别:" style="flex: 1">
                                    <el-radio-group v-model="form.sex">
                                        <el-radio label="0">男</el-radio>
                                        <el-radio label="1">女</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                                <!-- <el-form-item label="头像:" style="flex: 1;position: absolute;right: 40px;top: 0;z-index:20">
									<el-upload action="/" :show-file-list="false" :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload">
										<img v-if="avatarImageUrl" :src="avatarImageUrl" class="avatar" />
										<i v-else class="el-icon-plus avatar-uploader-icon"></i>
									</el-upload>
								</el-form-item>-->
                            </div>
                            <el-form-item label="部门:">
                                <!-- <el-select v-model="form.deptId" placeholder="请选择">
									<el-option v-for="item in departmentList" :key="item.deptId" :label="item.deptName" :value="item.deptId" />
								</el-select>-->
                                <el-cascader
                                    v-model="form.deptId"
                                    :options="departmentTree"
                                    :props="departmentTreeProps"
                                    clearable
                                    filterable
                                ></el-cascader>
                                <!-- <el-input @focus="openDempSelect" v-model="form.deptName" type="text" style="width:220px" /> -->
                                <!-- <DempSelect ref="dempSelect" @ok="handleDempSelect" /> -->
                            </el-form-item>
                            <el-form-item
                                label="角色:"
                                prop="roleIds"
                                placeholder="请选择"
                            >
                                <el-select
                                    v-model="form.roleIds"
                                    multiple
                                    collapse-tags
                                    filterable
                                    style="width: 220px"
                                >
                                    <el-option
                                        v-for="(
                                            item, index
                                        ) in platformRoleList"
                                        :disabled="item.disabled"
                                        :label="item.roleName"
                                        :key="index"
                                        :value="item.roleId"
                                    />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="状态:">
                                <!-- <el-select v-model="form.isEnabled" style="width:220px">
									<el-option label="正常" value="0" />
									<el-option label="停用" value="1" />
								</el-select>-->
                                <el-switch
                                    v-model="form.isEnabled"
                                    active-text="正常"
                                    inactive-text="停用"
                                ></el-switch>
                            </el-form-item>
                        </el-form>
                    </div>
                    <span slot="footer">
                        <el-button @click="handleAddEdlitCancel"
                            >取 消</el-button
                        >
                        <el-button
                            :loading="dialog.btnSubmitLoading"
                            type="primary"
                            @click="handleAddEdlitSubmit"
                            >确 定</el-button
                        >
                    </span>
                </el-dialog>
            </div>
        </el-card>
    </div>
</template>

<script>
import YdTable from "@/components/YdTable";
import Platform from "./platform";
// import DempSelect from "./dempSelect";
import {
    getUserList,
    createUser,
    getUserInfo,
    updateUser,
    removeUser,
    resetPassword,
    getDepartmentList,
} from "@/api/systemManagement.js";
import mixins from "./mixins";
export default {
    mixins: [mixins],
    components: {
        YdTable,
        Platform,
        // DempSelect
    },
    data() {
        const _this = this;

        return {
            departmentTreeProps: {
                emitPath: false,
                checkStrictly: true,
                value: "deptId",
                label: "deptName",
                children: "children",
            },
            //
            tableColumn: [
                // {
                // 	label: "#",
                // 	width: 55,
                // 	type: "selection",
                // 	align: "center",
                // 	isShow: true,
                // },
                {
                    label: "序号",
                    type: "index",
                    align: "center",
                    width: "60px",
                    isShow: true,
                },
                {
                    label: "用户名称",
                    index: "userName",
                    isShow: true,
                },
                {
                    label: "性别",
                    isShow: false,
                    formatter: (row) => ["男", "女"][row.sex],
                },
                {
                    label: "手机号",
                    index: "phonenumber",
                    isShow: true,
                },
                {
                    label: "邮箱",
                    index: "email",
                    isShow: false,
                },
                {
                    label: "部门",
                    isShow: false,
                    formatter: (row) => {
                        return row.dept && row.dept.deptName;
                    },
                },
                {
                    label: "角色",
                    isShow: true,
                    formatter: (row) => {
                        const roles = row.roles.map((i) => i.roleName);

                        return roles.join(",");
                    },
                },
                {
                    label: "状态",
                    isShow: true,
                    align: "center",
                    formatter: (row) => (row.isEnabled ? "正常" : "停用"),
                },
                {
                    label: "最近登录",
                    isShow: false,
                    index: "loginDate",
                },
                {
                    label: "最近登录IP",
                    isShow: false,
                    index: "loginIp",
                },
                {
                    label: "创建时间",
                    isShow: true,
                    index: "createTime",
                },
                {
                    label: "操作",
                    isShow: true,
                    align: "right",
                    width: "220",
                    scopedSlots: { customRender: "action" },
                },
            ],
            removeVisible: false,
            queryParam: {
                userName: "",
                phonenumber: "",
            },
            loadData: (parameter) => {
                return getUserList(
                    Object.assign(
                        { ...parameter, platform: this.currentPlatform },
                        this.queryParam
                    )
                );
            },

            //
            dialog: {
                status: true,
                visible: false,
                width: "720px",
                loading: false,
                btnSubmitLoading: false,
            },
            //
            avatarImageUrl: "",
            form: {
                userId: "",
                deptId: "",
                userName: "",
                nickName: "",
                password: "",
                phonenumber: "",
                email: "",
                sex: "0",
                isEnabled: true,
                roleIds: [],
                deptName: "",
            },
            rules: {
                userName: [
                    {
                        required: true,
                        message: "请输入用户名",
                        trigger: "blur",
                    },
                ],
                nickName: [
                    { required: true, message: "请输入姓名", trigger: "blur" },
                ],
                phonenumber: [
                    {
                        required: true,
                        message: "请输入手机号",
                        trigger: "blur",
                    },
                    {
                        pattern: /^1[3-9]\d{9}$/,
                        message: "请输入正确的手机号",
                        trigger: "blur",
                    },
                ],
                password: [
                    { required: true, message: "请输入密码", trigger: "blur" },
                    {
                        pattern: /^[A-Za-z0-9]{6,20}$/,
                        message: "请输入6-20位数字或者英文",
                        trigger: "blur",
                    },
                ],
                email: [
                    {
                        required: false,
                        message: "请输入邮箱地址",
                        trigger: "blur",
                    },
                    {
                        type: "email",
                        message: "请输入正确的邮箱地址",
                        trigger: ["blur", "change"],
                    },
                ],
                roleIds: [
                    {
                        required: true,
                        message: "请选择角色",
                        trigger: "change",
                    },
                ],
                deptId: [
                    // { required: true, message: "请选择部门", trigger: "change" },
                    {
                        type: "array",
                        required: true,
                        message: "请选择部门",
                        trigger: "change",
                    },
                ],
            },
            // actionList: [
            // 	{ label: "新增", value: "add" },
            // 	{ label: "编辑", value: "edit" },
            // 	{ label: "删除", value: "del" },
            // 	{ label: "导入", value: "import" },
            // 	{ label: "导出", value: "export" },
            // ],
            departmentList: [],
            selectionTableList: [],
            departmentTree: [],
        };
    },
    methods: {
        // 递归判断列表，把最后的children去掉
        getTreeData(data) {
            for (let i = 0; i < data.length; i++) {
                if (data[i].children.length < 1) {
                    delete data[i].children;
                } else {
                    this.getTreeData(data[i].children);
                }
            }
            return data;
        },
        getDepartmentList(params) {
            // this.depaLoading = true;
            getDepartmentList(params).then((res) => {
                if (res.code == 200) {
                    // this.departmentTreeProps.value = res.data[0].deptId;
                    this.departmentTree = this.getTreeData(res.data);
                }
            });
        },
        searchResetForm() {
            this.queryParam.userName = "";
            this.queryParam.phonenumber = "";
            this.$refs.table.handleRefresh();
        },
        //
        // getUserList(isSearch){
        // 	const { pagination:{size,current},queryParam:{} } = this;
        // 	let params = {
        // 		pageSize: size,
        // 		pageNum: current,
        // 		// userName: this.queryParam.userName,
        // 		// phonenumber: this.queryParam.phonenumber,
        // 	};
        // 	getUserList(params).then((res)=>{
        // 		const {} = res
        // 	})
        // },
        //
        resetPassword(row) {
            this.$prompt("请输入密码", "重置密码", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                inputPattern: /^[A-Za-z0-9]{6,20}$/,
                inputErrorMessage: "请输入6-20位数字或者英文",
            })
                .then(({ value }) => {
                    resetPassword({
                        userId: row.userId,
                        password: value,
                    }).then(() => {
                        this.$message({
                            type: "success",
                            message: "密码已重置",
                        });
                    });
                })
                .catch(() => {});
        },
        // openDempSelect() {
        // 	this.$refs.dempSelect.visible = true;
        // 	this.$refs.dempSelect.getDepartmentList({
        // 		platform: this.currentPlatform
        // 	});
        // },
        // handleDempSelect(data) {
        // 	this.form.deptId = data[0].deptId;
        // 	this.form.deptName = data[0].deptName;
        // 	this.$refs.dempSelect.visible = false;
        // },

        //
        beforeAvatarUpload() {},
        handleAvatarSuccess() {},
        //
        selectionChange(v) {
            this.selectionTableList = v.map((i) => i.userId);
        },
        // 查询
        handleSearch() {
            this.getUserList("search");
        },
        // 取消
        handleAddEdlitCancel() {
            this.dialog.visible = false;
            this.$refs.form.resetFields();
            this.form.nickName = "";
            this.form.deptId = [];
            this.form.sex = "0";
        },
        // 确定
        handleAddEdlitSubmit() {
            this.$refs.form.validate((valid) => {
                if (valid) {
                    this.dialog.btnSubmitLoading = true;
                    const {
                            form,
                            currentPlatform,
                            dialog: { status },
                            userId,
                        } = this,
                        params = {
                            ...form,
                            platform: currentPlatform,
                            userId: "",
                        };

                    if (status) {
                        // 新竲
                        createUser(params)
                            .then(({ msg }) => {
                                this.$message.success(msg);
                                this.handleAddEdlitCancel();
                                this.$refs.table.handleRefresh();
                            })
                            .finally(() => {
                                this.dialog.btnSubmitLoading = false;
                            });
                    } else {
                        // 编辑
                        updateUser({
                            ...params,
                            userId,
                        })
                            .then(({ msg }) => {
                                this.$message.success(msg);
                                this.handleAddEdlitCancel();
                                this.$refs.table.handleRefresh();
                            })
                            .finally(() => {
                                this.dialog.btnSubmitLoading = false;
                            });
                    }
                } else {
                    return false;
                }
            });
        },
        // 添加,编辑
        handleAddEditOpen(isEdit, row) {
            this.getDepartmentList({ platform: this.currentPlatform });
            this.dialog.status = isEdit;
            this.dialog.visible = true;
            //
            this.getPlatformRoleList();
            //
            // this.getPlatformDempList();

            this.$nextTick(() => {
                this.$refs.form.resetFields();
                this.form.nickName = "";
                this.form.deptId = [];
                this.form.sex = "0";
            });
            if (!isEdit) {
                this.userId = row.userId;
                getUserInfo({ userId: row.userId }).then(
                    ({ data, roleIds }) => {
                        const {
                            dept,
                            userName,
                            nickName,
                            phonenumber,
                            email,
                            sex,
                            isEnabled,
                            userId,
                        } = data;

                        /* eslint-disable */
                        this.form = {
                            deptId: dept && dept.deptId,
                            userName,
                            nickName,
                            phonenumber,
                            email,
                            sex,
                            isEnabled,
                            roleIds: roleIds,
                            userId,
                            password: "",
                        };
                        /* eslint-disable */
                    }
                );
            }
        },
        // 删除
        handleRemove(action, row) {
            const params = {
                batch: this.selectionTableList,
                single: row && row.userId ? [row.userId] : [],
            };
            removeUser(params[action]).then(({ msg }) => {
                this.$message.success(msg);
                // 如果参数为 true, 则刷新到第一页
                this.$refs.table.handleRefresh();
            });
        },
        // 平台切换
        platformChang(v) {
            this.$refs.table.handleRefresh();
        },
    },
};
</script>

<style lang="scss" scoped>
.user {
    margin-top: 16px;
    .authForm {
        height: 620px;
        overflow-y: auto;
        .el-row {
            height: 100%;
            .el-col {
                height: 100%;
                border: 1px solid #ebeef5;
            }
        }
    }
    ::v-deep .avatar-uploader .el-upload {
        border: 1px dashed #d9d9d9 !important;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }
    ::v-deep .avatar-uploader .el-upload:hover {
        border-color: #409eff;
    }
    ::v-deep .avatar-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 178px;
        height: 178px;
        line-height: 178px;
        text-align: center;
    }
    ::v-deep .avatar {
        width: 178px;
        height: 178px;
        display: block;
    }
}
</style>
