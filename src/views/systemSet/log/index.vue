<template>
    <div  class="height100">
        <MyIframe :src="src" :isPostMessage="true"></MyIframe>
    </div>
</template>

<script>
import MyIframe from "@/components/Iframe/index";
import Vue from "vue";
import { ACCESS_TOKEN } from "@/store/mutation-types";
export default {
    components: {
        MyIframe,
    },
    data() {
        return {
            src: "",
        };
    },

    created() {
        const token = `Bearer ${Vue.ls.get(ACCESS_TOKEN)}`;
        const color = localStorage.getItem("COLOR_THEME");
        this.src = `${process.env.VUE_APP_API_BASE_Permission}/#/sys/log?token=${token}&sysCode=manage&sysColor=${color}`;
    },
};
</script>
