<template>
    <div class="card_warp">
        <el-form
            ref="versionForm"
            :model="query"
            status-icon
            :inline="true"
            label-width="auto"
            class="demo-ruleForm"
        >
            <el-form-item label="业务类型：">
                <el-select
                    v-model="query.businessType"
                    placeholder="请选择业务类型"
                >
                    <el-option
                        v-for="item in compensateTypeList"
                        :value="item.optionKey"
                        :key="item.optionKey"
                        :label="item.optionValue"
                    />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="search" icon="el-icon-search"
                    >查询</el-button
                >
                <el-button @click="searchResetForm">重置</el-button>
            </el-form-item>
        </el-form>
        <!-- 按钮 -->
        <div
            class="btn_warp"
            style="
                display: flex;
                align-items: center;
                justify-content: space-between;
            "
        >
            <span>补偿列表</span>
        </div>
        <!-- 表格 -->
        <el-table
            class="el-tables"
            style="width: 100%; margin-top: 16px"
            :data="tableData"
            border
            @selection-change="handleSelectionChange"
            :header-cell-style="{ background: '#fafafa', color: '#5b5d61' }"
        >
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column
                type="index"
                width="50"
                label="序号"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="businessName"
                show-overflow-tooltip
                label="业务类型名称"
                align="center"
            >
            </el-table-column>
            <el-table-column
                prop="businessType"
                show-overflow-tooltip
                label="业务类型Code"
                align="center"
            >
            </el-table-column>
            <el-table-column
                prop="status"
                label="状态"
                show-overflow-tooltip
                align="center"
            >
                <template slot-scope="scope">
                    <span v-if="scope.row.status == 1">未执行</span>
                    <span v-if="scope.row.status == 2">成功</span>
                    <span v-if="scope.row.status == 3">失败</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="createTime"
                show-overflow-tooltip
                label="创建时间"
                align="center"
            >
                <template slot-scope="scope">
                    <span>{{ scope.row.createTime }}</span>
                </template>
            </el-table-column>
            <el-table-column
                fixed="right"
                label="操作"
                width="180"
                align="right"
            >
                <template slot-scope="scope">
                    <el-button type="text" @click="look(scope.row)"
                        >查看</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <el-pagination
            background
            style="margin-top: 16px; text-align: right"
            :current-page="pagination.pageNo"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="pagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
        <!-- 新增、编辑弹窗 -->
        <el-drawer
            title="任务明细"
            :visible.sync="visibleForm"
            :wrapper-closable="false"
            :size="500"
            class="drawerClass"
        >
            <el-table :data="detailList" style="width: 100%">
                <el-table-column prop="operateName" label="业务名称">
                </el-table-column>
                <el-table-column
                    prop="status"
                    label="状态"
                    show-overflow-tooltip
                    align="center"
                >
                    <template slot-scope="scope">
                        <span v-if="scope.row.status == 1">未执行</span>
                        <span v-if="scope.row.status == 2">成功</span>
                        <span v-if="scope.row.status == 3">失败</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="errorLog"
                    label="失败原因"
                    :show-overflow-tooltip="true"
                >
                </el-table-column>
                <el-table-column fixed="right" label="操作" align="center">
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            :disabled="scope.row.status == 2"
                            @click="execute(scope.row)"
                            >重新执行</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </el-drawer>
    </div>
</template>

<script>
import { Axios } from "@/utils/axios";
export default {
    data() {
        return {
            form: {},
            tableData: [],
            pagination: {},
            detailList: [],
            item: {},
            visibleForm: false,
            FormItem: {},
            compensateTypeList: [],
            query: { pageNo: 1, pageSize: 10, businessType: "" },
        };
    },
    created() {
        this.getList();
        this.getCompensateTypeList();
    },
    watch: {},
    methods: {
        execute(item) {
            Axios.post("/manage/rabbitCompensate/retryCompensateData", {
                ids: [item.id],
            }).then((res) => {
                this.$message({
                    message: "操作成功",
                    type: "success",
                });
                this.look(this.item);
            });
        },
        look(item) {
            this.item = item;
            Axios.get(
                `/manage/rabbitCompensate/detailList?compensateId=${item.id}`
            ).then((res) => {
                this.detailList = res.data;
                this.visibleForm = true;
            });
        },
        search() {
            this.getList();
        },
        getList() {
            Axios.post(
                "/manage/rabbitCompensate/compensatePage",
                this.query
            ).then((res) => {
                this.tableData = res.data.list;
                this.pagination.total = res.data.total;
            });
        },
        getCompensateTypeList() {
            Axios.get("/manage/rabbitCompensate/compensateTypeList").then(
                (res) => {
                    this.compensateTypeList = res.data;
                }
            );
        },
        handleSelectionChange() {},
        // 重置
        searchResetForm() {
            this.versionForm.versionName = "";
            this.versionForm.appTypeId = "";
            this.versionForm.createTime = "";
            this.versionForm.updateTime = "";
            this.getVersionList();
        },
        // 分页
        handleSizeChange(val) {
            this.query.pageNo = 1;
            this.query.pageSize = val;
            this.getList();
        },
        handleCurrentChange(val) {
            this.query.pageNo = val;
            this.getList();
        },
    },
};
</script>

<style lang="scss" scoped>
.card_warp {
    background-color: #fff;
    padding: 20px;
    .btn_warp {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;
        padding-top: 16px;
        border-top: 1px solid #ebeef5;
        span {
            margin: 10px;
        }
        div {
            display: flex;
        }
    }
}
.inputClass {
    width: 100%;
    padding: 0px 50px;
    margin: auto;
    overflow-y: auto;
    height: 714px;
    i {
        margin-left: 5px;
        cursor: pointer;
    }
    li {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 20px;
        width: 80%;
        img {
            margin-right: 10px;
        }
    }
}
</style>
<style lang="scss">
.drawerClass {
    .el-drawer__body {
        padding-right: 30px;
    }
}
.administrationClass,
.demo-ruleForm {
    .el-drawer__body {
        position: relative;
    }
    .yd-form-footer {
        width: 100%;
        height: 70px;
        justify-content: center;
        display: flex;
        align-items: center;
        border-top: 1px #eee solid;
        background-color: #fff;
        position: absolute;
        bottom: 0px;
        left: 0px;
        .el-form-item__content {
            margin: 0px !important;
        }
    }
}
</style>
