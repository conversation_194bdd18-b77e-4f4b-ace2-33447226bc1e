<!--
 * @Descripttion: 
 * @version: 1.0.0
 * @Author: jingrou
 * @Date: 2022-07-07 17:57:38
 * @LastEditors: jingrou
 * @LastEditTime: 2022-07-11 11:45:51
-->
<template>
    <el-card shadow="never" class="card_warp" v-loading="loading">
        <!-- rc  让我把它移出来吧 -->
        <el-tabs v-model="typeId" @tab-click="handleClick">
            <el-tab-pane
                :label="item.name"
                :name="item.id"
                v-for="item in getTypeList"
                :key="item.id"
            >
            </el-tab-pane>
        </el-tabs>
        <TemplateChild :typeMessId="typeId" />

        <div style="height: 750px" v-if="getTypeList === []">
            <el-empty :image-size="200"></el-empty>
        </div>
    </el-card>
</template>

<script>
import TemplateChild from "@/components/TemplateChild";
import { getMessTypeList } from "@/api/templateManage.js";
export default {
    components: { TemplateChild },
    data() {
        return {
            typeId: "1",
            getTypeList: [],
            loading: false,
        };
    },
    methods: {
        getTemplateTypeList() {
            this.loading = true;
            getMessTypeList()
                .then((res) => {
                    this.getTypeList = res.data;
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        handleClick(tab, event) {
            console.log(tab);
        },
    },
    created() {
        this.getTemplateTypeList();
    },
};
</script>

<style lang="scss" scoped></style>
