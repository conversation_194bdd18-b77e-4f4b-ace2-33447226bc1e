<template>
    <div>
        <div class="bg-banner"></div>
        <div id="login-box">
            <div class="login-banner">
                <img src="../../assets/loginBanner.png" />
            </div>
            <!-- 登录的el-form -->
            <el-form
                v-show="formType === 'login'"
                ref="loginFormRef"
                :model="loginForm"
                :rules="rules"
                :hide-required-asterisk="true"
                class="login-form"
            >
                <div class="title-container">
                    <img src="../../assets/logo_icon.png" />
                    <span class="title">一德综合管理平台</span>
                </div>
                <div>
                    <el-form-item prop="username" label="账号">
                        <el-input
                            v-model="loginForm.username"
                            placeholder="请输入手机号/用户名"
                            type="text"
                            tabindex="1"
                        >
                            <template #prefix>
                                <i class="el-icon-user-solid"></i>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item prop="password" label="密码">
                        <el-input
                            v-model="loginForm.password"
                            type="password"
                            placeholder="密码"
                            tabindex="2"
                            show-password
                            @keyup.enter="handleLogin"
                        >
                            <template #prefix>
                                <i class="el-icon-lock"></i>
                            </template>
                        </el-input>
                    </el-form-item>
                </div>
                <div class="flex-bar">
                    <el-checkbox v-model="remember"> 记住我 </el-checkbox>
                    <el-button
                        type="text"
                        link
                        @click="setModifyPasswd('forget')"
                        >忘记密码</el-button
                    >
                </div>
                <el-button
                    :loading="loading"
                    type="primary"
                    size="large"
                    style="width: 100%; padding: 22px 16px"
                    @click.prevent="handleLogin"
                >
                    登录
                </el-button>
                <div class="sub-link">
                    <el-checkbox v-model="copyright">
                        <div :class="{ shake: copyrightMark }">
                            我已认真阅读，理解并同意
                            <a
                                class="agreement"
                                target="_blank"
                                href="/html/protocol.html"
                                >一加壹用户协议、隐私政策。</a
                            >
                        </div>
                    </el-checkbox>
                </div>
            </el-form>
            <!-- 首次修改密码的el-form -->
            <el-form
                v-show="formType === 'reset'"
                ref="resetFormRef"
                :model="resetForm"
                :rules="resetRules"
                :hide-required-asterisk="true"
                class="login-form"
            >
                <div class="reset-title-container">
                    <div class="reset-title">{{ resetTitle }}</div>
                    <div
                        class="reset-text"
                        :class="{ active: isAdmin }"
                        v-if="isTips == 'set'"
                    >
                        {{ loginTips }}
                    </div>
                </div>
                <div>
                    <template v-if="!isAdmin">
                        <el-form-item
                            label="当前手机号："
                            v-if="isTips == 'set'"
                        >
                            <span>{{ resetForm.currentPhone }}</span>
                        </el-form-item>

                        <el-form-item
                            class="form-item-code"
                            prop="currentPhone"
                        >
                            <el-input
                                size="large"
                                class="code-input"
                                maxlength="11"
                                v-model:value="resetForm.currentPhone"
                                placeholder="请输入手机号"
                            >
                            </el-input>
                        </el-form-item>

                        <el-form-item class="form-item-code" prop="verifyCode">
                            <el-input
                                size="large"
                                class="code-input"
                                autocomplete="new-password"
                                v-model:value="resetForm.verifyCode"
                                placeholder="请输入验证码"
                            >
                                <template #suffix>
                                    <el-button
                                        size="small"
                                        type="text"
                                        link
                                        class="code-num"
                                        v-if="codeNum != 180"
                                    >
                                        {{ codeNum }} 秒
                                    </el-button>
                                    <el-button
                                        class="code-num"
                                        size="small"
                                        type="text"
                                        link
                                        v-else
                                        @click="handleCode"
                                        >获取验证码</el-button
                                    >
                                </template>
                            </el-input>
                        </el-form-item>
                    </template>
                    <el-form-item prop="newPassword">
                        <el-input
                            v-model="resetForm.newPassword"
                            autocomplete="new-password"
                            placeholder="请输入含字母、数字、特殊字符，长度为8-20个字符的密码"
                            type="password"
                            show-password
                            tabindex="1"
                        >
                            <template #prefix>
                                <i class="el-icon-lock"></i>
                            </template>
                        </el-input>
                        <!-- <div>
                            <i class="el-icon-warning"></i>
                            请输入含字母、数字、特殊字符，长度为8-20个字符的密码
                        </div> -->
                    </el-form-item>

                    <el-form-item prop="againNewPassword">
                        <el-input
                            :disabled="!resetForm.newPassword"
                            v-model="resetForm.againNewPassword"
                            autocomplete="new-password"
                            @paste="onPaste"
                            type="password"
                            show-password
                            placeholder="请再次输入密码"
                            tabindex="2"
                            @keyup.enter="handleReset"
                        >
                            <template #prefix>
                                <i class="el-icon-lock"></i>
                            </template>
                        </el-input>
                    </el-form-item>
                </div>
                <el-button
                    :loading="loading"
                    type="primary"
                    size="large"
                    style="width: 100%; padding: 22px 16px"
                    @click.prevent="handleReset"
                >
                    确 定
                </el-button>
                <div style="text-align: center">
                    <el-button
                        class="btn-back"
                        type="text"
                        size="large"
                        @click.prevent="handleBack"
                    >
                        返 回
                    </el-button>
                </div>
            </el-form>
        </div>
        <div class="footer copyright">
            <div class="titleTxt">
                ©2024 一德教育科技集团股份有限公司 粤ICP备17028019号
            </div>
        </div>
    </div>
</template>
<script>
import { ACCESS_TOKEN, USER_ID } from "@/store/mutation-types";
import users from "@/api/users";
import { updatePwd, forgetPassword, smsMessage } from "@/api/user";
import { mapMutations } from "vuex";
import { setRouter } from "@/router";
import RSA from "@/utils/rsa.js";
import { verifyPhone, checkField } from "@/utils/toolsValidate.js";

const tipsTitle = {
    3: "为了您的账号安全，初次进入系统请设置新密码",
    4: "系统检测到您许久未登录系统，请设置新密码",
    5: "系统检测到您的账号密码安全系数较弱，请设置新密码",
};
export default {
    data() {
        const reg =
            /^(?=.*[A-Za-z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{}|;':",.\/<>?])[A-Za-z\d!@#$%^&*()_+\-=\[\]{}|;':",.\/<>?]{8,20}$/;
        const validatePassword = (rule, value, callback) => {
            if (value === "") {
                callback(new Error("请输入密码"));
            } else {
                if (!checkField(value, 6, 20)) {
                    return callback(
                        new Error(
                            "请输入含字母、数字、特殊字符，长度为8-20个字符的密码"
                        )
                    );
                }
                if (!reg.test(value)) {
                    return callback(
                        new Error(
                            "请输入含字母、数字、特殊字符，长度为8-20个字符的密码"
                        )
                    );
                }
                callback();
            }
        };
        const validateAgainPassword = (rule, value, callback) => {
            if (value === "") {
                callback(new Error("请再次输入密码!"));
            } else if (value !== this.resetForm.newPassword) {
                callback(new Error("两次输入密码不一致!"));
            } else {
                callback();
            }
        };

        const validateCode = async (_rule, value) => {
            if (!value) {
                return Promise.reject("请输入验证码");
            }
            return Promise.resolve();
        };

        const validateCurrentPhone = async (_rule, value) => {
            if (value && verifyPhone(value)) {
                this.isCorrectPhone = true;
                return Promise.resolve();
            }
            this.isCorrectPhone = false;
            return Promise.reject("请输入正确的手机号码");
        };
        return {
            formType: "login",
            remember: false,
            copyright: false,
            copyrightMark: false,
            userInfo: {},
            loginForm: {
                username: "",
                password: "",
                grant_type: "password",
                client_id: "yide-manage",
                client_secret: "yide1234567",
                captchaId: "",
                ticket: "",
            },
            resetForm: {
                newPassword: "",
                againNewPassword: "",
                currentPhone: "",
                verifyCode: "",
            },
            rules: {
                username: [
                    {
                        required: true,
                        message: "请输入用户名",
                        trigger: "blur",
                    },
                ],
                password: [
                    {
                        required: true,
                        message: "请输入密码",
                        trigger: "blur",
                    },
                ],
            },
            resetRules: {
                newPassword: [
                    {
                        required: true,
                        message: "请输入新密码",
                        trigger: "blur",
                    },
                    { validator: validatePassword, trigger: "blur" },
                ],
                againNewPassword: [
                    {
                        required: true,
                        message: "请再次确认新密码",
                        trigger: "blur",
                    },
                    { validator: validateAgainPassword, trigger: "blur" },
                ],
                verifyCode: [
                    {
                        required: true,
                        message: "请输入验证码",
                        trigger: "blur",
                    },
                    { validator: validateCode, trigger: "blur" },
                ],

                currentPhone: [
                    {
                        required: true,
                        message: "请输入手机号码",
                        trigger: "blur",
                    },
                    { validator: validateCurrentPhone, trigger: "blur" },
                ],
            },
            // 滑块验证
            loading: false,
            resetTitle: "设置新密码",
            isTips: "set",
            time: null,
            codeNum: 180,
            isAdmin: false,
            isCorrectPhone: false,
            loginTips: "为保护您的账号安全及隐私，请设置新密码 ",
        };
    },
    mounted() {
        console.log(process.env);
        if (
            process.env.NODE_ENV == "development" ||
            process.env.NODE_ENV == "uat"
        ) {
            this.loginForm.username = "yide";
            this.loginForm.password = "yide8888";
            this.copyright = true;
        }
    },
    methods: {
        ...mapMutations(["setUserInfo"]),
        // 禁止粘贴
        onPaste(event) {
            event.preventDefault();
        },
        submitLogin() {
            this.loading = true;
            const formData = new FormData();
            const userData = {
                // 加密
                paramEncipher: RSA.encrypt(JSON.stringify(this.loginForm)),
            };
            for (let k in userData) {
                formData.append(k, userData[k]);
            }
            this.isAdmin = false;
            users
                .login(formData)
                .then(async ({ code, data, message }) => {
                    const { accessToken } = data;
                    if (code === 0) {
                        this.$ls.set(ACCESS_TOKEN, accessToken);
                        // 在这里需要 获取一下这个用户信息的密码是不是初始密码,是的话让他去修改密码 不是初始密码就正常登录页面进去
                        const { data: userInfo } = await users.getUserInfo();
                        const { logoutStatus, isAdmin } = userInfo;
                        // logoutStatus （枚举）：
                        // (1, "校验正常"),
                        //     (2, "已注销"),
                        //     (3, "初始化密码（第一次登录）"),
                        //     (4, "用户长时间未登录系统"),
                        //     (5, "弱密码"),
                        if ([3, 4, 5].includes(logoutStatus)) {
                            this.loginTips = tipsTitle[logoutStatus];
                            this.isAdmin = isAdmin;
                            this.isCorrectPhone = true;
                            this.resetForm.currentPhone =
                                this.loginForm.username;
                            this.setModifyPasswd("reset");
                        }
                        this.userInfo = userInfo;
                        if (userInfo && userInfo.isInitializePwd) {
                            this.formType = "reset";
                        } else {
                            await setRouter();
                            await users.loginPage();
                            userInfo && this.setUserInfo(userInfo);
                            const routers = this.$store.state.routers;
                            const path = routers.length
                                ? routers[0].path
                                : "/home";
                            this.$router.push(path);
                        }
                    } else {
                        this.$message.error(message || "登录失败");
                    }
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        handleLogin() {
            if (!this.copyright) {
                this.copyrightMark = true;
                // 使用动画完成抖动效果
                setTimeout(() => {
                    this.copyrightMark = false; // 还原状态
                }, 300);
                return;
            }
            this.$refs.loginFormRef.validate((valid) => {
                if (valid) {
                    // 滑块验证
                    if (
                        ["production", "development", "uat"].includes(
                            process.env.NODE_ENV
                        )
                    ) {
                        this.onVerification();
                    } else {
                        this.submitLogin();
                    }
                } else {
                    return false;
                }
            });
        },
        handleReset() {
            this.$refs.resetFormRef.validate(async (valid) => {
                if (valid) {
                    const {
                        newPassword,
                        againNewPassword,
                        currentPhone,
                        verifyCode,
                    } = this.resetForm;
                    const params = { paramEncipher: "" };
                    let API = null;
                    // 忘记密码
                    if (this.isTips === "forget") {
                        params.paramEncipher = RSA.encrypt(
                            JSON.stringify({
                                password: againNewPassword,
                                phone: currentPhone,
                                verifyCode,
                            })
                        );
                        API = forgetPassword;
                    } else {
                        params.paramEncipher = RSA.encrypt(
                            JSON.stringify({
                                verifyCode,
                                phone: currentPhone,
                                newPwd: newPassword,
                                confirmPwd: againNewPassword,
                            })
                        );
                        API = updatePwd;
                    }
                    try {
                        await API(params);
                    } catch (e) {
                        // 捕获到这里的报错说明setRouter 失败了,没得登录了
                        this.codeNum = 180;
                        clearInterval(this.time);
                        throw e;
                    }
                    // 458309
                    // try {
                    //     await setRouter();
                    // } catch (e) {
                    //     // 捕获到这里的报错说明setRouter 失败了,没得登录了
                    //     this.formType = 'login'
                    //     throw e;
                    // }
                    // await users.loginPage();
                    // this.setUserInfo(this.userInfo);
                    this.handleBack();
                    const routers = this.$store.state.routers;
                    const path = routers.length ? routers[0].path : "/home";
                    this.$router.push(path);
                }
            });
        },

        // ------------- 滑块验证 -------------
        // 发送验证码
        handleCode() {
            // this.$refs.resetFormRef.validate();
            let isSms = true;
            if (this.isCorrectPhone && isSms) {
                const params = {
                    phone: this.resetForm.currentPhone,
                };
                isSms = false;
                smsMessage(params)
                    .then((res) => {
                        this.codeNum = 180;
                        clearInterval(this.time);
                        this.time = setInterval(() => {
                            if (this.codeNum <= 1) {
                                clearInterval(this.time);
                                this.codeNum = 180;
                            } else {
                                this.codeNum--;
                            }
                        }, 1000);
                    })
                    .finally(() => {
                        isSms = true;
                    });
            }
        },
        setModifyPasswd(item) {
            this.formType = "reset";
            this.isTips = item;
            this.resetTitle = item === "forget" ? "忘记密码" : "设置新密码";
        },
        handleBack() {
            this.formType = "login";
            this.$refs.resetFormRef.resetFields();
            this.codeNum = 180;
            clearInterval(this.time);
        },
        // 定义回调函数
        callbacks(res) {
            // ret 验证结果，0：验证成功。2：用户主动关闭验证码。
            if (res.ret === 0) {
                const str = `【randstr】->【${res.randstr}'】      【ticket】->【'${res.ticket}'】'`;
                const ipt = document.createElement("input");
                ipt.value = str;
                document.body.appendChild(ipt);
                ipt.select();
                // document.execCommand("Copy");
                document.body.removeChild(ipt);
                this.loginForm.randStr = res.randstr;
                this.loginForm.ticket = res.ticket;
                this.submitLogin();
            }
        },
        // 定义验证码js加载错误处理函数
        loadErrorCallback() {
            // 生成容灾票据或自行做其它处理
            // eslint-disable-next-line node/no-callback-literal
            this.callbacks({
                ret: 0,
                randstr: "193884511", // 本次验证的随机串，后续票据校验时需传递该参数。
                ticket: "jPH4ltECNN6xlmAWGvqf34X4X", // 验证成功的票据，当且仅当 ret = 0 时 ticket 有值。
                CaptchaType: 1,
                errorCode: 1001,
                errorMessage: "jsload_error",
            });
        },
        // 登录验证
        onVerification() {
            try {
                // https://cloud.tencent.com/document/product/1110/36841
                // 生成一个验证码对象
                // eslint-disable-next-line no-undef
                const captcha = new TencentCaptcha(
                    "193884511",
                    this.callbacks,
                    {
                        needFeedBack: false,
                    }
                );
                // 调用方法，显示验证码
                captcha.show();
            } catch (error) {
                // 加载异常，调用验证码js加载错误处理函数
                this.loadErrorCallback();
            }
        },
    },
    beforeDestroy() {
        if (this.formType === "reset") {
            // // 清除所有缓存
            localStorage.clear();
            window.location.reload();
        }
    },
};
</script>

<style lang="scss" scoped>
.bg-banner {
    position: fixed;
    z-index: 0;
    width: 100%;
    height: 100%;
    background: url(../../assets/loginPage.png) no-repeat center;
    background-size: 1920px 1080px;
}

// 去掉vue中type=password自带的眼睛图案
:deep(input[type="password"]::-ms-reveal) {
    display: none;
}

#login-box {
    padding: 50px;
    position: absolute;
    top: 50%;
    left: 50%;
    display: flex;
    justify-content: space-between;
    overflow: hidden;
    background-color: #ffffff;
    border-radius: 10px;
    box-shadow: var(--el-box-shadow);
    transform: translateX(-50%) translateY(-50%);

    .login-banner {
        position: relative;
        width: 551px;
        overflow: hidden;

        .banner {
            width: 100%;
        }

        .logo {
            position: absolute;
            top: 20px;
            left: 20px;
            height: 30px;
            border-radius: 4px;
            box-shadow: var(--el-box-shadow-light);
        }
    }

    .login-form {
        display: flex;
        flex-direction: column;
        justify-content: center;
        width: 500px;
        min-height: 437px;
        padding-left: 60px;
        overflow: hidden;

        .title-container {
            position: relative;
            margin-bottom: 40px;

            .title {
                font-size: 32px;
                font-weight: 600;
                color: #333333;

                position: absolute;
                top: 50%;
                left: 38%;
                transform: translate(-50%, -50%);
            }
        }

        .reset-title-container {
            .reset-title {
                margin-bottom: 20px;
                font-size: 24px;
                font-weight: 600;
                color: #333333;
            }

            .reset-text {
                font-size: 14px;
                font-weight: 400;
                color: rgba(0, 0, 0, 0.65);

                &.active {
                    margin-bottom: 20px;
                }
            }
        }
    }

    .el-form-item {
        :deep(.el-input) {
            width: 100%;
            height: 40px;
            line-height: inherit;

            input {
                height: 40px;
            }

            .el-input__prefix,
            .el-input__suffix {
                display: flex;
                align-items: center;
            }

            .el-input__prefix {
                left: 10px;
            }

            .el-input__suffix {
                right: 10px;
            }
        }
    }

    :deep(.el-divider__text) {
        background-color: var(--g-container-bg);
    }

    .flex-bar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
    }

    .sub-link {
        display: flex;
        margin-top: 20px;
        font-size: 14px;

        .text {
            margin-right: 10px;
        }
    }
}

.copyright {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 20px;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    line-height: 1.25rem;
    --un-text-opacity: 1;
    color: rgb(120 113 108 / var(--un-text-opacity));

    .titleTxt {
        color: #1e1e1e;
    }
}

.agreement {
    font-size: 14px;
    font-weight: 400;
    color: #2c8aff;
}

.shake {
    animation: shake 0.3s;
}

@keyframes shake {
    0% {
        transform: translateX(0);
    }

    20% {
        transform: translateX(-15px);
    }

    40% {
        transform: translateX(15px);
    }

    60% {
        transform: translateX(-15px);
    }

    80% {
        transform: translateX(15px);
    }

    100% {
        transform: translateX(0);
    }
}

.btn-back {
    color: #999999;
    margin-top: 10px;
    font-size: 13px;
}
</style>
