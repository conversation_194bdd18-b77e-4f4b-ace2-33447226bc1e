<template>
    <!-- 收款配置 -->
    <el-dialog
        title="消费类型"
        :visible.sync="show"
        :before-close="cancel"
        :wrapper-closable="false"
        :footer="null"
        width="800px"
    >
        <el-table
            :header-cell-style="{
                background: '#fafafa',
                color: '#5b5d61',
            }"
            style="min-height: 300px"
            :data="tableData"
            row-key="code"
            border
        >
            <el-table-column type="index" label="序号" width="100" />
            <el-table-column prop="name" label="消费类型" />
        </el-table>
    </el-dialog>
</template>

<script>
export default {
    data() {
        return {
            tableData: [],
            show: false,
        };
    },
    methods: {
        getList() {},
        open(data) {
            this.show = true;
            console.log(data, "data");
            this.tableData = data.typeList;
        },
        cancel() {
            this.$emit("refurbish");
            this.show = false;
        },
    },
};
</script>

<style lang="scss" scoped>
.btn {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    padding-top: 16px;
    border-top: 1px solid #ebeef5;
    span {
        margin: 10px;
    }
}
.pagination {
    margin-top: 16px;
    text-align: right;
    display: flex;
    justify-content: flex-end;
}
</style>
