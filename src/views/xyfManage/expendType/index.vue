<template>
    <el-card shadow="never">
        <div class="btn">
            <span>消费场景管理</span>
        </div>
        <el-table
            :header-cell-style="{
                background: '#fafafa',
                color: '#5b5d61',
            }"
            style="min-height: 500px"
            :data="tableData"
            row-key="code"
            border
        >
            <el-table-column type="index" label="序号" width="100" />
            <el-table-column prop="name" label="场景" />
            <el-table-column
                prop="operation"
                label="操作"
                align="right"
                fixed="right"
            >
                <template #default="{ row }">
                    <el-link
                        type="primary"
                        size="small"
                        style="margin-left: 10px"
                        v-auth="'mamage.xyfManage.expendType.lookType'"
                        @click="lookType(row)"
                        >消费类型</el-link
                    >
                </template>
            </el-table-column>
        </el-table>
        <TypeList ref="typeListRef" />
    </el-card>
</template>

<script>
import { getSceneList } from "@/api/facilitator.js";
import TypeList from "./components/typeList.vue";
export default {
    components: {
        TypeList,
    },
    data() {
        return {
            tableData: [],
        };
    },
    methods: {
        getList() {
            getSceneList().then((res) => {
                this.tableData = res.data;
            });
        },
        lookType(data) {
            this.$refs.typeListRef.open(data);
        },
    },
    created() {
        this.getList();
    },
};
</script>

<style lang="scss" scoped>
.btn {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    padding-top: 16px;
    // border-top: 1px solid #ebeef5;
    span {
        font-size: 20px;
        margin: 10px;
    }
}
.pagination {
    margin-top: 16px;
    text-align: right;
    display: flex;
    justify-content: flex-end;
}
.look_type {
    cursor: pointer;
}
</style>
