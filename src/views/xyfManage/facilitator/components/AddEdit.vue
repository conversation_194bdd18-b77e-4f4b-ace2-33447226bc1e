<template>
    <div>
        <!-- 新增 -->
        <el-dialog
            :title="isEdit ? '编辑服务商' : '新建服务商'"
            :visible.sync="show"
            :before-close="cancel"
            :wrapper-closable="false"
            width="900px"
        >
            <el-form
                ref="ruleForm"
                :model="form"
                :rules="addEditRule"
                label-width="120px"
            >
                <el-form-item label="商户类型：" prop="partnerType">
                    <el-select
                        v-model="form.partnerType"
                        disabled
                        style="width: 100%"
                        placeholder="请选择商户类型（默认全选）"
                    >
                        <el-option label="服务商" value="服务商" />
                    </el-select>
                </el-form-item>
                <el-form-item label="商户名称：" prop="partnerName">
                    <el-input
                        class="input_padding"
                        v-model="form.partnerName"
                        show-word-limit
                        placeholder="请输入商户名称"
                        maxlength="50"
                    />
                </el-form-item>
                <el-form-item label="所在区域：" prop="areaList">
                    <el-cascader
                        @change="changeArea"
                        v-model="form.areaList"
                        :props="{
                            checkStrictly: false,
                            value: 'name',
                            label: 'name',
                            children: 'area',
                        }"
                        style="width: 100%"
                        placeholder="请选择地址省市区级联选择"
                        :options="areaList"
                    />
                </el-form-item>
                <el-form-item label="详细地址：">
                    <el-input
                        type="textarea"
                        placeholder="请输入详细地址"
                        v-model="form.address"
                        maxlength="100"
                        show-word-limit
                        :autosize="{ minRows: 2, maxRows: 6 }"
                    >
                    </el-input>
                </el-form-item>
                <el-form-item label="负责人：">
                    <el-input
                        v-model="form.headName"
                        maxlength="50"
                        show-word-limit
                        size="medium"
                        placeholder="请输入负责人"
                    />
                </el-form-item>
                <el-form-item label="负责人电话：" prop="headPhone">
                    <el-input
                        v-model="form.headPhone"
                        size="medium"
                        placeholder="请输入负责人电话"
                        maxlength="11"
                        show-word-limit
                    />
                </el-form-item>
                <el-form-item
                    v-if="isEdit"
                    label="超管账号："
                    prop="superAccount"
                >
                    <el-input
                        disabled
                        v-model="form.superAccount"
                        size="medium"
                        placeholder="请输入超管账号"
                    />
                </el-form-item>
            </el-form>
            <div slot="footer">
                <div class="footer">
                    <el-button type="primary" @click="confirm">确定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { getAreaList } from "@/api/administrator.js";
import { createPartner, updatePartner } from "@/api/facilitator.js";
import { addEditRule, merchantTypeOption } from "../data";
export default {
    name: "AddEdit",
    data() {
        return {
            areaList: [],
            merchantTypeOption,
            addEditRule,
            isEdit: false,
            show: false,
            form: { partnerType: "服务商", areaList: [] },
        };
    },
    methods: {
        open(status, form) {
            this.getAreaListFn();
            this.isEdit = status;
            this.show = true;
            this.form = { ...form, partnerType: "服务商", areaList: [] };
            this.form.areaList = form.areaName ? form.areaName.split("/") : [];
        },
        // 选择区域
        changeArea(arr) {
            this.form.areaList = arr;
            console.log(arr, this.form.areaList);
        },
        // 编辑
        updatePartnerFn() {
            updatePartner(this.form).then((res) => {
                this.$message.success(res.message);
                this.cancel();
            });
        },
        // 新增
        createPartnerFn() {
            createPartner(this.form).then((res) => {
                this.$message.success(res.message);
                this.cancel();
            });
        },
        // 取消
        cancel() {
            this.$refs.ruleForm.resetFields();
            this.show = false;
            this.$emit("refurbish");
        },
        // 确定
        confirm() {
            console.log(this.form.areaList);
            this.$refs.ruleForm.validate((valid) => {
                if (valid) {
                    this.form.areaName = Array.isArray(this.form.areaList)
                        ? this.form.areaList.join("/")
                        : [];
                    this.isEdit
                        ? this.updatePartnerFn()
                        : this.createPartnerFn();
                } else {
                    return false;
                }
            });
        },
        removeEmptyChildren(data) {
            data.forEach((item) => {
                if (item.area && item.area.length === 0) {
                    delete item.area;
                } else if (item.area) {
                    this.removeEmptyChildren(item.area);
                }
            });
            return data;
        },

        getAreaListFn() {
            getAreaList().then((res) => {
                this.areaList = this.removeEmptyChildren(res.data);
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.footer {
    display: flex;
    justify-content: flex-end;
}
:deep(.el-dialog__footer) {
    border-top: 1px solid #d9d9d9;
}
:deep(.el-dialog__header) {
    border-bottom: 1px solid #d9d9d9;
}
</style>
