<template>
    <div class="info">
        <div class="headBox">
            <i class="el-icon-back" @click="goBack"></i>商户详情
        </div>
        <div class="conten">
            <div class="conten_top">
                <div
                    class="status"
                    :style="{ background: statusBg[info.status] }"
                    v-if="info.status != null"
                >
                    {{ statusText[info.status] }}
                </div>
                <div class="top_tlitle">
                    {{ info.partnerName || "-" }}
                    <div class="tip">
                        {{ merchantTypeText[info.merchantType] }}
                    </div>
                </div>
                <el-row :gutter="20">
                    <el-col :span="8" v-if="info.merchantType == 0">
                        <div class="data_item">
                            商户ID：{{ info.partnerNo || "-" }}
                        </div>
                    </el-col>
                    <el-col :span="8" v-if="info.merchantType != 0">
                        <div class="data_item">
                            上级商户：{{ info.parentName || "-" }}
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <div class="data_item">
                            所在地区：{{ info.areaName + info.address || "-" }}
                        </div>
                    </el-col>
                    <el-col :span="8" v-if="info.merchantType == 0">
                        <div class="data_item">
                            超管账号：{{ info.superAccount || "-" }}
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <div class="data_item">
                            收款配置：{{ info.payMethods || "-" }}
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <div class="data_item">
                            绑定学校：{{ info.schoolNames || "-" }}
                        </div>
                    </el-col>
                </el-row>
                <div class="split_line"></div>
                <el-row :gutter="20">
                    <el-col :span="8">
                        <div class="data_item">
                            负责人：{{ info.headName || "-" }}
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <div class="data_item">
                            负责人电话：{{ info.headPhone || "-" }}
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <div class="data_item">
                            创建人：{{ info.createBy || "-" }}
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <div class="data_item">
                            更新时间：{{ info.updateTime || "-" }}
                        </div>
                    </el-col>
                </el-row>
            </div>
            <div class="split_title">
                <div class="split"></div>
                更新记录
            </div>
            <el-table
                class="table_class"
                :header-cell-style="{ background: '#fafafa', color: '#5b5d61' }"
                :data="tableData"
                border
            >
                <el-table-column type="index" label="序号" width="100" />
                <el-table-column prop="createBy" label="用户">
                </el-table-column>
                <el-table-column prop="changeContent" label="更新内容">
                </el-table-column>
                <el-table-column prop="updateTime" label="更新时间">
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <el-pagination
                background
                class="pagination"
                :current-page="pagination.pageNo"
                :page-sizes="[10, 20, 30, 50]"
                :page-size="pagination.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="pagination.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
    </div>
</template>

<script>
import { getPartnerInfo, changeLogPage } from "@/api/facilitator.js";
import { merchantTypeText, statusText, statusBg } from "../data";
export default {
    name: "FacilitatorInfo",
    data() {
        return {
            merchantTypeText,
            statusText,
            statusBg,
            info: {},
            partnerId: null,
            merchantType: null,
            tableData: [],
            // 分页
            pagination: {
                pageSize: 10,
                pageNo: 1,
                total: 0,
            },
        };
    },
    methods: {
        // 返回
        goBack() {
            this.$emit("back");
        },
        getList() {
            changeLogPage({
                partnerId: this.partnerId,
                merchantType: this.merchantType,
                ...this.pagination,
            }).then((res) => {
                const { total, pageNo, pageSize, list } = res.data;
                this.tableData = list;
                this.pagination.total = total;
                this.pagination.pageNo = pageNo;
                this.pagination.pageSize = pageSize;
            });
        },
        getInfo() {
            getPartnerInfo({
                id: this.partnerId,
                merchantType: this.merchantType,
            }).then((res) => {
                this.info = res.data;
                console.log(this.info);
            });
        },
        open(data) {
            console.log(data, "data");
            this.partnerId = data.id;
            this.merchantType = data.merchantType;
            this.getInfo();
            this.getList();
        },
        // 分页
        handleSizeChange(val) {
            this.pagination.pageNo = 1;
            this.pagination.pageSize = val;
            this.getList();
        },
        handleCurrentChange(val) {
            this.pagination.pageNo = val;
            this.getList();
        },
    },
};
</script>

<style lang="scss" scoped>
.table_class {
    width: 100%;
    margin-top: 16px;
    min-height: 400px;
}
.pagination {
    margin-top: 16px;
    text-align: right;
}
.info {
    height: 100%;
    .headBox {
        height: 62px;
        border-bottom: 1px solid #d9d9d9;
        padding-left: 24px;
        font-weight: 600;
        font-size: 18px;
        color: #262626;
        line-height: 22px;
        display: flex;
        align-items: center;
        .el-icon-back {
            cursor: pointer;
            padding-right: 8px;
            font-size: 24px;
            color: #318afb;
        }
    }
    .conten {
        padding: 20px 0px;
        width: 1024px;
        margin: auto;
        height: 100%;
        .conten_top {
            min-height: 190px;
            background: #f6f6f6;
            border-radius: 4px;
            position: relative;
            padding: 16px;
            .top_tlitle {
                display: flex;
                align-items: center;
                font-weight: 500;
                font-size: 18px;
                color: rgba(0, 0, 0, 0.85);
                line-height: 22px;
                margin-bottom: 10px;
                .tip {
                    width: 48px;
                    height: 18px;
                    background: rgba(49, 138, 251, 0.08);
                    border-radius: 4px;
                    border: 1px solid #318afb;
                    font-weight: 400;
                    font-size: 10px;
                    color: #318afb;
                    line-height: 18px;
                    text-align: center;
                    margin-left: 6px;
                }
            }
            .status {
                position: absolute;
                top: 0;
                right: 0;
                width: 55px;
                height: 24px;
                border-radius: 0px 4px 0px 13px;
                font-weight: 400;
                font-size: 14px;
                color: #ffffff;
                line-height: 24px;
                text-align: center;
            }
            .data_item {
                line-height: 24px;
                margin: 2px 0px;
                font-weight: 400;
                font-size: 14px;
                color: rgba(0, 0, 0, 0.65);
            }
            .split_line {
                height: 1px;
                background: #e6e6e6;
                margin: 10px 0px;
            }
        }
        .split_title {
            font-weight: 500;
            font-size: 14px;
            color: #262626;
            line-height: 20px;
            display: flex;
            margin: 24px 0px 12px 0px;
            align-items: center;
            .split {
                margin-right: 4px;
                width: 2px;
                height: 12px;
                background: #318afb;
            }
        }
    }
}
</style>
