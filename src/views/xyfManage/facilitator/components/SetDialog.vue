<template>
    <div>
        <!-- 收款配置 -->
        <el-dialog
            :title="this.title"
            :visible.sync="show"
            :before-close="cancel"
            :wrapper-closable="false"
            width="408px"
        >
            <el-form
                ref="ruleForm"
                :model="form"
                :rules="setRule"
                label-width="100px"
            >
                <el-form-item
                    v-if="type === 'CollectMoneySet'"
                    label="收款配置："
                    prop="payTypeList"
                >
                    <el-select
                        v-model="form.payTypeList"
                        style="width: 100%"
                        placeholder="请选择收款配置"
                        multiple
                    >
                        <el-option
                            v-for="(item, index) in collectMoneyOption"
                            :key="index"
                            :label="item.name"
                            :value="item.code"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item
                    v-else-if="type === 'BindSchool'"
                    label="绑定学校："
                    prop="schoolIds"
                >
                    <el-select
                        filterable
                        v-model="form.schoolIds"
                        style="width: 100%"
                        multiple
                        placeholder="请选择绑定学校"
                    >
                        <el-option
                            v-for="item in bindSchoolListOption"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item v-else label="分配场景：" prop="sceneCodes">
                    <el-select
                        filterable
                        v-model="form.sceneCodes"
                        style="width: 100%"
                        multiple
                        placeholder="请选择"
                    >
                        <el-option
                            v-for="item in sceneOption"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code"
                        />
                    </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <div class="footer">
                    <el-button type="primary" @click="submit">确定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {
    getPartnerPayList,
    updatePartnerPay,
    updatePartnerSchool,
    updatePartnerScene,
    getSchoolList,
    getPartnerInfo,
    getSceneList,
    getPartnerScene,
} from "@/api/facilitator.js";
import { setRule } from "../data";
export default {
    name: "SetDialog",
    data() {
        return {
            title: "",
            setRule,
            collectMoneyOption: [],
            bindSchoolListOption: [],
            sceneOption: [],
            // BindSchool-绑定学校
            // CollectMoneySet 收款配置
            // SceneSet 分配场景
            type: "CollectMoneySet",
            show: false,
            form: {
                schoolIds: [],
                payTypeList: [],
                sceneCodes: [],
            },
            partnerId: null,
            merchantType: null,
            titleText: {
                BindSchool: " 绑定学校",
                CollectMoneySet: " 收款配置",
                SceneSet: " 分配场景",
            },
        };
    },
    methods: {
        getInfo() {
            getPartnerInfo({
                id: this.partnerId,
                merchantType: this.merchantType,
            }).then((res) => {
                this.form.schoolIds = res.data.schoolIds || [];
                this.form.payTypeList = res.data.payTypeList || [];
            });
        },
        // 获取学校
        async getSchoolListFn() {
            await getSchoolList({ schoolName: null }).then((res) => {
                this.bindSchoolListOption = res.data;
            });
        },
        // 获取场景下拉
        async getSceneOptionFn() {
            await getSceneList({ id: this.partnerId }).then((res) => {
                this.sceneOption = res.data;
            });
        },
        // 回显场景
        getPartnerSceneFn() {
            getPartnerScene({ id: this.partnerId }).then((res) => {
                this.form.sceneCodes = res.data.map((i) => i.code);
            });
        },

        // 获取收款配置下拉选项
        async getPayList() {
            await getPartnerPayList().then((res) => {
                this.collectMoneyOption = res.data;
            });
        },
        // 确认收款配置
        collectMoneySetFn() {
            updatePartnerPay({ partnerId: this.partnerId, ...this.form }).then(
                (res) => {
                    this.$message.success(res.message);
                    this.cancel();
                }
            );
        },
        // 确认绑定学校
        bindSchoolFn() {
            updatePartnerSchool({
                partnerId: this.partnerId,
                ...this.form,
            }).then((res) => {
                this.$message.success(res.message);
                this.cancel();
            });
        },
        // 确定分配场景
        setSceneOptionFn() {
            updatePartnerScene({
                partnerId: this.partnerId,
                ...this.form,
            }).then((res) => {
                this.$message.success(res.message);
                this.cancel();
            });
        },
        open(myType, data) {
            this.title = this.titleText[myType];
            this.show = true;
            this.type = myType;
            this.partnerId = data.id;
            this.merchantType = data.merchantType;
            if (this.type === "CollectMoneySet") {
                this.getPayList();
                this.getInfo();
            } else if (this.type === "BindSchool") {
                this.getSchoolListFn();
                this.getInfo();
            } else {
                this.getSceneOptionFn();
                this.getPartnerSceneFn();
            }
        },
        submit() {
            if (this.type === "CollectMoneySet") {
                this.collectMoneySetFn();
            } else if (this.type === "BindSchool") {
                this.bindSchoolFn();
            } else {
                this.setSceneOptionFn();
            }
        },
        cancel() {
            this.form.collectMoneyOption = [];
            this.form.payTypeList = [];
            this.$refs.ruleForm.resetFields();
            this.$emit("refurbish");
            this.show = false;
        },
    },
};
</script>

<style lang="scss" scoped>
.footer {
    display: flex;
    justify-content: flex-end;
}
:deep(.el-dialog__footer) {
    border-top: 1px solid #d9d9d9;
}
:deep(.el-dialog__header) {
    border-bottom: 1px solid #d9d9d9;
}
</style>
