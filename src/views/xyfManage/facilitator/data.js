export const targetOption = [];

export const statusOption = [
    {
        label: "关闭",
        value: 0,
    },
    {
        label: "开启",
        value: 1,
    },
];

export const statusText = {
    0: "关闭",
    1: "开启",
};

export const statusBg = {
    0: "#D0D0D0",
    1: "#318afb",
};

export const merchantTypeText = {
    0: "服务商",
    1: "一级商户",
    2: "二级商户",
    3: "门店",
    4: "个人",
};

export const merchantTypeOption = [
    {
        label: "服务商",
        value: 0,
    },
    {
        label: "一级商户",
        value: 1,
    },
    {
        label: "二级商户",
        value: 2,
    },
    {
        label: "门店",
        value: 3,
    },
    {
        label: "个人",
        value: 4,
    },
];

export const statusEditText = {
    0: "启用",
    1: "禁用",
};

export const addEditRule = {
    partnerType: [
        {
            required: true,
            message: "请选择商户类型",
        },
    ],
    partnerName: [
        {
            required: true,
            message: "请输入商户名称",
        },
    ],
    areaList: [
        {
            required: true,
            message: "请选择地址省市区",
            trigger: "blur",
        },
    ],
    headPhone: [{ pattern: /^[0-9]+$/, message: "只能输入数字" }],
};

export const timeTransformOne = (timeList) => {
    const startTime = timeList && timeList[0] ? timeList[0] : "";
    const endTime = timeList && timeList[1] ? timeList[1] : "";
    return { startTime, endTime };
};

export const timeTransformTwo = ({ startTime, endTime }) => {
    const timeList = startTime && endTime ? [startTime, endTime] : [];
    return timeList;
};

export const setRule = {
    payTypeList: [
        {
            required: true,
            message: "请选择收款配置",
        },
    ],
    schoolIds: [
        {
            required: true,
            message: "请选择绑定学校",
        },
    ],
    sceneCodes: [
        {
            required: true,
            message: "请选择要分配的场景",
        },
    ],
};
