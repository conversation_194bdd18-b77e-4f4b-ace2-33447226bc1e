<template>
    <div style="height: 100%; background: #ffffff">
        <div class="facilitator" v-if="isShowTable">
            <el-card shadow="never">
                <!-- 表单 -->
                <el-form ref="form" :inline="true" :model="form">
                    <el-form-item label="服务商ID：">
                        <el-input
                            v-model="form.partnerNo"
                            placeholder="请输入"
                        />
                    </el-form-item>
                    <el-form-item label="服务商名称：">
                        <el-input
                            v-model="form.partnerName"
                            placeholder="请输入"
                        />
                    </el-form-item>
                    <el-form-item label="所在地址：">
                        <el-cascader
                            @change="changeArea"
                            v-model="form.areaList"
                            :props="{
                                checkStrictly: false,
                                value: 'name',
                                label: 'name',
                                children: 'area',
                            }"
                            :options="areaList"
                            clearable
                        />
                    </el-form-item>
                    <el-form-item label="商户状态：">
                        <el-select
                            v-model="form.status"
                            clearable
                            placeholder="全部"
                        >
                            <el-option
                                v-for="item in statusOption"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="负责人：">
                        <el-input
                            v-model="form.headName"
                            placeholder="请输入"
                        />
                    </el-form-item>
                    <el-form-item label="更新时间：" class="login_time_class">
                        <el-date-picker
                            clearable
                            @change="changePicker"
                            v-model="form.updateTime"
                            type="daterange"
                            range-separator="至"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                        >
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item>
                        <el-button
                            type="primary"
                            @click="handleSearch"
                            icon="el-icon-search"
                            >查询</el-button
                        >
                        <el-button @click="reset">重置</el-button>
                    </el-form-item>
                </el-form>
                <!-- 新增 -->
                <div class="btn">
                    <span>服务商管理</span>
                    <div class="multipartFile">
                        <el-button
                            size="medium"
                            icon="el-icon-plus"
                            type="primary"
                            v-auth="'mamage.xyfManage.facilitator.add'"
                            @click="handleAddEdit(false)"
                            >新建服务商</el-button
                        >
                    </div>
                </div>
                <el-table
                    :header-cell-style="{
                        background: '#fafafa',
                        color: '#5b5d61',
                    }"
                    style="min-height: 500px"
                    :data="tableData"
                    row-key="id"
                    border
                    lazy
                    :load="loadNode"
                    :tree-props="{
                        children: 'children',
                        hasChildren: 'id',
                    }"
                    :indent="16"
                >
                    <el-table-column prop="partnerNo" label="商户ID">
                    </el-table-column>
                    <el-table-column prop="partnerName" label="商户名称">
                    </el-table-column>
                    <el-table-column prop="areaName" label="所在地区">
                    </el-table-column>
                    <el-table-column prop="merchantType" label="商户类型">
                        <template #default="{ row }">
                            {{ merchantTypeText[row.merchantType] }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="status" label="商户状态">
                        <template #default="{ row }">
                            {{ statusText[row.status] }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="headName" label="负责人">
                    </el-table-column>
                    <el-table-column prop="updateTime" label="更新时间">
                    </el-table-column>
                    <el-table-column
                        prop="operation"
                        label="操作"
                        :width="360"
                        align="right"
                        fixed="right"
                    >
                        <template #default="{ row }">
                            <div class="btn_operation">
                                <el-link
                                    type="primary"
                                    size="small"
                                    style="margin-left: 10px"
                                    v-auth="'mamage.xyfManage.facilitator.info'"
                                    @click="facilitatorInfoFn(row)"
                                    >详情</el-link
                                >
                                <span v-if="row.merchantType == 0">
                                    <el-link
                                        :underline="false"
                                        type="primary"
                                        icon="el-icon-edit"
                                        style="margin-left: 10px"
                                        v-auth="
                                            'mamage.xyfManage.facilitator.edit'
                                        "
                                        @click="handleAddEdit(true, row)"
                                        >编辑</el-link
                                    >
                                    <el-link
                                        :underline="false"
                                        style="margin-left: 10px"
                                        type="primary"
                                        @click="updateStatusFn(row)"
                                        v-auth="
                                            'mamage.xyfManage.facilitator.statusEdit'
                                        "
                                    >
                                        {{ row.status == 1 ? "关闭" : "开启" }}
                                    </el-link>
                                    <el-link
                                        :underline="false"
                                        style="margin-left: 10px"
                                        type="primary"
                                        v-auth="
                                            'mamage.xyfManage.facilitator.collectMoneySet'
                                        "
                                        @click="
                                            setDialogFn('CollectMoneySet', row)
                                        "
                                    >
                                        收款配置
                                    </el-link>
                                    <el-link
                                        :underline="false"
                                        style="margin-left: 10px"
                                        type="primary"
                                        v-auth="
                                            'mamage.xyfManage.facilitator.bindSchool'
                                        "
                                        @click="setDialogFn('BindSchool', row)"
                                    >
                                        绑定学校
                                    </el-link>
                                    <el-link
                                        :underline="false"
                                        style="margin-left: 10px"
                                        type="primary"
                                        v-auth="
                                            'mamage.xyfManage.facilitator.sceneSet'
                                        "
                                        @click="setDialogFn('SceneSet', row)"
                                    >
                                        分配场景
                                    </el-link>
                                </span>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
                <!-- 分页 -->
                <el-pagination
                    background
                    class="pagination"
                    :current-page="pagination.pageNo"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="pagination.pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pagination.total"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
                <!-- 新增编辑 -->
                <AddEdit ref="addEditRef" @refurbish="getList"></AddEdit>
                <!-- 设置 -->
                <SetDialog ref="setDialogRef" @refurbish="getList" />
            </el-card>
        </div>
        <!-- 详情 -->
        <FacilitatorInfo
            v-show="!isShowTable"
            ref="facilitatorInfoRef"
            @back="isShowTable = true"
        />
    </div>
</template>

<script>
import AddEdit from "./components/AddEdit";
import FacilitatorInfo from "./components/FacilitatorInfo";
import SetDialog from "./components/SetDialog";
import { getAreaList } from "@/api/administrator.js";
import {
    getPartnerList,
    updateStatus,
    getPartnerChildren,
} from "@/api/facilitator.js";
import {
    merchantTypeText,
    statusText,
    statusOption,
    timeTransformOne,
} from "./data";
export default {
    components: {
        AddEdit,
        FacilitatorInfo,
        SetDialog,
    },
    data() {
        return {
            areaList: [],
            statusText,
            isShowTable: true,
            tableData: [],
            statusOption,
            merchantTypeText, // 对象字典
            form: {},
            // 分页
            pagination: {
                pageSize: 10,
                pageNo: 1,
                total: 0,
            },
            isEdit: false,
        };
    },
    methods: {
        loadNode(row, treeNode, resolve) {
            if (treeNode) {
                getPartnerChildren({
                    partnerId: row.partnerId || null,
                    pid: row.id || null,
                })
                    .then((res) => {
                        resolve(res.data || []);
                    })
                    .catch(() => {
                        resolve([]);
                    });
            } else {
                resolve([]);
            }
            console.log(this.tableData, "this.tableData;");
        },
        getList() {
            getPartnerList({ ...this.form, ...this.pagination }).then((res) => {
                const { total, pageNo, pageSize, list } = res.data;
                this.tableData = list;
                this.pagination.total = total;
                this.pagination.pageNo = pageNo;
                this.pagination.pageSize = pageSize;
            });
        },
        statusFn({ id, status }) {
            updateStatus({ id, status: status == 1 ? 0 : 1 }).then((res) => {
                this.$message.success(res.message);
                this.getList();
            });
        },
        // 编辑状态
        updateStatusFn(data) {
            if (data.status == 1) {
                this.$confirm("关闭后该商户不能进行收款，确认关闭？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(() => {
                        this.statusFn(data);
                    })
                    .catch(() => {
                        this.$message({
                            type: "info",
                            message: "已取消删除",
                        });
                    });
            } else {
                this.statusFn(data);
            }
        },
        // 新增编辑
        handleAddEdit(isEdit, form = {}) {
            this.isEdit = isEdit;
            this.$refs.addEditRef.open(isEdit, form);
        },
        facilitatorInfoFn(item) {
            console.log(item);
            this.isShowTable = false;
            this.$refs.facilitatorInfoRef.open(item);
        },
        changePicker(e) {
            const { endTime, startTime } = timeTransformOne(e);
            this.form.startTime = startTime;
            this.form.endTime = endTime;
        },
        changeArea(arr) {
            this.form.areaName = Array.isArray(arr) ? arr.join("/") : "";
        },
        // 搜素
        handleSearch() {
            this.pagination.pageNo = 1;
            this.getList();
        },
        // 重置
        reset() {
            this.pagination.pageNo = 1;
            this.form = {};
            this.getList();
        },
        // 收款配置
        setDialogFn(type, item) {
            this.$refs.setDialogRef.open(type, item);
        },
        // 分页
        handleSizeChange(val) {
            this.pagination.pageNo = 1;
            this.pagination.pageSize = val;
            this.getList();
        },
        handleCurrentChange(val) {
            this.pagination.pageNo = val;
            this.getList();
        },
        removeEmptyChildren(data) {
            data.forEach((item) => {
                if (item.area && item.area.length === 0) {
                    delete item.area;
                } else if (item.area) {
                    this.removeEmptyChildren(item.area);
                }
            });
            return data;
        },

        getAreaListFn() {
            getAreaList().then((res) => {
                this.areaList = this.removeEmptyChildren(res.data);
            });
        },
    },
    created() {
        this.getAreaListFn();
        this.getList();
    },
};
</script>

<style lang="scss" scoped>
.facilitator {
    height: 100%;
    padding: 20px;
}
.btn {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    padding-top: 16px;
    border-top: 1px solid #ebeef5;
    span {
        margin: 10px;
    }
    .multipartFile {
        display: flex;
    }
    .download {
        justify-content: center;
        display: flex;
        align-items: center;
        margin: 10px;
        a {
            color: #409eff;
        }
    }
}
.pagination {
    margin-top: 16px;
    text-align: right;
    display: flex;
    justify-content: flex-end;
}
.btn_operation {
    width: 100%;
}
</style>
