<template>
    <el-card shadow="never">
        <div class="payer_manage">
            <el-tabs v-model="type" @tab-click="handleClick">
                <el-tab-pane label="微信配置" name="wxConfig"></el-tab-pane>
            </el-tabs>
            <div style="margin-top: 10px">
                <!-- 表单 -->
                <el-form ref="form" :inline="true" :model="form">
                    <el-form-item label="服务商：">
                        <el-select
                            filterable
                            v-model="form.partnerId"
                            clearable
                            placeholder="全部"
                        >
                            <el-option
                                v-for="item in findPartnerOption"
                                :key="item.id"
                                :label="item.partnerName"
                                :value="item.id"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="学校：">
                        <el-select
                            filterable
                            v-model="form.schoolId"
                            clearable
                            placeholder="全部"
                        >
                            <el-option
                                v-for="item in schoolList"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="人脸机构ID：">
                        <el-input v-model="form.orgId" placeholder="请输入" />
                    </el-form-item>
                    <el-form-item>
                        <el-button
                            type="primary"
                            @click="getList"
                            icon="el-icon-search"
                            >查询</el-button
                        >
                        <el-button @click="reset">重置</el-button>
                    </el-form-item>
                </el-form>
                <div class="btn">
                    <span>支付商管理</span>
                </div>
                <el-table
                    :header-cell-style="{
                        background: '#fafafa',
                        color: '#5b5d61',
                    }"
                    style="min-height: 500px"
                    :data="tableData"
                    row-key="id"
                    border
                >
                    <el-table-column type="index" label="序号" width="100" />
                    <el-table-column prop="partnerId" label="服务商ID" />
                    <el-table-column prop="partnerName" label="服务商" />
                    <el-table-column prop="schoolName" label="学校" />
                    <el-table-column prop="orgId" label="人脸机构ID" />
                    <el-table-column prop="status" label="状态">
                        <template #default="{ row }">
                            {{ statusText[row.status] }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="createBy" label="创建人" />
                    <el-table-column prop="createTime" label="创建时间" />
                </el-table>
                <!-- 分页 -->
                <el-pagination
                    background
                    class="pagination"
                    :current-page="pagination.pageNo"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="pagination.pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pagination.total"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </div>
        </div>
    </el-card>
</template>

<script>
import { getFindPaged, getSchoolList } from "@/api/facilitator.js";
import { getFindPartner } from "@/api/gateRegistrationCode.js";
export default {
    data() {
        return {
            type: "wxConfig",
            form: {},
            statusText: {
                true: "启用",
                false: "禁用",
            },
            // 分页
            pagination: {
                pageSize: 10,
                pageNo: 1,
                total: 0,
            },
            tableData: [],
            schoolList: [],
            findPartnerOption: [],
        };
    },
    methods: {
        handleClick(e, i) {
            console.log(e, i);
        },
        getFindPartnerFn() {
            getFindPartner({}).then((res) => {
                this.findPartnerOption = res.data;
            });
        },
        // 获取学校
        getSchoolListFn() {
            getSchoolList({ schoolName: null }).then((res) => {
                this.schoolList = res.data;
            });
        },
        getList() {
            getFindPaged({ ...this.form, ...this.pagination }).then((res) => {
                const { total, pageNo, pageSize, list } = res.data;
                this.tableData = list;
                this.pagination.total = total;
                this.pagination.pageNo = pageNo;
                this.pagination.pageSize = pageSize;
            });
        },
        reset() {
            this.pagination.pageNo = 1;
            this.form = {};
            this.getList();
        },
        // 分页
        handleSizeChange(val) {
            this.pagination.pageNo = 1;
            this.pagination.pageSize = val;
            this.getList();
        },
        handleCurrentChange(val) {
            this.pagination.pageNo = val;
            this.getList();
        },
    },
    created() {
        this.getFindPartnerFn();
        this.getSchoolListFn();
        this.getList();
    },
};
</script>

<style lang="scss" scoped>
.payer_manage {
    .pagination {
        margin-top: 16px;
        text-align: right;
        display: flex;
        justify-content: flex-end;
    }
}
.btn {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    padding-top: 16px;
    border-top: 1px solid #ebeef5;
    span {
        margin: 10px;
    }
}
</style>
