const path = require("path");
const resolve = (dir) => path.join(__dirname, dir);
module.exports = {
    devServer: {
        open: true,
        port: 8080,
    },
    css: {
        extract: true,
        loaderOptions: {
            sass: {data: `@import "@/style/index.scss";`},
        },
    },
    chainWebpack: (config) => {
        // 移除 prefetch 插件 这样访问login 才不会加载其他路由js
        config.plugins.delete("prefetch");

        // 配置别名
        config.resolve.alias
            .set("@", resolve("src"))
            .set("components", resolve("src/components"));
    },
};
